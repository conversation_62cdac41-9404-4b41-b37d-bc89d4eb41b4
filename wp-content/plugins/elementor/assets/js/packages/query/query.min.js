/*! For license information please see query.min.js.LICENSE.txt */
!function(){"use strict";var t={7557:function(t){t.exports=window.React},9106:function(t,e,s){var r=s(7557),i=Symbol.for("react.element"),n=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};e.jsx=function(t,e,s){var r,u={},c=null,h=null;for(r in void 0!==s&&(c=""+s),void 0!==e.key&&(c=""+e.key),void 0!==e.ref&&(h=e.ref),e)n.call(e,r)&&!o.hasOwnProperty(r)&&(u[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps)void 0===u[r]&&(u[r]=e[r]);return{$$typeof:i,type:t,key:c,ref:h,props:u,_owner:a.current}}},9370:function(t,e,s){t.exports=s(9106)}},e={};function s(r){var i=e[r];if(void 0!==i)return i.exports;var n=e[r]={exports:{}};return t[r](n,n.exports,s),n.exports}s.d=function(t,e){for(var r in e)s.o(e,r)&&!s.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};s.r(r),s.d(r,{QueryClient:function(){return V},QueryClientProvider:function(){return nt},createQueryClient:function(){return St},getQueryClient:function(){return Pt},useInfiniteQuery:function(){return Ot},useMutation:function(){return Ct},useQuery:function(){return bt},useQueryClient:function(){return it}});var i="undefined"==typeof window||"Deno"in globalThis;function n(){}function a(t){return"number"==typeof t&&t>=0&&t!==1/0}function o(t,e){return Math.max(t+(e||0)-Date.now(),0)}function u(t,e){return"function"==typeof t?t(e):t}function c(t,e){return"function"==typeof t?t(e):t}function h(t,e){const{type:s="all",exact:r,fetchStatus:i,predicate:n,queryKey:a,stale:o}=t;if(a)if(r){if(e.queryHash!==d(a,e.options))return!1}else if(!p(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return!("boolean"==typeof o&&e.isStale()!==o||i&&i!==e.state.fetchStatus||n&&!n(e))}function l(t,e){const{exact:s,status:r,predicate:i,mutationKey:n}=t;if(n){if(!e.options.mutationKey)return!1;if(s){if(f(e.options.mutationKey)!==f(n))return!1}else if(!p(e.options.mutationKey,n))return!1}return!(r&&e.state.status!==r||i&&!i(e))}function d(t,e){return(e?.queryKeyHashFn||f)(t)}function f(t){return JSON.stringify(t,(t,e)=>b(e)?Object.keys(e).sort().reduce((t,s)=>(t[s]=e[s],t),{}):e)}function p(t,e){return t===e||typeof t==typeof e&&!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&Object.keys(e).every(s=>p(t[s],e[s]))}function y(t,e){if(t===e)return t;const s=v(t)&&v(e);if(s||b(t)&&b(e)){const r=s?t:Object.keys(t),i=r.length,n=s?e:Object.keys(e),a=n.length,o=s?[]:{},u=new Set(r);let c=0;for(let r=0;r<a;r++){const i=s?r:n[r];(!s&&u.has(i)||s)&&void 0===t[i]&&void 0===e[i]?(o[i]=void 0,c++):(o[i]=y(t[i],e[i]),o[i]===t[i]&&void 0!==t[i]&&c++)}return i===a&&c===i?t:o}return e}function m(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function v(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function b(t){if(!g(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!g(s)&&!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function g(t){return"[object Object]"===Object.prototype.toString.call(t)}function O(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?y(t,e):e}function R(t,e,s=0){const r=[...t,e];return s&&r.length>s?r.slice(1):r}function C(t,e,s=0){const r=[e,...t];return s&&r.length>s?r.slice(0,-1):r}var w=Symbol();function P(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==w?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}function S(t,e){return"function"==typeof t?t(...e):!!t}var Q=t=>setTimeout(t,0),q=function(){let t=[],e=0,s=t=>{t()},r=t=>{t()},i=Q;const n=r=>{e?t.push(r):i(()=>{s(r)})};return{batch:n=>{let a;e++;try{a=n()}finally{e--,e||(()=>{const e=t;t=[],e.length&&i(()=>{r(()=>{e.forEach(t=>{s(t)})})})})()}return a},batchCalls:t=>(...e)=>{n(()=>{t(...e)})},schedule:n,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{r=t},setScheduler:t=>{i=t}}}(),E=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},F=new class extends E{#t;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#t?this.#t:"hidden"!==globalThis.document?.visibilityState}},x=new class extends E{#r=!0;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#r!==t&&(this.#r=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#r}};function M(){let t,e;const s=new Promise((s,r)=>{t=s,e=r});function r(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=e=>{r({status:"fulfilled",value:e}),t(e)},s.reject=t=>{r({status:"rejected",reason:t}),e(t)},s}function T(t){return Math.min(1e3*2**t,3e4)}function D(t){return"online"!==(t??"online")||x.isOnline()}var I=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function A(t){return t instanceof I}function U(t){let e,s=!1,r=0,n=!1;const a=M(),o=()=>F.isFocused()&&("always"===t.networkMode||x.isOnline())&&t.canRun(),u=()=>D(t.networkMode)&&t.canRun(),c=s=>{n||(n=!0,t.onSuccess?.(s),e?.(),a.resolve(s))},h=s=>{n||(n=!0,t.onError?.(s),e?.(),a.reject(s))},l=()=>new Promise(s=>{e=t=>{(n||o())&&s(t)},t.onPause?.()}).then(()=>{e=void 0,n||t.onContinue?.()}),d=()=>{if(n)return;let e;const a=0===r?t.initialPromise:void 0;try{e=a??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(c).catch(e=>{if(n)return;const a=t.retry??(i?0:3),u=t.retryDelay??T,c="function"==typeof u?u(r,e):u,f=!0===a||"number"==typeof a&&r<a||"function"==typeof a&&a(r,e);var p;!s&&f?(r++,t.onFail?.(r,e),(p=c,new Promise(t=>{setTimeout(t,p)})).then(()=>o()?void 0:l()).then(()=>{s?h(e):d()})):h(e)})};return{promise:a,cancel:e=>{n||(h(new I(e)),t.abort?.())},continue:()=>(e?.(),a),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:u,start:()=>(u()?d():l().then(d),a)}}var k=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),a(this.gcTime)&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(i?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},j=class extends k{#n;#a;#o;#u;#c;#h;#l;constructor(t){super(),this.#l=!1,this.#h=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#u=t.client,this.#o=this.#u.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#n=function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,r=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(t){this.options={...this.#h,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(t,e){const s=O(this.state.data,t,this.options);return this.#d({data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){this.#d({type:"setState",state:t,setStateOptions:e})}cancel(t){const e=this.#c?.promise;return this.#c?.cancel(t),e?e.then(n).catch(n):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some(t=>!1!==c(t.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===w||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===u(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!o(this.state.dataUpdatedAt,t))}onFocus(){const t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){const t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#c&&(this.#l?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}const s=new AbortController,r=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#l=!0,s.signal)})},i=()=>{const t=P(this.options,e),s=(()=>{const t={client:this.#u,queryKey:this.queryKey,meta:this.meta};return r(t),t})();return this.#l=!1,this.options.persister?this.options.persister(t,s,this):t(s)},n=(()=>{const t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:i};return r(t),t})();this.options.behavior?.onFetch(n,this),this.#a=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===n.fetchOptions?.meta||this.#d({type:"fetch",meta:n.fetchOptions?.meta});const a=t=>{A(t)&&t.silent||this.#d({type:"error",error:t}),A(t)||(this.#o.config.onError?.(t,this),this.#o.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#c=U({initialPromise:e?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0!==t){try{this.setData(t)}catch(t){return void a(t)}this.#o.config.onSuccess?.(t,this),this.#o.config.onSettled?.(t,this.state.error,this),this.scheduleGc()}else a(new Error(`${this.queryHash} data is undefined`))},onError:a,onFail:(t,e)=>{this.#d({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#c.start()}#d(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...K(e.data,this.options),fetchMeta:t.meta??null};case"success":return this.#a=void 0,{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return A(s)&&s.revert&&this.#a?{...this.#a,fetchStatus:"idle"}:{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),q.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:t})})}};function K(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:D(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}var _=class extends E{constructor(t={}){super(),this.config=t,this.#f=new Map}#f;build(t,e,s){const r=e.queryKey,i=e.queryHash??d(r,e);let n=this.get(i);return n||(n=new j({client:t,queryKey:r,queryHash:i,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(r)}),this.add(n)),n}add(t){this.#f.has(t.queryHash)||(this.#f.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=this.#f.get(t.queryHash);e&&(t.destroy(),e===t&&this.#f.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){q.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#f.get(t)}getAll(){return[...this.#f.values()]}find(t){const e={exact:!0,...t};return this.getAll().find(t=>h(e,t))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter(e=>h(t,e)):e}notify(t){q.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){q.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){q.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},L=class extends k{#p;#y;#c;constructor(t){super(),this.mutationId=t.mutationId,this.#y=t.mutationCache,this.#p=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#p.includes(t)||(this.#p.push(t),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#p=this.#p.filter(e=>e!==t),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(t){const e=()=>{this.#d({type:"continue"})};this.#c=U({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{this.#d({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#d({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});const s="pending"===this.state.status,r=!this.#c.canStart();try{if(s)e();else{this.#d({type:"pending",variables:t,isPaused:r}),await(this.#y.config.onMutate?.(t,this));const e=await(this.options.onMutate?.(t));e!==this.state.context&&this.#d({type:"pending",context:e,variables:t,isPaused:r})}const i=await this.#c.start();return await(this.#y.config.onSuccess?.(i,t,this.state.context,this)),await(this.options.onSuccess?.(i,t,this.state.context)),await(this.#y.config.onSettled?.(i,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(i,null,t,this.state.context)),this.#d({type:"success",data:i}),i}catch(e){try{throw await(this.#y.config.onError?.(e,t,this.state.context,this)),await(this.options.onError?.(e,t,this.state.context)),await(this.#y.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,e,t,this.state.context)),e}finally{this.#d({type:"error",error:e})}}finally{this.#y.runNext(this)}}#d(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),q.batch(()=>{this.#p.forEach(e=>{e.onMutationUpdate(t)}),this.#y.notify({mutation:this,type:"updated",action:t})})}},H=class extends E{constructor(t={}){super(),this.config=t,this.#m=new Set,this.#v=new Map,this.#b=0}#m;#v;#b;build(t,e,s){const r=new L({mutationCache:this,mutationId:++this.#b,options:t.defaultMutationOptions(e),state:s});return this.add(r),r}add(t){this.#m.add(t);const e=N(t);if("string"==typeof e){const s=this.#v.get(e);s?s.push(t):this.#v.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#m.delete(t)){const e=N(t);if("string"==typeof e){const s=this.#v.get(e);if(s)if(s.length>1){const e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&this.#v.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=N(t);if("string"==typeof e){const s=this.#v.get(e),r=s?.find(t=>"pending"===t.state.status);return!r||r===t}return!0}runNext(t){const e=N(t);if("string"==typeof e){const s=this.#v.get(e)?.find(e=>e!==t&&e.state.isPaused);return s?.continue()??Promise.resolve()}return Promise.resolve()}clear(){q.batch(()=>{this.#m.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#m.clear(),this.#v.clear()})}getAll(){return Array.from(this.#m)}find(t){const e={exact:!0,...t};return this.getAll().find(t=>l(e,t))}findAll(t={}){return this.getAll().filter(e=>l(t,e))}notify(t){q.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){const t=this.getAll().filter(t=>t.state.isPaused);return q.batch(()=>Promise.all(t.map(t=>t.continue().catch(n))))}};function N(t){return t.options.scope?.id}function G(t){return{onFetch:(e,s)=>{const r=e.options,i=e.fetchOptions?.meta?.fetchMore?.direction,n=e.state.data?.pages||[],a=e.state.data?.pageParams||[];let o={pages:[],pageParams:[]},u=0;const c=async()=>{let s=!1;const c=P(e.options,e.fetchOptions),h=async(t,r,i)=>{if(s)return Promise.reject();if(null==r&&t.pages.length)return Promise.resolve(t);const n=(()=>{const t={client:e.client,queryKey:e.queryKey,pageParam:r,direction:i?"backward":"forward",meta:e.options.meta};var n;return n=t,Object.defineProperty(n,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",()=>{s=!0}),e.signal)}),t})(),a=await c(n),{maxPages:o}=e.options,u=i?C:R;return{pages:u(t.pages,a,o),pageParams:u(t.pageParams,r,o)}};if(i&&n.length){const t="backward"===i,e={pages:n,pageParams:a},s=(t?W:B)(r,e);o=await h(e,s,t)}else{const e=t??n.length;do{const t=0===u?a[0]??r.initialPageParam:B(r,o);if(u>0&&null==t)break;o=await h(o,t),u++}while(u<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(c,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=c}}}function B(t,{pages:e,pageParams:s}){const r=e.length-1;return e.length>0?t.getNextPageParam(e[r],e,s[r],s):void 0}function W(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}function $(t,e){return!!e&&null!=B(t,e)}function z(t,e){return!(!e||!t.getPreviousPageParam)&&null!=W(t,e)}var V=class{#g;#y;#h;#O;#R;#C;#w;#P;constructor(t={}){this.#g=t.queryCache||new _,this.#y=t.mutationCache||new H,this.#h=t.defaultOptions||{},this.#O=new Map,this.#R=new Map,this.#C=0}mount(){this.#C++,1===this.#C&&(this.#w=F.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#g.onFocus())}),this.#P=x.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#g.onOnline())}))}unmount(){this.#C--,0===this.#C&&(this.#w?.(),this.#w=void 0,this.#P?.(),this.#P=void 0)}isFetching(t){return this.#g.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#y.findAll({...t,status:"pending"}).length}getQueryData(t){const e=this.defaultQueryOptions({queryKey:t});return this.#g.get(e.queryHash)?.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=this.#g.build(this,e),r=s.state.data;return void 0===r?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(u(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(r))}getQueriesData(t){return this.#g.findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,s){const r=this.defaultQueryOptions({queryKey:t}),i=this.#g.get(r.queryHash),n=i?.state.data,a=function(t,e){return"function"==typeof t?t(e):t}(e,n);if(void 0!==a)return this.#g.build(this,r).setData(a,{...s,manual:!0})}setQueriesData(t,e,s){return q.batch(()=>this.#g.findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,s)]))}getQueryState(t){const e=this.defaultQueryOptions({queryKey:t});return this.#g.get(e.queryHash)?.state}removeQueries(t){const e=this.#g;q.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){const s=this.#g;return q.batch(()=>(s.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){const s={revert:!0,...e},r=q.batch(()=>this.#g.findAll(t).map(t=>t.cancel(s)));return Promise.all(r).then(n).catch(n)}invalidateQueries(t,e={}){return q.batch(()=>(this.#g.findAll(t).forEach(t=>{t.invalidate()}),"none"===t?.refetchType?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e)))}refetchQueries(t,e={}){const s={...e,cancelRefetch:e.cancelRefetch??!0},r=q.batch(()=>this.#g.findAll(t).filter(t=>!t.isDisabled()&&!t.isStatic()).map(t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(n)),"paused"===t.state.fetchStatus?Promise.resolve():e}));return Promise.all(r).then(n)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=this.#g.build(this,e);return s.isStaleByTime(u(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(n).catch(n)}fetchInfiniteQuery(t){return t.behavior=G(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(n).catch(n)}ensureInfiniteQueryData(t){return t.behavior=G(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return x.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#g}getMutationCache(){return this.#y}getDefaultOptions(){return this.#h}setDefaultOptions(t){this.#h=t}setQueryDefaults(t,e){this.#O.set(f(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...this.#O.values()],s={};return e.forEach(e=>{p(t,e.queryKey)&&Object.assign(s,e.defaultOptions)}),s}setMutationDefaults(t,e){this.#R.set(f(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...this.#R.values()],s={};return e.forEach(e=>{p(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...this.#h.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=d(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===w&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#h.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#g.clear(),this.#y.clear()}},J=class extends E{constructor(t,e){super(),this.options=e,this.#u=t,this.#S=null,this.#Q=M(),this.options.experimental_prefetchInRender||this.#Q.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#u;#q=void 0;#E=void 0;#F=void 0;#x;#M;#Q;#S;#T;#D;#I;#A;#U;#k;#j=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#q.addObserver(this),Y(this.#q,this.options)?this.#K():this.updateResult(),this.#_())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return X(this.#q,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return X(this.#q,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#L(),this.#H(),this.#q.removeObserver(this)}setOptions(t){const e=this.options,s=this.#q;if(this.options=this.#u.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof c(this.options.enabled,this.#q))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#N(),this.#q.setOptions(this.options),e._defaulted&&!m(this.options,e)&&this.#u.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#q,observer:this});const r=this.hasListeners();r&&Z(this.#q,s,this.options,e)&&this.#K(),this.updateResult(),!r||this.#q===s&&c(this.options.enabled,this.#q)===c(e.enabled,this.#q)&&u(this.options.staleTime,this.#q)===u(e.staleTime,this.#q)||this.#G();const i=this.#B();!r||this.#q===s&&c(this.options.enabled,this.#q)===c(e.enabled,this.#q)&&i===this.#k||this.#W(i)}getOptimisticResult(t){const e=this.#u.getQueryCache().build(this.#u,t),s=this.createResult(e,t);return r=s,!m(this.getCurrentResult(),r)&&(this.#F=s,this.#M=this.options,this.#x=this.#q.state),s;var r}getCurrentResult(){return this.#F}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),e?.(s),Reflect.get(t,s))})}trackProp(t){this.#j.add(t)}getCurrentQuery(){return this.#q}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=this.#u.defaultQueryOptions(t),s=this.#u.getQueryCache().build(this.#u,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#K({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#F))}#K(t){this.#N();let e=this.#q.fetch(this.options,t);return t?.throwOnError||(e=e.catch(n)),e}#G(){this.#L();const t=u(this.options.staleTime,this.#q);if(i||this.#F.isStale||!a(t))return;const e=o(this.#F.dataUpdatedAt,t)+1;this.#A=setTimeout(()=>{this.#F.isStale||this.updateResult()},e)}#B(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#q):this.options.refetchInterval)??!1}#W(t){this.#H(),this.#k=t,!i&&!1!==c(this.options.enabled,this.#q)&&a(this.#k)&&0!==this.#k&&(this.#U=setInterval(()=>{(this.options.refetchIntervalInBackground||F.isFocused())&&this.#K()},this.#k))}#_(){this.#G(),this.#W(this.#B())}#L(){this.#A&&(clearTimeout(this.#A),this.#A=void 0)}#H(){this.#U&&(clearInterval(this.#U),this.#U=void 0)}createResult(t,e){const s=this.#q,r=this.options,i=this.#F,n=this.#x,a=this.#M,o=t!==s?t.state:this.#E,{state:u}=t;let c,h={...u},l=!1;if(e._optimisticResults){const i=this.hasListeners(),n=!i&&Y(t,e),a=i&&Z(t,s,e,r);(n||a)&&(h={...h,...K(u.data,t.options)}),"isRestoring"===e._optimisticResults&&(h.fetchStatus="idle")}let{error:d,errorUpdatedAt:f,status:p}=h;c=h.data;let y=!1;if(void 0!==e.placeholderData&&void 0===c&&"pending"===p){let t;i?.isPlaceholderData&&e.placeholderData===a?.placeholderData?(t=i.data,y=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#I?.state.data,this.#I):e.placeholderData,void 0!==t&&(p="success",c=O(i?.data,t,e),l=!0)}if(e.select&&void 0!==c&&!y)if(i&&c===n?.data&&e.select===this.#T)c=this.#D;else try{this.#T=e.select,c=e.select(c),c=O(i?.data,c,e),this.#D=c,this.#S=null}catch(t){this.#S=t}this.#S&&(d=this.#S,c=this.#D,f=Date.now(),p="error");const m="fetching"===h.fetchStatus,v="pending"===p,b="error"===p,g=v&&m,R=void 0!==c,C={status:p,fetchStatus:h.fetchStatus,isPending:v,isSuccess:"success"===p,isError:b,isInitialLoading:g,isLoading:g,data:c,dataUpdatedAt:h.dataUpdatedAt,error:d,errorUpdatedAt:f,failureCount:h.fetchFailureCount,failureReason:h.fetchFailureReason,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>o.dataUpdateCount||h.errorUpdateCount>o.errorUpdateCount,isFetching:m,isRefetching:m&&!v,isLoadingError:b&&!R,isPaused:"paused"===h.fetchStatus,isPlaceholderData:l,isRefetchError:b&&R,isStale:tt(t,e),refetch:this.refetch,promise:this.#Q};if(this.options.experimental_prefetchInRender){const e=t=>{"error"===C.status?t.reject(C.error):void 0!==C.data&&t.resolve(C.data)},r=()=>{const t=this.#Q=C.promise=M();e(t)},i=this.#Q;switch(i.status){case"pending":t.queryHash===s.queryHash&&e(i);break;case"fulfilled":"error"!==C.status&&C.data===i.value||r();break;case"rejected":"error"===C.status&&C.error===i.reason||r()}}return C}updateResult(){const t=this.#F,e=this.createResult(this.#q,this.options);this.#x=this.#q.state,this.#M=this.options,void 0!==this.#x.data&&(this.#I=this.#q),m(e,t)||(this.#F=e,this.#$({listeners:(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!this.#j.size)return!0;const r=new Set(s??this.#j);return this.options.throwOnError&&r.add("error"),Object.keys(this.#F).some(e=>{const s=e;return this.#F[s]!==t[s]&&r.has(s)})})()}))}#N(){const t=this.#u.getQueryCache().build(this.#u,this.options);if(t===this.#q)return;const e=this.#q;this.#q=t,this.#E=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#_()}#$(t){q.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#F)}),this.#u.getQueryCache().notify({query:this.#q,type:"observerResultsUpdated"})})}};function Y(t,e){return function(t,e){return!1!==c(e.enabled,t)&&void 0===t.state.data&&!("error"===t.state.status&&!1===e.retryOnMount)}(t,e)||void 0!==t.state.data&&X(t,e,e.refetchOnMount)}function X(t,e,s){if(!1!==c(e.enabled,t)&&"static"!==u(e.staleTime,t)){const r="function"==typeof s?s(t):s;return"always"===r||!1!==r&&tt(t,e)}return!1}function Z(t,e,s,r){return(t!==e||!1===c(r.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&tt(t,s)}function tt(t,e){return!1!==c(e.enabled,t)&&t.isStaleByTime(u(e.staleTime,t))}var et=s(7557),st=s(9370),rt=et.createContext(void 0),it=t=>{const e=et.useContext(rt);if(t)return t;if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},nt=({client:t,children:e})=>(et.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,st.jsx)(rt.Provider,{value:t,children:e}));var at=et.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),ot=()=>et.useContext(at),ut=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))},ct=t=>{et.useEffect(()=>{t.clearReset()},[t])},ht=({result:t,errorResetBoundary:e,throwOnError:s,query:r,suspense:i})=>t.isError&&!e.isReset()&&!t.isFetching&&r&&(i&&void 0===t.data||S(s,[t.error,r])),lt=et.createContext(!1),dt=()=>et.useContext(lt),ft=(lt.Provider,t=>{if(t.suspense){const e=t=>"static"===t?t:Math.max(t??1e3,1e3),s=t.staleTime;t.staleTime="function"==typeof s?(...t)=>e(s(...t)):e(s),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}}),pt=(t,e)=>t.isLoading&&t.isFetching&&!e,yt=(t,e)=>t?.suspense&&e.isPending,mt=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function vt(t,e,s){const r=dt(),a=ot(),o=it(s),u=o.defaultQueryOptions(t);o.getDefaultOptions().queries?._experimental_beforeQuery?.(u),u._optimisticResults=r?"isRestoring":"optimistic",ft(u),ut(u,a),ct(a);const c=!o.getQueryCache().get(u.queryHash),[h]=et.useState(()=>new e(o,u)),l=h.getOptimisticResult(u),d=!r&&!1!==t.subscribed;if(et.useSyncExternalStore(et.useCallback(t=>{const e=d?h.subscribe(q.batchCalls(t)):n;return h.updateResult(),e},[h,d]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),et.useEffect(()=>{h.setOptions(u)},[u,h]),yt(u,l))throw mt(u,h,a);if(ht({result:l,errorResetBoundary:a,throwOnError:u.throwOnError,query:o.getQueryCache().get(u.queryHash),suspense:u.suspense}))throw l.error;if(o.getDefaultOptions().queries?._experimental_afterQuery?.(u,l),u.experimental_prefetchInRender&&!i&&pt(l,r)){const t=c?mt(u,h,a):o.getQueryCache().get(u.queryHash)?.promise;t?.catch(n).finally(()=>{h.updateResult()})}return u.notifyOnChangeProps?l:h.trackResult(l)}function bt(t,e){return vt(t,J,e)}var gt=class extends J{constructor(t,e){super(t,e)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(t){super.setOptions({...t,behavior:G()})}getOptimisticResult(t){return t.behavior=G(),super.getOptimisticResult(t)}fetchNextPage(t){return this.fetch({...t,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(t){return this.fetch({...t,meta:{fetchMore:{direction:"backward"}}})}createResult(t,e){const{state:s}=t,r=super.createResult(t,e),{isFetching:i,isRefetching:n,isError:a,isRefetchError:o}=r,u=s.fetchMeta?.fetchMore?.direction,c=a&&"forward"===u,h=i&&"forward"===u,l=a&&"backward"===u,d=i&&"backward"===u;return{...r,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:$(e,s.data),hasPreviousPage:z(e,s.data),isFetchNextPageError:c,isFetchingNextPage:h,isFetchPreviousPageError:l,isFetchingPreviousPage:d,isRefetchError:o&&!c&&!l,isRefetching:n&&!h&&!d}}};function Ot(t,e){return vt(t,gt,e)}var Rt=class extends E{#u;#F=void 0;#z;#V;constructor(t,e){super(),this.#u=t,this.setOptions(e),this.bindMethods(),this.#J()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){const e=this.options;this.options=this.#u.defaultMutationOptions(t),m(this.options,e)||this.#u.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#z,observer:this}),e?.mutationKey&&this.options.mutationKey&&f(e.mutationKey)!==f(this.options.mutationKey)?this.reset():"pending"===this.#z?.state.status&&this.#z.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#z?.removeObserver(this)}onMutationUpdate(t){this.#J(),this.#$(t)}getCurrentResult(){return this.#F}reset(){this.#z?.removeObserver(this),this.#z=void 0,this.#J(),this.#$()}mutate(t,e){return this.#V=e,this.#z?.removeObserver(this),this.#z=this.#u.getMutationCache().build(this.#u,this.options),this.#z.addObserver(this),this.#z.execute(t)}#J(){const t=this.#z?.state??{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};this.#F={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#$(t){q.batch(()=>{if(this.#V&&this.hasListeners()){const e=this.#F.variables,s=this.#F.context;"success"===t?.type?(this.#V.onSuccess?.(t.data,e,s),this.#V.onSettled?.(t.data,null,e,s)):"error"===t?.type&&(this.#V.onError?.(t.error,e,s),this.#V.onSettled?.(void 0,t.error,e,s))}this.listeners.forEach(t=>{t(this.#F)})})}};function Ct(t,e){const s=it(e),[r]=et.useState(()=>new Rt(s,t));et.useEffect(()=>{r.setOptions(t)},[r,t]);const i=et.useSyncExternalStore(et.useCallback(t=>r.subscribe(q.batchCalls(t)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),a=et.useCallback((t,e)=>{r.mutate(t,e).catch(n)},[r]);if(i.error&&S(r.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:a,mutateAsync:i.mutate}}let wt;function Pt(){if(!wt)throw new Error("Query client is not created yet.");return wt}function St(){if(wt)throw new Error("Query client is already created.");return wt=new V({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}}),wt}(window.elementorV2=window.elementorV2||{}).query=r}(),window.elementorV2.query?.init?.();