/** 
 * ATUM Inventory Management for WooCommerce - CSS 
 * @version 1.9.50 
 * <AUTHOR> REBEL  
 *
 * Author URI: https://berebel.studio 
 * License : ©2025 Stock Management Labs 
 */

 .input-group-prepend{border-left:none;border-right:1px solid}.input-group-append{border-right:none;border-left:1px solid}.input-group>.input-group-prepend>.btn,.input-group>.input-group-prepend>.input-group-text,.input-group>.input-group-append:not(:last-child)>.btn,.input-group>.input-group-append:not(:last-child)>.input-group-text,.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),.input-group>.input-group-append:last-child>.input-group-text:not(:last-child){border-bottom-left-radius:0;border-top-left-radius:0;border-bottom-right-radius:3px;border-top-right-radius:3px}.input-group>.input-group-append>.btn,.input-group>.input-group-append>.input-group-text,.input-group>.input-group-prepend:not(:first-child)>.btn,.input-group>.input-group-prepend:not(:first-child)>.input-group-text,.input-group>.input-group-prepend:first-child>.btn:not(:first-child),.input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child){border-bottom-right-radius:3px;border-top-right-radius:3px;border-bottom-left-radius:0;border-top-left-radius:0}.input-group>.select2-container.atum-enhanced-select .select2-selection--single{border-bottom-left-radius:4px;border-top-left-radius:4px;border-bottom-right-radius:0;border-top-right-radius:0}#woocommerce-product-data ul.wc-tabs li>a{display:-webkit-box !important;display:-ms-flexbox !important;display:flex !important;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:revert;flex-direction:revert}#woocommerce-product-data ul.wc-tabs li a:before{padding-top:4px}.product_data .atum-data-panel .atum-section-title{padding-right:10px}.product_data .atum-field input[type=text],.product_data .atum-field input[type=number]{border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:4px;border-bottom-left-radius:4px}.product_data .atum-field .input-group-prepend{border-top-right-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:0px;border-bottom-left-radius:0px}.product_data .atum-field .select2-container.atum-enhanced-select .select2-selection--single .select2-selection__clear{padding-left:25px}.btn-group>.btn:first-child{margin-right:0;margin-left:auto;border-top-right-radius:4px !important;border-bottom-right-radius:4px !important}.btn-group>.btn:last-child{border-top-left-radius:4px !important;border-bottom-left-radius:4px !important}.btn-group .btn+.btn,.btn-group .btn+.btn-group,.btn-group .btn-group+.btn,.btn-group .btn-group+.btn-group{margin-right:-1px}.btn-group>.btn:first-child{margin-right:0;margin-left:auto}.btn-group .btn-sm,.btn-group .btn-group-sm>.btn{border-radius:.2rem !important}.btn-group .btn-group .btn{border-radius:.2rem !important}.btn-group>.btn:not(:last-child):not(.dropdown-toggle),.btn-group>.btn-group:not(:last-child)>.btn{border-top-left-radius:0;border-bottom-left-radius:0}.btn-group>.btn:not(:first-child),.btn-group>.btn-group:not(:first-child)>.btn{border-top-right-radius:0;border-bottom-right-radius:0}