/** 
 * ATUM Inventory Management for WooCommerce - CSS 
 * @version 1.9.50 
 * <AUTHOR> REBEL  
 *
 * Author URI: https://berebel.studio 
 * License : ©2025 Stock Management Labs 
 */

 :root{--gray-100: #f8f9fa;--gray-100-hover: rgba(248, 249, 250, 0.6);--gray-100-shadow: rgba(248, 249, 250, 0.2);--gray-200: #e9ecef;--gray-200-hover: rgba(233, 236, 239, 0.6);--gray-200-shadow: rgba(233, 236, 239, 0.2);--gray-300: #dee2e6;--gray-300-hover: rgba(222, 226, 230, 0.6);--gray-300-shadow: rgba(222, 226, 230, 0.2);--gray-400: #ced4da;--gray-400-hover: rgba(206, 212, 218, 0.6);--gray-400-shadow: rgba(206, 212, 218, 0.2);--gray-500: #adb5bd;--gray-500-hover: rgba(173, 181, 189, 0.6);--gray-500-shadow: rgba(173, 181, 189, 0.2);--gray-600: #6c757d;--gray-600-hover: rgba(108, 117, 125, 0.6);--gray-600-shadow: rgba(108, 117, 125, 0.2);--gray-700: #495057;--gray-700-hover: rgba(73, 80, 87, 0.6);--gray-700-shadow: rgba(73, 80, 87, 0.2);--gray-800: #343a40;--gray-800-hover: rgba(52, 58, 64, 0.6);--gray-800-shadow: rgba(52, 58, 64, 0.2);--gray-900: #212529;--gray-900-hover: rgba(33, 37, 41, 0.6);--gray-900-shadow: rgba(33, 37, 41, 0.2);--blue: #00B8DB;--blue-hover: rgba(0, 184, 219, 0.6);--blue-shadow: rgba(0, 184, 219, 0.2);--blue-gray-light: #E3E8F5;--blue-gray-light-hover: rgba(227, 232, 245, 0.6);--blue-gray-light-shadow: rgba(227, 232, 245, 0.2);--orange: #EFAF00;--orange-hover: rgba(239, 175, 0, 0.6);--orange-shadow: rgba(239, 175, 0, 0.2);--red: #ff4848;--red-hover: rgba(255, 72, 72, 0.6);--red-shadow: rgba(255, 72, 72, 0.2);--red-light: #ffe1e1;--red-light-hover: rgba(255, 225, 225, 0.6);--red-light-shadow: rgba(255, 225, 225, 0.2);--red-light-2: rgb(255, 245.4, 245.4);--red-light-2-hover: rgba(255, 245.4, 245.4, 0.6);--red-light-2-shadow: rgba(255, 245.4, 245.4, 0.2);--green: #69C61D;--green-hover: rgba(105, 198, 29, 0.6);--green-shadow: rgba(105, 198, 29, 0.2);--green-light: rgb(212.5418502203, 244.9074889868, 186.0925110132);--green-light-hover: rgba(212.5418502203, 244.9074889868, 186.0925110132, 0.6);--green-light-shadow: rgba(212.5418502203, 244.9074889868, 186.0925110132, 0.2);--blue-light: rgb(219, 249.2465753425, 255);--blue-light-hover: rgba(219, 249.2465753425, 255, 0.6);--blue-light-shadow: rgba(219, 249.2465753425, 255, 0.2);--blue-dark: #27283B;--blue-dark-hover: rgba(39, 40, 59, 0.6);--blue-dark-shadow: rgba(39, 40, 59, 0.2);--blue-dark-lighten: #3b3d5a;--blue-dark-lighten-hover: rgba(59, 61, 90, 0.6);--blue-dark-lighten-shadow: rgba(59, 61, 90, 0.2);--blue-dark-light-2: rgb(129.7755102041, 131.8979591837, 172.2244897959);--blue-dark-light-2-hover: rgba(129.7755102041, 131.8979591837, 172.2244897959, 0.6);--blue-dark-light-2-shadow: rgba(129.7755102041, 131.8979591837, 172.2244897959, 0.2);--white: #fff;--white-hover: rgba(255, 255, 255, 0.6);--white-shadow: rgba(255, 255, 255, 0.2);--gray: #adb5bd;--gray-hover: rgba(173, 181, 189, 0.6);--gray-shadow: rgba(173, 181, 189, 0.2);--gray-light: #e9ecef;--gray-light-hover: rgba(233, 236, 239, 0.6);--gray-light-shadow: rgba(233, 236, 239, 0.2);--gray-dark: #343a40;--gray-dark-hover: rgba(52, 58, 64, 0.6);--gray-dark-shadow: rgba(52, 58, 64, 0.2);--purple: #BA7DF7;--purple-hover: rgba(186, 125, 247, 0.6);--purple-shadow: rgba(186, 125, 247, 0.2);--purple-light: rgb(237, 221.0869565217, 252.9130434783);--purple-light-hover: rgba(237, 221.0869565217, 252.9130434783, 0.6);--purple-light-shadow: rgba(237, 221.0869565217, 252.9130434783, 0.2);--purple-pl: #9357cc;--purple-pl-hover: rgba(147, 87, 204, 0.6);--purple-pl-shadow: rgba(147, 87, 204, 0.2);--black: #000;--black-hover: rgba(0, 0, 0, 0.6);--black-shadow: rgba(0, 0, 0, 0.2);--primary: #00B8DB;--primary-hover: rgba(0, 184, 219, 0.6);--primary-shadow: rgba(0, 184, 219, 0.2);--primary-hover-border: none;--secondary: #EFAF00;--secondary-hover: rgba(239, 175, 0, 0.6);--secondary-shadow: rgba(239, 175, 0, 0.2);--secondary-hover-border: none;--tertiary: #69C61D;--tertiary-hover: rgba(105, 198, 29, 0.6);--tertiary-shadow: rgba(105, 198, 29, 0.2);--tertiary-hover-border: none;--success: #69C61D;--success-hover: rgba(105, 198, 29, 0.6);--success-shadow: rgba(105, 198, 29, 0.2);--success-hover-border: none;--main: #00B8DB;--main-hover: rgba(0, 184, 219, 0.6);--main-shadow: rgba(0, 184, 219, 0.2);--main-hover-border: none;--info: #E3E8F5;--info-hover: rgba(227, 232, 245, 0.6);--info-shadow: rgba(227, 232, 245, 0.2);--info-hover-border: none;--warning: #EFAF00;--warning-hover: rgba(239, 175, 0, 0.6);--warning-shadow: rgba(239, 175, 0, 0.2);--warning-hover-border: none;--danger: #ff4848;--danger-hover: rgba(255, 72, 72, 0.6);--danger-shadow: rgba(255, 72, 72, 0.2);--danger-hover-border: none;--light: #f8f9fa;--light-hover: rgba(248, 249, 250, 0.6);--light-shadow: rgba(248, 249, 250, 0.2);--light-hover-border: none;--dark: #343a40;--dark-hover: rgba(52, 58, 64, 0.6);--dark-shadow: rgba(52, 58, 64, 0.2);--dark-hover-border: none;--purple-pl: #9357cc;--purple-pl-hover: rgba(147, 87, 204, 0.6);--purple-pl-shadow: rgba(147, 87, 204, 0.2);--purple-pl-hover-border: none;--gray: #6c757d;--gray-hover: rgba(108, 117, 125, 0.6);--gray-shadow: rgba(108, 117, 125, 0.2);--gray-hover-border: none;--default: #adb5bd;--default-hover: rgba(173, 181, 189, 0.6);--default-shadow: rgba(173, 181, 189, 0.2);--default-hover-border: none;--wp-link: #0073AA;--wp-link-hover: rgba(0, 115, 170, 0.6);--wp-link-shadow: rgba(0, 115, 170, 0.2);--wp-link-hover: #00A0D2;--wp-link-hover-hover: rgba(0, 160, 210, 0.6);--wp-link-hover-shadow: rgba(0, 160, 210, 0.2);--wp-input-focus: #007CBA;--wp-input-focus-hover: rgba(0, 124, 186, 0.6);--wp-input-focus-shadow: rgba(0, 124, 186, 0.2);--wp-pink: #E6CCE1;--wp-pink-hover: rgba(230, 204, 225, 0.6);--wp-pink-shadow: rgba(230, 204, 225, 0.2);--wp-pink-light: #F5EBF3;--wp-pink-light-hover: rgba(245, 235, 243, 0.6);--wp-pink-light-shadow: rgba(245, 235, 243, 0.2);--wp-pink-lighter: #FBF9FB;--wp-pink-lighter-hover: rgba(251, 249, 251, 0.6);--wp-pink-lighter-shadow: rgba(251, 249, 251, 0.2);--wp-gray-dark: #32373C;--wp-gray-dark-hover: rgba(50, 55, 60, 0.6);--wp-gray-dark-shadow: rgba(50, 55, 60, 0.2);--wp-gray-1: #999;--wp-gray-1-hover: rgba(153, 153, 153, 0.6);--wp-gray-1-shadow: rgba(153, 153, 153, 0.2);--wp-gray-3: #B4B9BE;--wp-gray-3-hover: rgba(180, 185, 190, 0.6);--wp-gray-3-shadow: rgba(180, 185, 190, 0.2);--wp-gray-4: #DDD;--wp-gray-4-hover: rgba(221, 221, 221, 0.6);--wp-gray-4-shadow: rgba(221, 221, 221, 0.2);--wp-gray-5: #E5E5E5;--wp-gray-5-hover: rgba(229, 229, 229, 0.6);--wp-gray-5-shadow: rgba(229, 229, 229, 0.2);--wp-gray-6: #f9f9f9;--wp-gray-6-hover: rgba(249, 249, 249, 0.6);--wp-gray-6-shadow: rgba(249, 249, 249, 0.2);--wp-gray-7: #888;--wp-gray-7-hover: rgba(136, 136, 136, 0.6);--wp-gray-7-shadow: rgba(136, 136, 136, 0.2);--wc-blue-gray: #2e4453;--wc-blue-gray-hover: rgba(46, 68, 83, 0.6);--wc-blue-gray-shadow: rgba(46, 68, 83, 0.2);--wc-blue-gray-light: #C8D7E1;--wc-blue-gray-light-hover: rgba(200, 215, 225, 0.6);--wc-blue-gray-light-shadow: rgba(200, 215, 225, 0.2);--wc-green: #5B841B;--wc-green-hover: rgba(91, 132, 27, 0.6);--wc-green-shadow: rgba(91, 132, 27, 0.2);--wc-green-light: #C6E1C6;--wc-green-light-hover: rgba(198, 225, 198, 0.6);--wc-green-light-shadow: rgba(198, 225, 198, 0.2);--wc-orange: #94660c;--wc-orange-hover: rgba(148, 102, 12, 0.6);--wc-orange-shadow: rgba(148, 102, 12, 0.2);--wc-orange-light: #f8dda7;--wc-orange-light-hover: rgba(248, 221, 167, 0.6);--wc-orange-light-shadow: rgba(248, 221, 167, 0.2);--orange-light-2: rgb(255, 247.9841004184, 228.8);--orange-light-2-hover: rgba(255, 247.9841004184, 228.8, 0.6);--orange-light-2-shadow: rgba(255, 247.9841004184, 228.8, 0.2);--wc-red: #761919;--wc-red-hover: rgba(118, 25, 25, 0.6);--wc-red-shadow: rgba(118, 25, 25, 0.2);--wc-red-light: #eba3a3;--wc-red-light-hover: rgba(235, 163, 163, 0.6);--wc-red-light-shadow: rgba(235, 163, 163, 0.2);--wc-purple: #9a69c7;--wc-purple-hover: rgba(154, 105, 199, 0.6);--wc-purple-shadow: rgba(154, 105, 199, 0.2);--atum-add-widget-title: #343a40;--atum-add-widget-separator: #f8f9fa;--atum-bg-white: #fff;--atum-border-expanded: #adb5bd;--atum-border-var: #e9ecef;--atum-cloned-list-table-shadow: rgba(0, 0, 0, 0.04);--atum-column-groups-bg: #e9ecef;--atum-dark-bg: #3b3d5a;--atum-dropdown-toggle-bg: #e9ecef;--atum-easytree-node: #343a40;--atum-expanded-bg: #fff;--atum-footer-title: #27283B;--atum-footer-link: #0073AA;--atum-footer-text: #495057;--atum-menu-text: #00B8DB;--atum-menu-text-highlight: #00B8DB;--atum-pagination-bg: #e9ecef;--atum-pagination-border: transparent;--atum-pagination-border-disabled: transparent;--atum-row-shadow: rgba(0, 0, 0, 0.04);--atum-settings-btn-save: #fff;--atum-settings-btn-save-hover: rgb(219, 249.2465753425, 255);--atum-settings-input-border: #e9ecef;--atum-settings-heads-bg: #00B8DB;--atum-settings-nav-bg: #fff;--atum-settings-nav-link: #00B8DB;--atum-settings-section-general-bg: #3b3d5a;--atum-settings-section-bg: #fff;--atum-settings-text-logo: #27283B;--atum-settings-wp-color-bg: transparent;--atum-table-bg: #fff;--atum-table-bg2: #f8f9fa;--atum-table-filter-dropdown: #6c757d;--atum-table-link-text: #00B8DB;--atum-table-row-variation-text: #fff;--atum-table-search-submit-bg: #adb5bd;--atum-table-search-text-disabled: #ced4da;--atum-table-views-tabs: #6c757d;--atum-table-views-tabs-active: #00B8DB;--atum-table-views-tabs-active-text: #fff;--atum-table-text: #343a40;--atum-text-color-dark2: #6c757d;--atum-text-color-var1: #adb5bd;--atum-text-color-var2: #adb5bd;--atum-text-color-var3: #6c757d;--atum-text-white: #fff;--atum-version: #fff;--atum-version-bg: rgba(0, 0, 0, 0.2);--atum-checkbox-label: #000;--atum-input-text: #6c757d;--atum-setting-info: #495057;--atum-section-field: #fff;--black-shadow-light: rgba(0, 0, 0, 0.04);--blue-light-expanded: rgb(219, 249.2465753425, 255);--danger-hover-border: 1px solid transparent;--danger-hover-text: #fff;--dash-add-widget-color: #adb5bd;--dash-add-widget-color-dark: #00B8DB;--dash-atum-version: rgba(0, 0, 0, 0.1);--dash-blue-trans: rgba(0, 183, 219, 0.79);--dash-card-bg: #f8f9fa;--dash-card-text: #6c757d;--dash-green-blue: rgba(30, 200, 149, 0.79);--dash-green-light: rgb(212.5418502203, 244.9074889868, 186.0925110132);--dash-green-trans: rgba(106, 200, 30, 0.79);--dash-input-group-bg: rgba(255, 255, 255, 0.66);--dash-input-group-shadow: rgba(255, 255, 255, 0.2);--dash-next-text: #e9ecef;--dash-nice-select-bg: #fff;--dash-nice-select-disabled: rgb(238.9052631579, 241.1, 243.2947368421);--dash-nice-select-disabled-after: rgb(229.5135135135, 232, 234.4864864865);--dash-nice-select-hover: rgb(203.4736842105, 210.5, 217.5263157895);--dash-nice-select-list-bg: #fff;--dash-nice-select-option-hover-bg: #f8f9fa;--dash-nice-select-option-selected-bg: rgb(219, 249.2465753425, 255);--dash-statistics-chart-type-bg: rgba(248, 249, 250, 0.2);--dash-statistics-chart-type-selected-bg: transparent;--dash-statistics-chart-type-selected-text: #EFAF00;--dash-statistics-grid-lines: #e9ecef;--dash-statistics-legend-switch-bg: rgba(248, 249, 250, 0.2);--dash-statistics-ticks: #27283B;--dash-stats-data-widget-primary: #00B8DB;--dash-stats-data-widget-success: #69C61D;--dash-subscription-input: rgba(255, 255, 255, 0.7);--dash-video-title: #343a40;--dash-video-subs-text: #adb5bd;--dash-widget-current-stock-value-bg: #ffe1e1;--dash-widget-current-stock-value-text: #ff4848;--dash-widget-icon: #adb5bd;--dash-widget-list-border: rgba(248, 249, 250, 0.5);--green-light-expanded: rgb(212.5418502203, 244.9074889868, 186.0925110132);--info-hover-border: 1px solid transparent;--main-border: rgba(255, 255, 255, 0.2);--main-border-alt: #6c757d;--main-dropdown-border: #dee2e6;--main-text: #343a40;--main-text-expanded: #fff;--main-title: #27283B;--orange-dark-light: rgb(255, 223.4016736402, 137);--order-green-light: rgb(212.5418502203, 244.9074889868, 186.0925110132);--overflow-opacity-left: linear-gradient(to left, rgba(255,255,255,0), rgba(255,255,255,0.9));--overflow-opacity-rigth: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9));--popover-black-shadow: rgba(0, 0, 0, 0.2);--primary-dark: rgb(219, 249.2465753425, 255);--primary-light: rgb(244.5, 253.3219178082, 255);--primary-hover-border: none;--primary-hover-text: #00B8DB;--primary-var-dark: #00B8DB;--primary-var-text2: #00B8DB;--secondary: #EFAF00;--secondary-dark: rgb(255, 237.0585774059, 188);--secondary-hover: rgba(239, 175, 0, 0.6);--secondary-hover-border: none;--secondary-hover-text: #EFAF00;--secondary-light: rgb(255, 247.9841004184, 228.8);--secondary-var: #EFAF00;--success-hover-border: 1px solid transparent;--tertiary-hover-border: none;--tertiary-hover-text: #69C61D;--tertiary-light: rgb(239.9515418502, 251.422907489, 230.577092511);--tertiary-var: #69C61D;--warning-hover-border: 1px solid transparent;--wp-link-hover: #00B8DB;--wp-nth-child: #f8f9fa;--wp-pink-darken-expanded: rgb(231.4, 207.8, 226.68);--wc-purple-expanded: #9a69c7;--wp-yiq-text-dark: #212529;--wp-yiq-text-light: #fff}.btn{display:inline-block;font-weight:400;text-align:center;white-space:nowrap;vertical-align:middle;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border:1px solid rgba(0,0,0,0);padding:.375rem .75rem;font-size:.875rem;line-height:1.5;border-radius:.25rem;-webkit-transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out}.btn:hover,.btn:focus{text-decoration:none}.btn:focus,.btn.focus{outline:0;-webkit-box-shadow:0 0 0 .2rem rgba(0,184,219,.25);box-shadow:0 0 0 .2rem rgba(0,184,219,.25)}.btn.disabled,.btn:disabled{opacity:.5;-webkit-box-shadow:none;box-shadow:none}.btn:not(:disabled):not(.disabled){cursor:pointer}.btn:not(:disabled):not(.disabled):active,.btn:not(:disabled):not(.disabled).active{background-image:none}a.btn.disabled,fieldset:disabled a.btn{pointer-events:none}.btn-lg{padding:.5rem 1rem;font-size:1.09375rem;line-height:1.5;border-radius:.3rem}.btn-sm{padding:.25rem .5rem;font-size:.765625rem;line-height:1.5;border-radius:.2rem}.btn-primary{color:var(--wp-yiq-text-light);background-color:var(--primary);border-color:var(--primary)}.btn-primary:hover{color:var(--wp-yiq-text-light);background-color:var(--primary-hover);border-color:var(--primary-hover)}.btn-primary:focus,.btn-primary.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(0,184,219,.5);box-shadow:0 0 0 .2rem rgba(0,184,219,.5)}.btn-primary.disabled,.btn-primary:disabled{color:var(--wp-yiq-text-light);background-color:var(--primary);border-color:var(--primary)}.btn-primary:not(:disabled):not(.disabled):active,.btn-primary:not(:disabled):not(.disabled).active,.show>.btn-primary.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--primary-hover);border-color:var(--primary-hover)}.btn-primary:not(:disabled):not(.disabled):active:focus,.btn-primary:not(:disabled):not(.disabled).active:focus,.show>.btn-primary.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(0,184,219,.5);box-shadow:0 0 0 .2rem rgba(0,184,219,.5)}.btn-secondary{color:var(--wp-yiq-text-light);background-color:var(--secondary);border-color:var(--secondary)}.btn-secondary:hover{color:var(--wp-yiq-text-light);background-color:var(--secondary-hover);border-color:var(--secondary-hover)}.btn-secondary:focus,.btn-secondary.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(239,175,0,.5);box-shadow:0 0 0 .2rem rgba(239,175,0,.5)}.btn-secondary.disabled,.btn-secondary:disabled{color:var(--wp-yiq-text-light);background-color:var(--secondary);border-color:var(--secondary)}.btn-secondary:not(:disabled):not(.disabled):active,.btn-secondary:not(:disabled):not(.disabled).active,.show>.btn-secondary.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--secondary-hover);border-color:var(--secondary-hover)}.btn-secondary:not(:disabled):not(.disabled):active:focus,.btn-secondary:not(:disabled):not(.disabled).active:focus,.show>.btn-secondary.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(239,175,0,.5);box-shadow:0 0 0 .2rem rgba(239,175,0,.5)}.btn-tertiary{color:var(--wp-yiq-text-light);background-color:var(--tertiary);border-color:var(--tertiary)}.btn-tertiary:hover{color:var(--wp-yiq-text-light);background-color:var(--tertiary-hover);border-color:var(--tertiary-hover)}.btn-tertiary:focus,.btn-tertiary.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(105,198,29,.5);box-shadow:0 0 0 .2rem rgba(105,198,29,.5)}.btn-tertiary.disabled,.btn-tertiary:disabled{color:var(--wp-yiq-text-light);background-color:var(--tertiary);border-color:var(--tertiary)}.btn-tertiary:not(:disabled):not(.disabled):active,.btn-tertiary:not(:disabled):not(.disabled).active,.show>.btn-tertiary.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--tertiary-hover);border-color:var(--tertiary-hover)}.btn-tertiary:not(:disabled):not(.disabled):active:focus,.btn-tertiary:not(:disabled):not(.disabled).active:focus,.show>.btn-tertiary.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(105,198,29,.5);box-shadow:0 0 0 .2rem rgba(105,198,29,.5)}.btn-success{color:var(--wp-yiq-text-light);background-color:var(--success);border-color:var(--success)}.btn-success:hover{color:var(--wp-yiq-text-light);background-color:var(--success-hover);border-color:var(--success-hover)}.btn-success:focus,.btn-success.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(105,198,29,.5);box-shadow:0 0 0 .2rem rgba(105,198,29,.5)}.btn-success.disabled,.btn-success:disabled{color:var(--wp-yiq-text-light);background-color:var(--success);border-color:var(--success)}.btn-success:not(:disabled):not(.disabled):active,.btn-success:not(:disabled):not(.disabled).active,.show>.btn-success.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--success-hover);border-color:var(--success-hover)}.btn-success:not(:disabled):not(.disabled):active:focus,.btn-success:not(:disabled):not(.disabled).active:focus,.show>.btn-success.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(105,198,29,.5);box-shadow:0 0 0 .2rem rgba(105,198,29,.5)}.btn-main{color:var(--wp-yiq-text-light);background-color:var(--main);border-color:var(--main)}.btn-main:hover{color:var(--wp-yiq-text-light);background-color:var(--main-hover);border-color:var(--main-hover)}.btn-main:focus,.btn-main.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(0,184,219,.5);box-shadow:0 0 0 .2rem rgba(0,184,219,.5)}.btn-main.disabled,.btn-main:disabled{color:var(--wp-yiq-text-light);background-color:var(--main);border-color:var(--main)}.btn-main:not(:disabled):not(.disabled):active,.btn-main:not(:disabled):not(.disabled).active,.show>.btn-main.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--main-hover);border-color:var(--main-hover)}.btn-main:not(:disabled):not(.disabled):active:focus,.btn-main:not(:disabled):not(.disabled).active:focus,.show>.btn-main.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(0,184,219,.5);box-shadow:0 0 0 .2rem rgba(0,184,219,.5)}.btn-info{color:var(--wp-yiq-text-dark);background-color:var(--info);border-color:var(--info)}.btn-info:hover{color:var(--wp-yiq-text-dark);background-color:var(--info-hover);border-color:var(--info-hover)}.btn-info:focus,.btn-info.focus{color:var(--wp-yiq-text-dark);-webkit-box-shadow:0 0 0 .2rem rgba(227,232,245,.5);box-shadow:0 0 0 .2rem rgba(227,232,245,.5)}.btn-info.disabled,.btn-info:disabled{color:var(--wp-yiq-text-dark);background-color:var(--info);border-color:var(--info)}.btn-info:not(:disabled):not(.disabled):active,.btn-info:not(:disabled):not(.disabled).active,.show>.btn-info.dropdown-toggle{color:var(--wp-yiq-text-dark);background-color:var(--info-hover);border-color:var(--info-hover)}.btn-info:not(:disabled):not(.disabled):active:focus,.btn-info:not(:disabled):not(.disabled).active:focus,.show>.btn-info.dropdown-toggle:focus{color:var(--wp-yiq-text-dark);-webkit-box-shadow:0 0 0 .2rem rgba(227,232,245,.5);box-shadow:0 0 0 .2rem rgba(227,232,245,.5)}.btn-warning{color:var(--wp-yiq-text-light);background-color:var(--warning);border-color:var(--warning)}.btn-warning:hover{color:var(--wp-yiq-text-light);background-color:var(--warning-hover);border-color:var(--warning-hover)}.btn-warning:focus,.btn-warning.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(239,175,0,.5);box-shadow:0 0 0 .2rem rgba(239,175,0,.5)}.btn-warning.disabled,.btn-warning:disabled{color:var(--wp-yiq-text-light);background-color:var(--warning);border-color:var(--warning)}.btn-warning:not(:disabled):not(.disabled):active,.btn-warning:not(:disabled):not(.disabled).active,.show>.btn-warning.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--warning-hover);border-color:var(--warning-hover)}.btn-warning:not(:disabled):not(.disabled):active:focus,.btn-warning:not(:disabled):not(.disabled).active:focus,.show>.btn-warning.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(239,175,0,.5);box-shadow:0 0 0 .2rem rgba(239,175,0,.5)}.btn-danger{color:var(--wp-yiq-text-light);background-color:var(--danger);border-color:var(--danger)}.btn-danger:hover{color:var(--wp-yiq-text-light);background-color:var(--danger-hover);border-color:var(--danger-hover)}.btn-danger:focus,.btn-danger.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(255,72,72,.5);box-shadow:0 0 0 .2rem rgba(255,72,72,.5)}.btn-danger.disabled,.btn-danger:disabled{color:var(--wp-yiq-text-light);background-color:var(--danger);border-color:var(--danger)}.btn-danger:not(:disabled):not(.disabled):active,.btn-danger:not(:disabled):not(.disabled).active,.show>.btn-danger.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--danger-hover);border-color:var(--danger-hover)}.btn-danger:not(:disabled):not(.disabled):active:focus,.btn-danger:not(:disabled):not(.disabled).active:focus,.show>.btn-danger.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(255,72,72,.5);box-shadow:0 0 0 .2rem rgba(255,72,72,.5)}.btn-light{color:var(--wp-yiq-text-dark);background-color:var(--light);border-color:var(--light)}.btn-light:hover{color:var(--wp-yiq-text-dark);background-color:var(--light-hover);border-color:var(--light-hover)}.btn-light:focus,.btn-light.focus{color:var(--wp-yiq-text-dark);-webkit-box-shadow:0 0 0 .2rem rgba(248,249,250,.5);box-shadow:0 0 0 .2rem rgba(248,249,250,.5)}.btn-light.disabled,.btn-light:disabled{color:var(--wp-yiq-text-dark);background-color:var(--light);border-color:var(--light)}.btn-light:not(:disabled):not(.disabled):active,.btn-light:not(:disabled):not(.disabled).active,.show>.btn-light.dropdown-toggle{color:var(--wp-yiq-text-dark);background-color:var(--light-hover);border-color:var(--light-hover)}.btn-light:not(:disabled):not(.disabled):active:focus,.btn-light:not(:disabled):not(.disabled).active:focus,.show>.btn-light.dropdown-toggle:focus{color:var(--wp-yiq-text-dark);-webkit-box-shadow:0 0 0 .2rem rgba(248,249,250,.5);box-shadow:0 0 0 .2rem rgba(248,249,250,.5)}.btn-dark{color:var(--wp-yiq-text-light);background-color:var(--dark);border-color:var(--dark)}.btn-dark:hover{color:var(--wp-yiq-text-light);background-color:var(--dark-hover);border-color:var(--dark-hover)}.btn-dark:focus,.btn-dark.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(52,58,64,.5);box-shadow:0 0 0 .2rem rgba(52,58,64,.5)}.btn-dark.disabled,.btn-dark:disabled{color:var(--wp-yiq-text-light);background-color:var(--dark);border-color:var(--dark)}.btn-dark:not(:disabled):not(.disabled):active,.btn-dark:not(:disabled):not(.disabled).active,.show>.btn-dark.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--dark-hover);border-color:var(--dark-hover)}.btn-dark:not(:disabled):not(.disabled):active:focus,.btn-dark:not(:disabled):not(.disabled).active:focus,.show>.btn-dark.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(52,58,64,.5);box-shadow:0 0 0 .2rem rgba(52,58,64,.5)}.btn-purple-pl{color:var(--wp-yiq-text-light);background-color:var(--purple-pl);border-color:var(--purple-pl)}.btn-purple-pl:hover{color:var(--wp-yiq-text-light);background-color:var(--purple-pl-hover);border-color:var(--purple-pl-hover)}.btn-purple-pl:focus,.btn-purple-pl.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(147,87,204,.5);box-shadow:0 0 0 .2rem rgba(147,87,204,.5)}.btn-purple-pl.disabled,.btn-purple-pl:disabled{color:var(--wp-yiq-text-light);background-color:var(--purple-pl);border-color:var(--purple-pl)}.btn-purple-pl:not(:disabled):not(.disabled):active,.btn-purple-pl:not(:disabled):not(.disabled).active,.show>.btn-purple-pl.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--purple-pl-hover);border-color:var(--purple-pl-hover)}.btn-purple-pl:not(:disabled):not(.disabled):active:focus,.btn-purple-pl:not(:disabled):not(.disabled).active:focus,.show>.btn-purple-pl.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(147,87,204,.5);box-shadow:0 0 0 .2rem rgba(147,87,204,.5)}.btn-gray{color:var(--wp-yiq-text-light);background-color:var(--gray);border-color:var(--gray)}.btn-gray:hover{color:var(--wp-yiq-text-light);background-color:var(--gray-hover);border-color:var(--gray-hover)}.btn-gray:focus,.btn-gray.focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(108,117,125,.5);box-shadow:0 0 0 .2rem rgba(108,117,125,.5)}.btn-gray.disabled,.btn-gray:disabled{color:var(--wp-yiq-text-light);background-color:var(--gray);border-color:var(--gray)}.btn-gray:not(:disabled):not(.disabled):active,.btn-gray:not(:disabled):not(.disabled).active,.show>.btn-gray.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--gray-hover);border-color:var(--gray-hover)}.btn-gray:not(:disabled):not(.disabled):active:focus,.btn-gray:not(:disabled):not(.disabled).active:focus,.show>.btn-gray.dropdown-toggle:focus{color:var(--wp-yiq-text-light);-webkit-box-shadow:0 0 0 .2rem rgba(108,117,125,.5);box-shadow:0 0 0 .2rem rgba(108,117,125,.5)}.btn-default{color:var(--wp-yiq-text-dark);background-color:var(--default);border-color:var(--default)}.btn-default:hover{color:var(--wp-yiq-text-dark);background-color:var(--default-hover);border-color:var(--default-hover)}.btn-default:focus,.btn-default.focus{color:var(--wp-yiq-text-dark);-webkit-box-shadow:0 0 0 .2rem rgba(173,181,189,.5);box-shadow:0 0 0 .2rem rgba(173,181,189,.5)}.btn-default.disabled,.btn-default:disabled{color:var(--wp-yiq-text-dark);background-color:var(--default);border-color:var(--default)}.btn-default:not(:disabled):not(.disabled):active,.btn-default:not(:disabled):not(.disabled).active,.show>.btn-default.dropdown-toggle{color:var(--wp-yiq-text-dark);background-color:var(--default-hover);border-color:var(--default-hover)}.btn-default:not(:disabled):not(.disabled):active:focus,.btn-default:not(:disabled):not(.disabled).active:focus,.show>.btn-default.dropdown-toggle:focus{color:var(--wp-yiq-text-dark);-webkit-box-shadow:0 0 0 .2rem rgba(173,181,189,.5);box-shadow:0 0 0 .2rem rgba(173,181,189,.5)}.btn-outline-primary{color:var(--primary);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--primary)}.btn-outline-primary:hover{color:var(--wp-yiq-text-light);background-color:var(--primary);border-color:var(--primary)}.btn-outline-primary:focus,.btn-outline-primary.focus{-webkit-box-shadow:0 0 0 .2rem var(--primary-shadow);box-shadow:0 0 0 .2rem var(--primary-shadow)}.btn-outline-primary.disabled,.btn-outline-primary:disabled{color:var(--primary);background-color:rgba(0,0,0,0)}.btn-outline-primary:not(:disabled):not(.disabled):active,.btn-outline-primary:not(:disabled):not(.disabled).active,.show>.btn-outline-primary.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--primary);border-color:var(--primary)}.btn-outline-primary:not(:disabled):not(.disabled):active:focus,.btn-outline-primary:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-primary.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--primary-shadow);box-shadow:0 0 0 .2rem var(--primary-shadow)}.btn-outline-secondary{color:var(--secondary);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--secondary)}.btn-outline-secondary:hover{color:var(--wp-yiq-text-light);background-color:var(--secondary);border-color:var(--secondary)}.btn-outline-secondary:focus,.btn-outline-secondary.focus{-webkit-box-shadow:0 0 0 .2rem var(--secondary-shadow);box-shadow:0 0 0 .2rem var(--secondary-shadow)}.btn-outline-secondary.disabled,.btn-outline-secondary:disabled{color:var(--secondary);background-color:rgba(0,0,0,0)}.btn-outline-secondary:not(:disabled):not(.disabled):active,.btn-outline-secondary:not(:disabled):not(.disabled).active,.show>.btn-outline-secondary.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--secondary);border-color:var(--secondary)}.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-secondary.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--secondary-shadow);box-shadow:0 0 0 .2rem var(--secondary-shadow)}.btn-outline-tertiary{color:var(--tertiary);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--tertiary)}.btn-outline-tertiary:hover{color:var(--wp-yiq-text-light);background-color:var(--tertiary);border-color:var(--tertiary)}.btn-outline-tertiary:focus,.btn-outline-tertiary.focus{-webkit-box-shadow:0 0 0 .2rem var(--tertiary-shadow);box-shadow:0 0 0 .2rem var(--tertiary-shadow)}.btn-outline-tertiary.disabled,.btn-outline-tertiary:disabled{color:var(--tertiary);background-color:rgba(0,0,0,0)}.btn-outline-tertiary:not(:disabled):not(.disabled):active,.btn-outline-tertiary:not(:disabled):not(.disabled).active,.show>.btn-outline-tertiary.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--tertiary);border-color:var(--tertiary)}.btn-outline-tertiary:not(:disabled):not(.disabled):active:focus,.btn-outline-tertiary:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-tertiary.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--tertiary-shadow);box-shadow:0 0 0 .2rem var(--tertiary-shadow)}.btn-outline-success{color:var(--success);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--success)}.btn-outline-success:hover{color:var(--wp-yiq-text-light);background-color:var(--success);border-color:var(--success)}.btn-outline-success:focus,.btn-outline-success.focus{-webkit-box-shadow:0 0 0 .2rem var(--success-shadow);box-shadow:0 0 0 .2rem var(--success-shadow)}.btn-outline-success.disabled,.btn-outline-success:disabled{color:var(--success);background-color:rgba(0,0,0,0)}.btn-outline-success:not(:disabled):not(.disabled):active,.btn-outline-success:not(:disabled):not(.disabled).active,.show>.btn-outline-success.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--success);border-color:var(--success)}.btn-outline-success:not(:disabled):not(.disabled):active:focus,.btn-outline-success:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-success.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--success-shadow);box-shadow:0 0 0 .2rem var(--success-shadow)}.btn-outline-main{color:var(--main);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--main)}.btn-outline-main:hover{color:var(--wp-yiq-text-light);background-color:var(--main);border-color:var(--main)}.btn-outline-main:focus,.btn-outline-main.focus{-webkit-box-shadow:0 0 0 .2rem var(--main-shadow);box-shadow:0 0 0 .2rem var(--main-shadow)}.btn-outline-main.disabled,.btn-outline-main:disabled{color:var(--main);background-color:rgba(0,0,0,0)}.btn-outline-main:not(:disabled):not(.disabled):active,.btn-outline-main:not(:disabled):not(.disabled).active,.show>.btn-outline-main.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--main);border-color:var(--main)}.btn-outline-main:not(:disabled):not(.disabled):active:focus,.btn-outline-main:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-main.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--main-shadow);box-shadow:0 0 0 .2rem var(--main-shadow)}.btn-outline-info{color:var(--info);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--info)}.btn-outline-info:hover{color:var(--wp-yiq-text-dark);background-color:var(--info);border-color:var(--info)}.btn-outline-info:focus,.btn-outline-info.focus{-webkit-box-shadow:0 0 0 .2rem var(--info-shadow);box-shadow:0 0 0 .2rem var(--info-shadow)}.btn-outline-info.disabled,.btn-outline-info:disabled{color:var(--info);background-color:rgba(0,0,0,0)}.btn-outline-info:not(:disabled):not(.disabled):active,.btn-outline-info:not(:disabled):not(.disabled).active,.show>.btn-outline-info.dropdown-toggle{color:var(--wp-yiq-text-dark);background-color:var(--info);border-color:var(--info)}.btn-outline-info:not(:disabled):not(.disabled):active:focus,.btn-outline-info:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-info.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--info-shadow);box-shadow:0 0 0 .2rem var(--info-shadow)}.btn-outline-warning{color:var(--warning);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--warning)}.btn-outline-warning:hover{color:var(--wp-yiq-text-light);background-color:var(--warning);border-color:var(--warning)}.btn-outline-warning:focus,.btn-outline-warning.focus{-webkit-box-shadow:0 0 0 .2rem var(--warning-shadow);box-shadow:0 0 0 .2rem var(--warning-shadow)}.btn-outline-warning.disabled,.btn-outline-warning:disabled{color:var(--warning);background-color:rgba(0,0,0,0)}.btn-outline-warning:not(:disabled):not(.disabled):active,.btn-outline-warning:not(:disabled):not(.disabled).active,.show>.btn-outline-warning.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--warning);border-color:var(--warning)}.btn-outline-warning:not(:disabled):not(.disabled):active:focus,.btn-outline-warning:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-warning.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--warning-shadow);box-shadow:0 0 0 .2rem var(--warning-shadow)}.btn-outline-danger{color:var(--danger);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--danger)}.btn-outline-danger:hover{color:var(--wp-yiq-text-light);background-color:var(--danger);border-color:var(--danger)}.btn-outline-danger:focus,.btn-outline-danger.focus{-webkit-box-shadow:0 0 0 .2rem var(--danger-shadow);box-shadow:0 0 0 .2rem var(--danger-shadow)}.btn-outline-danger.disabled,.btn-outline-danger:disabled{color:var(--danger);background-color:rgba(0,0,0,0)}.btn-outline-danger:not(:disabled):not(.disabled):active,.btn-outline-danger:not(:disabled):not(.disabled).active,.show>.btn-outline-danger.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--danger);border-color:var(--danger)}.btn-outline-danger:not(:disabled):not(.disabled):active:focus,.btn-outline-danger:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-danger.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--danger-shadow);box-shadow:0 0 0 .2rem var(--danger-shadow)}.btn-outline-light{color:var(--light);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--light)}.btn-outline-light:hover{color:var(--wp-yiq-text-dark);background-color:var(--light);border-color:var(--light)}.btn-outline-light:focus,.btn-outline-light.focus{-webkit-box-shadow:0 0 0 .2rem var(--light-shadow);box-shadow:0 0 0 .2rem var(--light-shadow)}.btn-outline-light.disabled,.btn-outline-light:disabled{color:var(--light);background-color:rgba(0,0,0,0)}.btn-outline-light:not(:disabled):not(.disabled):active,.btn-outline-light:not(:disabled):not(.disabled).active,.show>.btn-outline-light.dropdown-toggle{color:var(--wp-yiq-text-dark);background-color:var(--light);border-color:var(--light)}.btn-outline-light:not(:disabled):not(.disabled):active:focus,.btn-outline-light:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-light.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--light-shadow);box-shadow:0 0 0 .2rem var(--light-shadow)}.btn-outline-dark{color:var(--dark);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--dark)}.btn-outline-dark:hover{color:var(--wp-yiq-text-light);background-color:var(--dark);border-color:var(--dark)}.btn-outline-dark:focus,.btn-outline-dark.focus{-webkit-box-shadow:0 0 0 .2rem var(--dark-shadow);box-shadow:0 0 0 .2rem var(--dark-shadow)}.btn-outline-dark.disabled,.btn-outline-dark:disabled{color:var(--dark);background-color:rgba(0,0,0,0)}.btn-outline-dark:not(:disabled):not(.disabled):active,.btn-outline-dark:not(:disabled):not(.disabled).active,.show>.btn-outline-dark.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--dark);border-color:var(--dark)}.btn-outline-dark:not(:disabled):not(.disabled):active:focus,.btn-outline-dark:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-dark.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--dark-shadow);box-shadow:0 0 0 .2rem var(--dark-shadow)}.btn-outline-purple-pl{color:var(--purple-pl);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--purple-pl)}.btn-outline-purple-pl:hover{color:var(--wp-yiq-text-light);background-color:var(--purple-pl);border-color:var(--purple-pl)}.btn-outline-purple-pl:focus,.btn-outline-purple-pl.focus{-webkit-box-shadow:0 0 0 .2rem var(--purple-pl-shadow);box-shadow:0 0 0 .2rem var(--purple-pl-shadow)}.btn-outline-purple-pl.disabled,.btn-outline-purple-pl:disabled{color:var(--purple-pl);background-color:rgba(0,0,0,0)}.btn-outline-purple-pl:not(:disabled):not(.disabled):active,.btn-outline-purple-pl:not(:disabled):not(.disabled).active,.show>.btn-outline-purple-pl.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--purple-pl);border-color:var(--purple-pl)}.btn-outline-purple-pl:not(:disabled):not(.disabled):active:focus,.btn-outline-purple-pl:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-purple-pl.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--purple-pl-shadow);box-shadow:0 0 0 .2rem var(--purple-pl-shadow)}.btn-outline-gray{color:var(--gray);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--gray)}.btn-outline-gray:hover{color:var(--wp-yiq-text-light);background-color:var(--gray);border-color:var(--gray)}.btn-outline-gray:focus,.btn-outline-gray.focus{-webkit-box-shadow:0 0 0 .2rem var(--gray-shadow);box-shadow:0 0 0 .2rem var(--gray-shadow)}.btn-outline-gray.disabled,.btn-outline-gray:disabled{color:var(--gray);background-color:rgba(0,0,0,0)}.btn-outline-gray:not(:disabled):not(.disabled):active,.btn-outline-gray:not(:disabled):not(.disabled).active,.show>.btn-outline-gray.dropdown-toggle{color:var(--wp-yiq-text-light);background-color:var(--gray);border-color:var(--gray)}.btn-outline-gray:not(:disabled):not(.disabled):active:focus,.btn-outline-gray:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-gray.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--gray-shadow);box-shadow:0 0 0 .2rem var(--gray-shadow)}.btn-outline-default{color:var(--default);background-color:rgba(0,0,0,0);background-image:none;border-color:var(--default)}.btn-outline-default:hover{color:var(--wp-yiq-text-dark);background-color:var(--default);border-color:var(--default)}.btn-outline-default:focus,.btn-outline-default.focus{-webkit-box-shadow:0 0 0 .2rem var(--default-shadow);box-shadow:0 0 0 .2rem var(--default-shadow)}.btn-outline-default.disabled,.btn-outline-default:disabled{color:var(--default);background-color:rgba(0,0,0,0)}.btn-outline-default:not(:disabled):not(.disabled):active,.btn-outline-default:not(:disabled):not(.disabled).active,.show>.btn-outline-default.dropdown-toggle{color:var(--wp-yiq-text-dark);background-color:var(--default);border-color:var(--default)}.btn-outline-default:not(:disabled):not(.disabled):active:focus,.btn-outline-default:not(:disabled):not(.disabled).active:focus,.show>.btn-outline-default.dropdown-toggle:focus{-webkit-box-shadow:0 0 0 .2rem var(--default-shadow);box-shadow:0 0 0 .2rem var(--default-shadow)}.btn-pill{border-radius:16px}.btn-default,.btn-default:hover{color:#fff}.btn-link{font-weight:400;color:#00b8db;background-color:rgba(0,0,0,0)}.btn-link:hover{color:rgb(0,162.5753424658,193.5);background-color:rgba(0,0,0,0);border-color:rgba(0,0,0,0)}.btn-link:focus,.btn-link.focus{border-color:rgba(0,0,0,0);-webkit-box-shadow:none;box-shadow:none}.btn-link:disabled,.btn-link.disabled{color:#6c757d;pointer-events:none}.btn-block{display:block;width:100%}.btn-block+.btn-block{margin-top:.5rem}input[type=submit].btn-block,input[type=reset].btn-block,input[type=button].btn-block{width:100%}@font-face{font-family:"atum-icon-font";src:url("../../../atum-stock-manager-for-woocommerce/assets/fonts/atum-icon-font.eot?h88pav");src:url("../../../atum-stock-manager-for-woocommerce/assets/fonts/atum-icon-font.eot?h88pav#iefix") format("embedded-opentype"),url("../../../atum-stock-manager-for-woocommerce/assets/fonts/atum-icon-font.ttf?h88pav") format("truetype"),url("../../../atum-stock-manager-for-woocommerce/assets/fonts/atum-icon-font.woff?h88pav") format("woff"),url("../../../atum-stock-manager-for-woocommerce/assets/fonts/atum-icon-font.svg?h88pav#atum-icon-font") format("svg");font-weight:normal;font-style:normal}[class^=atmi-],[class*=" atmi-"],.atum-icon{font-family:"atum-icon-font" !important;speak:none;font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.atmi-barcodes-pro:before{content:""}.atmi-pick-pack:before{content:""}.atmi-stock-takes:before{content:""}.atmi-question:before{content:""}.atmi-ok:before{content:""}.atmi-po-pro:before{content:""}.atmi-featured:before{content:""}.atmi-logs:before{content:""}.atmi-read:before{content:""}.atmi-save:before{content:""}.atmi-duplicate:before{content:""}.atmi-shipping:before{content:""}.atmi-export:before{content:"󩣚"}.atmi-hidden:before{content:""}.atmi-variable-product-part:before{content:""}.atmi-variable-raw-material:before{content:""}.atmi-product-part:before{content:""}.atmi-raw-material:before{content:""}.atmi-atum:before{content:""}.atmi-bundle:before{content:""}.atmi-cog-solid:before{content:""}.atmi-database-solid:before{content:""}.atmi-funnel-solid:before{content:""}.atmi-highlight-solid:before{content:""}.atmi-magic-wand-solid:before{content:""}.atmi-wc-contract:before{content:""}.atmi-wc-expand:before{content:""}.atmi-wc-status:before{content:""}.atmi-wc-downloadable:before,.atmi-downloadable:before{content:""}.atmi-wc-grouped:before,.atmi-grouped:before{content:""}.atmi-wc-simple:before,.atmi-simple:before{content:""}.atmi-wc-variable:before,.atmi-variable:before{content:""}.atmi-wc-virtual:before,.atmi-virtual:before{content:""}.atmi-product-levels:before{content:""}.atmi-multi-inventory:before{content:""}.atmi-checkmark:before{content:""}.atmi-pdf:before{content:""}.atmi-view-list:before{content:""}.atmi-view-sidebar-left:before{content:""}.atmi-view-grid:before{content:""}.atmi-view-sidebar-right:before{content:""}.atmi-view-sticky-header:before{content:""}.atmi-info:before{content:""}.atmi-view-list-2:before{content:""}.atmi-view-grid-2:before{content:""}.atmi-tree:before{content:""}.atmi-pencil:before{content:""}.atmi-options:before{content:""}.atmi-chart-solid:before{content:""}.atmi-chart-outline:before{content:""}.atmi-chart-bars:before{content:""}.atmi-drag:before{content:""}.atmi-arrow-child:before{content:""}.atmi-alarm:before{content:""}.atmi-apartment:before{content:""}.atmi-arrow-down-circle:before{content:""}.atmi-arrow-down:before{content:""}.atmi-arrow-left-circle:before{content:""}.atmi-arrow-left:before{content:""}.atmi-arrow-right-circle:before{content:""}.atmi-arrow-right:before{content:""}.atmi-arrow-up-circle:before{content:""}.atmi-arrow-up:before{content:""}.atmi-bicycle:before{content:""}.atmi-bold:before{content:""}.atmi-book:before{content:""}.atmi-bookmark:before{content:""}.atmi-briefcase:before{content:""}.atmi-bubble:before{content:""}.atmi-bug:before{content:""}.atmi-bullhorn:before{content:""}.atmi-bus:before{content:""}.atmi-calendar-full:before{content:""}.atmi-camera-video:before{content:""}.atmi-camera:before{content:""}.atmi-car:before{content:""}.atmi-cart:before{content:""}.atmi-chart-bars-2:before{content:""}.atmi-checkmark-circle:before{content:""}.atmi-chevron-down-circle:before{content:""}.atmi-chevron-down:before{content:""}.atmi-chevron-left-circle:before{content:""}.atmi-chevron-left:before{content:""}.atmi-chevron-right-circle:before{content:""}.atmi-chevron-right:before{content:""}.atmi-chevron-up-circle:before{content:""}.atmi-chevron-up:before{content:""}.atmi-circle-minus:before{content:""}.atmi-clock:before{content:""}.atmi-cloud-check:before{content:""}.atmi-cloud-download:before{content:""}.atmi-cloud-sync:before{content:""}.atmi-cloud-upload:before{content:""}.atmi-cloud:before{content:""}.atmi-code:before{content:""}.atmi-coffee-cup:before{content:""}.atmi-cog:before{content:""}.atmi-construction:before{content:""}.atmi-crop:before{content:""}.atmi-cross-circle:before{content:""}.atmi-cross:before{content:""}.atmi-database:before{content:""}.atmi-diamond:before{content:""}.atmi-dice:before{content:""}.atmi-dinner:before{content:""}.atmi-direction-ltr:before{content:""}.atmi-direction-rtl:before{content:""}.atmi-download:before{content:""}.atmi-drop:before{content:""}.atmi-earth:before{content:""}.atmi-enter-down:before{content:""}.atmi-enter:before{content:""}.atmi-envelope:before{content:""}.atmi-exit-up:before{content:""}.atmi-exit:before{content:""}.atmi-eye:before{content:""}.atmi-file-add:before{content:""}.atmi-file-empty:before{content:""}.atmi-film-play:before{content:""}.atmi-flag:before{content:""}.atmi-frame-contract:before{content:""}.atmi-frame-expand:before{content:""}.atmi-funnel:before{content:""}.atmi-gift:before{content:""}.atmi-graduation-hat:before{content:""}.atmi-hand:before{content:""}.atmi-heart-pulse:before{content:""}.atmi-heart:before{content:""}.atmi-highlight:before{content:""}.atmi-history:before{content:""}.atmi-home:before{content:""}.atmi-hourglass:before{content:""}.atmi-inbox:before{content:""}.atmi-indent-decrease:before{content:""}.atmi-indent-increase:before{content:""}.atmi-italic:before{content:""}.atmi-keyboard:before{content:""}.atmi-laptop-phone:before{content:""}.atmi-laptop:before{content:""}.atmi-layers:before{content:""}.atmi-leaf:before{content:""}.atmi-license:before{content:""}.atmi-lighter:before{content:""}.atmi-line-spacing:before{content:""}.atmi-link:before{content:""}.atmi-list:before{content:""}.atmi-location:before{content:""}.atmi-lock:before{content:""}.atmi-magic-wand:before{content:""}.atmi-magnifier:before{content:""}.atmi-map-marker:before{content:""}.atmi-map:before{content:""}.atmi-menu-circle:before{content:""}.atmi-menu:before{content:""}.atmi-mic:before{content:""}.atmi-moon:before{content:""}.atmi-move:before{content:""}.atmi-music-note:before{content:""}.atmi-mustache:before{content:""}.atmi-neutral:before{content:""}.atmi-page-break:before{content:""}.atmi-paperclip:before{content:""}.atmi-paw:before{content:""}.atmi-phone-handset:before{content:""}.atmi-phone:before{content:""}.atmi-picture:before{content:""}.atmi-pie-chart:before{content:""}.atmi-pilcrow:before{content:""}.atmi-plus-circle:before{content:""}.atmi-pointer-down:before{content:""}.atmi-pointer-left:before{content:""}.atmi-pointer-right:before{content:""}.atmi-pointer-up:before{content:""}.atmi-poop:before{content:""}.atmi-power-switch:before{content:""}.atmi-printer:before{content:""}.atmi-pushpin:before{content:""}.atmi-question-circle:before{content:""}.atmi-redo:before{content:""}.atmi-rocket:before{content:""}.atmi-sad:before{content:""}.atmi-screen:before{content:""}.atmi-select:before{content:""}.atmi-shirt:before{content:""}.atmi-smartphone:before{content:""}.atmi-smile:before{content:""}.atmi-sort-alpha-asc:before{content:""}.atmi-sort-amount-asc:before{content:""}.atmi-spell-check:before{content:""}.atmi-star-empty:before{content:""}.atmi-star-half:before{content:""}.atmi-star:before{content:""}.atmi-store:before{content:""}.atmi-strikethrough:before{content:""}.atmi-sun:before{content:""}.atmi-sync:before{content:""}.atmi-tablet:before{content:""}.atmi-tag:before{content:""}.atmi-text-align-center:before{content:""}.atmi-text-align-justify:before{content:""}.atmi-text-align-left:before{content:""}.atmi-text-align-right:before{content:""}.atmi-text-format-remove:before{content:""}.atmi-text-format:before{content:""}.atmi-text-size:before{content:""}.atmi-thumbs-down:before{content:""}.atmi-thumbs-up:before{content:""}.atmi-time:before{content:""}.atmi-train:before{content:""}.atmi-trash:before{content:""}.atmi-underline:before{content:""}.atmi-undo:before{content:""}.atmi-unlink:before{content:""}.atmi-upload:before{content:""}.atmi-user:before{content:""}.atmi-users:before{content:""}.atmi-volume-high:before{content:""}.atmi-volume-low:before{content:""}.atmi-volume-medium:before{content:""}.atmi-volume:before{content:""}.atmi-warning:before{content:""}.atmi-wheelchair:before{content:""}.atmi-spin{display:inline-block;-webkit-animation-name:spin;animation-name:spin;-webkit-animation-duration:3000ms;animation-duration:3000ms;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:linear;animation-timing-function:linear}@-webkit-keyframes spin{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes scaleUpSwal{0%{-webkit-transform:scale(0.8) translateY(1000px);transform:scale(0.8) translateY(1000px);opacity:0}100%{-webkit-transform:scale(1) translateY(0px);transform:scale(1) translateY(0px);opacity:1}}@keyframes scaleUpSwal{0%{-webkit-transform:scale(0.8) translateY(1000px);transform:scale(0.8) translateY(1000px);opacity:0}100%{-webkit-transform:scale(1) translateY(0px);transform:scale(1) translateY(0px);opacity:1}}@-webkit-keyframes scaleDownSwal{0%{-webkit-transform:scale(1) translateY(0px);transform:scale(1) translateY(0px);opacity:1}100%{-webkit-transform:scale(0.8) translateY(1000px);transform:scale(0.8) translateY(1000px);opacity:0}}@keyframes scaleDownSwal{0%{-webkit-transform:scale(1) translateY(0px);transform:scale(1) translateY(0px);opacity:1}100%{-webkit-transform:scale(0.8) translateY(1000px);transform:scale(0.8) translateY(1000px);opacity:0}}@-webkit-keyframes fadeInSwal{0%{background:rgba(0,0,0,0)}100%{background:rgba(0,0,0,.7)}}@keyframes fadeInSwal{0%{background:rgba(0,0,0,0)}100%{background:rgba(0,0,0,.7)}}@-webkit-keyframes fadeOutSwal{0%{background:rgba(0,0,0,.7)}100%{background:rgba(0,0,0,0)}}@keyframes fadeOutSwal{0%{background:rgba(0,0,0,.7)}100%{background:rgba(0,0,0,0)}}.swal2-container{z-index:100000 !important;word-break:normal}.swal2-container .swal2-html-container{padding:0;overflow:visible}.swal2-container .swal2-html-container input[type=text]{margin:0;border-radius:4px;height:45px;-webkit-box-shadow:none;box-shadow:none}.swal2-container .swal2-html-container .atum-link{text-decoration:underline}.swal2-container .swal2-title{line-height:1.3;font-size:23px;color:var(--main-title)}.swal2-container .swal2-title small{-ms-flex-preferred-size:100%;flex-basis:100%}.swal2-container .swal2-modal{font-family:inherit}.swal2-container .swal2-modal .swal2-close{-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end;font-weight:100;font-family:inherit;font-size:30px;line-height:30px;color:var(--gray-500);z-index:100001;-webkit-transition:.2s ease-in-out;transition:.2s ease-in-out;padding-right:10px}.swal2-container .swal2-modal .swal2-close:hover{color:var(--gray-800)}.swal2-container .swal2-modal .swal2-validation-message{margin:1.625em 0 0;font-size:14px;text-align:left;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start}.swal2-container .swal2-modal p{font-size:15px;text-align:center}.swal2-container .swal2-modal button,.swal2-container .swal2-modal a{-webkit-transition:background-color .2s ease-in-out;transition:background-color .2s ease-in-out}.swal2-container .swal2-modal button:focus,.swal2-container .swal2-modal a:focus{outline:none;-webkit-box-shadow:none;box-shadow:none}.swal2-container .swal2-modal button{border-radius:5px;text-transform:uppercase;font-weight:400;padding:9px 19px;font-size:13px;letter-spacing:1px;background-image:none !important}.swal2-container .swal2-modal button.swal2-confirm{background-color:var(--primary)}.swal2-container .swal2-modal button.btn-sm{padding:.25rem .5rem;font-size:12px}.swal2-container .swal2-modal button .atum-icon{position:relative;font-size:1.15em;top:.15em;padding-right:3px}@media all and (max-width: 480px){.swal2-container .swal2-modal button{width:100%;margin-bottom:10px}}.swal2-container .swal2-modal a{text-decoration:none;color:var(--primary) !important}.swal2-container .swal2-modal a:hover{color:var(--wp-link-hover)}.swal2-container .swal2-modal .alert p{text-align:left;margin:0}.swal2-container .swal2-modal .swal2-loader{border-color:var(--primary) rgba(0,0,0,0) var(--primary) rgba(0,0,0,0)}.swal2-container .swal2-progress-steps .swal2-progress-step{margin-bottom:0}.swal2-container .atum-loading{width:38px;height:38px;border:3px solid rgba(0,184,219,.25);border-top-color:#00b8db;border-radius:50%;position:absolute;z-index:50;-webkit-animation:loader-rotate 1s linear infinite;animation:loader-rotate 1s linear infinite;top:30px;left:0;right:0;margin:auto}.swal2-container .swal2-show{opacity:0;-webkit-animation:scaleUpSwal .2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;animation:scaleUpSwal .2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards}.swal2-container .swal2-hide{-webkit-animation:scaleDownSwal .2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;animation:scaleDownSwal .2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards}.swal2-container.swal2-backdrop-show{background:rgba(0,0,0,0);-webkit-animation:fadeInSwal .2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;animation:fadeInSwal .2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards}.swal2-container.swal2-backdrop-hide{-webkit-animation:fadeOutSwal .2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;animation:fadeOutSwal .2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards}@-webkit-keyframes loader-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes loader-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.swal2-container.atum-modal{text-align:left}.swal2-container.atum-modal .swal2-popup{background-color:var(--atum-table-bg)}.swal2-container.atum-modal h2.swal2-title small{color:var(--atum-text-color-var1)}.swal2-container.atum-modal .swal2-title{color:var(--main-title);font-size:18px;text-align:left;padding-left:1.1em;padding-right:1.1em}.swal2-container.atum-modal .swal2-html-container{margin-top:0;margin-left:1.1em;margin-right:1.1em;z-index:unset}.swal2-container.atum-modal .atum-modal-content{position:relative;min-height:75px}.swal2-container.atum-modal .atum-modal-content .note{text-align:left;font-size:12px;color:var(--atum-text-color-dark2);margin-bottom:15px}.swal2-container.atum-modal .atum-modal-content hr{border-top-color:#ced4da;border-bottom:none;margin-left:-1.1em;margin-right:-1.1em}.swal2-container.atum-modal .atum-modal-content input[type=text],.swal2-container.atum-modal .atum-modal-content input[type=email],.swal2-container.atum-modal .atum-modal-content input[type=search]{width:100% !important;height:31px !important}.swal2-container.atum-modal .atum-modal-content input[type=text],.swal2-container.atum-modal .atum-modal-content input[type=email],.swal2-container.atum-modal .atum-modal-content input[type=search],.swal2-container.atum-modal .atum-modal-content input[type=number]{background-color:var(--atum-table-bg);color:var(--atum-text-color-var3);border-color:var(--main-border-alt)}.swal2-container.atum-modal .atum-modal-content input[type=text].placeholder,.swal2-container.atum-modal .atum-modal-content input[type=email].placeholder,.swal2-container.atum-modal .atum-modal-content input[type=search].placeholder,.swal2-container.atum-modal .atum-modal-content input[type=number].placeholder{text-align:left;font-size:12px;font-style:italic;color:var(--atum-text-color-var1)}.swal2-container.atum-modal .atum-modal-content input[type=text]::-moz-placeholder,.swal2-container.atum-modal .atum-modal-content input[type=email]::-moz-placeholder,.swal2-container.atum-modal .atum-modal-content input[type=search]::-moz-placeholder,.swal2-container.atum-modal .atum-modal-content input[type=number]::-moz-placeholder{text-align:left;font-size:12px;font-style:italic;color:var(--atum-text-color-var1);opacity:1}.swal2-container.atum-modal .atum-modal-content input[type=text]::-webkit-input-placeholder,.swal2-container.atum-modal .atum-modal-content input[type=email]::-webkit-input-placeholder,.swal2-container.atum-modal .atum-modal-content input[type=search]::-webkit-input-placeholder,.swal2-container.atum-modal .atum-modal-content input[type=number]::-webkit-input-placeholder{text-align:left;font-size:12px;font-style:italic;color:var(--atum-text-color-var1)}.swal2-container.atum-modal .atum-modal-content form{margin-left:-20px;margin-right:-20px}.swal2-container.atum-modal .atum-modal-content form fieldset:first-child{padding-bottom:10px}.swal2-container.atum-modal .atum-modal-content form fieldset:not(:first-child){padding-top:10px;border-top:1px solid #ced4da}.swal2-container.atum-modal .atum-modal-content form fieldset h3{text-align:left;padding-left:20px;font-size:14px;color:var(--main-title) !important;margin-bottom:10px}.swal2-container.atum-modal .atum-modal-content form .input-group{padding:5px 20px;-webkit-box-sizing:border-box;box-sizing:border-box;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.swal2-container.atum-modal .atum-modal-content form .input-group label{font-size:13px;color:var(--atum-text-color-var1)}.swal2-container.atum-modal .atum-modal-content form .input-group input[type=text],.swal2-container.atum-modal .atum-modal-content form .input-group input[type=email],.swal2-container.atum-modal .atum-modal-content form .input-group input[type=search],.swal2-container.atum-modal .atum-modal-content form .input-group select,.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select{width:230px !important;max-width:100%;background-color:var(--atum-table-bg);color:var(--atum-text-color-var3);border-color:var(--main-border-alt)}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection--single{border-radius:4px;text-align:left}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection__placeholder{font-size:12px;font-style:italic;color:var(--atum-text-color-var1)}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection__rendered{color:var(--atum-text-color-var3) !important;font-size:14px}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection__rendered li.select2-search.select2-search--inline{display:-webkit-box;display:-ms-flexbox;display:flex}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection__rendered li.select2-search.select2-search--inline input.select2-search__field{max-width:85%}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection__rendered li.select2-selection__choice{color:var(--atum-text-color-var3)}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection__arrow:after{display:none}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection--multiple{background-color:var(--atum-table-bg);border-color:var(--main-border-alt)}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection--multiple .select2-selection__rendered{padding-left:10px;padding-right:10px;-webkit-box-sizing:content-box;box-sizing:content-box}.swal2-container.atum-modal .atum-modal-content form .input-group .atum-enhanced-select .select2-selection--multiple .select2-selection__rendered .select2-selection__choice{height:16px}.swal2-container.atum-modal .atum-modal-content form .input-group .alert{text-align:left;display:block;font-size:11px;margin-bottom:0}.swal2-container.atum-modal .swal2-confirm{float:right}.swal2-container.atum-modal div.swal2-actions{width:100%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end;border-top:1px solid #ced4da;margin-top:0;padding-right:20px;padding-left:20px;padding-top:10px}.swal2-container.atum-modal .swal2-validation-message{text-align:left;font-size:13px}.atum-trial-modal .swal2-modal{width:532px;max-width:100%}.atum-trial-modal .swal2-title{margin-bottom:0;margin-top:-10px}.atum-trial-modal .swal2-container,.atum-trial-modal .swal2-html-container{margin-bottom:0}.atum-trial-modal .swal2-html-container p{font-size:18px;color:#6c757d;margin-top:.5em}.atum-trial-modal .atum-trial-list{list-style:none;margin-top:1em;margin-bottom:0;max-height:263px;overflow:auto;padding-right:6px}.atum-trial-modal .atum-trial-list li{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-ms-flex-wrap:nowrap;flex-wrap:nowrap;padding-bottom:10px}.atum-trial-modal .atum-trial-list li:not(:last-child){border-bottom:1px solid #ced4da}.atum-trial-modal .atum-trial-list li:not(:first-child){padding-top:3px}.atum-trial-modal .atum-trial-list__item{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;gap:5px}.atum-trial-modal .atum-trial-list__item>span{text-align:left;color:#27283b}.atum-trial-modal .atum-trial-list__item small,.atum-trial-modal .atum-trial-list__item i{color:#ff4848}.atum-trial-modal .atum-trial-list__item small{font-size:11px;display:block;margin-top:4px}.atum-trial-modal .atum-trial-list__item-thumb{display:-webkit-box;display:-ms-flexbox;display:flex;background-color:#e9ecef;border-radius:5px;padding:6px}.atum-trial-modal .atum-trial-list__item-thumb img{width:48px;height:auto}.atum-trial-modal .atum-trial-list__item-buttons .btn{padding:.25rem .55rem;border-radius:5px;text-transform:uppercase;-webkit-transition:.2s ease-in-out;transition:.2s ease-in-out;font-size:12px;letter-spacing:normal}.atum-trial-modal .atum-trial-list__item-buttons a.btn-primary{color:#fff !important}.atum-trial-modal .swal2-footer{font-size:12px;color:#6c757d;margin-top:0}.atum-trial-modal .swal2-footer a{color:#00b8db !important}