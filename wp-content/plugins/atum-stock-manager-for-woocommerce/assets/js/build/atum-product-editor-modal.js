(()=>{"use strict";var t,e={1669:t=>{t.exports=jQuery}},n={};function o(t){var i=n[t];if(void 0!==i)return i.exports;var r=n[t]={exports:{}};return e[t](r,r.exports,o),r.exports}o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var n in e)o.o(e,n)&&!o.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),function(t){t.success="#69C61D",t.primary="#00B8DB",t.warning="#EFAF00",t.danger="#FF4848",t.grey="#ADB5BD"}(t||(t={}));const i=function(){function t(t,e){void 0===e&&(e={}),this.varName=t,this.defaults=e,this.settings={};var n=void 0!==window[t]?window[t]:{};Object.assign(this.settings,e,n)}return t.prototype.get=function(t){if(void 0!==this.settings[t])return this.settings[t]},t.prototype.getAll=function(){return this.settings},t.prototype.delete=function(t){this.settings.hasOwnProperty(t)&&delete this.settings[t]},t}(),r=Swal;var s=o.n(r),c=o(1669),a=o(1669),u=function(){function e(t){this.settings=t,this.bindEvents()}return e.prototype.bindEvents=function(){var t=this;c("body").on("change","#woocommerce_feature_product_block_editor_enabled",(function(e){c(e.currentTarget).is(":checked")&&t.showModal()}))},e.prototype.showModal=function(){s().fire({icon:"warning",title:this.settings.get("title"),html:this.settings.get("text"),confirmButtonText:this.settings.get("confirm"),showCancelButton:!0,cancelButtonText:this.settings.get("cancel"),confirmButtonColor:t.warning,cancelButtonColor:t.primary,focusConfirm:!1,allowEscapeKey:!1,allowOutsideClick:!1,allowEnterKey:!1}).then((function(t){t.isDismissed&&c("#woocommerce_feature_product_block_editor_enabled").prop("checked",!1)}))},e}();a((function(t){var e=new i("atumProductEditorModalVars");new u(e)}))})();