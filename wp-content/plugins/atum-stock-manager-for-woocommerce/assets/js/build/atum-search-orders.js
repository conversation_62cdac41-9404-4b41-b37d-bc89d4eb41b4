(()=>{var e={1170:(e,t,n)=>{"use strict";n.r(t),n.d(t,{afterMain:()=>b,afterRead:()=>y,afterWrite:()=>C,applyStyles:()=>B,arrow:()=>X,auto:()=>a,basePlacements:()=>s,beforeMain:()=>E,beforeRead:()=>g,beforeWrite:()=>x,bottom:()=>i,clippingParents:()=>l,computeStyles:()=>ne,createPopper:()=>Be,createPopperBase:()=>Oe,createPopperLite:()=>Te,detectOverflow:()=>De,end:()=>f,eventListeners:()=>ie,flip:()=>ye,hide:()=>be,left:()=>o,main:()=>w,modifierPhases:()=>F,offset:()=>xe,placements:()=>v,popper:()=>h,popperGenerator:()=>Se,popperOffsets:()=>Ae,preventOverflow:()=>Ce,read:()=>D,reference:()=>m,right:()=>u,start:()=>c,top:()=>r,variationPlacements:()=>d,viewport:()=>p,write:()=>A});var r="top",i="bottom",u="right",o="left",a="auto",s=[r,i,u,o],c="start",f="end",l="clippingParents",p="viewport",h="popper",m="reference",d=s.reduce((function(e,t){return e.concat([t+"-"+c,t+"-"+f])}),[]),v=[].concat(s,[a]).reduce((function(e,t){return e.concat([t,t+"-"+c,t+"-"+f])}),[]),g="beforeRead",D="read",y="afterRead",E="beforeMain",w="main",b="afterMain",x="beforeWrite",A="write",C="afterWrite",F=[g,D,y,E,w,b,x,A,C];function N(e){return e?(e.nodeName||"").toLowerCase():null}function _(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function M(e){return e instanceof _(e).Element||e instanceof Element}function S(e){return e instanceof _(e).HTMLElement||e instanceof HTMLElement}function O(e){return"undefined"!=typeof ShadowRoot&&(e instanceof _(e).ShadowRoot||e instanceof ShadowRoot)}const B={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},i=t.elements[e];S(i)&&N(i)&&(Object.assign(i.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],i=t.attributes[e]||{},u=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});S(r)&&N(r)&&(Object.assign(r.style,u),Object.keys(i).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]};function T(e){return e.split("-")[0]}var R=Math.max,I=Math.min,z=Math.round;function P(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function U(){return!/^((?!chrome|android).)*safari/i.test(P())}function L(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),i=1,u=1;t&&S(e)&&(i=e.offsetWidth>0&&z(r.width)/e.offsetWidth||1,u=e.offsetHeight>0&&z(r.height)/e.offsetHeight||1);var o=(M(e)?_(e):window).visualViewport,a=!U()&&n,s=(r.left+(a&&o?o.offsetLeft:0))/i,c=(r.top+(a&&o?o.offsetTop:0))/u,f=r.width/i,l=r.height/u;return{width:f,height:l,top:c,right:s+f,bottom:c+l,left:s,x:s,y:c}}function j(e){var t=L(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function k(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&O(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function q(e){return _(e).getComputedStyle(e)}function H(e){return["table","td","th"].indexOf(N(e))>=0}function G(e){return((M(e)?e.ownerDocument:e.document)||window.document).documentElement}function V(e){return"html"===N(e)?e:e.assignedSlot||e.parentNode||(O(e)?e.host:null)||G(e)}function Y(e){return S(e)&&"fixed"!==q(e).position?e.offsetParent:null}function $(e){for(var t=_(e),n=Y(e);n&&H(n)&&"static"===q(n).position;)n=Y(n);return n&&("html"===N(n)||"body"===N(n)&&"static"===q(n).position)?t:n||function(e){var t=/firefox/i.test(P());if(/Trident/i.test(P())&&S(e)&&"fixed"===q(e).position)return null;var n=V(e);for(O(n)&&(n=n.host);S(n)&&["html","body"].indexOf(N(n))<0;){var r=q(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function W(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Z(e,t,n){return R(e,I(t,n))}function J(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Q(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}const X={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,a=e.name,c=e.options,f=n.elements.arrow,l=n.modifiersData.popperOffsets,p=T(n.placement),h=W(p),m=[o,u].indexOf(p)>=0?"height":"width";if(f&&l){var d=function(e,t){return J("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Q(e,s))}(c.padding,n),v=j(f),g="y"===h?r:o,D="y"===h?i:u,y=n.rects.reference[m]+n.rects.reference[h]-l[h]-n.rects.popper[m],E=l[h]-n.rects.reference[h],w=$(f),b=w?"y"===h?w.clientHeight||0:w.clientWidth||0:0,x=y/2-E/2,A=d[g],C=b-v[m]-d[D],F=b/2-v[m]/2+x,N=Z(A,F,C),_=h;n.modifiersData[a]=((t={})[_]=N,t.centerOffset=N-F,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&k(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function K(e){return e.split("-")[1]}var ee={top:"auto",right:"auto",bottom:"auto",left:"auto"};function te(e){var t,n=e.popper,a=e.popperRect,s=e.placement,c=e.variation,l=e.offsets,p=e.position,h=e.gpuAcceleration,m=e.adaptive,d=e.roundOffsets,v=e.isFixed,g=l.x,D=void 0===g?0:g,y=l.y,E=void 0===y?0:y,w="function"==typeof d?d({x:D,y:E}):{x:D,y:E};D=w.x,E=w.y;var b=l.hasOwnProperty("x"),x=l.hasOwnProperty("y"),A=o,C=r,F=window;if(m){var N=$(n),M="clientHeight",S="clientWidth";if(N===_(n)&&"static"!==q(N=G(n)).position&&"absolute"===p&&(M="scrollHeight",S="scrollWidth"),s===r||(s===o||s===u)&&c===f)C=i,E-=(v&&N===F&&F.visualViewport?F.visualViewport.height:N[M])-a.height,E*=h?1:-1;if(s===o||(s===r||s===i)&&c===f)A=u,D-=(v&&N===F&&F.visualViewport?F.visualViewport.width:N[S])-a.width,D*=h?1:-1}var O,B=Object.assign({position:p},m&&ee),T=!0===d?function(e,t){var n=e.x,r=e.y,i=t.devicePixelRatio||1;return{x:z(n*i)/i||0,y:z(r*i)/i||0}}({x:D,y:E},_(n)):{x:D,y:E};return D=T.x,E=T.y,h?Object.assign({},B,((O={})[C]=x?"0":"",O[A]=b?"0":"",O.transform=(F.devicePixelRatio||1)<=1?"translate("+D+"px, "+E+"px)":"translate3d("+D+"px, "+E+"px, 0)",O)):Object.assign({},B,((t={})[C]=x?E+"px":"",t[A]=b?D+"px":"",t.transform="",t))}const ne={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=void 0===r||r,u=n.adaptive,o=void 0===u||u,a=n.roundOffsets,s=void 0===a||a,c={placement:T(t.placement),variation:K(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,te(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:s})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,te(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var re={passive:!0};const ie={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,u=void 0===i||i,o=r.resize,a=void 0===o||o,s=_(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return u&&c.forEach((function(e){e.addEventListener("scroll",n.update,re)})),a&&s.addEventListener("resize",n.update,re),function(){u&&c.forEach((function(e){e.removeEventListener("scroll",n.update,re)})),a&&s.removeEventListener("resize",n.update,re)}},data:{}};var ue={left:"right",right:"left",bottom:"top",top:"bottom"};function oe(e){return e.replace(/left|right|bottom|top/g,(function(e){return ue[e]}))}var ae={start:"end",end:"start"};function se(e){return e.replace(/start|end/g,(function(e){return ae[e]}))}function ce(e){var t=_(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function fe(e){return L(G(e)).left+ce(e).scrollLeft}function le(e){var t=q(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function pe(e){return["html","body","#document"].indexOf(N(e))>=0?e.ownerDocument.body:S(e)&&le(e)?e:pe(V(e))}function he(e,t){var n;void 0===t&&(t=[]);var r=pe(e),i=r===(null==(n=e.ownerDocument)?void 0:n.body),u=_(r),o=i?[u].concat(u.visualViewport||[],le(r)?r:[]):r,a=t.concat(o);return i?a:a.concat(he(V(o)))}function me(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function de(e,t,n){return t===p?me(function(e,t){var n=_(e),r=G(e),i=n.visualViewport,u=r.clientWidth,o=r.clientHeight,a=0,s=0;if(i){u=i.width,o=i.height;var c=U();(c||!c&&"fixed"===t)&&(a=i.offsetLeft,s=i.offsetTop)}return{width:u,height:o,x:a+fe(e),y:s}}(e,n)):M(t)?function(e,t){var n=L(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):me(function(e){var t,n=G(e),r=ce(e),i=null==(t=e.ownerDocument)?void 0:t.body,u=R(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),o=R(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-r.scrollLeft+fe(e),s=-r.scrollTop;return"rtl"===q(i||n).direction&&(a+=R(n.clientWidth,i?i.clientWidth:0)-u),{width:u,height:o,x:a,y:s}}(G(e)))}function ve(e,t,n,r){var i="clippingParents"===t?function(e){var t=he(V(e)),n=["absolute","fixed"].indexOf(q(e).position)>=0&&S(e)?$(e):e;return M(n)?t.filter((function(e){return M(e)&&k(e,n)&&"body"!==N(e)})):[]}(e):[].concat(t),u=[].concat(i,[n]),o=u[0],a=u.reduce((function(t,n){var i=de(e,n,r);return t.top=R(i.top,t.top),t.right=I(i.right,t.right),t.bottom=I(i.bottom,t.bottom),t.left=R(i.left,t.left),t}),de(e,o,r));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function ge(e){var t,n=e.reference,a=e.element,s=e.placement,l=s?T(s):null,p=s?K(s):null,h=n.x+n.width/2-a.width/2,m=n.y+n.height/2-a.height/2;switch(l){case r:t={x:h,y:n.y-a.height};break;case i:t={x:h,y:n.y+n.height};break;case u:t={x:n.x+n.width,y:m};break;case o:t={x:n.x-a.width,y:m};break;default:t={x:n.x,y:n.y}}var d=l?W(l):null;if(null!=d){var v="y"===d?"height":"width";switch(p){case c:t[d]=t[d]-(n[v]/2-a[v]/2);break;case f:t[d]=t[d]+(n[v]/2-a[v]/2)}}return t}function De(e,t){void 0===t&&(t={});var n=t,o=n.placement,a=void 0===o?e.placement:o,c=n.strategy,f=void 0===c?e.strategy:c,d=n.boundary,v=void 0===d?l:d,g=n.rootBoundary,D=void 0===g?p:g,y=n.elementContext,E=void 0===y?h:y,w=n.altBoundary,b=void 0!==w&&w,x=n.padding,A=void 0===x?0:x,C=J("number"!=typeof A?A:Q(A,s)),F=E===h?m:h,N=e.rects.popper,_=e.elements[b?F:E],S=ve(M(_)?_:_.contextElement||G(e.elements.popper),v,D,f),O=L(e.elements.reference),B=ge({reference:O,element:N,strategy:"absolute",placement:a}),T=me(Object.assign({},N,B)),R=E===h?T:O,I={top:S.top-R.top+C.top,bottom:R.bottom-S.bottom+C.bottom,left:S.left-R.left+C.left,right:R.right-S.right+C.right},z=e.modifiersData.offset;if(E===h&&z){var P=z[a];Object.keys(I).forEach((function(e){var t=[u,i].indexOf(e)>=0?1:-1,n=[r,i].indexOf(e)>=0?"y":"x";I[e]+=P[n]*t}))}return I}const ye={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,f=e.name;if(!t.modifiersData[f]._skip){for(var l=n.mainAxis,p=void 0===l||l,h=n.altAxis,m=void 0===h||h,g=n.fallbackPlacements,D=n.padding,y=n.boundary,E=n.rootBoundary,w=n.altBoundary,b=n.flipVariations,x=void 0===b||b,A=n.allowedAutoPlacements,C=t.options.placement,F=T(C),N=g||(F===C||!x?[oe(C)]:function(e){if(T(e)===a)return[];var t=oe(e);return[se(e),t,se(t)]}(C)),_=[C].concat(N).reduce((function(e,n){return e.concat(T(n)===a?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=n.boundary,u=n.rootBoundary,o=n.padding,a=n.flipVariations,c=n.allowedAutoPlacements,f=void 0===c?v:c,l=K(r),p=l?a?d:d.filter((function(e){return K(e)===l})):s,h=p.filter((function(e){return f.indexOf(e)>=0}));0===h.length&&(h=p);var m=h.reduce((function(t,n){return t[n]=De(e,{placement:n,boundary:i,rootBoundary:u,padding:o})[T(n)],t}),{});return Object.keys(m).sort((function(e,t){return m[e]-m[t]}))}(t,{placement:n,boundary:y,rootBoundary:E,padding:D,flipVariations:x,allowedAutoPlacements:A}):n)}),[]),M=t.rects.reference,S=t.rects.popper,O=new Map,B=!0,R=_[0],I=0;I<_.length;I++){var z=_[I],P=T(z),U=K(z)===c,L=[r,i].indexOf(P)>=0,j=L?"width":"height",k=De(t,{placement:z,boundary:y,rootBoundary:E,altBoundary:w,padding:D}),q=L?U?u:o:U?i:r;M[j]>S[j]&&(q=oe(q));var H=oe(q),G=[];if(p&&G.push(k[P]<=0),m&&G.push(k[q]<=0,k[H]<=0),G.every((function(e){return e}))){R=z,B=!1;break}O.set(z,G)}if(B)for(var V=function(e){var t=_.find((function(t){var n=O.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return R=t,"break"},Y=x?3:1;Y>0;Y--){if("break"===V(Y))break}t.placement!==R&&(t.modifiersData[f]._skip=!0,t.placement=R,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Ee(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function we(e){return[r,u,i,o].some((function(t){return e[t]>=0}))}const be={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,u=t.modifiersData.preventOverflow,o=De(t,{elementContext:"reference"}),a=De(t,{altBoundary:!0}),s=Ee(o,r),c=Ee(a,i,u),f=we(s),l=we(c);t.modifiersData[n]={referenceClippingOffsets:s,popperEscapeOffsets:c,isReferenceHidden:f,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":l})}};const xe={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,i=e.name,a=n.offset,s=void 0===a?[0,0]:a,c=v.reduce((function(e,n){return e[n]=function(e,t,n){var i=T(e),a=[o,r].indexOf(i)>=0?-1:1,s="function"==typeof n?n(Object.assign({},t,{placement:e})):n,c=s[0],f=s[1];return c=c||0,f=(f||0)*a,[o,u].indexOf(i)>=0?{x:f,y:c}:{x:c,y:f}}(n,t.rects,s),e}),{}),f=c[t.placement],l=f.x,p=f.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=p),t.modifiersData[i]=c}};const Ae={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=ge({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}};const Ce={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,a=e.name,s=n.mainAxis,f=void 0===s||s,l=n.altAxis,p=void 0!==l&&l,h=n.boundary,m=n.rootBoundary,d=n.altBoundary,v=n.padding,g=n.tether,D=void 0===g||g,y=n.tetherOffset,E=void 0===y?0:y,w=De(t,{boundary:h,rootBoundary:m,padding:v,altBoundary:d}),b=T(t.placement),x=K(t.placement),A=!x,C=W(b),F="x"===C?"y":"x",N=t.modifiersData.popperOffsets,_=t.rects.reference,M=t.rects.popper,S="function"==typeof E?E(Object.assign({},t.rects,{placement:t.placement})):E,O="number"==typeof S?{mainAxis:S,altAxis:S}:Object.assign({mainAxis:0,altAxis:0},S),B=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,z={x:0,y:0};if(N){if(f){var P,U="y"===C?r:o,L="y"===C?i:u,k="y"===C?"height":"width",q=N[C],H=q+w[U],G=q-w[L],V=D?-M[k]/2:0,Y=x===c?_[k]:M[k],J=x===c?-M[k]:-_[k],Q=t.elements.arrow,X=D&&Q?j(Q):{width:0,height:0},ee=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},te=ee[U],ne=ee[L],re=Z(0,_[k],X[k]),ie=A?_[k]/2-V-re-te-O.mainAxis:Y-re-te-O.mainAxis,ue=A?-_[k]/2+V+re+ne+O.mainAxis:J+re+ne+O.mainAxis,oe=t.elements.arrow&&$(t.elements.arrow),ae=oe?"y"===C?oe.clientTop||0:oe.clientLeft||0:0,se=null!=(P=null==B?void 0:B[C])?P:0,ce=q+ue-se,fe=Z(D?I(H,q+ie-se-ae):H,q,D?R(G,ce):G);N[C]=fe,z[C]=fe-q}if(p){var le,pe="x"===C?r:o,he="x"===C?i:u,me=N[F],de="y"===F?"height":"width",ve=me+w[pe],ge=me-w[he],ye=-1!==[r,o].indexOf(b),Ee=null!=(le=null==B?void 0:B[F])?le:0,we=ye?ve:me-_[de]-M[de]-Ee+O.altAxis,be=ye?me+_[de]+M[de]-Ee-O.altAxis:ge,xe=D&&ye?function(e,t,n){var r=Z(e,t,n);return r>n?n:r}(we,me,be):Z(D?we:ve,me,D?be:ge);N[F]=xe,z[F]=xe-me}t.modifiersData[a]=z}},requiresIfExists:["offset"]};function Fe(e,t,n){void 0===n&&(n=!1);var r,i,u=S(t),o=S(t)&&function(e){var t=e.getBoundingClientRect(),n=z(t.width)/e.offsetWidth||1,r=z(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),a=G(t),s=L(e,o,n),c={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(u||!u&&!n)&&(("body"!==N(t)||le(a))&&(c=(r=t)!==_(r)&&S(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:ce(r)),S(t)?((f=L(t,!0)).x+=t.clientLeft,f.y+=t.clientTop):a&&(f.x=fe(a))),{x:s.left+c.scrollLeft-f.x,y:s.top+c.scrollTop-f.y,width:s.width,height:s.height}}function Ne(e){var t=new Map,n=new Set,r=[];function i(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&i(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||i(e)})),r}var _e={placement:"bottom",modifiers:[],strategy:"absolute"};function Me(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function Se(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,i=t.defaultOptions,u=void 0===i?_e:i;return function(e,t,n){void 0===n&&(n=u);var i,o,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},_e,u),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},s=[],c=!1,f={state:a,setOptions:function(n){var i="function"==typeof n?n(a.options):n;l(),a.options=Object.assign({},u,a.options,i),a.scrollParents={reference:M(e)?he(e):e.contextElement?he(e.contextElement):[],popper:he(t)};var o,c,p=function(e){var t=Ne(e);return F.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((o=[].concat(r,a.options.modifiers),c=o.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(c).map((function(e){return c[e]}))));return a.orderedModifiers=p.filter((function(e){return e.enabled})),a.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,i=e.effect;if("function"==typeof i){var u=i({state:a,name:t,instance:f,options:r}),o=function(){};s.push(u||o)}})),f.update()},forceUpdate:function(){if(!c){var e=a.elements,t=e.reference,n=e.popper;if(Me(t,n)){a.rects={reference:Fe(t,$(n),"fixed"===a.options.strategy),popper:j(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(e){return a.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<a.orderedModifiers.length;r++)if(!0!==a.reset){var i=a.orderedModifiers[r],u=i.fn,o=i.options,s=void 0===o?{}:o,l=i.name;"function"==typeof u&&(a=u({state:a,options:s,name:l,instance:f})||a)}else a.reset=!1,r=-1}}},update:(i=function(){return new Promise((function(e){f.forceUpdate(),e(a)}))},function(){return o||(o=new Promise((function(e){Promise.resolve().then((function(){o=void 0,e(i())}))}))),o}),destroy:function(){l(),c=!0}};if(!Me(e,t))return f;function l(){s.forEach((function(e){return e()})),s=[]}return f.setOptions(n).then((function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)})),f}}var Oe=Se(),Be=Se({defaultModifiers:[ie,Ae,ne,B,xe,ye,Ce,X,be]}),Te=Se({defaultModifiers:[ie,Ae,ne,B]})},1234:()=>{},1377:function(e){!function(){"use strict";var t={s:1,n:0,d:1};function n(e,t){if(isNaN(e=parseInt(e,10)))throw c();return e*t}function r(e,t){if(0===t)throw s();var n=Object.create(a.prototype);n.s=e<0?-1:1;var r=o(e=e<0?-e:e,t);return n.n=e/r,n.d=t/r,n}function i(e){for(var t={},n=e,r=2,i=4;i<=n;){for(;n%r==0;)n/=r,t[r]=(t[r]||0)+1;i+=1+2*r++}return n!==e?n>1&&(t[n]=(t[n]||0)+1):t[e]=(t[e]||0)+1,t}var u=function(e,r){var i,u=0,o=1,a=1,l=0,p=0,h=0,m=1,d=1,v=0,g=1,D=1,y=1,E=1e7;if(null==e);else if(void 0!==r){if(a=(u=e)*(o=r),u%1!=0||o%1!=0)throw f()}else switch(typeof e){case"object":if("d"in e&&"n"in e)u=e.n,o=e.d,"s"in e&&(u*=e.s);else{if(!(0 in e))throw c();u=e[0],1 in e&&(o=e[1])}a=u*o;break;case"number":if(e<0&&(a=e,e=-e),e%1==0)u=e;else if(e>0){for(e>=1&&(e/=d=Math.pow(10,Math.floor(1+Math.log(e)/Math.LN10)));g<=E&&y<=E;){if(e===(i=(v+D)/(g+y))){g+y<=E?(u=v+D,o=g+y):y>g?(u=D,o=y):(u=v,o=g);break}e>i?(v+=D,g+=y):(D+=v,y+=g),g>E?(u=D,o=y):(u=v,o=g)}u*=d}else(isNaN(e)||isNaN(r))&&(o=u=NaN);break;case"string":if(null===(g=e.match(/\d+|./g)))throw c();if("-"===g[v]?(a=-1,v++):"+"===g[v]&&v++,g.length===v+1?p=n(g[v++],a):"."===g[v+1]||"."===g[v]?("."!==g[v]&&(l=n(g[v++],a)),(++v+1===g.length||"("===g[v+1]&&")"===g[v+3]||"'"===g[v+1]&&"'"===g[v+3])&&(p=n(g[v],a),m=Math.pow(10,g[v].length),v++),("("===g[v]&&")"===g[v+2]||"'"===g[v]&&"'"===g[v+2])&&(h=n(g[v+1],a),d=Math.pow(10,g[v+1].length)-1,v+=3)):"/"===g[v+1]||":"===g[v+1]?(p=n(g[v],a),m=n(g[v+2],1),v+=3):"/"===g[v+3]&&" "===g[v+1]&&(l=n(g[v],a),p=n(g[v+2],a),m=n(g[v+4],1),v+=5),g.length<=v){a=u=h+(o=m*d)*l+d*p;break}default:throw c()}if(0===o)throw s();t.s=a<0?-1:1,t.n=Math.abs(u),t.d=Math.abs(o)};function o(e,t){if(!e)return t;if(!t)return e;for(;;){if(!(e%=t))return t;if(!(t%=e))return e}}function a(e,n){if(u(e,n),!(this instanceof a))return r(t.s*t.n,t.d);e=o(t.d,t.n),this.s=t.s,this.n=t.n/e,this.d=t.d/e}var s=function(){return new Error("Division by Zero")},c=function(){return new Error("Invalid argument")},f=function(){return new Error("Parameters must be integer")};a.prototype={s:1,n:0,d:1,abs:function(){return r(this.n,this.d)},neg:function(){return r(-this.s*this.n,this.d)},add:function(e,n){return u(e,n),r(this.s*this.n*t.d+t.s*this.d*t.n,this.d*t.d)},sub:function(e,n){return u(e,n),r(this.s*this.n*t.d-t.s*this.d*t.n,this.d*t.d)},mul:function(e,n){return u(e,n),r(this.s*t.s*this.n*t.n,this.d*t.d)},div:function(e,n){return u(e,n),r(this.s*t.s*this.n*t.d,this.d*t.n)},clone:function(){return r(this.s*this.n,this.d)},mod:function(e,n){if(isNaN(this.n)||isNaN(this.d))return new a(NaN);if(void 0===e)return r(this.s*this.n%this.d,1);if(u(e,n),0===t.n&&0===this.d)throw s();return r(this.s*(t.d*this.n)%(t.n*this.d),t.d*this.d)},gcd:function(e,n){return u(e,n),r(o(t.n,this.n)*o(t.d,this.d),t.d*this.d)},lcm:function(e,n){return u(e,n),0===t.n&&0===this.n?r(0,1):r(t.n*this.n,o(t.n,this.n)*o(t.d,this.d))},ceil:function(e){return e=Math.pow(10,e||0),isNaN(this.n)||isNaN(this.d)?new a(NaN):r(Math.ceil(e*this.s*this.n/this.d),e)},floor:function(e){return e=Math.pow(10,e||0),isNaN(this.n)||isNaN(this.d)?new a(NaN):r(Math.floor(e*this.s*this.n/this.d),e)},round:function(e){return e=Math.pow(10,e||0),isNaN(this.n)||isNaN(this.d)?new a(NaN):r(Math.round(e*this.s*this.n/this.d),e)},inverse:function(){return r(this.s*this.d,this.n)},pow:function(e,n){if(u(e,n),1===t.d)return t.s<0?r(Math.pow(this.s*this.d,t.n),Math.pow(this.n,t.n)):r(Math.pow(this.s*this.n,t.n),Math.pow(this.d,t.n));if(this.s<0)return null;var o=i(this.n),a=i(this.d),s=1,c=1;for(var f in o)if("1"!==f){if("0"===f){s=0;break}if(o[f]*=t.n,o[f]%t.d!=0)return null;o[f]/=t.d,s*=Math.pow(f,o[f])}for(var f in a)if("1"!==f){if(a[f]*=t.n,a[f]%t.d!=0)return null;a[f]/=t.d,c*=Math.pow(f,a[f])}return t.s<0?r(c,s):r(s,c)},equals:function(e,n){return u(e,n),this.s*this.n*t.d==t.s*t.n*this.d},compare:function(e,n){u(e,n);var r=this.s*this.n*t.d-t.s*t.n*this.d;return(0<r)-(r<0)},simplify:function(e){if(isNaN(this.n)||isNaN(this.d))return this;e=e||.001;for(var t=this.abs(),n=t.toContinued(),i=1;i<n.length;i++){for(var u=r(n[i-1],1),o=i-2;o>=0;o--)u=u.inverse().add(n[o]);if(Math.abs(u.sub(t).valueOf())<e)return u.mul(this.s)}return this},divisible:function(e,n){return u(e,n),!(!(t.n*this.d)||this.n*t.d%(t.n*this.d))},valueOf:function(){return this.s*this.n/this.d},toFraction:function(e){var t,n="",r=this.n,i=this.d;return this.s<0&&(n+="-"),1===i?n+=r:(e&&(t=Math.floor(r/i))>0&&(n+=t,n+=" ",r%=i),n+=r,n+="/",n+=i),n},toLatex:function(e){var t,n="",r=this.n,i=this.d;return this.s<0&&(n+="-"),1===i?n+=r:(e&&(t=Math.floor(r/i))>0&&(n+=t,r%=i),n+="\\frac{",n+=r,n+="}{",n+=i,n+="}"),n},toContinued:function(){var e,t=this.n,n=this.d,r=[];if(isNaN(t)||isNaN(n))return r;do{r.push(Math.floor(t/n)),e=t%n,t=n,n=e}while(1!==t);return r},toString:function(e){var t=this.n,n=this.d;if(isNaN(t)||isNaN(n))return"NaN";e=e||15;var r=function(e,t){for(;t%2==0;t/=2);for(;t%5==0;t/=5);if(1===t)return 0;for(var n=10%t,r=1;1!==n;r++)if(n=10*n%t,r>2e3)return 0;return r}(0,n),i=function(e,t,n){for(var r=1,i=function(e,t,n){for(var r=1;t>0;e=e*e%n,t>>=1)1&t&&(r=r*e%n);return r}(10,n,t),u=0;u<300;u++){if(r===i)return u;r=10*r%t,i=10*i%t}return 0}(0,n,r),u=this.s<0?"-":"";if(u+=t/n|0,t%=n,(t*=10)&&(u+="."),r){for(var o=i;o--;)u+=t/n|0,t%=n,t*=10;u+="(";for(o=r;o--;)u+=t/n|0,t%=n,t*=10;u+=")"}else for(o=e;t&&o--;)u+=t/n|0,t%=n,t*=10;return u}},Object.defineProperty(a,"__esModule",{value:!0}),a.default=a,a.Fraction=a,e.exports=a}()},1669:e=>{"use strict";e.exports=jQuery},1880:e=>{e.exports=function e(t,n){"use strict";var r,i,u=/(^([+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,o=/(^[ ]*|[ ]*$)/g,a=/(^([\w ]+,?[\w ]+)?[\w ]+,?[\w ]+\d+:\d+(:\d+)?[\w ]?|^\d{1,4}[\/\-]\d{1,4}[\/\-]\d{1,4}|^\w+, \w+ \d+, \d{4})/,s=/^0x[0-9a-f]+$/i,c=/^0/,f=function(t){return e.insensitive&&(""+t).toLowerCase()||""+t},l=f(t).replace(o,"")||"",p=f(n).replace(o,"")||"",h=l.replace(u,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),m=p.replace(u,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),d=parseInt(l.match(s),16)||1!==h.length&&l.match(a)&&Date.parse(l),v=parseInt(p.match(s),16)||d&&p.match(a)&&Date.parse(p)||null;if(v){if(d<v)return-1;if(d>v)return 1}for(var g=0,D=Math.max(h.length,m.length);g<D;g++){if(r=!(h[g]||"").match(c)&&parseFloat(h[g])||h[g]||0,i=!(m[g]||"").match(c)&&parseFloat(m[g])||m[g]||0,isNaN(r)!==isNaN(i))return isNaN(r)?1:-1;if(typeof r!=typeof i&&(r+="",i+=""),r<i)return-1;if(r>i)return 1}return 0}},2105:function(e,t,n){e.exports=function(e,t){"use strict";class n{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(n,r){const i=t.isElement(r)?e.getDataAttribute(r,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...t.isElement(r)?e.getDataAttributes(r):{},..."object"==typeof n?n:{}}}_typeCheckConfig(e,n=this.constructor.DefaultType){for(const[r,i]of Object.entries(n)){const n=e[r],u=t.isElement(n)?"element":t.toType(n);if(!new RegExp(i).test(u))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${r}" provided type "${u}" but expected type "${i}".`)}}}return n}(n(2333),n(4035))},2333:function(e){e.exports=function(){"use strict";function e(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function t(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}return{setDataAttribute(e,n,r){e.setAttribute(`data-bs-${t(n)}`,r)},removeDataAttribute(e,n){e.removeAttribute(`data-bs-${t(n)}`)},getDataAttributes(t){if(!t)return{};const n={},r=Object.keys(t.dataset).filter((e=>e.startsWith("bs")&&!e.startsWith("bsConfig")));for(const i of r){let r=i.replace(/^bs/,"");r=r.charAt(0).toLowerCase()+r.slice(1),n[r]=e(t.dataset[i])}return n},getDataAttribute:(n,r)=>e(n.getAttribute(`data-bs-${t(r)}`))}}()},2369:function(e){e.exports=function(){"use strict";function e(){return!0}function t(){return!1}function n(){}const r="Argument is not a typed-function.";function i(){function u(e){return"object"==typeof e&&null!==e&&e.constructor===Object}const o=[{name:"number",test:function(e){return"number"==typeof e}},{name:"string",test:function(e){return"string"==typeof e}},{name:"boolean",test:function(e){return"boolean"==typeof e}},{name:"Function",test:function(e){return"function"==typeof e}},{name:"Array",test:Array.isArray},{name:"Date",test:function(e){return e instanceof Date}},{name:"RegExp",test:function(e){return e instanceof RegExp}},{name:"Object",test:u},{name:"null",test:function(e){return null===e}},{name:"undefined",test:function(e){return void 0===e}}],a={name:"any",test:e,isAny:!0};let s,c,f=0,l={createCount:0};function p(e){const t=s.get(e);if(t)return t;let n='Unknown type "'+e+'"';const r=e.toLowerCase();let i;for(i of c)if(i.toLowerCase()===r){n+='. Did you mean "'+i+'" ?';break}throw new TypeError(n)}function h(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"any";const n=t?p(t).index:c.length,r=[];for(let t=0;t<e.length;++t){if(!e[t]||"string"!=typeof e[t].name||"function"!=typeof e[t].test)throw new TypeError("Object with properties {name: string, test: function} expected");const i=e[t].name;if(s.has(i))throw new TypeError('Duplicate type name "'+i+'"');r.push(i),s.set(i,{name:i,test:e[t].test,isAny:e[t].isAny,index:n+t,conversionsTo:[]})}const i=c.slice(n);c=c.slice(0,n).concat(r).concat(i);for(let e=n+r.length;e<c.length;++e)s.get(c[e]).index=e}function m(){s=new Map,c=[],f=0,h([a],!1)}function d(){let e;for(e of c)s.get(e).conversionsTo=[];f=0}function v(e){const t=c.filter((t=>{const n=s.get(t);return!n.isAny&&n.test(e)}));return t.length?t:["any"]}function g(e){return e&&"function"==typeof e&&"_typedFunctionData"in e}function D(e,t,n){if(!g(e))throw new TypeError(r);const i=n&&n.exact,u=C(Array.isArray(t)?t.join(","):t),o=w(u);if(!i||o in e.signatures){const t=e._typedFunctionData.signatureMap.get(o);if(t)return t}const a=u.length;let s,c;if(i){let t;for(t in s=[],e.signatures)s.push(e._typedFunctionData.signatureMap.get(t))}else s=e._typedFunctionData.signatures;for(let e=0;e<a;++e){const t=u[e],n=[];let r;for(r of s){const i=M(r.params,e);if(i&&(!t.restParam||i.restParam)){if(!i.hasAny){const e=A(i);if(t.types.some((t=>!e.has(t.name))))continue}n.push(r)}}if(s=n,0===s.length)break}for(c of s)if(c.params.length<=a)return c;throw new TypeError("Signature not found (signature: "+(e.name||"unnamed")+"("+w(u,", ")+"))")}function y(e,t,n){return D(e,t,n).implementation}function E(e,t){const n=p(t);if(n.test(e))return e;const r=n.conversionsTo;if(0===r.length)throw new Error("There are no conversions to "+t+" defined.");for(let t=0;t<r.length;t++)if(p(r[t].from).test(e))return r[t].convert(e);throw new Error("Cannot convert "+e+" to "+t)}function w(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:",";return e.map((e=>e.name)).join(t)}function b(e){const t=0===e.indexOf("..."),n=(t?e.length>3?e.slice(3):"any":e).split("|").map((e=>p(e.trim())));let r=!1,i=t?"...":"";return{types:n.map((function(e){return r=e.isAny||r,i+=e.name+"|",{name:e.name,typeIndex:e.index,test:e.test,isAny:e.isAny,conversion:null,conversionIndex:-1}})),name:i.slice(0,-1),hasAny:r,hasConversion:!1,restParam:t}}function x(e){const t=U(e.types.map((e=>e.name)));let n=e.hasAny,r=e.name;const i=t.map((function(e){const t=p(e.from);return n=t.isAny||n,r+="|"+e.from,{name:e.from,typeIndex:t.index,test:t.test,isAny:t.isAny,conversion:e,conversionIndex:e.index}}));return{types:e.types.concat(i),name:r,hasAny:n,hasConversion:i.length>0,restParam:e.restParam}}function A(e){return e.typeSet||(e.typeSet=new Set,e.types.forEach((t=>e.typeSet.add(t.name)))),e.typeSet}function C(e){const t=[];if("string"!=typeof e)throw new TypeError("Signatures must be strings");const n=e.trim();if(""===n)return t;const r=n.split(",");for(let e=0;e<r.length;++e){const n=b(r[e].trim());if(n.restParam&&e!==r.length-1)throw new SyntaxError('Unexpected rest parameter "'+r[e]+'": only allowed for the last parameter');if(0===n.types.length)return null;t.push(n)}return t}function F(e){const t=J(e);return!!t&&t.restParam}function N(t){if(t&&0!==t.types.length){if(1===t.types.length)return p(t.types[0].name).test;if(2===t.types.length){const e=p(t.types[0].name).test,n=p(t.types[1].name).test;return function(t){return e(t)||n(t)}}{const e=t.types.map((function(e){return p(e.name).test}));return function(t){for(let n=0;n<e.length;n++)if(e[n](t))return!0;return!1}}}return e}function _(e){let t,n,r;if(F(e)){t=Z(e).map(N);const n=t.length,r=N(J(e)),i=function(e){for(let t=n;t<e.length;t++)if(!r(e[t]))return!1;return!0};return function(e){for(let n=0;n<t.length;n++)if(!t[n](e[n]))return!1;return i(e)&&e.length>=n+1}}return 0===e.length?function(e){return 0===e.length}:1===e.length?(n=N(e[0]),function(e){return n(e[0])&&1===e.length}):2===e.length?(n=N(e[0]),r=N(e[1]),function(e){return n(e[0])&&r(e[1])&&2===e.length}):(t=e.map(N),function(e){for(let n=0;n<t.length;n++)if(!t[n](e[n]))return!1;return e.length===t.length})}function M(e,t){return t<e.length?e[t]:F(e)?J(e):null}function S(e,t){const n=M(e,t);return n?A(n):new Set}function O(e){return null===e.conversion||void 0===e.conversion}function B(e,t){const n=new Set;return e.forEach((e=>{const r=S(e.params,t);let i;for(i of r)n.add(i)})),n.has("any")?["any"]:Array.from(n)}function T(e,t,n){let r,i;const u=e||"unnamed";let o,a=n;for(o=0;o<t.length;o++){const e=[];if(a.forEach((n=>{const r=N(M(n.params,o));(o<n.params.length||F(n.params))&&r(t[o])&&e.push(n)})),0===e.length){if(i=B(a,o),i.length>0){const e=v(t[o]);return r=new TypeError("Unexpected type of argument in function "+u+" (expected: "+i.join(" or ")+", actual: "+e.join(" | ")+", index: "+o+")"),r.data={category:"wrongType",fn:u,index:o,actual:e,expected:i},r}}else a=e}const s=a.map((function(e){return F(e.params)?1/0:e.params.length}));if(t.length<Math.min.apply(null,s))return i=B(a,o),r=new TypeError("Too few arguments in function "+u+" (expected: "+i.join(" or ")+", index: "+t.length+")"),r.data={category:"tooFewArgs",fn:u,index:t.length,expected:i},r;const c=Math.max.apply(null,s);if(t.length>c)return r=new TypeError("Too many arguments in function "+u+" (expected: "+c+", actual: "+t.length+")"),r.data={category:"tooManyArgs",fn:u,index:t.length,expectedLength:c},r;const f=[];for(let e=0;e<t.length;++e)f.push(v(t[e]).join("|"));return r=new TypeError('Arguments of type "'+f.join(", ")+'" do not match any of the defined signatures of function '+u+"."),r.data={category:"mismatch",actual:f},r}function R(e){let t=c.length+1;for(let n=0;n<e.types.length;n++)O(e.types[n])&&(t=Math.min(t,e.types[n].typeIndex));return t}function I(e){let t=f+1;for(let n=0;n<e.types.length;n++)O(e.types[n])||(t=Math.min(t,e.types[n].conversionIndex));return t}function z(e,t){if(e.hasAny){if(!t.hasAny)return 1}else if(t.hasAny)return-1;if(e.restParam){if(!t.restParam)return 1}else if(t.restParam)return-1;if(e.hasConversion){if(!t.hasConversion)return 1}else if(t.hasConversion)return-1;const n=R(e)-R(t);if(n<0)return-1;if(n>0)return 1;const r=I(e)-I(t);return r<0?-1:r>0?1:0}function P(e,t){const n=e.params,r=t.params,i=J(n),u=J(r),o=F(n),a=F(r);if(o&&i.hasAny){if(!a||!u.hasAny)return 1}else if(a&&u.hasAny)return-1;let s,c=0,f=0;for(s of n)s.hasAny&&++c,s.hasConversion&&++f;let l=0,p=0;for(s of r)s.hasAny&&++l,s.hasConversion&&++p;if(c!==l)return c-l;if(o&&i.hasConversion){if(!a||!u.hasConversion)return 1}else if(a&&u.hasConversion)return-1;if(f!==p)return f-p;if(o){if(!a)return 1}else if(a)return-1;const h=(n.length-r.length)*(o?-1:1);if(0!==h)return h;const m=[];let d,v=0;for(let e=0;e<n.length;++e){const t=z(n[e],r[e]);m.push(t),v+=t}if(0!==v)return v;for(d of m)if(0!==d)return d;return 0}function U(e){if(0===e.length)return[];const t=e.map(p);e.length>1&&t.sort(((e,t)=>e.index-t.index));let n=t[0].conversionsTo;if(1===e.length)return n;n=n.concat([]);const r=new Set(e);for(let e=1;e<t.length;++e){let i;for(i of t[e].conversionsTo)r.has(i.from)||(n.push(i),r.add(i.from))}return n}function L(e,t){let n=t;if(e.some((e=>e.hasConversion))){const r=F(e),i=e.map(j);n=function(){const e=[],n=r?arguments.length-1:arguments.length;for(let t=0;t<n;t++)e[t]=i[t](arguments[t]);return r&&(e[n]=arguments[n].map(i[n])),t.apply(this,e)}}let r=n;if(F(e)){const t=e.length-1;r=function(){return n.apply(this,Q(arguments,0,t).concat([Q(arguments,t)]))}}return r}function j(e){let t,n,r,i;const u=[],o=[];switch(e.types.forEach((function(e){e.conversion&&(u.push(p(e.conversion.from).test),o.push(e.conversion.convert))})),o.length){case 0:return function(e){return e};case 1:return t=u[0],r=o[0],function(e){return t(e)?r(e):e};case 2:return t=u[0],n=u[1],r=o[0],i=o[1],function(e){return t(e)?r(e):n(e)?i(e):e};default:return function(e){for(let t=0;t<o.length;t++)if(u[t](e))return o[t](e);return e}}}function k(e){function t(e,n,r){if(n<e.length){const i=e[n];let u=[];if(i.restParam){const e=i.types.filter(O);e.length<i.types.length&&u.push({types:e,name:"..."+e.map((e=>e.name)).join("|"),hasAny:e.some((e=>e.isAny)),hasConversion:!1,restParam:!0}),u.push(i)}else u=i.types.map((function(e){return{types:[e],name:e.name,hasAny:e.isAny,hasConversion:e.conversion,restParam:!1}}));return K(u,(function(i){return t(e,n+1,r.concat([i]))}))}return[r]}return t(e,0,[])}function q(e,t){const n=Math.max(e.length,t.length);for(let r=0;r<n;r++){const n=S(e,r),i=S(t,r);let u,o=!1;for(u of i)if(n.has(u)){o=!0;break}if(!o)return!1}const r=e.length,i=t.length,u=F(e),o=F(t);return u?o?r===i:i>=r:o?r>=i:r===i}function H(e){return e.map((e=>ie(e)?ne(e.referToSelf.callback):re(e)?te(e.referTo.references,e.referTo.callback):e))}function G(e,t,n){const r=[];let i;for(i of e){let e=n[i];if("number"!=typeof e)throw new TypeError('No definition for referenced signature "'+i+'"');if(e=t[e],"function"!=typeof e)return!1;r.push(e)}return r}function V(e,t,n){const r=H(e),i=new Array(r.length).fill(!1);let u=!0;for(;u;){u=!1;let e=!0;for(let o=0;o<r.length;++o){if(i[o])continue;const a=r[o];if(ie(a))r[o]=a.referToSelf.callback(n),r[o].referToSelf=a.referToSelf,i[o]=!0,e=!1;else if(re(a)){const n=G(a.referTo.references,r,t);n?(r[o]=a.referTo.callback.apply(this,n),r[o].referTo=a.referTo,i[o]=!0,e=!1):u=!0}}if(e&&u)throw new SyntaxError("Circular reference detected in resolving typed.referTo")}return r}function Y(e){const t=/\bthis(\(|\.signatures\b)/;Object.keys(e).forEach((n=>{const r=e[n];if(t.test(r.toString()))throw new SyntaxError("Using `this` to self-reference a function is deprecated since typed-function@3. Use typed.referTo and typed.referToSelf instead.")}))}function $(e,r){if(l.createCount++,0===Object.keys(r).length)throw new SyntaxError("No signatures provided");l.warnAgainstDeprecatedThis&&Y(r);const i=[],u=[],o={},a=[];let s;for(s in r){if(!Object.prototype.hasOwnProperty.call(r,s))continue;const e=C(s);if(!e)continue;i.forEach((function(t){if(q(t,e))throw new TypeError('Conflicting signatures "'+w(t)+'" and "'+w(e)+'".')})),i.push(e);const t=u.length;u.push(r[s]);const n=e.map(x);let c;for(c of k(n)){const e=w(c);a.push({params:c,name:e,fn:t}),c.every((e=>!e.hasConversion))&&(o[e]=t)}}a.sort(P);const c=V(u,o,se);let f;for(f in o)Object.prototype.hasOwnProperty.call(o,f)&&(o[f]=c[o[f]]);const p=[],h=new Map;for(f of a)h.has(f.name)||(f.fn=c[f.fn],p.push(f),h.set(f.name,f));const m=p[0]&&p[0].params.length<=2&&!F(p[0].params),d=p[1]&&p[1].params.length<=2&&!F(p[1].params),v=p[2]&&p[2].params.length<=2&&!F(p[2].params),g=p[3]&&p[3].params.length<=2&&!F(p[3].params),D=p[4]&&p[4].params.length<=2&&!F(p[4].params),y=p[5]&&p[5].params.length<=2&&!F(p[5].params),E=m&&d&&v&&g&&D&&y;for(let e=0;e<p.length;++e)p[e].test=_(p[e].params);const b=m?N(p[0].params[0]):t,A=d?N(p[1].params[0]):t,M=v?N(p[2].params[0]):t,S=g?N(p[3].params[0]):t,O=D?N(p[4].params[0]):t,B=y?N(p[5].params[0]):t,T=m?N(p[0].params[1]):t,R=d?N(p[1].params[1]):t,I=v?N(p[2].params[1]):t,z=g?N(p[3].params[1]):t,U=D?N(p[4].params[1]):t,j=y?N(p[5].params[1]):t;for(let e=0;e<p.length;++e)p[e].implementation=L(p[e].params,p[e].fn);const H=m?p[0].implementation:n,G=d?p[1].implementation:n,$=v?p[2].implementation:n,W=g?p[3].implementation:n,Z=D?p[4].implementation:n,J=y?p[5].implementation:n,Q=m?p[0].params.length:-1,X=d?p[1].params.length:-1,K=v?p[2].params.length:-1,ee=g?p[3].params.length:-1,te=D?p[4].params.length:-1,ne=y?p[5].params.length:-1,re=E?6:0,ie=p.length,ue=p.map((e=>e.test)),oe=p.map((e=>e.implementation)),ae=function(){for(let e=re;e<ie;e++)if(ue[e](arguments))return oe[e].apply(this,arguments);return l.onMismatch(e,arguments,p)};function se(e,t){return arguments.length===Q&&b(e)&&T(t)?H.apply(this,arguments):arguments.length===X&&A(e)&&R(t)?G.apply(this,arguments):arguments.length===K&&M(e)&&I(t)?$.apply(this,arguments):arguments.length===ee&&S(e)&&z(t)?W.apply(this,arguments):arguments.length===te&&O(e)&&U(t)?Z.apply(this,arguments):arguments.length===ne&&B(e)&&j(t)?J.apply(this,arguments):ae.apply(this,arguments)}try{Object.defineProperty(se,"name",{value:e})}catch(e){}return se.signatures=o,se._typedFunctionData={signatures:p,signatureMap:h},se}function W(e,t,n){throw T(e,t,n)}function Z(e){return Q(e,0,e.length-1)}function J(e){return e[e.length-1]}function Q(e,t,n){return Array.prototype.slice.call(e,t,n)}function X(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return e[n]}function K(e,t){return Array.prototype.concat.apply([],e.map(t))}function ee(){const e=Z(arguments).map((e=>w(C(e)))),t=J(arguments);if("function"!=typeof t)throw new TypeError("Callback function expected as last argument");return te(e,t)}function te(e,t){return{referTo:{references:e,callback:t}}}function ne(e){if("function"!=typeof e)throw new TypeError("Callback function expected as first argument");return{referToSelf:{callback:e}}}function re(e){return e&&"object"==typeof e.referTo&&Array.isArray(e.referTo.references)&&"function"==typeof e.referTo.callback}function ie(e){return e&&"object"==typeof e.referToSelf&&"function"==typeof e.referToSelf.callback}function ue(e,t){if(!e)return t;if(t&&t!==e){const n=new Error("Function names do not match (expected: "+e+", actual: "+t+")");throw n.data={actual:t,expected:e},n}return e}function oe(e){let t;for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(g(e[n])||"string"==typeof e[n].signature)&&(t=ue(t,e[n].name));return t}function ae(e,t){let n;for(n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(n in e&&t[n]!==e[n]){const r=new Error('Signature "'+n+'" is defined twice');throw r.data={signature:n,sourceFunction:t[n],destFunction:e[n]},r}e[n]=t[n]}}m(),h(o);const se=l;function ce(e){if(!e||"string"!=typeof e.from||"string"!=typeof e.to||"function"!=typeof e.convert)throw new TypeError("Object with properties {from: string, to: string, convert: function} expected");if(e.to===e.from)throw new SyntaxError('Illegal to define conversion from "'+e.from+'" to itself.')}return l=function(e){const t="string"==typeof e;let n=t?e:"";const r={};for(let e=t?1:0;e<arguments.length;++e){const i=arguments[e];let o,a={};if("function"==typeof i?(o=i.name,"string"==typeof i.signature?a[i.signature]=i:g(i)&&(a=i.signatures)):u(i)&&(a=i,t||(o=oe(i))),0===Object.keys(a).length){const t=new TypeError("Argument to 'typed' at index "+e+" is not a (typed) function, nor an object with signatures as keys and functions as values.");throw t.data={index:e,argument:i},t}t||(n=ue(n,o)),ae(r,a)}return $(n||"",r)},l.create=i,l.createCount=se.createCount,l.onMismatch=W,l.throwMismatchError=W,l.createError=T,l.clear=m,l.clearConversions=d,l.addTypes=h,l._findType=p,l.referTo=ee,l.referToSelf=ne,l.convert=E,l.findSignature=D,l.find=y,l.isTypedFunction=g,l.warnAgainstDeprecatedThis=!0,l.addType=function(e,t){let n="any";!1!==t&&s.has("Object")&&(n="Object"),l.addTypes([e],n)},l.addConversion=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{override:!1};ce(e);const n=p(e.to),r=n.conversionsTo.find((t=>t.from===e.from));if(r){if(!t||!t.override)throw new Error('There is already a conversion from "'+e.from+'" to "'+n.name+'"');l.removeConversion({from:r.from,to:e.to,convert:r.convert})}n.conversionsTo.push({from:e.from,convert:e.convert,index:f++})},l.addConversions=function(e,t){e.forEach((e=>l.addConversion(e,t)))},l.removeConversion=function(e){ce(e);const t=p(e.to),n=X(t.conversionsTo,(t=>t.from===e.from));if(!n)throw new Error("Attempt to remove nonexistent conversion from "+e.from+" to "+e.to);if(n.convert!==e.convert)throw new Error("Conversion to remove does not match existing conversion");const r=t.conversionsTo.indexOf(n);t.conversionsTo.splice(r,1)},l.resolve=function(e,t){if(!g(e))throw new TypeError(r);const n=e._typedFunctionData.signatures;for(let e=0;e<n.length;++e)if(n[e].test(t))return n[e];return null},l}return i()}()},2812:function(e,t){!function(e){"use strict";const t={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},n=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),r=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,i=(e,t)=>{const i=e.nodeName.toLowerCase();return t.includes(i)?!n.has(i)||Boolean(r.test(e.nodeValue)):t.filter((e=>e instanceof RegExp)).some((e=>e.test(i)))};function u(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);const r=(new window.DOMParser).parseFromString(e,"text/html"),u=[].concat(...r.body.querySelectorAll("*"));for(const e of u){const n=e.nodeName.toLowerCase();if(!Object.keys(t).includes(n)){e.remove();continue}const r=[].concat(...e.attributes),u=[].concat(t["*"]||[],t[n]||[]);for(const t of r)i(t,u)||e.removeAttribute(t.nodeName)}return r.body.innerHTML}e.DefaultAllowlist=t,e.sanitizeHtml=u,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(t)},3029:function(e,t,n){e.exports=function(e,t,n,r,i,u,o){"use strict";function a(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e)for(const n in e)if("default"!==n){const r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:()=>e[n]})}return t.default=e,Object.freeze(t)}const s=a(e),c="tooltip",f=new Set(["sanitize","allowList","sanitizeFn"]),l="fade",p="show",h=".tooltip-inner",m=".modal",d="hide.bs.modal",v="hover",g="focus",D="click",y="manual",E="hide",w="hidden",b="show",x="shown",A="inserted",C="click",F="focusin",N="focusout",_="mouseenter",M="mouseleave",S={AUTO:"auto",TOP:"top",RIGHT:i.isRTL()?"left":"right",BOTTOM:"bottom",LEFT:i.isRTL()?"right":"left"},O={allowList:u.DefaultAllowlist,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},B={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class T extends t{constructor(e,t){if(void 0===s)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return O}static get DefaultType(){return B}static get NAME(){return c}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),n.off(this._element.closest(m),d,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=n.trigger(this._element,this.constructor.eventName(b)),t=(i.findShadowRoot(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const r=this._getTipElement();this._element.setAttribute("aria-describedby",r.getAttribute("id"));const{container:u}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(u.append(r),n.trigger(this._element,this.constructor.eventName(A))),this._popper=this._createPopper(r),r.classList.add(p),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))n.on(e,"mouseover",i.noop);const o=()=>{n.trigger(this._element,this.constructor.eventName(x)),!1===this._isHovered&&this._leave(),this._isHovered=!1};this._queueCallback(o,this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(n.trigger(this._element,this.constructor.eventName(E)).defaultPrevented)return;if(this._getTipElement().classList.remove(p),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))n.off(e,"mouseover",i.noop);this._activeTrigger[D]=!1,this._activeTrigger[g]=!1,this._activeTrigger[v]=!1,this._isHovered=null;const e=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),n.trigger(this._element,this.constructor.eventName(w)))};this._queueCallback(e,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(l,p),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=i.getUID(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(l),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new o({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[h]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(l)}_isShown(){return this.tip&&this.tip.classList.contains(p)}_createPopper(e){const t=i.execute(this._config.placement,[this,e,this._element]),n=S[t.toUpperCase()];return s.createPopper(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return i.execute(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...i.execute(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if("click"===t)n.on(this._element,this.constructor.eventName(C),this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger[D]=!(t._isShown()&&t._activeTrigger[D]),t.toggle()}));else if(t!==y){const e=t===v?this.constructor.eventName(_):this.constructor.eventName(F),r=t===v?this.constructor.eventName(M):this.constructor.eventName(N);n.on(this._element,e,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?g:v]=!0,t._enter()})),n.on(this._element,r,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?g:v]=t._element.contains(e.relatedTarget),t._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},n.on(this._element.closest(m),d,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=r.getDataAttributes(this._element);for(const e of Object.keys(t))f.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:i.getElement(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each((function(){const t=T.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}return i.defineJQueryPlugin(T),T}(n(1170),n(9011),n(7956),n(2333),n(4035),n(2812),n(3982))},3031:function(e,t,n){var r;!function(e,i){function u(e){var t=this,n="";t.next=function(){var e=t.x^t.x>>>2;return t.x=t.y,t.y=t.z,t.z=t.w,t.w=t.v,(t.d=t.d+362437|0)+(t.v=t.v^t.v<<4^e^e<<1)|0},t.x=0,t.y=0,t.z=0,t.w=0,t.v=0,e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),r==n.length&&(t.d=t.x<<10^t.x>>>4),t.next()}function o(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t.v=e.v,t.d=e.d,t}function a(e,t){var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&o(r,n),i.state=function(){return o(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.xorwow=a}(0,e=n.nmd(e),n.amdD)},3181:function(e,t,n){var r;!function(e,i){function u(e){var t=this,n="";t.x=0,t.y=0,t.z=0,t.w=0,t.next=function(){var e=t.x^t.x<<11;return t.x=t.y,t.y=t.z,t.z=t.w,t.w^=t.w>>>19^e^e>>>8},e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),t.next()}function o(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t}function a(e,t){var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&o(r,n),i.state=function(){return o(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.xor128=a}(0,e=n.nmd(e),n.amdD)},3717:function(e,t,n){var r;!function(e,i){function u(e){var t=this,n="";t.next=function(){var e=t.b,n=t.c,r=t.d,i=t.a;return e=e<<25^e>>>7^n,n=n-r|0,r=r<<24^r>>>8^i,i=i-e|0,t.b=e=e<<20^e>>>12^n,t.c=n=n-r|0,t.d=r<<16^n>>>16^i,t.a=i-e|0},t.a=0,t.b=0,t.c=-1640531527,t.d=1367130551,e===Math.floor(e)?(t.a=e/4294967296|0,t.b=0|e):n+=e;for(var r=0;r<n.length+20;r++)t.b^=0|n.charCodeAt(r),t.next()}function o(e,t){return t.a=e.a,t.b=e.b,t.c=e.c,t.d=e.d,t}function a(e,t){var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&o(r,n),i.state=function(){return o(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.tychei=a}(0,e=n.nmd(e),n.amdD)},3982:function(e,t,n){e.exports=function(e,t,n,r){"use strict";const i="TemplateFactory",u={allowList:n.DefaultAllowlist,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},o={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},a={entry:"(string|element|function|null)",selector:"(string|element)"};class s extends t{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return u}static get DefaultType(){return o}static get NAME(){return i}getContent(){return Object.values(this._config.content).map((e=>this._resolvePossibleFunction(e))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[t,n]of Object.entries(this._config.content))this._setContent(e,n,t);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},a)}_setContent(t,n,i){const u=e.findOne(i,t);u&&((n=this._resolvePossibleFunction(n))?r.isElement(n)?this._putElementInTemplate(r.getElement(n),u):this._config.html?u.innerHTML=this._maybeSanitize(n):u.textContent=n:u.remove())}_maybeSanitize(e){return this._config.sanitize?n.sanitizeHtml(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return r.execute(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}return s}(n(5411),n(2105),n(2812),n(4035))},4035:function(e,t){!function(e){"use strict";const t=1e6,n=1e3,r="transitionend",i=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,((e,t)=>`#${CSS.escape(t)}`))),e),u=e=>null==e?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),o=e=>{do{e+=Math.floor(Math.random()*t)}while(document.getElementById(e));return e},a=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:r}=window.getComputedStyle(e);const i=Number.parseFloat(t),u=Number.parseFloat(r);return i||u?(t=t.split(",")[0],r=r.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(r))*n):0},s=e=>{e.dispatchEvent(new Event(r))},c=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),f=e=>c(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(i(e)):null,l=e=>{if(!c(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const t=e.closest("summary");if(t&&t.parentNode!==n)return!1;if(null===t)return!1}return t},p=e=>!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),h=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?h(e.parentNode):null},m=()=>{},d=e=>{e.offsetHeight},v=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,g=[],D=e=>{"loading"===document.readyState?(g.length||document.addEventListener("DOMContentLoaded",(()=>{for(const e of g)e()})),g.push(e)):e()},y=()=>"rtl"===document.documentElement.dir,E=e=>{D((()=>{const t=v();if(t){const n=e.NAME,r=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=r,e.jQueryInterface)}}))},w=(e,t=[],n=e)=>"function"==typeof e?e.call(...t):n,b=(e,t,n=!0)=>{if(!n)return void w(e);const i=5,u=a(t)+i;let o=!1;const c=({target:n})=>{n===t&&(o=!0,t.removeEventListener(r,c),w(e))};t.addEventListener(r,c),setTimeout((()=>{o||s(t)}),u)},x=(e,t,n,r)=>{const i=e.length;let u=e.indexOf(t);return-1===u?!n&&r?e[i-1]:e[0]:(u+=n?1:-1,r&&(u=(u+i)%i),e[Math.max(0,Math.min(u,i-1))])};e.defineJQueryPlugin=E,e.execute=w,e.executeAfterTransition=b,e.findShadowRoot=h,e.getElement=f,e.getNextActiveElement=x,e.getTransitionDurationFromElement=a,e.getUID=o,e.getjQuery=v,e.isDisabled=p,e.isElement=c,e.isRTL=y,e.isVisible=l,e.noop=m,e.onDOMContentLoaded=D,e.parseSelector=i,e.reflow=d,e.toType=u,e.triggerTransitionEnd=s,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(t)},4801:function(e,t,n){var r;!function(i,u,o){var a,s=256,c=o.pow(s,6),f=o.pow(2,52),l=2*f,p=255;function h(e,t,n){var r=[],p=g(v((t=1==t?{entropy:!0}:t||{}).entropy?[e,D(u)]:null==e?function(){try{var e;return a&&(e=a.randomBytes)?e=e(s):(e=new Uint8Array(s),(i.crypto||i.msCrypto).getRandomValues(e)),D(e)}catch(e){var t=i.navigator,n=t&&t.plugins;return[+new Date,i,n,i.screen,D(u)]}}():e,3),r),h=new m(r),y=function(){for(var e=h.g(6),t=c,n=0;e<f;)e=(e+n)*s,t*=s,n=h.g(1);for(;e>=l;)e/=2,t/=2,n>>>=1;return(e+n)/t};return y.int32=function(){return 0|h.g(4)},y.quick=function(){return h.g(4)/4294967296},y.double=y,g(D(h.S),u),(t.pass||n||function(e,t,n,r){return r&&(r.S&&d(r,h),e.state=function(){return d(h,{})}),n?(o.random=e,t):e})(y,p,"global"in t?t.global:this==o,t.state)}function m(e){var t,n=e.length,r=this,i=0,u=r.i=r.j=0,o=r.S=[];for(n||(e=[n++]);i<s;)o[i]=i++;for(i=0;i<s;i++)o[i]=o[u=p&u+e[i%n]+(t=o[i])],o[u]=t;(r.g=function(e){for(var t,n=0,i=r.i,u=r.j,o=r.S;e--;)t=o[i=p&i+1],n=n*s+o[p&(o[i]=o[u=p&u+t])+(o[u]=t)];return r.i=i,r.j=u,n})(s)}function d(e,t){return t.i=e.i,t.j=e.j,t.S=e.S.slice(),t}function v(e,t){var n,r=[],i=typeof e;if(t&&"object"==i)for(n in e)try{r.push(v(e[n],t-1))}catch(e){}return r.length?r:"string"==i?e:e+"\0"}function g(e,t){for(var n,r=e+"",i=0;i<r.length;)t[p&i]=p&(n^=19*t[p&i])+r.charCodeAt(i++);return D(t)}function D(e){return String.fromCharCode.apply(0,e)}if(g(o.random(),u),e.exports){e.exports=h;try{a=n(1234)}catch(e){}}else void 0===(r=function(){return h}.call(t,n,t,e))||(e.exports=r)}("undefined"!=typeof self?self:this,[],Math)},5411:function(e,t,n){e.exports=function(e){"use strict";const t=t=>{let n=t.getAttribute("data-bs-target");if(!n||"#"===n){let e=t.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;e.includes("#")&&!e.startsWith("#")&&(e=`#${e.split("#")[1]}`),n=e&&"#"!==e?e.trim():null}return n?n.split(",").map((t=>e.parseSelector(t))).join(","):null},n={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter((e=>e.matches(t))),parents(e,t){const n=[];let r=e.parentNode.closest(t);for(;r;)n.push(r),r=r.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(t){const n=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((e=>`${e}:not([tabindex^="-"])`)).join(",");return this.find(n,t).filter((t=>!e.isDisabled(t)&&e.isVisible(t)))},getSelectorFromElement(e){const r=t(e);return r&&n.findOne(r)?r:null},getElementFromSelector(e){const r=t(e);return r?n.findOne(r):null},getMultipleElementsFromSelector(e){const r=t(e);return r?n.find(r):[]}};return n}(n(4035))},6833:function(e,t,n){var r;!function(e,i){function u(e){var t=this;t.next=function(){var e,n,r=t.w,i=t.X,u=t.i;return t.w=r=r+1640531527|0,n=i[u+34&127],e=i[u=u+1&127],n^=n<<13,e^=e<<17,n^=n>>>15,e^=e>>>12,n=i[u]=n^e,t.i=u,n+(r^r>>>16)|0},function(e,t){var n,r,i,u,o,a=[],s=128;for(t===(0|t)?(r=t,t=null):(t+="\0",r=0,s=Math.max(s,t.length)),i=0,u=-32;u<s;++u)t&&(r^=t.charCodeAt((u+32)%t.length)),0===u&&(o=r),r^=r<<10,r^=r>>>15,r^=r<<4,r^=r>>>13,u>=0&&(o=o+1640531527|0,i=0==(n=a[127&u]^=r+o)?i+1:0);for(i>=128&&(a[127&(t&&t.length||0)]=-1),i=127,u=512;u>0;--u)r=a[i+34&127],n=a[i=i+1&127],r^=r<<13,n^=n<<17,r^=r>>>15,n^=n>>>12,a[i]=r^n;e.w=o,e.X=a,e.i=i}(t,e)}function o(e,t){return t.i=e.i,t.w=e.w,t.X=e.X.slice(),t}function a(e,t){null==e&&(e=+new Date);var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&(r.X&&o(r,n),i.state=function(){return o(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.xor4096=a}(0,e=n.nmd(e),n.amdD)},7180:function(e,t,n){var r;!function(e,i){function u(e){var t=this,n=function(){var e=4022871197,t=function(t){t=String(t);for(var n=0;n<t.length;n++){var r=.02519603282416938*(e+=t.charCodeAt(n));r-=e=r>>>0,e=(r*=e)>>>0,e+=4294967296*(r-=e)}return 2.3283064365386963e-10*(e>>>0)};return t}();t.next=function(){var e=2091639*t.s0+2.3283064365386963e-10*t.c;return t.s0=t.s1,t.s1=t.s2,t.s2=e-(t.c=0|e)},t.c=1,t.s0=n(" "),t.s1=n(" "),t.s2=n(" "),t.s0-=n(e),t.s0<0&&(t.s0+=1),t.s1-=n(e),t.s1<0&&(t.s1+=1),t.s2-=n(e),t.s2<0&&(t.s2+=1),n=null}function o(e,t){return t.c=e.c,t.s0=e.s0,t.s1=e.s1,t.s2=e.s2,t}function a(e,t){var n=new u(e),r=t&&t.state,i=n.next;return i.int32=function(){return 4294967296*n.next()|0},i.double=function(){return i()+11102230246251565e-32*(2097152*i()|0)},i.quick=i,r&&("object"==typeof r&&o(r,n),i.state=function(){return o(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.alea=a}(0,e=n.nmd(e),n.amdD)},7269:function(e){e.exports=function(){"use strict";const e=new Map;return{set(t,n,r){e.has(t)||e.set(t,new Map);const i=e.get(t);i.has(n)||0===i.size?i.set(n,r):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(i.keys())[0]}.`)},get:(t,n)=>e.has(t)&&e.get(t).get(n)||null,remove(t,n){if(!e.has(t))return;const r=e.get(t);r.delete(n),0===r.size&&e.delete(t)}}}()},7391:(e,t,n)=>{var r=n(7180),i=n(3181),u=n(3031),o=n(9067),a=n(6833),s=n(3717),c=n(4801);c.alea=r,c.xor128=i,c.xorwow=u,c.xorshift7=o,c.xor4096=a,c.tychei=s,e.exports=c},7956:function(e,t,n){e.exports=function(e){"use strict";const t=/[^.]*(?=\..*)\.|.*/,n=/\..*/,r=/::\d+$/,i={};let u=1;const o={mouseenter:"mouseover",mouseleave:"mouseout"},a=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function s(e,t){return t&&`${t}::${u++}`||e.uidEvent||u++}function c(e){const t=s(e);return e.uidEvent=t,i[t]=i[t]||{},i[t]}function f(e,t){return function n(r){return y(r,{delegateTarget:e}),n.oneOff&&D.off(e,r.type,t),t.apply(e,[r])}}function l(e,t,n){return function r(i){const u=e.querySelectorAll(t);for(let{target:o}=i;o&&o!==this;o=o.parentNode)for(const a of u)if(a===o)return y(i,{delegateTarget:o}),r.oneOff&&D.off(e,i.type,t,n),n.apply(o,[i])}}function p(e,t,n=null){return Object.values(e).find((e=>e.callable===t&&e.delegationSelector===n))}function h(e,t,n){const r="string"==typeof t,i=r?n:t||n;let u=g(e);return a.has(u)||(u=e),[r,i,u]}function m(e,n,r,i,u){if("string"!=typeof n||!e)return;let[a,m,d]=h(n,r,i);if(n in o){const e=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)};m=e(m)}const v=c(e),g=v[d]||(v[d]={}),D=p(g,m,a?r:null);if(D)return void(D.oneOff=D.oneOff&&u);const y=s(m,n.replace(t,"")),E=a?l(e,r,m):f(e,m);E.delegationSelector=a?r:null,E.callable=m,E.oneOff=u,E.uidEvent=y,g[y]=E,e.addEventListener(d,E,a)}function d(e,t,n,r,i){const u=p(t[n],r,i);u&&(e.removeEventListener(n,u,Boolean(i)),delete t[n][u.uidEvent])}function v(e,t,n,r){const i=t[n]||{};for(const[u,o]of Object.entries(i))u.includes(r)&&d(e,t,n,o.callable,o.delegationSelector)}function g(e){return e=e.replace(n,""),o[e]||e}const D={on(e,t,n,r){m(e,t,n,r,!1)},one(e,t,n,r){m(e,t,n,r,!0)},off(e,t,n,i){if("string"!=typeof t||!e)return;const[u,o,a]=h(t,n,i),s=a!==t,f=c(e),l=f[a]||{},p=t.startsWith(".");if(void 0===o){if(p)for(const n of Object.keys(f))v(e,f,n,t.slice(1));for(const[n,i]of Object.entries(l)){const u=n.replace(r,"");s&&!t.includes(u)||d(e,f,a,i.callable,i.delegationSelector)}}else{if(!Object.keys(l).length)return;d(e,f,a,o,u?n:null)}},trigger(t,n,r){if("string"!=typeof n||!t)return null;const i=e.getjQuery();let u=null,o=!0,a=!0,s=!1;n!==g(n)&&i&&(u=i.Event(n,r),i(t).trigger(u),o=!u.isPropagationStopped(),a=!u.isImmediatePropagationStopped(),s=u.isDefaultPrevented());const c=y(new Event(n,{bubbles:o,cancelable:!0}),r);return s&&c.preventDefault(),a&&t.dispatchEvent(c),c.defaultPrevented&&u&&u.preventDefault(),c}};function y(e,t={}){for(const[n,r]of Object.entries(t))try{e[n]=r}catch(t){Object.defineProperty(e,n,{configurable:!0,get:()=>r})}return e}return D}(n(4035))},9011:function(e,t,n){e.exports=function(e,t,n,r){"use strict";const i="5.3.7";class u extends n{constructor(t,n){super(),(t=r.getElement(t))&&(this._element=t,this._config=this._getConfig(n),e.set(this._element,this.constructor.DATA_KEY,this))}dispose(){e.remove(this._element,this.constructor.DATA_KEY),t.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){r.executeAfterTransition(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(t){return e.get(r.getElement(t),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return i}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}return u}(n(7269),n(7956),n(2105),n(4035))},9067:function(e,t,n){var r;!function(e,i){function u(e){var t=this;t.next=function(){var e,n,r=t.x,i=t.i;return e=r[i],n=(e^=e>>>7)^e<<24,n^=(e=r[i+1&7])^e>>>10,n^=(e=r[i+3&7])^e>>>3,n^=(e=r[i+4&7])^e<<7,e=r[i+7&7],n^=(e^=e<<13)^e<<9,r[i]=n,t.i=i+1&7,n},function(e,t){var n,r=[];if(t===(0|t))r[0]=t;else for(t=""+t,n=0;n<t.length;++n)r[7&n]=r[7&n]<<15^t.charCodeAt(n)+r[n+1&7]<<13;for(;r.length<8;)r.push(0);for(n=0;n<8&&0===r[n];++n);for(8==n?r[7]=-1:r[n],e.x=r,e.i=0,n=256;n>0;--n)e.next()}(t,e)}function o(e,t){return t.x=e.x.slice(),t.i=e.i,t}function a(e,t){null==e&&(e=+new Date);var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&(r.x&&o(r,n),i.state=function(){return o(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.xorshift7=a}(0,e=n.nmd(e),n.amdD)}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var u=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(u.exports,u,u.exports,n),u.loaded=!0,u.exports}n.amdD=function(){throw new Error("define cannot be used indirect")},n.amdO={},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{"use strict";function e(){return e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.apply(null,arguments)}var t={epsilon:1e-12,matrix:"Matrix",number:"number",precision:64,predictable:!1,randomSeed:null},r=["Matrix","Array"],i=["number","BigNumber","Fraction"];var u=function(e){if(e)throw new Error("The global config is readonly. \nPlease create a mathjs instance if you want to change the default configuration. \nExample:\n\n  import { create, all } from 'mathjs';\n  const mathjs = create(all);\n  mathjs.config({ number: 'BigNumber' });\n");return Object.freeze(t)};e(u,t,{MATRIX_OPTIONS:r,NUMBER_OPTIONS:i});var o,a,s=9e15,c=1e9,f="0123456789abcdef",l="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",p="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",h={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-s,maxE:s,crypto:!1},m=!0,d="[DecimalError] ",v=d+"Invalid argument: ",g=d+"Precision limit exceeded",D=d+"crypto unavailable",y="[object Decimal]",E=Math.floor,w=Math.pow,b=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,x=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,A=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,C=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,F=1e7,N=l.length-1,_=p.length-1,M={toStringTag:y};function S(e){var t,n,r,i=e.length-1,u="",o=e[0];if(i>0){for(u+=o,t=1;t<i;t++)(n=7-(r=e[t]+"").length)&&(u+=k(n)),u+=r;(n=7-(r=(o=e[t])+"").length)&&(u+=k(n))}else if(0===o)return"0";for(;o%10==0;)o/=10;return u+o}function O(e,t,n){if(e!==~~e||e<t||e>n)throw Error(v+e)}function B(e,t,n,r){var i,u,o,a;for(u=e[0];u>=10;u/=10)--t;return--t<0?(t+=7,i=0):(i=Math.ceil((t+1)/7),t%=7),u=w(10,7-t),a=e[i]%u|0,null==r?t<3?(0==t?a=a/100|0:1==t&&(a=a/10|0),o=n<4&&99999==a||n>3&&49999==a||5e4==a||0==a):o=(n<4&&a+1==u||n>3&&a+1==u/2)&&(e[i+1]/u/100|0)==w(10,t-2)-1||(a==u/2||0==a)&&!(e[i+1]/u/100|0):t<4?(0==t?a=a/1e3|0:1==t?a=a/100|0:2==t&&(a=a/10|0),o=(r||n<4)&&9999==a||!r&&n>3&&4999==a):o=((r||n<4)&&a+1==u||!r&&n>3&&a+1==u/2)&&(e[i+1]/u/1e3|0)==w(10,t-3)-1,o}function T(e,t,n){for(var r,i,u=[0],o=0,a=e.length;o<a;){for(i=u.length;i--;)u[i]*=t;for(u[0]+=f.indexOf(e.charAt(o++)),r=0;r<u.length;r++)u[r]>n-1&&(void 0===u[r+1]&&(u[r+1]=0),u[r+1]+=u[r]/n|0,u[r]%=n)}return u.reverse()}M.absoluteValue=M.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),I(e)},M.ceil=function(){return I(new this.constructor(this),this.e+1,2)},M.clampedTo=M.clamp=function(e,t){var n=this,r=n.constructor;if(e=new r(e),t=new r(t),!e.s||!t.s)return new r(NaN);if(e.gt(t))throw Error(v+t);return n.cmp(e)<0?e:n.cmp(t)>0?t:new r(n)},M.comparedTo=M.cmp=function(e){var t,n,r,i,u=this,o=u.d,a=(e=new u.constructor(e)).d,s=u.s,c=e.s;if(!o||!a)return s&&c?s!==c?s:o===a?0:!o^s<0?1:-1:NaN;if(!o[0]||!a[0])return o[0]?s:a[0]?-c:0;if(s!==c)return s;if(u.e!==e.e)return u.e>e.e^s<0?1:-1;for(t=0,n=(r=o.length)<(i=a.length)?r:i;t<n;++t)if(o[t]!==a[t])return o[t]>a[t]^s<0?1:-1;return r===i?0:r>i^s<0?1:-1},M.cosine=M.cos=function(){var e,t,n=this,r=n.constructor;return n.d?n.d[0]?(e=r.precision,t=r.rounding,r.precision=e+Math.max(n.e,n.sd())+7,r.rounding=1,n=function(e,t){var n,r,i;if(t.isZero())return t;r=t.d.length,r<32?i=(1/Q(4,n=Math.ceil(r/3))).toString():(n=16,i="2.3283064365386962890625e-10");e.precision+=n,t=J(e,1,t.times(i),new e(1));for(var u=n;u--;){var o=t.times(t);t=o.times(o).minus(o).times(8).plus(1)}return e.precision-=n,t}(r,X(r,n)),r.precision=e,r.rounding=t,I(2==a||3==a?n.neg():n,e,t,!0)):new r(1):new r(NaN)},M.cubeRoot=M.cbrt=function(){var e,t,n,r,i,u,o,a,s,c,f=this,l=f.constructor;if(!f.isFinite()||f.isZero())return new l(f);for(m=!1,(u=f.s*w(f.s*f,1/3))&&Math.abs(u)!=1/0?r=new l(u.toString()):(n=S(f.d),(u=((e=f.e)-n.length+1)%3)&&(n+=1==u||-2==u?"0":"00"),u=w(n,1/3),e=E((e+1)/3)-(e%3==(e<0?-1:2)),(r=new l(n=u==1/0?"5e"+e:(n=u.toExponential()).slice(0,n.indexOf("e")+1)+e)).s=f.s),o=(e=l.precision)+3;;)if(c=(s=(a=r).times(a).times(a)).plus(f),r=R(c.plus(f).times(a),c.plus(s),o+2,1),S(a.d).slice(0,o)===(n=S(r.d)).slice(0,o)){if("9999"!=(n=n.slice(o-3,o+1))&&(i||"4999"!=n)){+n&&(+n.slice(1)||"5"!=n.charAt(0))||(I(r,e+1,1),t=!r.times(r).times(r).eq(f));break}if(!i&&(I(a,e+1,0),a.times(a).times(a).eq(f))){r=a;break}o+=4,i=1}return m=!0,I(r,e,l.rounding,t)},M.decimalPlaces=M.dp=function(){var e,t=this.d,n=NaN;if(t){if(n=7*((e=t.length-1)-E(this.e/7)),e=t[e])for(;e%10==0;e/=10)n--;n<0&&(n=0)}return n},M.dividedBy=M.div=function(e){return R(this,new this.constructor(e))},M.dividedToIntegerBy=M.divToInt=function(e){var t=this.constructor;return I(R(this,new t(e),0,1,1),t.precision,t.rounding)},M.equals=M.eq=function(e){return 0===this.cmp(e)},M.floor=function(){return I(new this.constructor(this),this.e+1,3)},M.greaterThan=M.gt=function(e){return this.cmp(e)>0},M.greaterThanOrEqualTo=M.gte=function(e){var t=this.cmp(e);return 1==t||0===t},M.hyperbolicCosine=M.cosh=function(){var e,t,n,r,i,u=this,o=u.constructor,a=new o(1);if(!u.isFinite())return new o(u.s?1/0:NaN);if(u.isZero())return a;n=o.precision,r=o.rounding,o.precision=n+Math.max(u.e,u.sd())+4,o.rounding=1,(i=u.d.length)<32?t=(1/Q(4,e=Math.ceil(i/3))).toString():(e=16,t="2.3283064365386962890625e-10"),u=J(o,1,u.times(t),new o(1),!0);for(var s,c=e,f=new o(8);c--;)s=u.times(u),u=a.minus(s.times(f.minus(s.times(f))));return I(u,o.precision=n,o.rounding=r,!0)},M.hyperbolicSine=M.sinh=function(){var e,t,n,r,i=this,u=i.constructor;if(!i.isFinite()||i.isZero())return new u(i);if(t=u.precision,n=u.rounding,u.precision=t+Math.max(i.e,i.sd())+4,u.rounding=1,(r=i.d.length)<3)i=J(u,2,i,i,!0);else{e=(e=1.4*Math.sqrt(r))>16?16:0|e,i=J(u,2,i=i.times(1/Q(5,e)),i,!0);for(var o,a=new u(5),s=new u(16),c=new u(20);e--;)o=i.times(i),i=i.times(a.plus(o.times(s.times(o).plus(c))))}return u.precision=t,u.rounding=n,I(i,t,n,!0)},M.hyperbolicTangent=M.tanh=function(){var e,t,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+7,r.rounding=1,R(n.sinh(),n.cosh(),r.precision=e,r.rounding=t)):new r(n.s)},M.inverseCosine=M.acos=function(){var e=this,t=e.constructor,n=e.abs().cmp(1),r=t.precision,i=t.rounding;return-1!==n?0===n?e.isNeg()?L(t,r,i):new t(0):new t(NaN):e.isZero()?L(t,r+4,i).times(.5):(t.precision=r+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=r,t.rounding=i,e.times(2))},M.inverseHyperbolicCosine=M.acosh=function(){var e,t,n=this,r=n.constructor;return n.lte(1)?new r(n.eq(1)?0:NaN):n.isFinite()?(e=r.precision,t=r.rounding,r.precision=e+Math.max(Math.abs(n.e),n.sd())+4,r.rounding=1,m=!1,n=n.times(n).minus(1).sqrt().plus(n),m=!0,r.precision=e,r.rounding=t,n.ln()):new r(n)},M.inverseHyperbolicSine=M.asinh=function(){var e,t,n=this,r=n.constructor;return!n.isFinite()||n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+2*Math.max(Math.abs(n.e),n.sd())+6,r.rounding=1,m=!1,n=n.times(n).plus(1).sqrt().plus(n),m=!0,r.precision=e,r.rounding=t,n.ln())},M.inverseHyperbolicTangent=M.atanh=function(){var e,t,n,r,i=this,u=i.constructor;return i.isFinite()?i.e>=0?new u(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=u.precision,t=u.rounding,r=i.sd(),Math.max(r,e)<2*-i.e-1?I(new u(i),e,t,!0):(u.precision=n=r-i.e,i=R(i.plus(1),new u(1).minus(i),n+e,1),u.precision=e+4,u.rounding=1,i=i.ln(),u.precision=e,u.rounding=t,i.times(.5))):new u(NaN)},M.inverseSine=M.asin=function(){var e,t,n,r,i=this,u=i.constructor;return i.isZero()?new u(i):(t=i.abs().cmp(1),n=u.precision,r=u.rounding,-1!==t?0===t?((e=L(u,n+4,r).times(.5)).s=i.s,e):new u(NaN):(u.precision=n+6,u.rounding=1,i=i.div(new u(1).minus(i.times(i)).sqrt().plus(1)).atan(),u.precision=n,u.rounding=r,i.times(2)))},M.inverseTangent=M.atan=function(){var e,t,n,r,i,u,o,a,s,c=this,f=c.constructor,l=f.precision,p=f.rounding;if(c.isFinite()){if(c.isZero())return new f(c);if(c.abs().eq(1)&&l+4<=_)return(o=L(f,l+4,p).times(.25)).s=c.s,o}else{if(!c.s)return new f(NaN);if(l+4<=_)return(o=L(f,l+4,p).times(.5)).s=c.s,o}for(f.precision=a=l+10,f.rounding=1,e=n=Math.min(28,a/7+2|0);e;--e)c=c.div(c.times(c).plus(1).sqrt().plus(1));for(m=!1,t=Math.ceil(a/7),r=1,s=c.times(c),o=new f(c),i=c;-1!==e;)if(i=i.times(s),u=o.minus(i.div(r+=2)),i=i.times(s),void 0!==(o=u.plus(i.div(r+=2))).d[t])for(e=t;o.d[e]===u.d[e]&&e--;);return n&&(o=o.times(2<<n-1)),m=!0,I(o,f.precision=l,f.rounding=p,!0)},M.isFinite=function(){return!!this.d},M.isInteger=M.isInt=function(){return!!this.d&&E(this.e/7)>this.d.length-2},M.isNaN=function(){return!this.s},M.isNegative=M.isNeg=function(){return this.s<0},M.isPositive=M.isPos=function(){return this.s>0},M.isZero=function(){return!!this.d&&0===this.d[0]},M.lessThan=M.lt=function(e){return this.cmp(e)<0},M.lessThanOrEqualTo=M.lte=function(e){return this.cmp(e)<1},M.logarithm=M.log=function(e){var t,n,r,i,u,o,a,s,c=this,f=c.constructor,l=f.precision,p=f.rounding;if(null==e)e=new f(10),t=!0;else{if(n=(e=new f(e)).d,e.s<0||!n||!n[0]||e.eq(1))return new f(NaN);t=e.eq(10)}if(n=c.d,c.s<0||!n||!n[0]||c.eq(1))return new f(n&&!n[0]?-1/0:1!=c.s?NaN:n?0:1/0);if(t)if(n.length>1)u=!0;else{for(i=n[0];i%10==0;)i/=10;u=1!==i}if(m=!1,o=Y(c,a=l+5),r=t?U(f,a+10):Y(e,a),B((s=R(o,r,a,1)).d,i=l,p))do{if(o=Y(c,a+=10),r=t?U(f,a+10):Y(e,a),s=R(o,r,a,1),!u){+S(s.d).slice(i+1,i+15)+1==1e14&&(s=I(s,l+1,0));break}}while(B(s.d,i+=10,p));return m=!0,I(s,l,p)},M.minus=M.sub=function(e){var t,n,r,i,u,o,a,s,c,f,l,p,h=this,d=h.constructor;if(e=new d(e),!h.d||!e.d)return h.s&&e.s?h.d?e.s=-e.s:e=new d(e.d||h.s!==e.s?h:NaN):e=new d(NaN),e;if(h.s!=e.s)return e.s=-e.s,h.plus(e);if(c=h.d,p=e.d,a=d.precision,s=d.rounding,!c[0]||!p[0]){if(p[0])e.s=-e.s;else{if(!c[0])return new d(3===s?-0:0);e=new d(h)}return m?I(e,a,s):e}if(n=E(e.e/7),f=E(h.e/7),c=c.slice(),u=f-n){for((l=u<0)?(t=c,u=-u,o=p.length):(t=p,n=f,o=c.length),u>(r=Math.max(Math.ceil(a/7),o)+2)&&(u=r,t.length=1),t.reverse(),r=u;r--;)t.push(0);t.reverse()}else{for((l=(r=c.length)<(o=p.length))&&(o=r),r=0;r<o;r++)if(c[r]!=p[r]){l=c[r]<p[r];break}u=0}for(l&&(t=c,c=p,p=t,e.s=-e.s),o=c.length,r=p.length-o;r>0;--r)c[o++]=0;for(r=p.length;r>u;){if(c[--r]<p[r]){for(i=r;i&&0===c[--i];)c[i]=F-1;--c[i],c[r]+=F}c[r]-=p[r]}for(;0===c[--o];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(e.d=c,e.e=P(c,n),m?I(e,a,s):e):new d(3===s?-0:0)},M.modulo=M.mod=function(e){var t,n=this,r=n.constructor;return e=new r(e),!n.d||!e.s||e.d&&!e.d[0]?new r(NaN):!e.d||n.d&&!n.d[0]?I(new r(n),r.precision,r.rounding):(m=!1,9==r.modulo?(t=R(n,e.abs(),0,3,1)).s*=e.s:t=R(n,e,0,r.modulo,1),t=t.times(e),m=!0,n.minus(t))},M.naturalExponential=M.exp=function(){return V(this)},M.naturalLogarithm=M.ln=function(){return Y(this)},M.negated=M.neg=function(){var e=new this.constructor(this);return e.s=-e.s,I(e)},M.plus=M.add=function(e){var t,n,r,i,u,o,a,s,c,f,l=this,p=l.constructor;if(e=new p(e),!l.d||!e.d)return l.s&&e.s?l.d||(e=new p(e.d||l.s===e.s?l:NaN)):e=new p(NaN),e;if(l.s!=e.s)return e.s=-e.s,l.minus(e);if(c=l.d,f=e.d,a=p.precision,s=p.rounding,!c[0]||!f[0])return f[0]||(e=new p(l)),m?I(e,a,s):e;if(u=E(l.e/7),r=E(e.e/7),c=c.slice(),i=u-r){for(i<0?(n=c,i=-i,o=f.length):(n=f,r=u,o=c.length),i>(o=(u=Math.ceil(a/7))>o?u+1:o+1)&&(i=o,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((o=c.length)-(i=f.length)<0&&(i=o,n=f,f=c,c=n),t=0;i;)t=(c[--i]=c[i]+f[i]+t)/F|0,c[i]%=F;for(t&&(c.unshift(t),++r),o=c.length;0==c[--o];)c.pop();return e.d=c,e.e=P(c,r),m?I(e,a,s):e},M.precision=M.sd=function(e){var t,n=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(v+e);return n.d?(t=j(n.d),e&&n.e+1>t&&(t=n.e+1)):t=NaN,t},M.round=function(){var e=this,t=e.constructor;return I(new t(e),e.e+1,t.rounding)},M.sine=M.sin=function(){var e,t,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+Math.max(n.e,n.sd())+7,r.rounding=1,n=function(e,t){var n,r=t.d.length;if(r<3)return t.isZero()?t:J(e,2,t,t);n=(n=1.4*Math.sqrt(r))>16?16:0|n,t=t.times(1/Q(5,n)),t=J(e,2,t,t);for(var i,u=new e(5),o=new e(16),a=new e(20);n--;)i=t.times(t),t=t.times(u.plus(i.times(o.times(i).minus(a))));return t}(r,X(r,n)),r.precision=e,r.rounding=t,I(a>2?n.neg():n,e,t,!0)):new r(NaN)},M.squareRoot=M.sqrt=function(){var e,t,n,r,i,u,o=this,a=o.d,s=o.e,c=o.s,f=o.constructor;if(1!==c||!a||!a[0])return new f(!c||c<0&&(!a||a[0])?NaN:a?o:1/0);for(m=!1,0==(c=Math.sqrt(+o))||c==1/0?(((t=S(a)).length+s)%2==0&&(t+="0"),c=Math.sqrt(t),s=E((s+1)/2)-(s<0||s%2),r=new f(t=c==1/0?"5e"+s:(t=c.toExponential()).slice(0,t.indexOf("e")+1)+s)):r=new f(c.toString()),n=(s=f.precision)+3;;)if(r=(u=r).plus(R(o,u,n+2,1)).times(.5),S(u.d).slice(0,n)===(t=S(r.d)).slice(0,n)){if("9999"!=(t=t.slice(n-3,n+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(I(r,s+1,1),e=!r.times(r).eq(o));break}if(!i&&(I(u,s+1,0),u.times(u).eq(o))){r=u;break}n+=4,i=1}return m=!0,I(r,s,f.rounding,e)},M.tangent=M.tan=function(){var e,t,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+10,r.rounding=1,(n=n.sin()).s=1,n=R(n,new r(1).minus(n.times(n)).sqrt(),e+10,0),r.precision=e,r.rounding=t,I(2==a||4==a?n.neg():n,e,t,!0)):new r(NaN)},M.times=M.mul=function(e){var t,n,r,i,u,o,a,s,c,f=this,l=f.constructor,p=f.d,h=(e=new l(e)).d;if(e.s*=f.s,!(p&&p[0]&&h&&h[0]))return new l(!e.s||p&&!p[0]&&!h||h&&!h[0]&&!p?NaN:p&&h?0*e.s:e.s/0);for(n=E(f.e/7)+E(e.e/7),(s=p.length)<(c=h.length)&&(u=p,p=h,h=u,o=s,s=c,c=o),u=[],r=o=s+c;r--;)u.push(0);for(r=c;--r>=0;){for(t=0,i=s+r;i>r;)a=u[i]+h[r]*p[i-r-1]+t,u[i--]=a%F|0,t=a/F|0;u[i]=(u[i]+t)%F|0}for(;!u[--o];)u.pop();return t?++n:u.shift(),e.d=u,e.e=P(u,n),m?I(e,l.precision,l.rounding):e},M.toBinary=function(e,t){return K(this,2,e,t)},M.toDecimalPlaces=M.toDP=function(e,t){var n=this,r=n.constructor;return n=new r(n),void 0===e?n:(O(e,0,c),void 0===t?t=r.rounding:O(t,0,8),I(n,e+n.e+1,t))},M.toExponential=function(e,t){var n,r=this,i=r.constructor;return void 0===e?n=z(r,!0):(O(e,0,c),void 0===t?t=i.rounding:O(t,0,8),n=z(r=I(new i(r),e+1,t),!0,e+1)),r.isNeg()&&!r.isZero()?"-"+n:n},M.toFixed=function(e,t){var n,r,i=this,u=i.constructor;return void 0===e?n=z(i):(O(e,0,c),void 0===t?t=u.rounding:O(t,0,8),n=z(r=I(new u(i),e+i.e+1,t),!1,e+r.e+1)),i.isNeg()&&!i.isZero()?"-"+n:n},M.toFraction=function(e){var t,n,r,i,u,o,a,s,c,f,l,p,h=this,d=h.d,g=h.constructor;if(!d)return new g(h);if(c=n=new g(1),r=s=new g(0),o=(u=(t=new g(r)).e=j(d)-h.e-1)%7,t.d[0]=w(10,o<0?7+o:o),null==e)e=u>0?t:c;else{if(!(a=new g(e)).isInt()||a.lt(c))throw Error(v+a);e=a.gt(t)?u>0?t:c:a}for(m=!1,a=new g(S(d)),f=g.precision,g.precision=u=7*d.length*2;l=R(a,t,0,1,1),1!=(i=n.plus(l.times(r))).cmp(e);)n=r,r=i,i=c,c=s.plus(l.times(i)),s=i,i=t,t=a.minus(l.times(i)),a=i;return i=R(e.minus(n),r,0,1,1),s=s.plus(i.times(c)),n=n.plus(i.times(r)),s.s=c.s=h.s,p=R(c,r,u,1).minus(h).abs().cmp(R(s,n,u,1).minus(h).abs())<1?[c,r]:[s,n],g.precision=f,m=!0,p},M.toHexadecimal=M.toHex=function(e,t){return K(this,16,e,t)},M.toNearest=function(e,t){var n=this,r=n.constructor;if(n=new r(n),null==e){if(!n.d)return n;e=new r(1),t=r.rounding}else{if(e=new r(e),void 0===t?t=r.rounding:O(t,0,8),!n.d)return e.s?n:e;if(!e.d)return e.s&&(e.s=n.s),e}return e.d[0]?(m=!1,n=R(n,e,0,t,1).times(e),m=!0,I(n)):(e.s=n.s,n=e),n},M.toNumber=function(){return+this},M.toOctal=function(e,t){return K(this,8,e,t)},M.toPower=M.pow=function(e){var t,n,r,i,u,o,a=this,s=a.constructor,c=+(e=new s(e));if(!(a.d&&e.d&&a.d[0]&&e.d[0]))return new s(w(+a,c));if((a=new s(a)).eq(1))return a;if(r=s.precision,u=s.rounding,e.eq(1))return I(a,r,u);if((t=E(e.e/7))>=e.d.length-1&&(n=c<0?-c:c)<=9007199254740991)return i=q(s,a,n,r),e.s<0?new s(1).div(i):I(i,r,u);if((o=a.s)<0){if(t<e.d.length-1)return new s(NaN);if(1&e.d[t]||(o=1),0==a.e&&1==a.d[0]&&1==a.d.length)return a.s=o,a}return(t=0!=(n=w(+a,c))&&isFinite(n)?new s(n+"").e:E(c*(Math.log("0."+S(a.d))/Math.LN10+a.e+1)))>s.maxE+1||t<s.minE-1?new s(t>0?o/0:0):(m=!1,s.rounding=a.s=1,n=Math.min(12,(t+"").length),(i=V(e.times(Y(a,r+n)),r)).d&&B((i=I(i,r+5,1)).d,r,u)&&(t=r+10,+S((i=I(V(e.times(Y(a,t+n)),t),t+5,1)).d).slice(r+1,r+15)+1==1e14&&(i=I(i,r+1,0))),i.s=o,m=!0,s.rounding=u,I(i,r,u))},M.toPrecision=function(e,t){var n,r=this,i=r.constructor;return void 0===e?n=z(r,r.e<=i.toExpNeg||r.e>=i.toExpPos):(O(e,1,c),void 0===t?t=i.rounding:O(t,0,8),n=z(r=I(new i(r),e,t),e<=r.e||r.e<=i.toExpNeg,e)),r.isNeg()&&!r.isZero()?"-"+n:n},M.toSignificantDigits=M.toSD=function(e,t){var n=this.constructor;return void 0===e?(e=n.precision,t=n.rounding):(O(e,1,c),void 0===t?t=n.rounding:O(t,0,8)),I(new n(this),e,t)},M.toString=function(){var e=this,t=e.constructor,n=z(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+n:n},M.truncated=M.trunc=function(){return I(new this.constructor(this),this.e+1,1)},M.valueOf=M.toJSON=function(){var e=this,t=e.constructor,n=z(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+n:n};var R=function(){function e(e,t,n){var r,i=0,u=e.length;for(e=e.slice();u--;)r=e[u]*t+i,e[u]=r%n|0,i=r/n|0;return i&&e.unshift(i),e}function t(e,t,n,r){var i,u;if(n!=r)u=n>r?1:-1;else for(i=u=0;i<n;i++)if(e[i]!=t[i]){u=e[i]>t[i]?1:-1;break}return u}function n(e,t,n,r){for(var i=0;n--;)e[n]-=i,i=e[n]<t[n]?1:0,e[n]=i*r+e[n]-t[n];for(;!e[0]&&e.length>1;)e.shift()}return function(r,i,u,a,s,c){var f,l,p,h,m,d,v,g,D,y,w,b,x,A,C,N,_,M,S,O,B=r.constructor,T=r.s==i.s?1:-1,R=r.d,z=i.d;if(!(R&&R[0]&&z&&z[0]))return new B(r.s&&i.s&&(R?!z||R[0]!=z[0]:z)?R&&0==R[0]||!z?0*T:T/0:NaN);for(c?(m=1,l=r.e-i.e):(c=F,m=7,l=E(r.e/m)-E(i.e/m)),S=z.length,_=R.length,y=(D=new B(T)).d=[],p=0;z[p]==(R[p]||0);p++);if(z[p]>(R[p]||0)&&l--,null==u?(A=u=B.precision,a=B.rounding):A=s?u+(r.e-i.e)+1:u,A<0)y.push(1),d=!0;else{if(A=A/m+2|0,p=0,1==S){for(h=0,z=z[0],A++;(p<_||h)&&A--;p++)C=h*c+(R[p]||0),y[p]=C/z|0,h=C%z|0;d=h||p<_}else{for((h=c/(z[0]+1)|0)>1&&(z=e(z,h,c),R=e(R,h,c),S=z.length,_=R.length),N=S,b=(w=R.slice(0,S)).length;b<S;)w[b++]=0;(O=z.slice()).unshift(0),M=z[0],z[1]>=c/2&&++M;do{h=0,(f=t(z,w,S,b))<0?(x=w[0],S!=b&&(x=x*c+(w[1]||0)),(h=x/M|0)>1?(h>=c&&(h=c-1),1==(f=t(v=e(z,h,c),w,g=v.length,b=w.length))&&(h--,n(v,S<g?O:z,g,c))):(0==h&&(f=h=1),v=z.slice()),(g=v.length)<b&&v.unshift(0),n(w,v,b,c),-1==f&&(f=t(z,w,S,b=w.length))<1&&(h++,n(w,S<b?O:z,b,c)),b=w.length):0===f&&(h++,w=[0]),y[p++]=h,f&&w[0]?w[b++]=R[N]||0:(w=[R[N]],b=1)}while((N++<_||void 0!==w[0])&&A--);d=void 0!==w[0]}y[0]||y.shift()}if(1==m)D.e=l,o=d;else{for(p=1,h=y[0];h>=10;h/=10)p++;D.e=p+l*m-1,I(D,s?u+D.e+1:u,a,d)}return D}}();function I(e,t,n,r){var i,u,o,a,s,c,f,l,p,h=e.constructor;e:if(null!=t){if(!(l=e.d))return e;for(i=1,a=l[0];a>=10;a/=10)i++;if((u=t-i)<0)u+=7,o=t,s=(f=l[p=0])/w(10,i-o-1)%10|0;else if((p=Math.ceil((u+1)/7))>=(a=l.length)){if(!r)break e;for(;a++<=p;)l.push(0);f=s=0,i=1,o=(u%=7)-7+1}else{for(f=a=l[p],i=1;a>=10;a/=10)i++;s=(o=(u%=7)-7+i)<0?0:f/w(10,i-o-1)%10|0}if(r=r||t<0||void 0!==l[p+1]||(o<0?f:f%w(10,i-o-1)),c=n<4?(s||r)&&(0==n||n==(e.s<0?3:2)):s>5||5==s&&(4==n||r||6==n&&(u>0?o>0?f/w(10,i-o):0:l[p-1])%10&1||n==(e.s<0?8:7)),t<1||!l[0])return l.length=0,c?(t-=e.e+1,l[0]=w(10,(7-t%7)%7),e.e=-t||0):l[0]=e.e=0,e;if(0==u?(l.length=p,a=1,p--):(l.length=p+1,a=w(10,7-u),l[p]=o>0?(f/w(10,i-o)%w(10,o)|0)*a:0),c)for(;;){if(0==p){for(u=1,o=l[0];o>=10;o/=10)u++;for(o=l[0]+=a,a=1;o>=10;o/=10)a++;u!=a&&(e.e++,l[0]==F&&(l[0]=1));break}if(l[p]+=a,l[p]!=F)break;l[p--]=0,a=1}for(u=l.length;0===l[--u];)l.pop()}return m&&(e.e>h.maxE?(e.d=null,e.e=NaN):e.e<h.minE&&(e.e=0,e.d=[0])),e}function z(e,t,n){if(!e.isFinite())return $(e);var r,i=e.e,u=S(e.d),o=u.length;return t?(n&&(r=n-o)>0?u=u.charAt(0)+"."+u.slice(1)+k(r):o>1&&(u=u.charAt(0)+"."+u.slice(1)),u=u+(e.e<0?"e":"e+")+e.e):i<0?(u="0."+k(-i-1)+u,n&&(r=n-o)>0&&(u+=k(r))):i>=o?(u+=k(i+1-o),n&&(r=n-i-1)>0&&(u=u+"."+k(r))):((r=i+1)<o&&(u=u.slice(0,r)+"."+u.slice(r)),n&&(r=n-o)>0&&(i+1===o&&(u+="."),u+=k(r))),u}function P(e,t){var n=e[0];for(t*=7;n>=10;n/=10)t++;return t}function U(e,t,n){if(t>N)throw m=!0,n&&(e.precision=n),Error(g);return I(new e(l),t,1,!0)}function L(e,t,n){if(t>_)throw Error(g);return I(new e(p),t,n,!0)}function j(e){var t=e.length-1,n=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)n--;for(t=e[0];t>=10;t/=10)n++}return n}function k(e){for(var t="";e--;)t+="0";return t}function q(e,t,n,r){var i,u=new e(1),o=Math.ceil(r/7+4);for(m=!1;;){if(n%2&&ee((u=u.times(t)).d,o)&&(i=!0),0===(n=E(n/2))){n=u.d.length-1,i&&0===u.d[n]&&++u.d[n];break}ee((t=t.times(t)).d,o)}return m=!0,u}function H(e){return 1&e.d[e.d.length-1]}function G(e,t,n){for(var r,i,u=new e(t[0]),o=0;++o<t.length;){if(!(i=new e(t[o])).s){u=i;break}((r=u.cmp(i))===n||0===r&&u.s===n)&&(u=i)}return u}function V(e,t){var n,r,i,u,o,a,s,c=0,f=0,l=0,p=e.constructor,h=p.rounding,d=p.precision;if(!e.d||!e.d[0]||e.e>17)return new p(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(m=!1,s=d):s=t,a=new p(.03125);e.e>-2;)e=e.times(a),l+=5;for(s+=r=Math.log(w(2,l))/Math.LN10*2+5|0,n=u=o=new p(1),p.precision=s;;){if(u=I(u.times(e),s,1),n=n.times(++f),S((a=o.plus(R(u,n,s,1))).d).slice(0,s)===S(o.d).slice(0,s)){for(i=l;i--;)o=I(o.times(o),s,1);if(null!=t)return p.precision=d,o;if(!(c<3&&B(o.d,s-r,h,c)))return I(o,p.precision=d,h,m=!0);p.precision=s+=10,n=u=a=new p(1),f=0,c++}o=a}}function Y(e,t){var n,r,i,u,o,a,s,c,f,l,p,h=1,d=e,v=d.d,g=d.constructor,D=g.rounding,y=g.precision;if(d.s<0||!v||!v[0]||!d.e&&1==v[0]&&1==v.length)return new g(v&&!v[0]?-1/0:1!=d.s?NaN:v?0:d);if(null==t?(m=!1,f=y):f=t,g.precision=f+=10,r=(n=S(v)).charAt(0),!(Math.abs(u=d.e)<15e14))return c=U(g,f+2,y).times(u+""),d=Y(new g(r+"."+n.slice(1)),f-10).plus(c),g.precision=y,null==t?I(d,y,D,m=!0):d;for(;r<7&&1!=r||1==r&&n.charAt(1)>3;)r=(n=S((d=d.times(e)).d)).charAt(0),h++;for(u=d.e,r>1?(d=new g("0."+n),u++):d=new g(r+"."+n.slice(1)),l=d,s=o=d=R(d.minus(1),d.plus(1),f,1),p=I(d.times(d),f,1),i=3;;){if(o=I(o.times(p),f,1),S((c=s.plus(R(o,new g(i),f,1))).d).slice(0,f)===S(s.d).slice(0,f)){if(s=s.times(2),0!==u&&(s=s.plus(U(g,f+2,y).times(u+""))),s=R(s,new g(h),f,1),null!=t)return g.precision=y,s;if(!B(s.d,f-10,D,a))return I(s,g.precision=y,D,m=!0);g.precision=f+=10,c=o=d=R(l.minus(1),l.plus(1),f,1),p=I(d.times(d),f,1),i=a=1}s=c,i+=2}}function $(e){return String(e.s*e.s/0)}function W(e,t){var n,r,i;for((n=t.indexOf("."))>-1&&(t=t.replace(".","")),(r=t.search(/e/i))>0?(n<0&&(n=r),n+=+t.slice(r+1),t=t.substring(0,r)):n<0&&(n=t.length),r=0;48===t.charCodeAt(r);r++);for(i=t.length;48===t.charCodeAt(i-1);--i);if(t=t.slice(r,i)){if(i-=r,e.e=n=n-r-1,e.d=[],r=(n+1)%7,n<0&&(r+=7),r<i){for(r&&e.d.push(+t.slice(0,r)),i-=7;r<i;)e.d.push(+t.slice(r,r+=7));r=7-(t=t.slice(r)).length}else r-=i;for(;r--;)t+="0";e.d.push(+t),m&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function Z(e,t){var n,r,i,u,o,a,s,c,f;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),C.test(t))return W(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(x.test(t))n=16,t=t.toLowerCase();else if(b.test(t))n=2;else{if(!A.test(t))throw Error(v+t);n=8}for((u=t.search(/p/i))>0?(s=+t.slice(u+1),t=t.substring(2,u)):t=t.slice(2),o=(u=t.indexOf("."))>=0,r=e.constructor,o&&(u=(a=(t=t.replace(".","")).length)-u,i=q(r,new r(n),u,2*u)),u=f=(c=T(t,n,F)).length-1;0===c[u];--u)c.pop();return u<0?new r(0*e.s):(e.e=P(c,f),e.d=c,m=!1,o&&(e=R(e,i,4*a)),s&&(e=e.times(Math.abs(s)<54?w(2,s):ke.pow(2,s))),m=!0,e)}function J(e,t,n,r,i){var u,o,a,s,c=e.precision,f=Math.ceil(c/7);for(m=!1,s=n.times(n),a=new e(r);;){if(o=R(a.times(s),new e(t++*t++),c,1),a=i?r.plus(o):r.minus(o),r=R(o.times(s),new e(t++*t++),c,1),void 0!==(o=a.plus(r)).d[f]){for(u=f;o.d[u]===a.d[u]&&u--;);if(-1==u)break}u=a,a=r,r=o,o=u}return m=!0,o.d.length=f+1,o}function Q(e,t){for(var n=e;--t;)n*=e;return n}function X(e,t){var n,r=t.s<0,i=L(e,e.precision,1),u=i.times(.5);if((t=t.abs()).lte(u))return a=r?4:1,t;if((n=t.divToInt(i)).isZero())a=r?3:2;else{if((t=t.minus(n.times(i))).lte(u))return a=H(n)?r?2:3:r?4:1,t;a=H(n)?r?1:4:r?3:2}return t.minus(i).abs()}function K(e,t,n,r){var i,u,a,s,l,p,h,m,d,v=e.constructor,g=void 0!==n;if(g?(O(n,1,c),void 0===r?r=v.rounding:O(r,0,8)):(n=v.precision,r=v.rounding),e.isFinite()){for(g?(i=2,16==t?n=4*n-3:8==t&&(n=3*n-2)):i=t,(a=(h=z(e)).indexOf("."))>=0&&(h=h.replace(".",""),(d=new v(1)).e=h.length-a,d.d=T(z(d),10,i),d.e=d.d.length),u=l=(m=T(h,10,i)).length;0==m[--l];)m.pop();if(m[0]){if(a<0?u--:((e=new v(e)).d=m,e.e=u,m=(e=R(e,d,n,r,0,i)).d,u=e.e,p=o),a=m[n],s=i/2,p=p||void 0!==m[n+1],p=r<4?(void 0!==a||p)&&(0===r||r===(e.s<0?3:2)):a>s||a===s&&(4===r||p||6===r&&1&m[n-1]||r===(e.s<0?8:7)),m.length=n,p)for(;++m[--n]>i-1;)m[n]=0,n||(++u,m.unshift(1));for(l=m.length;!m[l-1];--l);for(a=0,h="";a<l;a++)h+=f.charAt(m[a]);if(g){if(l>1)if(16==t||8==t){for(a=16==t?4:3,--l;l%a;l++)h+="0";for(l=(m=T(h,i,t)).length;!m[l-1];--l);for(a=1,h="1.";a<l;a++)h+=f.charAt(m[a])}else h=h.charAt(0)+"."+h.slice(1);h=h+(u<0?"p":"p+")+u}else if(u<0){for(;++u;)h="0"+h;h="0."+h}else if(++u>l)for(u-=l;u--;)h+="0";else u<l&&(h=h.slice(0,u)+"."+h.slice(u))}else h=g?"0p+0":"0";h=(16==t?"0x":2==t?"0b":8==t?"0o":"")+h}else h=$(e);return e.s<0?"-"+h:h}function ee(e,t){if(e.length>t)return e.length=t,!0}function te(e){return new this(e).abs()}function ne(e){return new this(e).acos()}function re(e){return new this(e).acosh()}function ie(e,t){return new this(e).plus(t)}function ue(e){return new this(e).asin()}function oe(e){return new this(e).asinh()}function ae(e){return new this(e).atan()}function se(e){return new this(e).atanh()}function ce(e,t){e=new this(e),t=new this(t);var n,r=this.precision,i=this.rounding,u=r+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(n=t.s<0?L(this,r,i):new this(0)).s=e.s:!e.d||t.isZero()?(n=L(this,u,1).times(.5)).s=e.s:t.s<0?(this.precision=u,this.rounding=1,n=this.atan(R(e,t,u,1)),t=L(this,u,1),this.precision=r,this.rounding=i,n=e.s<0?n.minus(t):n.plus(t)):n=this.atan(R(e,t,u,1)):(n=L(this,u,1).times(t.s>0?.25:.75)).s=e.s:n=new this(NaN),n}function fe(e){return new this(e).cbrt()}function le(e){return I(e=new this(e),e.e+1,2)}function pe(e,t,n){return new this(e).clamp(t,n)}function he(e){if(!e||"object"!=typeof e)throw Error(d+"Object expected");var t,n,r,i=!0===e.defaults,u=["precision",1,c,"rounding",0,8,"toExpNeg",-s,0,"toExpPos",0,s,"maxE",0,s,"minE",-s,0,"modulo",0,9];for(t=0;t<u.length;t+=3)if(n=u[t],i&&(this[n]=h[n]),void 0!==(r=e[n])){if(!(E(r)===r&&r>=u[t+1]&&r<=u[t+2]))throw Error(v+n+": "+r);this[n]=r}if(n="crypto",i&&(this[n]=h[n]),void 0!==(r=e[n])){if(!0!==r&&!1!==r&&0!==r&&1!==r)throw Error(v+n+": "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw Error(D);this[n]=!0}else this[n]=!1}return this}function me(e){return new this(e).cos()}function de(e){return new this(e).cosh()}function ve(e,t){return new this(e).div(t)}function ge(e){return new this(e).exp()}function De(e){return I(e=new this(e),e.e+1,3)}function ye(){var e,t,n=new this(0);for(m=!1,e=0;e<arguments.length;)if((t=new this(arguments[e++])).d)n.d&&(n=n.plus(t.times(t)));else{if(t.s)return m=!0,new this(1/0);n=t}return m=!0,n.sqrt()}function Ee(e){return e instanceof ke||e&&e.toStringTag===y||!1}function we(e){return new this(e).ln()}function be(e,t){return new this(e).log(t)}function xe(e){return new this(e).log(2)}function Ae(e){return new this(e).log(10)}function Ce(){return G(this,arguments,-1)}function Fe(){return G(this,arguments,1)}function Ne(e,t){return new this(e).mod(t)}function _e(e,t){return new this(e).mul(t)}function Me(e,t){return new this(e).pow(t)}function Se(e){var t,n,r,i,u=0,o=new this(1),a=[];if(void 0===e?e=this.precision:O(e,1,c),r=Math.ceil(e/7),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(r));u<r;)(i=t[u])>=429e7?t[u]=crypto.getRandomValues(new Uint32Array(1))[0]:a[u++]=i%1e7;else{if(!crypto.randomBytes)throw Error(D);for(t=crypto.randomBytes(r*=4);u<r;)(i=t[u]+(t[u+1]<<8)+(t[u+2]<<16)+((127&t[u+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,u):(a.push(i%1e7),u+=4);u=r/4}else for(;u<r;)a[u++]=1e7*Math.random()|0;for(e%=7,(r=a[--u])&&e&&(i=w(10,7-e),a[u]=(r/i|0)*i);0===a[u];u--)a.pop();if(u<0)n=0,a=[0];else{for(n=-1;0===a[0];n-=7)a.shift();for(r=1,i=a[0];i>=10;i/=10)r++;r<7&&(n-=7-r)}return o.e=n,o.d=a,o}function Oe(e){return I(e=new this(e),e.e+1,this.rounding)}function Be(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function Te(e){return new this(e).sin()}function Re(e){return new this(e).sinh()}function Ie(e){return new this(e).sqrt()}function ze(e,t){return new this(e).sub(t)}function Pe(){var e=0,t=arguments,n=new this(t[e]);for(m=!1;n.s&&++e<t.length;)n=n.plus(t[e]);return m=!0,I(n,this.precision,this.rounding)}function Ue(e){return new this(e).tan()}function Le(e){return new this(e).tanh()}function je(e){return I(e=new this(e),e.e+1,1)}M[Symbol.for("nodejs.util.inspect.custom")]=M.toString,M[Symbol.toStringTag]="Decimal";var ke=M.constructor=function e(t){var n,r,i;function u(e){var t,n,r,i=this;if(!(i instanceof u))return new u(e);if(i.constructor=u,Ee(e))return i.s=e.s,void(m?!e.d||e.e>u.maxE?(i.e=NaN,i.d=null):e.e<u.minE?(i.e=0,i.d=[0]):(i.e=e.e,i.d=e.d.slice()):(i.e=e.e,i.d=e.d?e.d.slice():e.d));if("number"===(r=typeof e)){if(0===e)return i.s=1/e<0?-1:1,i.e=0,void(i.d=[0]);if(e<0?(e=-e,i.s=-1):i.s=1,e===~~e&&e<1e7){for(t=0,n=e;n>=10;n/=10)t++;return void(m?t>u.maxE?(i.e=NaN,i.d=null):t<u.minE?(i.e=0,i.d=[0]):(i.e=t,i.d=[e]):(i.e=t,i.d=[e]))}return 0*e!=0?(e||(i.s=NaN),i.e=NaN,void(i.d=null)):W(i,e.toString())}if("string"===r)return 45===(n=e.charCodeAt(0))?(e=e.slice(1),i.s=-1):(43===n&&(e=e.slice(1)),i.s=1),C.test(e)?W(i,e):Z(i,e);if("bigint"===r)return e<0?(e=-e,i.s=-1):i.s=1,W(i,e.toString());throw Error(v+e)}if(u.prototype=M,u.ROUND_UP=0,u.ROUND_DOWN=1,u.ROUND_CEIL=2,u.ROUND_FLOOR=3,u.ROUND_HALF_UP=4,u.ROUND_HALF_DOWN=5,u.ROUND_HALF_EVEN=6,u.ROUND_HALF_CEIL=7,u.ROUND_HALF_FLOOR=8,u.EUCLID=9,u.config=u.set=he,u.clone=e,u.isDecimal=Ee,u.abs=te,u.acos=ne,u.acosh=re,u.add=ie,u.asin=ue,u.asinh=oe,u.atan=ae,u.atanh=se,u.atan2=ce,u.cbrt=fe,u.ceil=le,u.clamp=pe,u.cos=me,u.cosh=de,u.div=ve,u.exp=ge,u.floor=De,u.hypot=ye,u.ln=we,u.log=be,u.log10=Ae,u.log2=xe,u.max=Ce,u.min=Fe,u.mod=Ne,u.mul=_e,u.pow=Me,u.random=Se,u.round=Oe,u.sign=Be,u.sin=Te,u.sinh=Re,u.sqrt=Ie,u.sub=ze,u.sum=Pe,u.tan=Ue,u.tanh=Le,u.trunc=je,void 0===t&&(t={}),t&&!0!==t.defaults)for(i=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],n=0;n<i.length;)t.hasOwnProperty(r=i[n++])||(t[r]=this[r]);return u.config(t),u}(h);l=new ke(l),p=new ke(p);const qe=ke;function He(e){return"number"==typeof e}function Ge(e){return!(!e||"object"!=typeof e||"function"!=typeof e.constructor)&&(!0===e.isBigNumber&&"object"==typeof e.constructor.prototype&&!0===e.constructor.prototype.isBigNumber||"function"==typeof e.constructor.isDecimal&&!0===e.constructor.isDecimal(e))}function Ve(e){return e&&"object"==typeof e&&!0===Object.getPrototypeOf(e).isComplex||!1}function Ye(e){return e&&"object"==typeof e&&!0===Object.getPrototypeOf(e).isFraction||!1}function $e(e){return e&&!0===e.constructor.prototype.isUnit||!1}function We(e){return"string"==typeof e}var Ze=Array.isArray;function Je(e){return e&&!0===e.constructor.prototype.isMatrix||!1}function Qe(e){return Array.isArray(e)||Je(e)}function Xe(e){return e&&e.isDenseMatrix&&!0===e.constructor.prototype.isMatrix||!1}function Ke(e){return e&&e.isSparseMatrix&&!0===e.constructor.prototype.isMatrix||!1}function et(e){return e&&!0===e.constructor.prototype.isRange||!1}function tt(e){return e&&!0===e.constructor.prototype.isIndex||!1}function nt(e){return"boolean"==typeof e}function rt(e){return e&&!0===e.constructor.prototype.isResultSet||!1}function it(e){return e&&!0===e.constructor.prototype.isHelp||!1}function ut(e){return"function"==typeof e}function ot(e){return e instanceof Date}function at(e){return e instanceof RegExp}function st(e){return!(!e||"object"!=typeof e||e.constructor!==Object||Ve(e)||Ye(e))}function ct(e){return null===e}function ft(e){return void 0===e}function lt(e){return e&&!0===e.isAccessorNode&&!0===e.constructor.prototype.isNode||!1}function pt(e){return e&&!0===e.isArrayNode&&!0===e.constructor.prototype.isNode||!1}function ht(e){return e&&!0===e.isAssignmentNode&&!0===e.constructor.prototype.isNode||!1}function mt(e){return e&&!0===e.isBlockNode&&!0===e.constructor.prototype.isNode||!1}function dt(e){return e&&!0===e.isConditionalNode&&!0===e.constructor.prototype.isNode||!1}function vt(e){return e&&!0===e.isConstantNode&&!0===e.constructor.prototype.isNode||!1}function gt(e){return e&&!0===e.isFunctionAssignmentNode&&!0===e.constructor.prototype.isNode||!1}function Dt(e){return e&&!0===e.isFunctionNode&&!0===e.constructor.prototype.isNode||!1}function yt(e){return e&&!0===e.isIndexNode&&!0===e.constructor.prototype.isNode||!1}function Et(e){return e&&!0===e.isNode&&!0===e.constructor.prototype.isNode||!1}function wt(e){return e&&!0===e.isObjectNode&&!0===e.constructor.prototype.isNode||!1}function bt(e){return e&&!0===e.isOperatorNode&&!0===e.constructor.prototype.isNode||!1}function xt(e){return e&&!0===e.isParenthesisNode&&!0===e.constructor.prototype.isNode||!1}function At(e){return e&&!0===e.isRangeNode&&!0===e.constructor.prototype.isNode||!1}function Ct(e){return e&&!0===e.isRelationalNode&&!0===e.constructor.prototype.isNode||!1}function Ft(e){return e&&!0===e.isSymbolNode&&!0===e.constructor.prototype.isNode||!1}function Nt(e){return e&&!0===e.constructor.prototype.isChain||!1}function _t(e){var t=typeof e;return"object"===t?null===e?"null":Ge(e)?"BigNumber":e.constructor&&e.constructor.name?e.constructor.name:"Object":t}function Mt(e){var t=typeof e;if("number"===t||"string"===t||"boolean"===t||null==e)return e;if("function"==typeof e.clone)return e.clone();if(Array.isArray(e))return e.map((function(e){return Mt(e)}));if(e instanceof Date)return new Date(e.valueOf());if(Ge(e))return e;if(st(e))return function(e,t){var n={};for(var r in e)Bt(e,r)&&(n[r]=t(e[r]));return n}(e,Mt);throw new TypeError("Cannot clone: unknown type of value (value: ".concat(e,")"))}function St(e,t){for(var n in t)Bt(t,n)&&(e[n]=t[n]);return e}function Ot(e,t){var n,r,i;if(Array.isArray(e)){if(!Array.isArray(t))return!1;if(e.length!==t.length)return!1;for(r=0,i=e.length;r<i;r++)if(!Ot(e[r],t[r]))return!1;return!0}if("function"==typeof e)return e===t;if(e instanceof Object){if(Array.isArray(t)||!(t instanceof Object))return!1;for(n in e)if(!(n in t)||!Ot(e[n],t[n]))return!1;for(n in t)if(!(n in e))return!1;return!0}return e===t}function Bt(e,t){return e&&Object.hasOwnProperty.call(e,t)}function Tt(e,t,n,r){function i(r){var i=function(e,t){for(var n={},r=0;r<t.length;r++){var i=t[r],u=e[i];void 0!==u&&(n[i]=u)}return n}(r,t.map(Rt));return function(e,t,n){var r=t.filter((e=>!function(e){return e&&"?"===e[0]}(e))).every((e=>void 0!==n[e]));if(!r){var i=t.filter((e=>void 0===n[e]));throw new Error('Cannot create function "'.concat(e,'", ')+"some dependencies are missing: ".concat(i.map((e=>'"'.concat(e,'"'))).join(", "),"."))}}(e,t,r),n(i)}return i.isFactory=!0,i.fn=e,i.dependencies=t.slice().sort(),r&&(i.meta=r),i}function Rt(e){return e&&"?"===e[0]?e.slice(1):e}var It=Tt("BigNumber",["?on","config"],(e=>{var{on:t,config:n}=e,r=qe.clone({precision:n.precision,modulo:qe.EUCLID});return r.prototype=Object.create(r.prototype),r.prototype.type="BigNumber",r.prototype.isBigNumber=!0,r.prototype.toJSON=function(){return{mathjs:"BigNumber",value:this.toString()}},r.fromJSON=function(e){return new r(e.value)},t&&t("config",(function(e,t){e.precision!==t.precision&&r.config({precision:e.precision})})),r}),{isClass:!0});const zt=Math.cosh||function(e){return Math.abs(e)<1e-9?1-e:.5*(Math.exp(e)+Math.exp(-e))},Pt=Math.sinh||function(e){return Math.abs(e)<1e-9?e:.5*(Math.exp(e)-Math.exp(-e))},Ut=function(e,t){return(e=Math.abs(e))<(t=Math.abs(t))&&([e,t]=[t,e]),e<1e8?Math.sqrt(e*e+t*t):(t/=e,e*Math.sqrt(1+t*t))},Lt=function(){throw SyntaxError("Invalid Param")};function jt(e,t){const n=Math.abs(e),r=Math.abs(t);return 0===e?Math.log(r):0===t?Math.log(n):n<3e3&&r<3e3?.5*Math.log(e*e+t*t):(e*=.5,t*=.5,.5*Math.log(e*e+t*t)+Math.LN2)}const kt={re:0,im:0},qt=function(e,t){const n=kt;if(null==e)n.re=n.im=0;else if(void 0!==t)n.re=e,n.im=t;else switch(typeof e){case"object":if("im"in e&&"re"in e)n.re=e.re,n.im=e.im;else if("abs"in e&&"arg"in e){if(!isFinite(e.abs)&&isFinite(e.arg))return Ht.INFINITY;n.re=e.abs*Math.cos(e.arg),n.im=e.abs*Math.sin(e.arg)}else if("r"in e&&"phi"in e){if(!isFinite(e.r)&&isFinite(e.phi))return Ht.INFINITY;n.re=e.r*Math.cos(e.phi),n.im=e.r*Math.sin(e.phi)}else 2===e.length?(n.re=e[0],n.im=e[1]):Lt();break;case"string":n.im=n.re=0;const t=e.replace(/_/g,"").match(/\d+\.?\d*e[+-]?\d+|\d+\.?\d*|\.\d+|./g);let r=1,i=0;null===t&&Lt();for(let e=0;e<t.length;e++){const u=t[e];" "===u||"\t"===u||"\n"===u||("+"===u?r++:"-"===u?i++:"i"===u||"I"===u?(r+i===0&&Lt()," "===t[e+1]||isNaN(t[e+1])?n.im+=parseFloat((i%2?"-":"")+"1"):(n.im+=parseFloat((i%2?"-":"")+t[e+1]),e++),r=i=0):((r+i===0||isNaN(u))&&Lt(),"i"===t[e+1]||"I"===t[e+1]?(n.im+=parseFloat((i%2?"-":"")+u),e++):n.re+=parseFloat((i%2?"-":"")+u),r=i=0))}r+i>0&&Lt();break;case"number":n.im=0,n.re=e;break;default:Lt()}return isNaN(n.re)||isNaN(n.im),n};function Ht(e,t){if(!(this instanceof Ht))return new Ht(e,t);const n=qt(e,t);this.re=n.re,this.im=n.im}function Gt(e){return"boolean"==typeof e||!!isFinite(e)&&e===Math.round(e)}Ht.prototype={re:0,im:0,sign:function(){const e=Ut(this.re,this.im);return new Ht(this.re/e,this.im/e)},add:function(e,t){const n=qt(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im));return r||i?r&&i?Ht.NAN:Ht.INFINITY:new Ht(this.re+n.re,this.im+n.im)},sub:function(e,t){const n=qt(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im));return r||i?r&&i?Ht.NAN:Ht.INFINITY:new Ht(this.re-n.re,this.im-n.im)},mul:function(e,t){const n=qt(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im)),u=0===this.re&&0===this.im,o=0===n.re&&0===n.im;return r&&o||i&&u?Ht.NAN:r||i?Ht.INFINITY:0===n.im&&0===this.im?new Ht(this.re*n.re,0):new Ht(this.re*n.re-this.im*n.im,this.re*n.im+this.im*n.re)},div:function(e,t){const n=qt(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im)),u=0===this.re&&0===this.im,o=0===n.re&&0===n.im;if(u&&o||r&&i)return Ht.NAN;if(o||r)return Ht.INFINITY;if(u||i)return Ht.ZERO;if(0===n.im)return new Ht(this.re/n.re,this.im/n.re);if(Math.abs(n.re)<Math.abs(n.im)){const e=n.re/n.im,t=n.re*e+n.im;return new Ht((this.re*e+this.im)/t,(this.im*e-this.re)/t)}{const e=n.im/n.re,t=n.im*e+n.re;return new Ht((this.re+this.im*e)/t,(this.im-this.re*e)/t)}},pow:function(e,t){const n=qt(e,t),r=0===this.re&&0===this.im;if(0===n.re&&0===n.im)return Ht.ONE;if(0===n.im){if(0===this.im&&this.re>0)return new Ht(Math.pow(this.re,n.re),0);if(0===this.re)switch((n.re%4+4)%4){case 0:return new Ht(Math.pow(this.im,n.re),0);case 1:return new Ht(0,Math.pow(this.im,n.re));case 2:return new Ht(-Math.pow(this.im,n.re),0);case 3:return new Ht(0,-Math.pow(this.im,n.re))}}if(r&&n.re>0)return Ht.ZERO;const i=Math.atan2(this.im,this.re),u=jt(this.re,this.im);let o=Math.exp(n.re*u-n.im*i),a=n.im*u+n.re*i;return new Ht(o*Math.cos(a),o*Math.sin(a))},sqrt:function(){const e=this.re,t=this.im;if(0===t)return e>=0?new Ht(Math.sqrt(e),0):new Ht(0,Math.sqrt(-e));const n=Ut(e,t);let r=Math.sqrt(.5*(n+Math.abs(e))),i=Math.abs(t)/(2*r);return e>=0?new Ht(r,t<0?-i:i):new Ht(i,t<0?-r:r)},exp:function(){const e=Math.exp(this.re);return 0===this.im?new Ht(e,0):new Ht(e*Math.cos(this.im),e*Math.sin(this.im))},expm1:function(){const e=this.re,t=this.im;return new Ht(Math.expm1(e)*Math.cos(t)+function(e){const t=Math.PI/4;if(-t>e||e>t)return Math.cos(e)-1;const n=e*e;return n*(n*(n*(n*(n*(n*(n*(n/20922789888e3-1/87178291200)+1/479001600)-1/3628800)+1/40320)-1/720)+1/24)-.5)}(t),Math.exp(e)*Math.sin(t))},log:function(){const e=this.re,t=this.im;return 0===t&&e>0?new Ht(Math.log(e),0):new Ht(jt(e,t),Math.atan2(t,e))},abs:function(){return Ut(this.re,this.im)},arg:function(){return Math.atan2(this.im,this.re)},sin:function(){const e=this.re,t=this.im;return new Ht(Math.sin(e)*zt(t),Math.cos(e)*Pt(t))},cos:function(){const e=this.re,t=this.im;return new Ht(Math.cos(e)*zt(t),-Math.sin(e)*Pt(t))},tan:function(){const e=2*this.re,t=2*this.im,n=Math.cos(e)+zt(t);return new Ht(Math.sin(e)/n,Pt(t)/n)},cot:function(){const e=2*this.re,t=2*this.im,n=Math.cos(e)-zt(t);return new Ht(-Math.sin(e)/n,Pt(t)/n)},sec:function(){const e=this.re,t=this.im,n=.5*zt(2*t)+.5*Math.cos(2*e);return new Ht(Math.cos(e)*zt(t)/n,Math.sin(e)*Pt(t)/n)},csc:function(){const e=this.re,t=this.im,n=.5*zt(2*t)-.5*Math.cos(2*e);return new Ht(Math.sin(e)*zt(t)/n,-Math.cos(e)*Pt(t)/n)},asin:function(){const e=this.re,t=this.im,n=new Ht(t*t-e*e+1,-2*e*t).sqrt(),r=new Ht(n.re-t,n.im+e).log();return new Ht(r.im,-r.re)},acos:function(){const e=this.re,t=this.im,n=new Ht(t*t-e*e+1,-2*e*t).sqrt(),r=new Ht(n.re-t,n.im+e).log();return new Ht(Math.PI/2-r.im,r.re)},atan:function(){const e=this.re,t=this.im;if(0===e){if(1===t)return new Ht(0,1/0);if(-1===t)return new Ht(0,-1/0)}const n=e*e+(1-t)*(1-t),r=new Ht((1-t*t-e*e)/n,-2*e/n).log();return new Ht(-.5*r.im,.5*r.re)},acot:function(){const e=this.re,t=this.im;if(0===t)return new Ht(Math.atan2(1,e),0);const n=e*e+t*t;return 0!==n?new Ht(e/n,-t/n).atan():new Ht(0!==e?e/0:0,0!==t?-t/0:0).atan()},asec:function(){const e=this.re,t=this.im;if(0===e&&0===t)return new Ht(0,1/0);const n=e*e+t*t;return 0!==n?new Ht(e/n,-t/n).acos():new Ht(0!==e?e/0:0,0!==t?-t/0:0).acos()},acsc:function(){const e=this.re,t=this.im;if(0===e&&0===t)return new Ht(Math.PI/2,1/0);const n=e*e+t*t;return 0!==n?new Ht(e/n,-t/n).asin():new Ht(0!==e?e/0:0,0!==t?-t/0:0).asin()},sinh:function(){const e=this.re,t=this.im;return new Ht(Pt(e)*Math.cos(t),zt(e)*Math.sin(t))},cosh:function(){const e=this.re,t=this.im;return new Ht(zt(e)*Math.cos(t),Pt(e)*Math.sin(t))},tanh:function(){const e=2*this.re,t=2*this.im,n=zt(e)+Math.cos(t);return new Ht(Pt(e)/n,Math.sin(t)/n)},coth:function(){const e=2*this.re,t=2*this.im,n=zt(e)-Math.cos(t);return new Ht(Pt(e)/n,-Math.sin(t)/n)},csch:function(){const e=this.re,t=this.im,n=Math.cos(2*t)-zt(2*e);return new Ht(-2*Pt(e)*Math.cos(t)/n,2*zt(e)*Math.sin(t)/n)},sech:function(){const e=this.re,t=this.im,n=Math.cos(2*t)+zt(2*e);return new Ht(2*zt(e)*Math.cos(t)/n,-2*Pt(e)*Math.sin(t)/n)},asinh:function(){let e=this.im;this.im=-this.re,this.re=e;const t=this.asin();return this.re=-this.im,this.im=e,e=t.re,t.re=-t.im,t.im=e,t},acosh:function(){const e=this.acos();if(e.im<=0){const t=e.re;e.re=-e.im,e.im=t}else{const t=e.im;e.im=-e.re,e.re=t}return e},atanh:function(){const e=this.re,t=this.im,n=e>1&&0===t,r=1-e,i=1+e,u=r*r+t*t,o=0!==u?new Ht((i*r-t*t)/u,(t*r+i*t)/u):new Ht(-1!==e?e/0:0,0!==t?t/0:0),a=o.re;return o.re=jt(o.re,o.im)/2,o.im=Math.atan2(o.im,a)/2,n&&(o.im=-o.im),o},acoth:function(){const e=this.re,t=this.im;if(0===e&&0===t)return new Ht(0,Math.PI/2);const n=e*e+t*t;return 0!==n?new Ht(e/n,-t/n).atanh():new Ht(0!==e?e/0:0,0!==t?-t/0:0).atanh()},acsch:function(){const e=this.re,t=this.im;if(0===t)return new Ht(0!==e?Math.log(e+Math.sqrt(e*e+1)):1/0,0);const n=e*e+t*t;return 0!==n?new Ht(e/n,-t/n).asinh():new Ht(0!==e?e/0:0,0!==t?-t/0:0).asinh()},asech:function(){const e=this.re,t=this.im;if(this.isZero())return Ht.INFINITY;const n=e*e+t*t;return 0!==n?new Ht(e/n,-t/n).acosh():new Ht(0!==e?e/0:0,0!==t?-t/0:0).acosh()},inverse:function(){if(this.isZero())return Ht.INFINITY;if(this.isInfinite())return Ht.ZERO;const e=this.re,t=this.im,n=e*e+t*t;return new Ht(e/n,-t/n)},conjugate:function(){return new Ht(this.re,-this.im)},neg:function(){return new Ht(-this.re,-this.im)},ceil:function(e){return e=Math.pow(10,e||0),new Ht(Math.ceil(this.re*e)/e,Math.ceil(this.im*e)/e)},floor:function(e){return e=Math.pow(10,e||0),new Ht(Math.floor(this.re*e)/e,Math.floor(this.im*e)/e)},round:function(e){return e=Math.pow(10,e||0),new Ht(Math.round(this.re*e)/e,Math.round(this.im*e)/e)},equals:function(e,t){const n=qt(e,t);return Math.abs(n.re-this.re)<=Ht.EPSILON&&Math.abs(n.im-this.im)<=Ht.EPSILON},clone:function(){return new Ht(this.re,this.im)},toString:function(){let e=this.re,t=this.im,n="";return this.isNaN()?"NaN":this.isInfinite()?"Infinity":(Math.abs(e)<Ht.EPSILON&&(e=0),Math.abs(t)<Ht.EPSILON&&(t=0),0===t?n+e:(0!==e?(n+=e,n+=" ",t<0?(t=-t,n+="-"):n+="+",n+=" "):t<0&&(t=-t,n+="-"),1!==t&&(n+=t),n+"i"))},toVector:function(){return[this.re,this.im]},valueOf:function(){return 0===this.im?this.re:null},isNaN:function(){return isNaN(this.re)||isNaN(this.im)},isZero:function(){return 0===this.im&&0===this.re},isFinite:function(){return isFinite(this.re)&&isFinite(this.im)},isInfinite:function(){return!this.isFinite()}},Ht.ZERO=new Ht(0,0),Ht.ONE=new Ht(1,0),Ht.I=new Ht(0,1),Ht.PI=new Ht(Math.PI,0),Ht.E=new Ht(Math.E,0),Ht.INFINITY=new Ht(1/0,1/0),Ht.NAN=new Ht(NaN,NaN),Ht.EPSILON=1e-15;var Vt=Math.sign||function(e){return e>0?1:e<0?-1:0},Yt=Math.log2||function(e){return Math.log(e)/Math.LN2},$t=Math.log10||function(e){return Math.log(e)/Math.LN10},Wt=(Math.log1p,Math.cbrt||function(e){if(0===e)return e;var t,n=e<0;return n&&(e=-e),t=isFinite(e)?(e/((t=Math.exp(Math.log(e)/3))*t)+2*t)/3:e,n?-t:t}),Zt=Math.expm1||function(e){return e>=2e-4||e<=-2e-4?Math.exp(e)-1:e+e*e/2+e*e*e/6};function Jt(e,t,n){var r={2:"0b",8:"0o",16:"0x"}[t],i="";if(n){if(n<1)throw new Error("size must be in greater than 0");if(!Gt(n))throw new Error("size must be an integer");if(e>2**(n-1)-1||e<-(2**(n-1)))throw new Error("Value must be in range [-2^".concat(n-1,", 2^").concat(n-1,"-1]"));if(!Gt(e))throw new Error("Value must be an integer");e<0&&(e+=2**n),i="i".concat(n)}var u="";return e<0&&(e=-e,u="-"),"".concat(u).concat(r).concat(e.toString(t)).concat(i)}function Qt(e,t){if("function"==typeof t)return t(e);if(e===1/0)return"Infinity";if(e===-1/0)return"-Infinity";if(isNaN(e))return"NaN";var{notation:n,precision:r,wordSize:i}=Xt(t);switch(n){case"fixed":return en(e,r);case"exponential":return tn(e,r);case"engineering":return function(e,t){if(isNaN(e)||!isFinite(e))return String(e);var n=Kt(e),r=nn(n,t),i=r.exponent,u=r.coefficients,o=i%3==0?i:i<0?i-3-i%3:i-i%3;if(He(t))for(;t>u.length||i-o+1>u.length;)u.push(0);else for(var a=Math.abs(i-o)-(u.length-1),s=0;s<a;s++)u.push(0);var c=Math.abs(i-o),f=1;for(;c>0;)f++,c--;var l=u.slice(f).join(""),p=He(t)&&l.length||l.match(/[1-9]/)?"."+l:"",h=u.slice(0,f).join("")+p+"e"+(i>=0?"+":"")+o.toString();return r.sign+h}(e,r);case"bin":return Jt(e,2,i);case"oct":return Jt(e,8,i);case"hex":return Jt(e,16,i);case"auto":return function(e,t,n){if(isNaN(e)||!isFinite(e))return String(e);var r=pn(null==n?void 0:n.lowerExp,-3),i=pn(null==n?void 0:n.upperExp,5),u=Kt(e),o=t?nn(u,t):u;if(o.exponent<r||o.exponent>=i)return tn(e,t);var a=o.coefficients,s=o.exponent;a.length<t&&(a=a.concat(rn(t-a.length))),a=a.concat(rn(s-a.length+1+(a.length<t?t-a.length:0)));var c=s>0?s:0;return c<(a=rn(-s).concat(a)).length-1&&a.splice(c+1,0,"."),o.sign+a.join("")}(e,r,t).replace(/((\.\d*?)(0+))($|e)/,(function(){var e=arguments[2],t=arguments[4];return"."!==e?e+t:t}));default:throw new Error('Unknown notation "'+n+'". Choose "auto", "exponential", "fixed", "bin", "oct", or "hex.')}}function Xt(e){var t,n,r="auto";if(void 0!==e)if(He(e))t=e;else if(Ge(e))t=e.toNumber();else{if(!st(e))throw new Error("Unsupported type of options, number, BigNumber, or object expected");void 0!==e.precision&&(t=ln(e.precision,(()=>{throw new Error('Option "precision" must be a number or BigNumber')}))),void 0!==e.wordSize&&(n=ln(e.wordSize,(()=>{throw new Error('Option "wordSize" must be a number or BigNumber')}))),e.notation&&(r=e.notation)}return{notation:r,precision:t,wordSize:n}}function Kt(e){var t=String(e).toLowerCase().match(/^(-?)(\d+\.?\d*)(e([+-]?\d+))?$/);if(!t)throw new SyntaxError("Invalid number "+e);var n=t[1],r=t[2],i=parseFloat(t[4]||"0"),u=r.indexOf(".");i+=-1!==u?u-1:r.length-1;var o=r.replace(".","").replace(/^0*/,(function(e){return i-=e.length,""})).replace(/0*$/,"").split("").map((function(e){return parseInt(e)}));return 0===o.length&&(o.push(0),i++),{sign:n,coefficients:o,exponent:i}}function en(e,t){if(isNaN(e)||!isFinite(e))return String(e);var n=Kt(e),r="number"==typeof t?nn(n,n.exponent+1+t):n,i=r.coefficients,u=r.exponent+1,o=u+(t||0);return i.length<o&&(i=i.concat(rn(o-i.length))),u<0&&(i=rn(1-u).concat(i),u=1),u<i.length&&i.splice(u,0,0===u?"0.":"."),r.sign+i.join("")}function tn(e,t){if(isNaN(e)||!isFinite(e))return String(e);var n=Kt(e),r=t?nn(n,t):n,i=r.coefficients,u=r.exponent;i.length<t&&(i=i.concat(rn(t-i.length)));var o=i.shift();return r.sign+o+(i.length>0?"."+i.join(""):"")+"e"+(u>=0?"+":"")+u}function nn(e,t){for(var n={sign:e.sign,coefficients:e.coefficients,exponent:e.exponent},r=n.coefficients;t<=0;)r.unshift(0),n.exponent++,t++;if(r.length>t&&r.splice(t,r.length-t)[0]>=5){var i=t-1;for(r[i]++;10===r[i];)r.pop(),0===i&&(r.unshift(0),n.exponent++,i++),r[--i]++}return n}function rn(e){for(var t=[],n=0;n<e;n++)t.push(0);return t}var un=Number.EPSILON||2220446049250313e-31;function on(e,t,n){if(null==n)return e===t;if(e===t)return!0;if(isNaN(e)||isNaN(t))return!1;if(isFinite(e)&&isFinite(t)){var r=Math.abs(e-t);return r<=un||r<=Math.max(Math.abs(e),Math.abs(t))*n}return!1}var an=Math.acosh||function(e){return Math.log(Math.sqrt(e*e-1)+e)},sn=Math.asinh||function(e){return Math.log(Math.sqrt(e*e+1)+e)},cn=Math.atanh||function(e){return Math.log((1+e)/(1-e))/2},fn=(Math.cosh,Math.sinh||function(e){return(Math.exp(e)-Math.exp(-e))/2});Math.tanh;function ln(e,t){return He(e)?e:Ge(e)?e.toNumber():void t()}function pn(e,t){return He(e)?e:Ge(e)?e.toNumber():t}var hn=Tt("Complex",[],(()=>(Object.defineProperty(Ht,"name",{value:"Complex"}),Ht.prototype.constructor=Ht,Ht.prototype.type="Complex",Ht.prototype.isComplex=!0,Ht.prototype.toJSON=function(){return{mathjs:"Complex",re:this.re,im:this.im}},Ht.prototype.toPolar=function(){return{r:this.abs(),phi:this.arg()}},Ht.prototype.format=function(e){var t=this.im,n=this.re,r=Qt(this.re,e),i=Qt(this.im,e),u=He(e)?e:e?e.precision:null;if(null!==u){var o=Math.pow(10,-u);Math.abs(n/t)<o&&(n=0),Math.abs(t/n)<o&&(t=0)}return 0===t?r:0===n?1===t?"i":-1===t?"-i":i+"i":t<0?-1===t?r+" - i":r+" - "+i.substring(1)+"i":1===t?r+" + i":r+" + "+i+"i"},Ht.fromPolar=function(e){switch(arguments.length){case 1:var t=arguments[0];if("object"==typeof t)return Ht(t);throw new TypeError("Input has to be an object with r and phi keys.");case 2:var n=arguments[0],r=arguments[1];if(He(n)){if($e(r)&&r.hasBase("ANGLE")&&(r=r.toNumber("rad")),He(r))return new Ht({r:n,phi:r});throw new TypeError("Phi is not a number nor an angle unit.")}throw new TypeError("Radius r is not a number.");default:throw new SyntaxError("Wrong number of arguments in function fromPolar")}},Ht.prototype.valueOf=Ht.prototype.toString,Ht.fromJSON=function(e){return new Ht(e)},Ht.compare=function(e,t){return e.re>t.re?1:e.re<t.re?-1:e.im>t.im?1:e.im<t.im?-1:0},Ht)),{isClass:!0});function mn(e){var t=0,n=1,r=Object.create(null),i=Object.create(null),u=0,o=function(e){var o=i[e];if(o&&(delete r[o],delete i[e],--t,n===o)){if(!t)return u=0,void(n=1);for(;!Object.prototype.hasOwnProperty.call(r,++n););}};return e=Math.abs(e),{hit:function(a){var s=i[a],c=++u;if(r[c]=a,i[a]=c,!s){if(++t<=e)return;return a=r[n],o(a),a}if(delete r[s],n===s)for(;!Object.prototype.hasOwnProperty.call(r,++n););},delete:o,clear:function(){t=u=0,n=1,r=Object.create(null),i=Object.create(null)}}}function dn(e){var{hasher:t,limit:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n=null==n?Number.POSITIVE_INFINITY:n,t=null==t?JSON.stringify:t,function r(){"object"!=typeof r.cache&&(r.cache={values:new Map,lru:mn(n||Number.POSITIVE_INFINITY)});for(var i=[],u=0;u<arguments.length;u++)i[u]=arguments[u];var o=t(i);if(r.cache.values.has(o))return r.cache.lru.hit(o),r.cache.values.get(o);var a=e.apply(e,i);return r.cache.values.set(o,a),r.cache.values.delete(r.cache.lru.hit(o)),a}}function vn(e){return Object.keys(e.signatures||{}).reduce((function(e,t){var n=(t.match(/,/g)||[]).length+1;return Math.max(e,n)}),-1)}dn((function(e){return new e(1).exp()}),{hasher:Dn}),dn((function(e){return new e(1).plus(new e(5).sqrt()).div(2)}),{hasher:Dn});var gn=dn((function(e){return e.acos(-1)}),{hasher:Dn});dn((function(e){return gn(e).times(2)}),{hasher:Dn});function Dn(e){return e[0].precision}Math.PI,Math.PI,Math.E;yn("fineStructure",.0072973525693),yn("weakMixingAngle",.2229),yn("efimovFactor",22.7),yn("sackurTetrode",-1.16487052358);function yn(e,t){return Tt(e,["config","BigNumber"],(e=>{var{config:n,BigNumber:r}=e;return"BigNumber"===n.number?new r(t):t}))}var En=n(1377),wn=Tt("Fraction",[],(()=>(Object.defineProperty(En,"name",{value:"Fraction"}),En.prototype.constructor=En,En.prototype.type="Fraction",En.prototype.isFraction=!0,En.prototype.toJSON=function(){return{mathjs:"Fraction",n:this.s*this.n,d:this.d}},En.fromJSON=function(e){return new En(e)},En)),{isClass:!0}),bn=Tt("Matrix",[],(()=>{function e(){if(!(this instanceof e))throw new SyntaxError("Constructor must be called with the new operator")}return e.prototype.type="Matrix",e.prototype.isMatrix=!0,e.prototype.storage=function(){throw new Error("Cannot invoke storage on a Matrix interface")},e.prototype.datatype=function(){throw new Error("Cannot invoke datatype on a Matrix interface")},e.prototype.create=function(e,t){throw new Error("Cannot invoke create on a Matrix interface")},e.prototype.subset=function(e,t,n){throw new Error("Cannot invoke subset on a Matrix interface")},e.prototype.get=function(e){throw new Error("Cannot invoke get on a Matrix interface")},e.prototype.set=function(e,t,n){throw new Error("Cannot invoke set on a Matrix interface")},e.prototype.resize=function(e,t){throw new Error("Cannot invoke resize on a Matrix interface")},e.prototype.reshape=function(e,t){throw new Error("Cannot invoke reshape on a Matrix interface")},e.prototype.clone=function(){throw new Error("Cannot invoke clone on a Matrix interface")},e.prototype.size=function(){throw new Error("Cannot invoke size on a Matrix interface")},e.prototype.map=function(e,t){throw new Error("Cannot invoke map on a Matrix interface")},e.prototype.forEach=function(e){throw new Error("Cannot invoke forEach on a Matrix interface")},e.prototype[Symbol.iterator]=function(){throw new Error("Cannot iterate a Matrix interface")},e.prototype.toArray=function(){throw new Error("Cannot invoke toArray on a Matrix interface")},e.prototype.valueOf=function(){throw new Error("Cannot invoke valueOf on a Matrix interface")},e.prototype.format=function(e){throw new Error("Cannot invoke format on a Matrix interface")},e.prototype.toString=function(){throw new Error("Cannot invoke toString on a Matrix interface")},e}),{isClass:!0});function xn(e,t,n){var r=new(0,e.constructor)(2),i="";if(n){if(n<1)throw new Error("size must be in greater than 0");if(!Gt(n))throw new Error("size must be an integer");if(e.greaterThan(r.pow(n-1).sub(1))||e.lessThan(r.pow(n-1).mul(-1)))throw new Error("Value must be in range [-2^".concat(n-1,", 2^").concat(n-1,"-1]"));if(!e.isInteger())throw new Error("Value must be an integer");e.lessThan(0)&&(e=e.add(r.pow(n))),i="i".concat(n)}switch(t){case 2:return"".concat(e.toBinary()).concat(i);case 8:return"".concat(e.toOctal()).concat(i);case 16:return"".concat(e.toHexadecimal()).concat(i);default:throw new Error("Base ".concat(t," not supported "))}}function An(e,t){if("function"==typeof t)return t(e);if(!e.isFinite())return e.isNaN()?"NaN":e.gt(0)?"Infinity":"-Infinity";var{notation:n,precision:r,wordSize:i}=Xt(t);switch(n){case"fixed":return function(e,t){return e.toFixed(t)}(e,r);case"exponential":return Cn(e,r);case"engineering":return function(e,t){var n=e.e,r=n%3==0?n:n<0?n-3-n%3:n-n%3,i=e.mul(Math.pow(10,-r)),u=i.toPrecision(t);if(u.includes("e")){u=new(0,e.constructor)(u).toFixed()}return u+"e"+(n>=0?"+":"")+r.toString()}(e,r);case"bin":return xn(e,2,i);case"oct":return xn(e,8,i);case"hex":return xn(e,16,i);case"auto":var u=Fn(null==t?void 0:t.lowerExp,-3),o=Fn(null==t?void 0:t.upperExp,5);if(e.isZero())return"0";var a=e.toSignificantDigits(r),s=a.e;return(s>=u&&s<o?a.toFixed():Cn(e,r)).replace(/((\.\d*?)(0+))($|e)/,(function(){var e=arguments[2],t=arguments[4];return"."!==e?e+t:t}));default:throw new Error('Unknown notation "'+n+'". Choose "auto", "exponential", "fixed", "bin", "oct", or "hex.')}}function Cn(e,t){return void 0!==t?e.toExponential(t-1):e.toExponential()}function Fn(e,t){return He(e)?e:Ge(e)?e.toNumber():t}function Nn(e,t){var n=e.length-t.length,r=e.length;return e.substring(n,r)===t}function _n(e,t){var n=function(e,t){if("number"==typeof e)return Qt(e,t);if(Ge(e))return An(e,t);if(function(e){return e&&"object"==typeof e&&"number"==typeof e.s&&"number"==typeof e.n&&"number"==typeof e.d||!1}(e))return t&&"decimal"===t.fraction?e.toString():e.s*e.n+"/"+e.d;if(Array.isArray(e))return On(e,t);if(We(e))return Mn(e);if("function"==typeof e)return e.syntax?String(e.syntax):"function";if(e&&"object"==typeof e){return"function"==typeof e.format?e.format(t):e&&e.toString(t)!=={}.toString()?e.toString(t):"{"+Object.keys(e).map((n=>Mn(n)+": "+_n(e[n],t))).join(", ")+"}"}return String(e)}(e,t);return t&&"object"==typeof t&&"truncate"in t&&n.length>t.truncate?n.substring(0,t.truncate-3)+"...":n}function Mn(e){for(var t=String(e),n="",r=0;r<t.length;){var i=t.charAt(r);n+=i in Sn?Sn[i]:i,r++}return'"'+n+'"'}var Sn={'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t"};function On(e,t){if(Array.isArray(e)){for(var n="[",r=e.length,i=0;i<r;i++)0!==i&&(n+=", "),n+=On(e[i],t);return n+="]"}return _n(e,t)}function Bn(e,t){if(!We(e))throw new TypeError("Unexpected type of argument in function compareText (expected: string or Array or Matrix, actual: "+_t(e)+", index: 0)");if(!We(t))throw new TypeError("Unexpected type of argument in function compareText (expected: string or Array or Matrix, actual: "+_t(t)+", index: 1)");return e===t?0:e>t?1:-1}function Tn(e,t,n){if(!(this instanceof Tn))throw new SyntaxError("Constructor must be called with the new operator");this.actual=e,this.expected=t,this.relation=n,this.message="Dimension mismatch ("+(Array.isArray(e)?"["+e.join(", ")+"]":e)+" "+(this.relation||"!=")+" "+(Array.isArray(t)?"["+t.join(", ")+"]":t)+")",this.stack=(new Error).stack}function Rn(e,t,n){if(!(this instanceof Rn))throw new SyntaxError("Constructor must be called with the new operator");this.index=e,arguments.length<3?(this.min=0,this.max=t):(this.min=t,this.max=n),void 0!==this.min&&this.index<this.min?this.message="Index out of range ("+this.index+" < "+this.min+")":void 0!==this.max&&this.index>=this.max?this.message="Index out of range ("+this.index+" > "+(this.max-1)+")":this.message="Index out of range ("+this.index+")",this.stack=(new Error).stack}function In(e){for(var t=[];Array.isArray(e);)t.push(e.length),e=e[0];return t}function zn(e,t,n){var r,i=e.length;if(i!==t[n])throw new Tn(i,t[n]);if(n<t.length-1){var u=n+1;for(r=0;r<i;r++){var o=e[r];if(!Array.isArray(o))throw new Tn(t.length-1,t.length,"<");zn(e[r],t,u)}}else for(r=0;r<i;r++)if(Array.isArray(e[r]))throw new Tn(t.length+1,t.length,">")}function Pn(e,t){if(0===t.length){if(Array.isArray(e))throw new Tn(e.length,0)}else zn(e,t,0)}function Un(e,t){if(void 0!==e){if(!He(e)||!Gt(e))throw new TypeError("Index must be an integer (value: "+e+")");if(e<0||"number"==typeof t&&e>=t)throw new Rn(e,t)}}function Ln(e,t,n){if(!Array.isArray(t))throw new TypeError("Array expected");if(0===t.length)throw new Error("Resizing to scalar is not supported");return t.forEach((function(e){if(!He(e)||!Gt(e)||e<0)throw new TypeError("Invalid size, must contain positive integers (size: "+_n(t)+")")})),(He(e)||Ge(e))&&(e=[e]),jn(e,t,0,void 0!==n?n:0),e}function jn(e,t,n,r){var i,u,o=e.length,a=t[n],s=Math.min(o,a);if(e.length=a,n<t.length-1){var c=n+1;for(i=0;i<s;i++)u=e[i],Array.isArray(u)||(u=[u],e[i]=u),jn(u,t,c,r);for(i=s;i<a;i++)u=[],e[i]=u,jn(u,t,c,r)}else{for(i=0;i<s;i++)for(;Array.isArray(e[i]);)e[i]=e[i][0];for(i=s;i<a;i++)e[i]=r}}function kn(e,t){var n=Yn(e),r=n.length;if(!Array.isArray(e)||!Array.isArray(t))throw new TypeError("Array expected");if(0===t.length)throw new Tn(0,r,"!=");var i=Hn(t=qn(t,r));if(r!==i)throw new Tn(i,r,"!=");try{return function(e,t){for(var n,r=e,i=t.length-1;i>0;i--){var u=t[i];n=[];for(var o=r.length/u,a=0;a<o;a++)n.push(r.slice(a*u,(a+1)*u));r=n}return r}(n,t)}catch(e){if(e instanceof Tn)throw new Tn(i,r,"!=");throw e}}function qn(e,t){var n=Hn(e),r=e.slice(),i=e.indexOf(-1);if(e.indexOf(-1,i+1)>=0)throw new Error("More than one wildcard in sizes");if(i>=0){if(!(t%n==0))throw new Error("Could not replace wildcard, since "+t+" is no multiple of "+-n);r[i]=-t/n}return r}function Hn(e){return e.reduce(((e,t)=>e*t),1)}function Gn(e,t,n,r){var i=r||In(e);if(n)for(var u=0;u<n;u++)e=[e],i.unshift(1);for(e=Vn(e,t,0);i.length<t;)i.push(1);return e}function Vn(e,t,n){var r,i;if(Array.isArray(e)){var u=n+1;for(r=0,i=e.length;r<i;r++)e[r]=Vn(e[r],t,u)}else for(var o=n;o<t;o++)e=[e];return e}function Yn(e){if(!Array.isArray(e))return e;var t=[];return e.forEach((function e(n){Array.isArray(n)?n.forEach(e):t.push(n)})),t}function $n(e,t){for(var n,r=0,i=0;i<e.length;i++){var u=e[i],o=Array.isArray(u);if(0===i&&o&&(r=u.length),o&&u.length!==r)return;var a=o?$n(u,t):t(u);if(void 0===n)n=a;else if(n!==a)return"mixed"}return n}function Wn(e,t,n,r){if(r<n){if(e.length!==t.length)throw new Tn(e.length,t.length);for(var i=[],u=0;u<e.length;u++)i[u]=Wn(e[u],t[u],n,r+1);return i}return e.concat(t)}function Zn(){var e=Array.prototype.slice.call(arguments,0,-1),t=Array.prototype.slice.call(arguments,-1);if(1===e.length)return e[0];if(e.length>1)return e.slice(1).reduce((function(e,n){return Wn(e,n,t,0)}),e[0]);throw new Error("Wrong number of arguments in function concat")}function Jn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var r=t.map((e=>e.length)),i=Math.max(...r),u=new Array(i).fill(null),o=0;o<t.length;o++)for(var a=t[o],s=r[o],c=0;c<s;c++){var f=i-s+c;a[c]>u[f]&&(u[f]=a[c])}for(var l=0;l<t.length;l++)Qn(t[l],u);return u}function Qn(e,t){for(var n=t.length,r=e.length,i=0;i<r;i++){var u=n-r+i;if(e[i]<t[u]&&e[i]>1||e[i]>t[u])throw new Error("shape missmatch: missmatch is found in arg with shape (".concat(e,") not possible to broadcast dimension ").concat(r," with size ").concat(e[i]," to size ").concat(t[u]))}}function Xn(t,n){var r=In(t);if(Ot(r,n))return t;Qn(r,n);var i,u,o,a=Jn(r,n),s=a.length,c=[...Array(s-r.length).fill(1),...r],f=function(t){return e([],t)}(t);r.length<s&&(r=In(f=kn(f,c)));for(var l=0;l<s;l++)r[l]<a[l]&&(i=f,u=a[l],o=l,r=In(f=Zn(...Array(u).fill(i),o)));return f}Tn.prototype=new RangeError,Tn.prototype.constructor=RangeError,Tn.prototype.name="DimensionError",Tn.prototype.isDimensionError=!0,Rn.prototype=new RangeError,Rn.prototype.constructor=RangeError,Rn.prototype.name="IndexError",Rn.prototype.isIndexError=!0;var Kn=Tt("DenseMatrix",["Matrix"],(e=>{var{Matrix:t}=e;function n(e,t){if(!(this instanceof n))throw new SyntaxError("Constructor must be called with the new operator");if(t&&!We(t))throw new Error("Invalid datatype: "+t);if(Je(e))"DenseMatrix"===e.type?(this._data=Mt(e._data),this._size=Mt(e._size),this._datatype=t||e._datatype):(this._data=e.toArray(),this._size=e.size(),this._datatype=t||e._datatype);else if(e&&Ze(e.data)&&Ze(e.size))this._data=e.data,this._size=e.size,Pn(this._data,this._size),this._datatype=t||e.datatype;else if(Ze(e))this._data=a(e),this._size=In(this._data),Pn(this._data,this._size),this._datatype=t;else{if(e)throw new TypeError("Unsupported type of data ("+_t(e)+")");this._data=[],this._size=[0],this._datatype=t}}function r(e,t,n,i){var u=i===n-1,o=t.dimension(i);return u?o.map((function(t){return Un(t,e.length),e[t]})).valueOf():o.map((function(u){return Un(u,e.length),r(e[u],t,n,i+1)})).valueOf()}function i(e,t,n,r,u){var o=u===r-1,a=t.dimension(u);o?a.forEach((function(t,r){Un(t),e[t]=n[r[0]]})):a.forEach((function(o,a){Un(o),i(e[o],t,n[a[0]],r,u+1)}))}function u(e,t,n){if(0===t.length){for(var r=e._data;Ze(r);)r=r[0];return r}return e._size=t.slice(0),e._data=Ln(e._data,e._size,n),e}function o(e,t,n){for(var r=e._size.slice(0),i=!1;r.length<t.length;)r.push(0),i=!0;for(var o=0,a=t.length;o<a;o++)t[o]>r[o]&&(r[o]=t[o],i=!0);i&&u(e,r,n)}function a(e){return Je(e)?a(e.valueOf()):Ze(e)?e.map(a):e}return n.prototype=new t,n.prototype.createDenseMatrix=function(e,t){return new n(e,t)},Object.defineProperty(n,"name",{value:"DenseMatrix"}),n.prototype.constructor=n,n.prototype.type="DenseMatrix",n.prototype.isDenseMatrix=!0,n.prototype.getDataType=function(){return $n(this._data,_t)},n.prototype.storage=function(){return"dense"},n.prototype.datatype=function(){return this._datatype},n.prototype.create=function(e,t){return new n(e,t)},n.prototype.subset=function(e,t,u){switch(arguments.length){case 1:return function(e,t){if(!tt(t))throw new TypeError("Invalid index");var i=t.isScalar();if(i)return e.get(t.min());var u=t.size();if(u.length!==e._size.length)throw new Tn(u.length,e._size.length);for(var o=t.min(),a=t.max(),s=0,c=e._size.length;s<c;s++)Un(o[s],e._size[s]),Un(a[s],e._size[s]);return new n(r(e._data,t,u.length,0),e._datatype)}(this,e);case 2:case 3:return function(e,t,n,r){if(!t||!0!==t.isIndex)throw new TypeError("Invalid index");var u,a=t.size(),s=t.isScalar();Je(n)?(u=n.size(),n=n.valueOf()):u=In(n);if(s){if(0!==u.length)throw new TypeError("Scalar expected");e.set(t.min(),n,r)}else{if(!Ot(u,a))try{u=In(n=0===u.length?Xn([n],a):Xn(n,a))}catch(e){}if(a.length<e._size.length)throw new Tn(a.length,e._size.length,"<");if(u.length<a.length){for(var c=0,f=0;1===a[c]&&1===u[c];)c++;for(;1===a[c];)f++,c++;n=Gn(n,a.length,f,u)}if(!Ot(a,u))throw new Tn(a,u,">");var l=t.max().map((function(e){return e+1}));o(e,l,r);var p=a.length,h=0;i(e._data,t,n,p,h)}return e}(this,e,t,u);default:throw new SyntaxError("Wrong number of arguments")}},n.prototype.get=function(e){if(!Ze(e))throw new TypeError("Array expected");if(e.length!==this._size.length)throw new Tn(e.length,this._size.length);for(var t=0;t<e.length;t++)Un(e[t],this._size[t]);for(var n=this._data,r=0,i=e.length;r<i;r++){var u=e[r];Un(u,n.length),n=n[u]}return n},n.prototype.set=function(e,t,n){if(!Ze(e))throw new TypeError("Array expected");if(e.length<this._size.length)throw new Tn(e.length,this._size.length,"<");var r,i,u,a=e.map((function(e){return e+1}));o(this,a,n);var s=this._data;for(r=0,i=e.length-1;r<i;r++)Un(u=e[r],s.length),s=s[u];return Un(u=e[e.length-1],s.length),s[u]=t,this},n.prototype.resize=function(e,t,n){if(!Qe(e))throw new TypeError("Array or Matrix expected");var r=e.valueOf().map((e=>Array.isArray(e)&&1===e.length?e[0]:e));return u(n?this.clone():this,r,t)},n.prototype.reshape=function(e,t){var n=t?this.clone():this;n._data=kn(n._data,e);var r=n._size.reduce(((e,t)=>e*t));return n._size=qn(e,r),n},n.prototype.clone=function(){return new n({data:Mt(this._data),size:Mt(this._size),datatype:this._datatype})},n.prototype.size=function(){return this._size.slice(0)},n.prototype.map=function(e){var t=this,r=vn(e),i=function n(i,u){return Ze(i)?i.map((function(e,t){return n(e,u.concat(t))})):1===r?e(i):2===r?e(i,u):e(i,u,t)}(this._data,[]);return new n(i,void 0!==this._datatype?$n(i,_t):void 0)},n.prototype.forEach=function(e){var t=this;!function n(r,i){Ze(r)?r.forEach((function(e,t){n(e,i.concat(t))})):e(r,i,t)}(this._data,[])},n.prototype[Symbol.iterator]=function*(){yield*function*e(t,n){if(Ze(t))for(var r=0;r<t.length;r++)yield*e(t[r],n.concat(r));else yield{value:t,index:n}}(this._data,[])},n.prototype.rows=function(){var e=[];if(2!==this.size().length)throw new TypeError("Rows can only be returned for a 2D matrix.");var t=this._data;for(var r of t)e.push(new n([r],this._datatype));return e},n.prototype.columns=function(){var e=this,t=[],r=this.size();if(2!==r.length)throw new TypeError("Rows can only be returned for a 2D matrix.");for(var i=this._data,u=function(r){var u=i.map((e=>[e[r]]));t.push(new n(u,e._datatype))},o=0;o<r[1];o++)u(o);return t},n.prototype.toArray=function(){return Mt(this._data)},n.prototype.valueOf=function(){return this._data},n.prototype.format=function(e){return _n(this._data,e)},n.prototype.toString=function(){return _n(this._data)},n.prototype.toJSON=function(){return{mathjs:"DenseMatrix",data:this._data,size:this._size,datatype:this._datatype}},n.prototype.diagonal=function(e){if(e){if(Ge(e)&&(e=e.toNumber()),!He(e)||!Gt(e))throw new TypeError("The parameter k must be an integer number")}else e=0;for(var t=e>0?e:0,r=e<0?-e:0,i=this._size[0],u=this._size[1],o=Math.min(i-r,u-t),a=[],s=0;s<o;s++)a[s]=this._data[s+r][s+t];return new n({data:a,size:[o],datatype:this._datatype})},n.diagonal=function(e,t,r,i){if(!Ze(e))throw new TypeError("Array expected, size parameter");if(2!==e.length)throw new Error("Only two dimensions matrix are supported");if(e=e.map((function(e){if(Ge(e)&&(e=e.toNumber()),!He(e)||!Gt(e)||e<1)throw new Error("Size values must be positive integers");return e})),r){if(Ge(r)&&(r=r.toNumber()),!He(r)||!Gt(r))throw new TypeError("The parameter k must be an integer number")}else r=0;var u,o=r>0?r:0,a=r<0?-r:0,s=e[0],c=e[1],f=Math.min(s-a,c-o);if(Ze(t)){if(t.length!==f)throw new Error("Invalid value array length");u=function(e){return t[e]}}else if(Je(t)){var l=t.size();if(1!==l.length||l[0]!==f)throw new Error("Invalid matrix length");u=function(e){return t.get([e])}}else u=function(){return t};i||(i=Ge(u(0))?u(0).mul(0):0);var p=[];if(e.length>0){p=Ln(p,e,i);for(var h=0;h<f;h++)p[h+a][h+o]=u(h)}return new n({data:p,size:[s,c]})},n.fromJSON=function(e){return new n(e)},n.prototype.swapRows=function(e,t){if(!(He(e)&&Gt(e)&&He(t)&&Gt(t)))throw new Error("Row index must be positive integers");if(2!==this._size.length)throw new Error("Only two dimensional matrix is supported");return Un(e,this._size[0]),Un(t,this._size[0]),n._swapRows(e,t,this._data),this},n._swapRows=function(e,t,n){var r=n[e];n[e]=n[t],n[t]=r},n}),{isClass:!0}),er=n(2369);function tr(e,t){if(ur(e)&&rr(e,t))return e[t];if("function"==typeof e[t]&&ir(e,t))throw new Error('Cannot access method "'+t+'" as a property');throw new Error('No access to property "'+t+'"')}function nr(e,t,n){if(ur(e)&&rr(e,t))return e[t]=n,n;throw new Error('No access to property "'+t+'"')}function rr(e,t){return!(!e||"object"!=typeof e)&&(!!Bt(or,t)||!(t in Object.prototype)&&!(t in Function.prototype))}function ir(e,t){return null!=e&&"function"==typeof e[t]&&(!(Bt(e,t)&&Object.getPrototypeOf&&t in Object.getPrototypeOf(e))&&(!!Bt(ar,t)||!(t in Object.prototype)&&!(t in Function.prototype)))}function ur(e){return"object"==typeof e&&e&&e.constructor===Object}var or={length:!0,name:!0},ar={toString:!0,valueOf:!0,toLocaleString:!0};class sr{constructor(e){this.wrappedObject=e,this[Symbol.iterator]=this.entries}keys(){return Object.keys(this.wrappedObject).values()}get(e){return tr(this.wrappedObject,e)}set(e,t){return nr(this.wrappedObject,e,t),this}has(e){return t=this.wrappedObject,e in t;var t}entries(){return cr(this.keys(),(e=>[e,this.get(e)]))}forEach(e){for(var t of this.keys())e(this.get(t),t,this)}delete(e){delete this.wrappedObject[e]}clear(){for(var e of this.keys())this.delete(e)}get size(){return Object.keys(this.wrappedObject).length}}function cr(e,t){return{next:()=>{var n=e.next();return n.done?n:{value:t(n.value),done:!1}}}}function fr(e){return!!e&&(e instanceof Map||e instanceof sr||"function"==typeof e.set&&"function"==typeof e.get&&"function"==typeof e.keys&&"function"==typeof e.has)}var lr=function(){return lr=er.create,er},pr=Tt("typed",["?BigNumber","?Complex","?DenseMatrix","?Fraction"],(function(e){var{BigNumber:t,Complex:n,DenseMatrix:r,Fraction:i}=e,u=lr();return u.clear(),u.addTypes([{name:"number",test:He},{name:"Complex",test:Ve},{name:"BigNumber",test:Ge},{name:"Fraction",test:Ye},{name:"Unit",test:$e},{name:"identifier",test:e=>We&&/^(?:[A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDCD0-\uDCEB\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDCD0-\uDCEB\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])*$/.test(e)},{name:"string",test:We},{name:"Chain",test:Nt},{name:"Array",test:Ze},{name:"Matrix",test:Je},{name:"DenseMatrix",test:Xe},{name:"SparseMatrix",test:Ke},{name:"Range",test:et},{name:"Index",test:tt},{name:"boolean",test:nt},{name:"ResultSet",test:rt},{name:"Help",test:it},{name:"function",test:ut},{name:"Date",test:ot},{name:"RegExp",test:at},{name:"null",test:ct},{name:"undefined",test:ft},{name:"AccessorNode",test:lt},{name:"ArrayNode",test:pt},{name:"AssignmentNode",test:ht},{name:"BlockNode",test:mt},{name:"ConditionalNode",test:dt},{name:"ConstantNode",test:vt},{name:"FunctionNode",test:Dt},{name:"FunctionAssignmentNode",test:gt},{name:"IndexNode",test:yt},{name:"Node",test:Et},{name:"ObjectNode",test:wt},{name:"OperatorNode",test:bt},{name:"ParenthesisNode",test:xt},{name:"RangeNode",test:At},{name:"RelationalNode",test:Ct},{name:"SymbolNode",test:Ft},{name:"Map",test:fr},{name:"Object",test:st}]),u.addConversions([{from:"number",to:"BigNumber",convert:function(e){if(t||hr(e),e.toExponential().replace(/e.*$/,"").replace(/^0\.?0*|\./,"").length>15)throw new TypeError("Cannot implicitly convert a number with >15 significant digits to BigNumber (value: "+e+"). Use function bignumber(x) to convert to BigNumber.");return new t(e)}},{from:"number",to:"Complex",convert:function(e){return n||mr(e),new n(e,0)}},{from:"BigNumber",to:"Complex",convert:function(e){return n||mr(e),new n(e.toNumber(),0)}},{from:"Fraction",to:"BigNumber",convert:function(e){throw new TypeError("Cannot implicitly convert a Fraction to BigNumber or vice versa. Use function bignumber(x) to convert to BigNumber or fraction(x) to convert to Fraction.")}},{from:"Fraction",to:"Complex",convert:function(e){return n||mr(e),new n(e.valueOf(),0)}},{from:"number",to:"Fraction",convert:function(e){i||dr(e);var t=new i(e);if(t.valueOf()!==e)throw new TypeError("Cannot implicitly convert a number to a Fraction when there will be a loss of precision (value: "+e+"). Use function fraction(x) to convert to Fraction.");return t}},{from:"string",to:"number",convert:function(e){var t=Number(e);if(isNaN(t))throw new Error('Cannot convert "'+e+'" to a number');return t}},{from:"string",to:"BigNumber",convert:function(e){t||hr(e);try{return new t(e)}catch(t){throw new Error('Cannot convert "'+e+'" to BigNumber')}}},{from:"string",to:"Fraction",convert:function(e){i||dr(e);try{return new i(e)}catch(t){throw new Error('Cannot convert "'+e+'" to Fraction')}}},{from:"string",to:"Complex",convert:function(e){n||mr(e);try{return new n(e)}catch(t){throw new Error('Cannot convert "'+e+'" to Complex')}}},{from:"boolean",to:"number",convert:function(e){return+e}},{from:"boolean",to:"BigNumber",convert:function(e){return t||hr(e),new t(+e)}},{from:"boolean",to:"Fraction",convert:function(e){return i||dr(e),new i(+e)}},{from:"boolean",to:"string",convert:function(e){return String(e)}},{from:"Array",to:"Matrix",convert:function(e){return r||function(){throw new Error("Cannot convert array into a Matrix: no class 'DenseMatrix' provided")}(),new r(e)}},{from:"Matrix",to:"Array",convert:function(e){return e.valueOf()}}]),u.onMismatch=(e,t,n)=>{var r=u.createError(e,t,n);if(["wrongType","mismatch"].includes(r.data.category)&&1===t.length&&Qe(t[0])&&n.some((e=>!e.params.includes(",")))){var i=new TypeError("Function '".concat(e,"' doesn't apply to matrices. To call it ")+"elementwise on a matrix 'M', try 'map(M, ".concat(e,")'."));throw i.data=r.data,i}throw r},u.onMismatch=(e,t,n)=>{var r=u.createError(e,t,n);if(["wrongType","mismatch"].includes(r.data.category)&&1===t.length&&Qe(t[0])&&n.some((e=>!e.params.includes(",")))){var i=new TypeError("Function '".concat(e,"' doesn't apply to matrices. To call it ")+"elementwise on a matrix 'M', try 'map(M, ".concat(e,")'."));throw i.data=r.data,i}throw r},u}));function hr(e){throw new Error("Cannot convert value ".concat(e," into a BigNumber: no class 'BigNumber' provided"))}function mr(e){throw new Error("Cannot convert value ".concat(e," into a Complex number: no class 'Complex' provided"))}function dr(e){throw new Error("Cannot convert value ".concat(e," into a Fraction, no class 'Fraction' provided."))}function vr(e,t,n){return e&&"function"==typeof e.map?e.map((function(e){return vr(e,t,n)})):t(e)}var gr="number",Dr="number, number";function yr(e){return Math.abs(e)}function Er(e,t){return e+t}function wr(e,t){return e-t}function br(e,t){return e*t}function xr(e){return-e}function Ar(e){return e}function Cr(e){return Wt(e)}function Fr(e){return e*e*e}function Nr(e){return Math.exp(e)}function _r(e){return Zt(e)}function Mr(e,t){if(!Gt(e)||!Gt(t))throw new Error("Parameters in function lcm must be integer numbers");if(0===e||0===t)return 0;for(var n,r=e*t;0!==t;)t=e%(n=t),e=n;return Math.abs(r/e)}function Sr(e){return $t(e)}function Or(e){return Yt(e)}function Br(e){return Vt(e)}function Tr(e){return e*e}function Rr(e,t){var n,r,i,u=0,o=1,a=1,s=0;if(!Gt(e)||!Gt(t))throw new Error("Parameters in function xgcd must be integer numbers");for(;t;)i=e-(r=Math.floor(e/t))*t,n=u,u=o-r*u,o=n,n=a,a=s-r*a,s=n,e=t,t=i;return e<0?[-e,-o,-s]:[e,e?o:0,s]}function Ir(e,t){return e*e<1&&t===1/0||e*e>1&&t===-1/0?0:Math.pow(e,t)}function zr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!Gt(t)||t<0||t>15)throw new Error("Number of decimals in function round must be an integer from 0 to 15 inclusive");return parseFloat(en(e,t))}yr.signature=gr,Er.signature=Dr,wr.signature=Dr,br.signature=Dr,xr.signature=gr,Ar.signature=gr,Cr.signature=gr,Fr.signature=gr,Nr.signature=gr,_r.signature=gr,Mr.signature=Dr,Sr.signature=gr,Or.signature=gr,Br.signature=gr,Tr.signature=gr,Rr.signature=Dr,Ir.signature=Dr;var Pr=Tt("abs",["typed"],(e=>{var{typed:t}=e;return t("abs",{number:yr,"Complex | BigNumber | Fraction | Unit":e=>e.abs(),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0)))})})),Ur="number";function Lr(e){return an(e)}function jr(e){return Math.atan(1/e)}function kr(e){return isFinite(e)?(Math.log((e+1)/e)+Math.log(e/(e-1)))/2:0}function qr(e){return Math.asin(1/e)}function Hr(e){var t=1/e;return Math.log(t+Math.sqrt(t*t+1))}function Gr(e){return Math.acos(1/e)}function Vr(e){var t=1/e,n=Math.sqrt(t*t-1);return Math.log(n+t)}function Yr(e){return sn(e)}function $r(e){return cn(e)}function Wr(e){return 1/Math.tan(e)}function Zr(e){var t=Math.exp(2*e);return(t+1)/(t-1)}function Jr(e){return 1/Math.sin(e)}function Qr(e){return 0===e?Number.POSITIVE_INFINITY:Math.abs(2/(Math.exp(e)-Math.exp(-e)))*Vt(e)}function Xr(e){return 1/Math.cos(e)}function Kr(e){return 2/(Math.exp(e)+Math.exp(-e))}function ei(e){return fn(e)}Lr.signature=Ur,jr.signature=Ur,kr.signature=Ur,qr.signature=Ur,Hr.signature=Ur,Gr.signature=Ur,Vr.signature=Ur,Yr.signature=Ur,$r.signature=Ur,Wr.signature=Ur,Zr.signature=Ur,Jr.signature=Ur,Qr.signature=Ur,Xr.signature=Ur,Kr.signature=Ur,ei.signature=Ur;var ti="addScalar",ni=Tt(ti,["typed"],(e=>{var{typed:t}=e;return t(ti,{"number, number":Er,"Complex, Complex":function(e,t){return e.add(t)},"BigNumber, BigNumber":function(e,t){return e.plus(t)},"Fraction, Fraction":function(e,t){return e.add(t)},"Unit, Unit":t.referToSelf((e=>(n,r)=>{if(null===n.value||void 0===n.value)throw new Error("Parameter x contains a unit with undefined value");if(null===r.value||void 0===r.value)throw new Error("Parameter y contains a unit with undefined value");if(!n.equalBase(r))throw new Error("Units do not match");var i=n.clone();return i.value=t.find(e,[i.valueType(),r.valueType()])(i.value,r.value),i.fixPrefix=!1,i}))})})),ri=Tt("bignumber",["typed","BigNumber"],(e=>{var{typed:t,BigNumber:n}=e;return t("bignumber",{"":function(){return new n(0)},number:function(e){return new n(e+"")},string:function(e){var t=e.match(/(0[box][0-9a-fA-F]*)i([0-9]*)/);if(t){var r=t[2],i=n(t[1]),u=new n(2).pow(Number(r));if(i.gt(u.sub(1)))throw new SyntaxError('String "'.concat(e,'" is out of range'));var o=new n(2).pow(Number(r)-1);return i.gte(o)?i.sub(u):i}return new n(e)},BigNumber:function(e){return e},Unit:t.referToSelf((e=>t=>{var n=t.clone();return n.value=e(t.value),n})),Fraction:function(e){return new n(e.n).div(e.d).times(e.s)},null:function(e){return new n(0)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})}));var ii="number, number";function ui(e,t){if(!Gt(e)||!Gt(t))throw new Error("Integers expected in function bitAnd");return e&t}function oi(e){if(!Gt(e))throw new Error("Integer expected in function bitNot");return~e}function ai(e,t){if(!Gt(e)||!Gt(t))throw new Error("Integers expected in function bitOr");return e|t}function si(e,t){if(!Gt(e)||!Gt(t))throw new Error("Integers expected in function bitXor");return e^t}function ci(e,t){if(!Gt(e)||!Gt(t))throw new Error("Integers expected in function leftShift");return e<<t}function fi(e,t){if(!Gt(e)||!Gt(t))throw new Error("Integers expected in function rightArithShift");return e>>t}function li(e,t){if(!Gt(e)||!Gt(t))throw new Error("Integers expected in function rightLogShift");return e>>>t}ui.signature=ii,oi.signature="number",ai.signature=ii,si.signature=ii,ci.signature=ii,fi.signature=ii,li.signature=ii;function pi(e,t){if(t<e)return 1;if(t===e)return t;var n=t+e>>1;return pi(e,n)*pi(n+1,t)}function hi(e,t){if(!Gt(e)||e<0)throw new TypeError("Positive integer value expected in function combinations");if(!Gt(t)||t<0)throw new TypeError("Positive integer value expected in function combinations");if(t>e)throw new TypeError("k must be less than or equal to n");for(var n=e-t,r=1,i=2,u=t<n?t:n,o=t<n?n+1:t+1;o<=e;++o)for(r*=o;i<=u&&r%i==0;)r/=i,++i;return i<=u&&(r/=pi(i,u)),r}hi.signature="number, number";var mi="conj",di=Tt(mi,["typed"],(e=>{var{typed:t}=e;return t(mi,{"number | BigNumber | Fraction":e=>e,Complex:e=>e.conjugate(),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})}));function vi(e,t,n){if(null==n)return e.eq(t);if(e.eq(t))return!0;if(e.isNaN()||t.isNaN())return!1;if(e.isFinite()&&t.isFinite()){var r=e.minus(t).abs();if(r.isZero())return!0;var i=e.constructor.max(e.abs(),t.abs());return r.lte(i.times(n))}return!1}var gi=Tt("compareUnits",["typed"],(e=>{var{typed:t}=e;return{"Unit, Unit":t.referToSelf((e=>(n,r)=>{if(!n.equalBase(r))throw new Error("Cannot compare units with different base");return t.find(e,[n.valueType(),r.valueType()])(n.value,r.value)}))}})),Di="equalScalar",yi=Tt(Di,["typed","config"],(e=>{var{typed:t,config:n}=e,r=gi({typed:t});return t(Di,{"boolean, boolean":function(e,t){return e===t},"number, number":function(e,t){return on(e,t,n.epsilon)},"BigNumber, BigNumber":function(e,t){return e.eq(t)||vi(e,t,n.epsilon)},"Fraction, Fraction":function(e,t){return e.equals(t)},"Complex, Complex":function(e,t){return function(e,t,n){return on(e.re,t.re,n)&&on(e.im,t.im,n)}(e,t,n.epsilon)}},r)}));Tt(Di,["typed","config"],(e=>{var{typed:t,config:n}=e;return t(Di,{"number, number":function(e,t){return on(e,t,n.epsilon)}})})),Math.pow(2,53);var Ei="format",wi=Tt(Ei,["typed"],(e=>{var{typed:t}=e;return t(Ei,{any:_n,"any, Object | function | number | BigNumber":_n})})),bi=(Tt("hex",["typed","format"],(e=>{var{typed:t,format:n}=e;return t("hex",{"number | BigNumber":function(e){return n(e,{notation:"hex"})},"number | BigNumber, number | BigNumber":function(e,t){return n(e,{notation:"hex",wordSize:t})}})})),"isInteger"),xi=Tt(bi,["typed"],(e=>{var{typed:t}=e;return t(bi,{number:Gt,BigNumber:function(e){return e.isInt()},Fraction:function(e){return 1===e.d&&isFinite(e.n)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})})),Ai="number";function Ci(e){return e<0}function Fi(e){return e>0}function Ni(e){return 0===e}function _i(e){return Number.isNaN(e)}Ci.signature=Ai,Fi.signature=Ai,Ni.signature=Ai,_i.signature=Ai;var Mi="isZero",Si=Tt(Mi,["typed"],(e=>{var{typed:t}=e;return t(Mi,{number:Ni,BigNumber:function(e){return e.isZero()},Complex:function(e){return 0===e.re&&0===e.im},Fraction:function(e){return 1===e.d&&0===e.n},Unit:t.referToSelf((e=>n=>t.find(e,n.valueType())(n.value))),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})}));function Oi(e){var t;if(Gt(e))return e<=0?isFinite(e)?1/0:NaN:e>171?1/0:pi(1,e-1);if(e<.5)return Math.PI/(Math.sin(Math.PI*e)*Oi(1-e));if(e>=171.35)return 1/0;if(e>85){var n=e*e,r=n*e,i=r*e,u=i*e;return Math.sqrt(2*Math.PI/e)*Math.pow(e/Math.E,e)*(1+1/(12*e)+1/(288*n)-139/(51840*r)-571/(2488320*i)+163879/(209018880*u)+5246819/(75246796800*u*e))}--e,t=Ti[0];for(var o=1;o<Ti.length;++o)t+=Ti[o]/(e+o);var a=e+Bi+.5;return Math.sqrt(2*Math.PI)*Math.pow(a,e+.5)*Math.exp(-a)*t}Oi.signature="number";var Bi=4.7421875,Ti=[.9999999999999971,57.15623566586292,-59.59796035547549,14.136097974741746,-.4919138160976202,3399464998481189e-20,4652362892704858e-20,-9837447530487956e-20,.0001580887032249125,-.00021026444172410488,.00021743961811521265,-.0001643181065367639,8441822398385275e-20,-26190838401581408e-21,36899182659531625e-22],Ri=.9189385332046728,Ii=[1.000000000190015,76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18];function zi(e){if(e<0)return NaN;if(0===e)return 1/0;if(!isFinite(e))return e;if(e<.5)return Math.log(Math.PI/Math.sin(Math.PI*e))-zi(1-e);for(var t=(e-=1)+5+.5,n=Ii[0],r=6;r>=1;r--)n+=Ii[r]/(e+r);return Ri+(e+.5)*Math.log(t)-t+Math.log(n)}zi.signature="number";var Pi=Tt("multiplyScalar",["typed"],(e=>{var{typed:t}=e;return t("multiplyScalar",{"number, number":br,"Complex, Complex":function(e,t){return e.mul(t)},"BigNumber, BigNumber":function(e,t){return e.times(t)},"Fraction, Fraction":function(e,t){return e.mul(t)},"number | Fraction | BigNumber | Complex, Unit":(e,t)=>t.multiply(e),"Unit, number | Fraction | BigNumber | Complex | Unit":(e,t)=>e.multiply(t)})})),Ui="number, number";function Li(e){return!e}function ji(e,t){return!(!e&&!t)}function ki(e,t){return!!e!=!!t}function qi(e,t){return!(!e||!t)}Li.signature="number",ji.signature=Ui,ki.signature=Ui,qi.signature=Ui;var Hi=Tt("number",["typed"],(e=>{var{typed:t}=e,n=t("number",{"":function(){return 0},number:function(e){return e},string:function(e){if("NaN"===e)return NaN;var t,n,r=(n=(t=e).match(/(0[box])([0-9a-fA-F]*)\.([0-9a-fA-F]*)/))?{input:t,radix:{"0b":2,"0o":8,"0x":16}[n[1]],integerPart:n[2],fractionalPart:n[3]}:null;if(r)return function(e){for(var t=parseInt(e.integerPart,e.radix),n=0,r=0;r<e.fractionalPart.length;r++)n+=parseInt(e.fractionalPart[r],e.radix)/Math.pow(e.radix,r+1);var i=t+n;if(isNaN(i))throw new SyntaxError('String "'+e.input+'" is not a valid number');return i}(r);var i=0,u=e.match(/(0[box][0-9a-fA-F]*)i([0-9]*)/);u&&(i=Number(u[2]),e=u[1]);var o=Number(e);if(isNaN(o))throw new SyntaxError('String "'+e+'" is not a valid number');if(u){if(o>2**i-1)throw new SyntaxError('String "'.concat(e,'" is out of range'));o>=2**(i-1)&&(o-=2**i)}return o},BigNumber:function(e){return e.toNumber()},Fraction:function(e){return e.valueOf()},Unit:t.referToSelf((e=>t=>{var n=t.clone();return n.value=e(t.value),n})),null:function(e){return 0},"Unit, string | Unit":function(e,t){return e.toNumber(t)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))});return n.fromJSON=function(e){return parseFloat(e.value)},n})),Gi=(Tt("oct",["typed","format"],(e=>{var{typed:t,format:n}=e;return t("oct",{"number | BigNumber":function(e){return n(e,{notation:"oct"})},"number | BigNumber, number | BigNumber":function(e,t){return n(e,{notation:"oct",wordSize:t})}})})),n(7391));Date.now();var Vi=Tt("SparseMatrix",["typed","equalScalar","Matrix"],(e=>{var{typed:t,equalScalar:n,Matrix:r}=e;function i(e,t){if(!(this instanceof i))throw new SyntaxError("Constructor must be called with the new operator");if(t&&!We(t))throw new Error("Invalid datatype: "+t);if(Je(e))!function(e,t,n){"SparseMatrix"===t.type?(e._values=t._values?Mt(t._values):void 0,e._index=Mt(t._index),e._ptr=Mt(t._ptr),e._size=Mt(t._size),e._datatype=n||t._datatype):u(e,t.valueOf(),n||t._datatype)}(this,e,t);else if(e&&Ze(e.index)&&Ze(e.ptr)&&Ze(e.size))this._values=e.values,this._index=e.index,this._ptr=e.ptr,this._size=e.size,this._datatype=t||e.datatype;else if(Ze(e))u(this,e,t);else{if(e)throw new TypeError("Unsupported type of data ("+_t(e)+")");this._values=[],this._index=[],this._ptr=[0],this._size=[0,0],this._datatype=t}}function u(e,r,i){e._values=[],e._index=[],e._ptr=[],e._datatype=i;var u=r.length,o=0,a=n,s=0;if(We(i)&&(a=t.find(n,[i,i])||n,s=t.convert(0,i)),u>0){var c=0;do{e._ptr.push(e._index.length);for(var f=0;f<u;f++){var l=r[f];if(Ze(l)){if(0===c&&o<l.length&&(o=l.length),c<l.length){var p=l[c];a(p,s)||(e._values.push(p),e._index.push(f))}}else 0===c&&o<1&&(o=1),a(l,s)||(e._values.push(l),e._index.push(f))}c++}while(c<o)}e._ptr.push(e._index.length),e._size=[u,o]}function o(e,t,n,r){if(n-t==0)return n;for(var i=t;i<n;i++)if(r[i]===e)return i;return t}function a(e,t,n,r,i,u,o){i.splice(e,0,r),u.splice(e,0,t);for(var a=n+1;a<o.length;a++)o[a]++}function s(e,r,i,u){var o=u||0,a=n,s=0;We(e._datatype)&&(a=t.find(n,[e._datatype,e._datatype])||n,s=t.convert(0,e._datatype),o=t.convert(o,e._datatype));var c,f,l,p=!a(o,s),h=e._size[0],m=e._size[1];if(i>m){for(f=m;f<i;f++)if(e._ptr[f]=e._values.length,p)for(c=0;c<h;c++)e._values.push(o),e._index.push(c);e._ptr[i]=e._values.length}else i<m&&(e._ptr.splice(i+1,m-i),e._values.splice(e._ptr[i],e._values.length),e._index.splice(e._ptr[i],e._index.length));if(m=i,r>h){if(p){var d=0;for(f=0;f<m;f++){e._ptr[f]=e._ptr[f]+d,l=e._ptr[f+1]+d;var v=0;for(c=h;c<r;c++,v++)e._values.splice(l+v,0,o),e._index.splice(l+v,0,c),d++}e._ptr[m]=e._values.length}}else if(r<h){var g=0;for(f=0;f<m;f++){e._ptr[f]=e._ptr[f]-g;var D=e._ptr[f],y=e._ptr[f+1]-g;for(l=D;l<y;l++)(c=e._index[l])>r-1&&(e._values.splice(l,1),e._index.splice(l,1),g++)}e._ptr[f]=e._values.length}return e._size[0]=r,e._size[1]=i,e}function c(e,t,n,r,i){var u,o,a=r[0],s=r[1],c=[];for(u=0;u<a;u++)for(c[u]=[],o=0;o<s;o++)c[u][o]=0;for(o=0;o<s;o++)for(var f=n[o],l=n[o+1],p=f;p<l;p++)c[u=t[p]][o]=e?i?Mt(e[p]):e[p]:1;return c}return i.prototype=new r,i.prototype.createSparseMatrix=function(e,t){return new i(e,t)},Object.defineProperty(i,"name",{value:"SparseMatrix"}),i.prototype.constructor=i,i.prototype.type="SparseMatrix",i.prototype.isSparseMatrix=!0,i.prototype.getDataType=function(){return $n(this._values,_t)},i.prototype.storage=function(){return"sparse"},i.prototype.datatype=function(){return this._datatype},i.prototype.create=function(e,t){return new i(e,t)},i.prototype.density=function(){var e=this._size[0],t=this._size[1];return 0!==e&&0!==t?this._index.length/(e*t):0},i.prototype.subset=function(e,t,n){if(!this._values)throw new Error("Cannot invoke subset on a Pattern only matrix");switch(arguments.length){case 1:return function(e,t){if(!tt(t))throw new TypeError("Invalid index");if(t.isScalar())return e.get(t.min());var n,r,u,o,a=t.size();if(a.length!==e._size.length)throw new Tn(a.length,e._size.length);var s=t.min(),c=t.max();for(n=0,r=e._size.length;n<r;n++)Un(s[n],e._size[n]),Un(c[n],e._size[n]);var f=e._values,l=e._index,p=e._ptr,h=t.dimension(0),m=t.dimension(1),d=[],v=[];h.forEach((function(e,t){v[e]=t[0],d[e]=!0}));var g=f?[]:void 0,D=[],y=[];return m.forEach((function(e){for(y.push(D.length),u=p[e],o=p[e+1];u<o;u++)n=l[u],!0===d[n]&&(D.push(v[n]),g&&g.push(f[u]))})),y.push(D.length),new i({values:g,index:D,ptr:y,size:a,datatype:e._datatype})}(this,e);case 2:case 3:return function(e,t,n,r){if(!t||!0!==t.isIndex)throw new TypeError("Invalid index");var i,u=t.size(),o=t.isScalar();Je(n)?(i=n.size(),n=n.toArray()):i=In(n);if(o){if(0!==i.length)throw new TypeError("Scalar expected");e.set(t.min(),n,r)}else{if(1!==u.length&&2!==u.length)throw new Tn(u.length,e._size.length,"<");if(i.length<u.length){for(var a=0,s=0;1===u[a]&&1===i[a];)a++;for(;1===u[a];)s++,a++;n=Gn(n,u.length,s,i)}if(!Ot(u,i))throw new Tn(u,i,">");if(1===u.length){t.dimension(0).forEach((function(t,i){Un(t),e.set([t,0],n[i[0]],r)}))}else{var c=t.dimension(0),f=t.dimension(1);c.forEach((function(t,i){Un(t),f.forEach((function(u,o){Un(u),e.set([t,u],n[i[0]][o[0]],r)}))}))}}return e}(this,e,t,n);default:throw new SyntaxError("Wrong number of arguments")}},i.prototype.get=function(e){if(!Ze(e))throw new TypeError("Array expected");if(e.length!==this._size.length)throw new Tn(e.length,this._size.length);if(!this._values)throw new Error("Cannot invoke get on a Pattern only matrix");var t=e[0],n=e[1];Un(t,this._size[0]),Un(n,this._size[1]);var r=o(t,this._ptr[n],this._ptr[n+1],this._index);return r<this._ptr[n+1]&&this._index[r]===t?this._values[r]:0},i.prototype.set=function(e,r,i){if(!Ze(e))throw new TypeError("Array expected");if(e.length!==this._size.length)throw new Tn(e.length,this._size.length);if(!this._values)throw new Error("Cannot invoke set on a Pattern only matrix");var u=e[0],c=e[1],f=this._size[0],l=this._size[1],p=n,h=0;We(this._datatype)&&(p=t.find(n,[this._datatype,this._datatype])||n,h=t.convert(0,this._datatype)),(u>f-1||c>l-1)&&(s(this,Math.max(u+1,f),Math.max(c+1,l),i),f=this._size[0],l=this._size[1]),Un(u,f),Un(c,l);var m=o(u,this._ptr[c],this._ptr[c+1],this._index);return m<this._ptr[c+1]&&this._index[m]===u?p(r,h)?function(e,t,n,r,i){n.splice(e,1),r.splice(e,1);for(var u=t+1;u<i.length;u++)i[u]--}(m,c,this._values,this._index,this._ptr):this._values[m]=r:p(r,h)||a(m,u,c,r,this._values,this._index,this._ptr),this},i.prototype.resize=function(e,t,n){if(!Qe(e))throw new TypeError("Array or Matrix expected");var r=e.valueOf().map((e=>Array.isArray(e)&&1===e.length?e[0]:e));if(2!==r.length)throw new Error("Only two dimensions matrix are supported");return r.forEach((function(e){if(!He(e)||!Gt(e)||e<0)throw new TypeError("Invalid size, must contain positive integers (size: "+_n(r)+")")})),s(n?this.clone():this,r[0],r[1],t)},i.prototype.reshape=function(e,t){if(!Ze(e))throw new TypeError("Array expected");if(2!==e.length)throw new Error("Sparse matrices can only be reshaped in two dimensions");e.forEach((function(t){if(!He(t)||!Gt(t)||t<=-2||0===t)throw new TypeError("Invalid size, must contain positive integers or -1 (size: "+_n(e)+")")}));var n=this._size[0]*this._size[1];if(n!==(e=qn(e,n))[0]*e[1])throw new Error("Reshaping sparse matrix will result in the wrong number of elements");var r=t?this.clone():this;if(this._size[0]===e[0]&&this._size[1]===e[1])return r;for(var i=[],u=0;u<r._ptr.length;u++)for(var s=0;s<r._ptr[u+1]-r._ptr[u];s++)i.push(u);for(var c=r._values.slice(),f=r._index.slice(),l=0;l<r._index.length;l++){var p=f[l],h=i[l],m=p*r._size[1]+h;i[l]=m%e[1],f[l]=Math.floor(m/e[1])}r._values.length=0,r._index.length=0,r._ptr.length=e[1]+1,r._size=e.slice();for(var d=0;d<r._ptr.length;d++)r._ptr[d]=0;for(var v=0;v<c.length;v++){var g=f[v],D=i[v],y=c[v];a(o(g,r._ptr[D],r._ptr[D+1],r._index),g,D,y,r._values,r._index,r._ptr)}return r},i.prototype.clone=function(){return new i({values:this._values?Mt(this._values):void 0,index:Mt(this._index),ptr:Mt(this._ptr),size:Mt(this._size),datatype:this._datatype})},i.prototype.size=function(){return this._size.slice(0)},i.prototype.map=function(e,r){if(!this._values)throw new Error("Cannot invoke map on a Pattern only matrix");var u=this,o=this._size[0],a=this._size[1],s=vn(e);return function(e,r,u,o,a,s,c){var f=[],l=[],p=[],h=n,m=0;We(e._datatype)&&(h=t.find(n,[e._datatype,e._datatype])||n,m=t.convert(0,e._datatype));for(var d=function(e,t,n){e=s(e,t,n),h(e,m)||(f.push(e),l.push(t))},v=o;v<=a;v++){p.push(f.length);var g=e._ptr[v],D=e._ptr[v+1];if(c)for(var y=g;y<D;y++){var E=e._index[y];E>=r&&E<=u&&d(e._values[y],E-r,v-o)}else{for(var w={},b=g;b<D;b++){w[e._index[b]]=e._values[b]}for(var x=r;x<=u;x++){d(x in w?w[x]:0,x-r,v-o)}}}return p.push(f.length),new i({values:f,index:l,ptr:p,size:[u-r+1,a-o+1]})}(this,0,o-1,0,a-1,(function(t,n,r){return 1===s?e(t):2===s?e(t,[n,r]):e(t,[n,r],u)}),r)},i.prototype.forEach=function(e,t){if(!this._values)throw new Error("Cannot invoke forEach on a Pattern only matrix");for(var n=this._size[0],r=this._size[1],i=0;i<r;i++){var u=this._ptr[i],o=this._ptr[i+1];if(t)for(var a=u;a<o;a++){var s=this._index[a];e(this._values[a],[s,i],this)}else{for(var c={},f=u;f<o;f++){c[this._index[f]]=this._values[f]}for(var l=0;l<n;l++){e(l in c?c[l]:0,[l,i],this)}}}},i.prototype[Symbol.iterator]=function*(){if(!this._values)throw new Error("Cannot iterate a Pattern only matrix");for(var e=this._size[1],t=0;t<e;t++)for(var n=this._ptr[t],r=this._ptr[t+1],i=n;i<r;i++){var u=this._index[i];yield{value:this._values[i],index:[u,t]}}},i.prototype.toArray=function(){return c(this._values,this._index,this._ptr,this._size,!0)},i.prototype.valueOf=function(){return c(this._values,this._index,this._ptr,this._size,!1)},i.prototype.format=function(e){for(var t=this._size[0],n=this._size[1],r=this.density(),i="Sparse Matrix ["+_n(t,e)+" x "+_n(n,e)+"] density: "+_n(r,e)+"\n",u=0;u<n;u++)for(var o=this._ptr[u],a=this._ptr[u+1],s=o;s<a;s++){i+="\n    ("+_n(this._index[s],e)+", "+_n(u,e)+") ==> "+(this._values?_n(this._values[s],e):"X")}return i},i.prototype.toString=function(){return _n(this.toArray())},i.prototype.toJSON=function(){return{mathjs:"SparseMatrix",values:this._values,index:this._index,ptr:this._ptr,size:this._size,datatype:this._datatype}},i.prototype.diagonal=function(e){if(e){if(Ge(e)&&(e=e.toNumber()),!He(e)||!Gt(e))throw new TypeError("The parameter k must be an integer number")}else e=0;var t=e>0?e:0,n=e<0?-e:0,r=this._size[0],u=this._size[1],o=Math.min(r-n,u-t),a=[],s=[],c=[];c[0]=0;for(var f=t;f<u&&a.length<o;f++)for(var l=this._ptr[f],p=this._ptr[f+1],h=l;h<p;h++){var m=this._index[h];if(m===f-t+n){a.push(this._values[h]),s[a.length-1]=m-n;break}}return c.push(a.length),new i({values:a,index:s,ptr:c,size:[o,1]})},i.fromJSON=function(e){return new i(e)},i.diagonal=function(e,r,u,o,a){if(!Ze(e))throw new TypeError("Array expected, size parameter");if(2!==e.length)throw new Error("Only two dimensions matrix are supported");if(e=e.map((function(e){if(Ge(e)&&(e=e.toNumber()),!He(e)||!Gt(e)||e<1)throw new Error("Size values must be positive integers");return e})),u){if(Ge(u)&&(u=u.toNumber()),!He(u)||!Gt(u))throw new TypeError("The parameter k must be an integer number")}else u=0;var s=n,c=0;We(a)&&(s=t.find(n,[a,a])||n,c=t.convert(0,a));var f,l=u>0?u:0,p=u<0?-u:0,h=e[0],m=e[1],d=Math.min(h-p,m-l);if(Ze(r)){if(r.length!==d)throw new Error("Invalid value array length");f=function(e){return r[e]}}else if(Je(r)){var v=r.size();if(1!==v.length||v[0]!==d)throw new Error("Invalid matrix length");f=function(e){return r.get([e])}}else f=function(){return r};for(var g=[],D=[],y=[],E=0;E<m;E++){y.push(g.length);var w=E-l;if(w>=0&&w<d){var b=f(w);s(b,c)||(D.push(w+p),g.push(b))}}return y.push(g.length),new i({values:g,index:D,ptr:y,size:[h,m]})},i.prototype.swapRows=function(e,t){if(!(He(e)&&Gt(e)&&He(t)&&Gt(t)))throw new Error("Row index must be positive integers");if(2!==this._size.length)throw new Error("Only two dimensional matrix is supported");return Un(e,this._size[0]),Un(t,this._size[0]),i._swapRows(e,t,this._size[1],this._values,this._index,this._ptr),this},i._forEachRow=function(e,t,n,r,i){for(var u=r[e],o=r[e+1],a=u;a<o;a++)i(n[a],t[a])},i._swapRows=function(e,t,n,r,i,u){for(var a=0;a<n;a++){var s=u[a],c=u[a+1],f=o(e,s,c,i),l=o(t,s,c,i);if(f<c&&l<c&&i[f]===e&&i[l]===t){if(r){var p=r[f];r[f]=r[l],r[l]=p}}else if(f<c&&i[f]===e&&(l>=c||i[l]!==t)){var h=r?r[f]:void 0;i.splice(l,0,t),r&&r.splice(l,0,h),i.splice(l<=f?f+1:f,1),r&&r.splice(l<=f?f+1:f,1)}else if(l<c&&i[l]===t&&(f>=c||i[f]!==e)){var m=r?r[l]:void 0;i.splice(f,0,e),r&&r.splice(f,0,m),i.splice(f<=l?l+1:l,1),r&&r.splice(f<=l?l+1:l,1)}}},i}),{isClass:!0}),Yi="subtractScalar",$i=Tt(Yi,["typed"],(e=>{var{typed:t}=e;return t(Yi,{"number, number":wr,"Complex, Complex":function(e,t){return e.sub(t)},"BigNumber, BigNumber":function(e,t){return e.minus(t)},"Fraction, Fraction":function(e,t){return e.sub(t)},"Unit, Unit":t.referToSelf((e=>(n,r)=>{if(null===n.value||void 0===n.value)throw new Error("Parameter x contains a unit with undefined value");if(null===r.value||void 0===r.value)throw new Error("Parameter y contains a unit with undefined value");if(!n.equalBase(r))throw new Error("Units do not match");var i=n.clone();return i.value=t.find(e,[i.valueType(),r.valueType()])(i.value,r.value),i.fixPrefix=!1,i}))})}));Tt("bin",["typed","format"],(e=>{var{typed:t,format:n}=e;return t("bin",{"number | BigNumber":function(e){return n(e,{notation:"bin"})},"number | BigNumber, number | BigNumber":function(e,t){return n(e,{notation:"bin",wordSize:t})}})}));var Wi="unaryMinus",Zi=Tt(Wi,["typed"],(e=>{var{typed:t}=e;return t(Wi,{number:xr,"Complex | BigNumber | Fraction":e=>e.neg(),Unit:t.referToSelf((e=>n=>{var r=n.clone();return r.value=t.find(e,r.valueType())(n.value),r})),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0)))})})),Ji=Tt("fraction",["typed","Fraction"],(e=>{var{typed:t,Fraction:n}=e;return t("fraction",{number:function(e){if(!isFinite(e)||isNaN(e))throw new Error(e+" cannot be represented as a fraction");return new n(e)},string:function(e){return new n(e)},"number, number":function(e,t){return new n(e,t)},null:function(e){return new n(0)},BigNumber:function(e){return new n(e.toString())},Fraction:function(e){return e},Unit:t.referToSelf((e=>t=>{var n=t.clone();return n.value=e(t.value),n})),Object:function(e){return new n(e)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})})),Qi="isNumeric",Xi=Tt(Qi,["typed"],(e=>{var{typed:t}=e;return t(Qi,{"number | BigNumber | Fraction | boolean":()=>!0,"Complex | Unit | string | null | undefined | Node":()=>!1,"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})})),Ki="matrix",eu=Tt(Ki,["typed","Matrix","DenseMatrix","SparseMatrix"],(e=>{var{typed:t,Matrix:n,DenseMatrix:r,SparseMatrix:i}=e;return t(Ki,{"":function(){return u([])},string:function(e){return u([],e)},"string, string":function(e,t){return u([],e,t)},Array:function(e){return u(e)},Matrix:function(e){return u(e,e.storage())},"Array | Matrix, string":u,"Array | Matrix, string, string":u});function u(e,t,n){if("dense"===t||"default"===t||void 0===t)return new r(e,n);if("sparse"===t)return new i(e,n);throw new TypeError("Unknown matrix type "+JSON.stringify(t)+".")}}));function tu(){throw new Error('No "bignumber" implementation available')}function nu(){throw new Error('No "fraction" implementation available')}function ru(){throw new Error('No "matrix" implementation available')}var iu=Tt("numeric",["number","?bignumber","?fraction"],(e=>{var{number:t,bignumber:n,fraction:r}=e,i={string:!0,number:!0,BigNumber:!0,Fraction:!0},u={number:e=>t(e),BigNumber:n?e=>n(e):tu,Fraction:r?e=>r(e):nu};return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"number";if(void 0!==(arguments.length>2?arguments[2]:void 0))throw new SyntaxError("numeric() takes one or two arguments");var n=_t(e);if(!(n in i))throw new TypeError("Cannot convert "+e+' of type "'+n+'"; valid input types are '+Object.keys(i).join(", "));if(!(t in u))throw new TypeError("Cannot convert "+e+' to type "'+t+'"; valid output types are '+Object.keys(u).join(", "));return t===n?e:u[t](e)}}));var uu="size",ou=Tt(uu,["typed","config","?matrix"],(e=>{var{typed:t,config:n,matrix:r}=e;return t(uu,{Matrix:function(e){return e.create(e.size())},Array:In,string:function(e){return"Array"===n.matrix?[e.length]:r([e.length])},"number | Complex | BigNumber | Unit | boolean | null":function(e){return"Array"===n.matrix?[]:r?r([]):ru()}})})),au="zeros",su=Tt(au,["typed","config","matrix","BigNumber"],(e=>{var{typed:t,config:n,matrix:r,BigNumber:i}=e;return t(au,{"":function(){return"Array"===n.matrix?u([]):u([],"default")},"...number | BigNumber | string":function(e){if("string"==typeof e[e.length-1]){var t=e.pop();return u(e,t)}return"Array"===n.matrix?u(e):u(e,"default")},Array:u,Matrix:function(e){var t=e.storage();return u(e.valueOf(),t)},"Array | Matrix, string":function(e,t){return u(e.valueOf(),t)}});function u(e,t){var n=function(e){var t=!1;return e.forEach((function(e,n,r){Ge(e)&&(t=!0,r[n]=e.toNumber())})),t}(e),u=n?new i(0):0;if(function(e){e.forEach((function(e){if("number"!=typeof e||!Gt(e)||e<0)throw new Error("Parameters in function zeros must be positive integers")}))}(e),t){var o=r(t);return e.length>0?o.resize(e,u):o}var a=[];return e.length>0?Ln(a,e,u):a}})),cu="concat",fu=Tt(cu,["typed","matrix","isInteger"],(e=>{var{typed:t,matrix:n,isInteger:r}=e;return t(cu,{"...Array | Matrix | number | BigNumber":function(e){var t,i,u=e.length,o=-1,a=!1,s=[];for(t=0;t<u;t++){var c=e[t];if(Je(c)&&(a=!0),He(c)||Ge(c)){if(t!==u-1)throw new Error("Dimension must be specified as last argument");if(i=o,o=c.valueOf(),!r(o))throw new TypeError("Integer number expected for dimension");if(o<0||t>0&&o>i)throw new Rn(o,i+1)}else{var f=Mt(c).valueOf(),l=In(f);if(s[t]=f,i=o,o=l.length-1,t>0&&o!==i)throw new Tn(i+1,o+1)}}if(0===s.length)throw new SyntaxError("At least one matrix expected");for(var p=s.shift();s.length;)p=Zn(p,s.shift(),o);return a?n(p):p},"...string":function(e){return e.join("")}})})),lu="divideScalar",pu=Tt(lu,["typed","numeric"],(e=>{var{typed:t,numeric:n}=e;return t(lu,{"number, number":function(e,t){return e/t},"Complex, Complex":function(e,t){return e.div(t)},"BigNumber, BigNumber":function(e,t){return e.div(t)},"Fraction, Fraction":function(e,t){return e.div(t)},"Unit, number | Complex | Fraction | BigNumber | Unit":(e,t)=>e.divide(t),"number | Fraction | Complex | BigNumber, Unit":(e,t)=>t.divideInto(e)})})),hu=Tt("matAlgo03xDSf",["typed"],(e=>{var{typed:t}=e;return function(e,n,r,i){var u=e._data,o=e._size,a=e._datatype||e.getDataType(),s=n._values,c=n._index,f=n._ptr,l=n._size,p=n._datatype||void 0===n._data?n._datatype:n.getDataType();if(o.length!==l.length)throw new Tn(o.length,l.length);if(o[0]!==l[0]||o[1]!==l[1])throw new RangeError("Dimension mismatch. Matrix A ("+o+") must match Matrix B ("+l+")");if(!s)throw new Error("Cannot perform operation on Dense Matrix and Pattern Sparse Matrix");var h,m=o[0],d=o[1],v=0,g=r;"string"==typeof a&&a===p&&"mixed"!==a&&(h=a,v=t.convert(0,h),g=t.find(r,[h,h]));for(var D=[],y=0;y<m;y++)D[y]=[];for(var E=[],w=[],b=0;b<d;b++){for(var x=b+1,A=f[b],C=f[b+1],F=A;F<C;F++){var N=c[F];E[N]=i?g(s[F],u[N][b]):g(u[N][b],s[F]),w[N]=x}for(var _=0;_<m;_++)w[_]===x?D[_][b]=E[_]:D[_][b]=i?g(v,u[_][b]):g(u[_][b],v)}return e.createDenseMatrix({data:D,size:[m,d],datatype:a===e._datatype&&p===n._datatype?h:void 0})}})),mu=Tt("matAlgo07xSSf",["typed","DenseMatrix"],(e=>{var{typed:t,DenseMatrix:n}=e;return function(e,i,u){var o=e._size,a=e._datatype||void 0===e._data?e._datatype:e.getDataType(),s=i._size,c=i._datatype||void 0===i._data?i._datatype:i.getDataType();if(o.length!==s.length)throw new Tn(o.length,s.length);if(o[0]!==s[0]||o[1]!==s[1])throw new RangeError("Dimension mismatch. Matrix A ("+o+") must match Matrix B ("+s+")");var f,l,p,h=o[0],m=o[1],d=0,v=u;"string"==typeof a&&a===c&&"mixed"!==a&&(f=a,d=t.convert(0,f),v=t.find(u,[f,f]));var g=[];for(l=0;l<h;l++)g[l]=[];var D=[],y=[],E=[],w=[];for(p=0;p<m;p++){var b=p+1;for(r(e,p,E,D,b),r(i,p,w,y,b),l=0;l<h;l++){var x=E[l]===b?D[l]:d,A=w[l]===b?y[l]:d;g[l][p]=v(x,A)}}return new n({data:g,size:[h,m],datatype:a===e._datatype&&c===i._datatype?f:void 0})};function r(e,t,n,r,i){for(var u=e._values,o=e._index,a=e._ptr,s=a[t],c=a[t+1];s<c;s++){var f=o[s];n[f]=i,r[f]=u[s]}}})),du=Tt("matAlgo11xS0s",["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return function(e,r,i,u){var o=e._values,a=e._index,s=e._ptr,c=e._size,f=e._datatype;if(!o)throw new Error("Cannot perform operation on Pattern Sparse Matrix and Scalar value");var l,p=c[0],h=c[1],m=n,d=0,v=i;"string"==typeof f&&(l=f,m=t.find(n,[l,l]),d=t.convert(0,l),r=t.convert(r,l),v=t.find(i,[l,l]));for(var g=[],D=[],y=[],E=0;E<h;E++){y[E]=D.length;for(var w=s[E],b=s[E+1],x=w;x<b;x++){var A=a[x],C=u?v(r,o[x]):v(o[x],r);m(C,d)||(D.push(A),g.push(C))}}return y[h]=D.length,e.createSparseMatrix({values:g,index:D,ptr:y,size:[p,h],datatype:l})}})),vu=Tt("matAlgo12xSfs",["typed","DenseMatrix"],(e=>{var{typed:t,DenseMatrix:n}=e;return function(e,r,i,u){var o=e._values,a=e._index,s=e._ptr,c=e._size,f=e._datatype;if(!o)throw new Error("Cannot perform operation on Pattern Sparse Matrix and Scalar value");var l,p=c[0],h=c[1],m=i;"string"==typeof f&&(l=f,r=t.convert(r,l),m=t.find(i,[l,l]));for(var d=[],v=[],g=[],D=0;D<h;D++){for(var y=D+1,E=s[D],w=s[D+1],b=E;b<w;b++){var x=a[b];v[x]=o[b],g[x]=y}for(var A=0;A<p;A++)0===D&&(d[A]=[]),g[A]===y?d[A][D]=u?m(r,v[A]):m(v[A],r):d[A][D]=u?m(r,0):m(0,r)}return new n({data:d,size:[p,h],datatype:l})}})),gu=Tt("matAlgo13xDD",["typed"],(e=>{var{typed:t}=e;return function(e,r,i){var u,o=e._data,a=e._size,s=e._datatype,c=r._data,f=r._size,l=r._datatype,p=[];if(a.length!==f.length)throw new Tn(a.length,f.length);for(var h=0;h<a.length;h++){if(a[h]!==f[h])throw new RangeError("Dimension mismatch. Matrix A ("+a+") must match Matrix B ("+f+")");p[h]=a[h]}var m=i;"string"==typeof s&&s===l&&(u=s,m=t.find(i,[u,u]));var d=p.length>0?n(m,0,p,p[0],o,c):[];return e.createDenseMatrix({data:d,size:p,datatype:u})};function n(e,t,r,i,u,o){var a=[];if(t===r.length-1)for(var s=0;s<i;s++)a[s]=e(u[s],o[s]);else for(var c=0;c<i;c++)a[c]=n(e,t+1,r,r[t+1],u[c],o[c]);return a}})),Du=Tt("matAlgo14xDs",["typed"],(e=>{var{typed:t}=e;return function(e,r,i,u){var o,a=e._data,s=e._size,c=e._datatype,f=i;"string"==typeof c&&(o=c,r=t.convert(r,o),f=t.find(i,[o,o]));var l=s.length>0?n(f,0,s,s[0],a,r,u):[];return e.createDenseMatrix({data:l,size:Mt(s),datatype:o})};function n(e,t,r,i,u,o,a){var s=[];if(t===r.length-1)for(var c=0;c<i;c++)s[c]=a?e(o,u[c]):e(u[c],o);else for(var f=0;f<i;f++)s[f]=n(e,t+1,r,r[t+1],u[f],o,a);return s}})),yu=Tt("broadcast",["concat"],(e=>{var{concat:t}=e;return function(e,t){var i=Math.max(e._size.length,t._size.length);if(e._size.length===t._size.length&&e._size.every(((e,n)=>e===t._size[n])))return[e,t];for(var u=n(e._size,i,0),o=n(t._size,i,0),a=[],s=0;s<i;s++)a[s]=Math.max(u[s],o[s]);Qn(u,a),Qn(o,a);var c=e.clone(),f=t.clone();c._size.length<i?c.reshape(n(c._size,i,1)):f._size.length<i&&f.reshape(n(f._size,i,1));for(var l=0;l<i;l++)c._size[l]<a[l]&&(c=r(c,a[l],l)),f._size[l]<a[l]&&(f=r(f,a[l],l));return[c,f]};function n(e,t,n){return[...Array(t-e.length).fill(n),...e]}function r(e,n,r){return t(...Array(n).fill(e),r)}})),Eu=Tt("matrixAlgorithmSuite",["typed","matrix","concat"],(e=>{var{typed:t,matrix:n,concat:r}=e,i=gu({typed:t}),u=Du({typed:t}),o=yu({concat:r});return function(e){var r,a=e.elop,s=e.SD||e.DS;a?(r={"DenseMatrix, DenseMatrix":(e,t)=>i(...o(e,t),a),"Array, Array":(e,t)=>i(...o(n(e),n(t)),a).valueOf(),"Array, DenseMatrix":(e,t)=>i(...o(n(e),t),a),"DenseMatrix, Array":(e,t)=>i(...o(e,n(t)),a)},e.SS&&(r["SparseMatrix, SparseMatrix"]=(t,n)=>e.SS(...o(t,n),a,!1)),e.DS&&(r["DenseMatrix, SparseMatrix"]=(t,n)=>e.DS(...o(t,n),a,!1),r["Array, SparseMatrix"]=(t,r)=>e.DS(...o(n(t),r),a,!1)),s&&(r["SparseMatrix, DenseMatrix"]=(e,t)=>s(...o(t,e),a,!0),r["SparseMatrix, Array"]=(e,t)=>s(...o(n(t),e),a,!0))):(r={"DenseMatrix, DenseMatrix":t.referToSelf((e=>(t,n)=>i(...o(t,n),e))),"Array, Array":t.referToSelf((e=>(t,r)=>i(...o(n(t),n(r)),e).valueOf())),"Array, DenseMatrix":t.referToSelf((e=>(t,r)=>i(...o(n(t),r),e))),"DenseMatrix, Array":t.referToSelf((e=>(t,r)=>i(...o(t,n(r)),e)))},e.SS&&(r["SparseMatrix, SparseMatrix"]=t.referToSelf((t=>(n,r)=>e.SS(...o(n,r),t,!1)))),e.DS&&(r["DenseMatrix, SparseMatrix"]=t.referToSelf((t=>(n,r)=>e.DS(...o(n,r),t,!1))),r["Array, SparseMatrix"]=t.referToSelf((t=>(r,i)=>e.DS(...o(n(r),i),t,!1)))),s&&(r["SparseMatrix, DenseMatrix"]=t.referToSelf((e=>(t,n)=>s(...o(n,t),e,!0))),r["SparseMatrix, Array"]=t.referToSelf((e=>(t,r)=>s(...o(n(r),t),e,!0)))));var c=e.scalar||"any";(e.Ds||e.Ss)&&(a?(r["DenseMatrix,"+c]=(e,t)=>u(e,t,a,!1),r[c+", DenseMatrix"]=(e,t)=>u(t,e,a,!0),r["Array,"+c]=(e,t)=>u(n(e),t,a,!1).valueOf(),r[c+", Array"]=(e,t)=>u(n(t),e,a,!0).valueOf()):(r["DenseMatrix,"+c]=t.referToSelf((e=>(t,n)=>u(t,n,e,!1))),r[c+", DenseMatrix"]=t.referToSelf((e=>(t,n)=>u(n,t,e,!0))),r["Array,"+c]=t.referToSelf((e=>(t,r)=>u(n(t),r,e,!1).valueOf())),r[c+", Array"]=t.referToSelf((e=>(t,r)=>u(n(r),t,e,!0).valueOf()))));var f=void 0!==e.sS?e.sS:e.Ss;return a?(e.Ss&&(r["SparseMatrix,"+c]=(t,n)=>e.Ss(t,n,a,!1)),f&&(r[c+", SparseMatrix"]=(e,t)=>f(t,e,a,!0))):(e.Ss&&(r["SparseMatrix,"+c]=t.referToSelf((t=>(n,r)=>e.Ss(n,r,t,!1)))),f&&(r[c+", SparseMatrix"]=t.referToSelf((e=>(t,n)=>f(n,t,e,!0))))),a&&a.signatures&&St(r,a.signatures),r}})),wu="equal",bu=Tt(wu,["typed","matrix","equalScalar","DenseMatrix","concat"],(e=>{var{typed:t,matrix:n,equalScalar:r,DenseMatrix:i,concat:u}=e,o=hu({typed:t}),a=mu({typed:t,DenseMatrix:i}),s=vu({typed:t,DenseMatrix:i}),c=Eu({typed:t,matrix:n,concat:u});return t(wu,xu({typed:t,equalScalar:r}),c({elop:r,SS:a,DS:o,Ss:s}))})),xu=Tt(wu,["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return t(wu,{"any, any":function(e,t){return null===e?null===t:null===t?null===e:void 0===e?void 0===t:void 0===t?void 0===e:n(e,t)}})})),Au="hasNumericValue",Cu=Tt(Au,["typed","isNumeric"],(e=>{var{typed:t,isNumeric:n}=e;return t(Au,{boolean:()=>!0,string:function(e){return e.trim().length>0&&!isNaN(Number(e))},any:function(e){return n(e)}})})),Fu="identity",Nu=Tt(Fu,["typed","config","matrix","BigNumber","DenseMatrix","SparseMatrix"],(e=>{var{typed:t,config:n,matrix:r,BigNumber:i,DenseMatrix:u,SparseMatrix:o}=e;return t(Fu,{"":function(){return"Matrix"===n.matrix?r([]):[]},string:function(e){return r(e)},"number | BigNumber":function(e){return s(e,e,"Matrix"===n.matrix?"dense":void 0)},"number | BigNumber, string":function(e,t){return s(e,e,t)},"number | BigNumber, number | BigNumber":function(e,t){return s(e,t,"Matrix"===n.matrix?"dense":void 0)},"number | BigNumber, number | BigNumber, string":function(e,t,n){return s(e,t,n)},Array:function(e){return a(e)},"Array, string":function(e,t){return a(e,t)},Matrix:function(e){return a(e.valueOf(),e.storage())},"Matrix, string":function(e,t){return a(e.valueOf(),t)}});function a(e,t){switch(e.length){case 0:return t?r(t):[];case 1:return s(e[0],e[0],t);case 2:return s(e[0],e[1],t);default:throw new Error("Vector containing two values expected")}}function s(e,t,n){var r=Ge(e)||Ge(t)?i:null;if(Ge(e)&&(e=e.toNumber()),Ge(t)&&(t=t.toNumber()),!Gt(e)||e<1)throw new Error("Parameters in function identity must be positive integers");if(!Gt(t)||t<1)throw new Error("Parameters in function identity must be positive integers");var a=r?new i(1):1,s=r?new r(0):0,c=[e,t];if(n){if("sparse"===n)return o.diagonal(c,a,0,s);if("dense"===n)return u.diagonal(c,a,0,s);throw new TypeError('Unknown matrix type "'.concat(n,'"'))}for(var f=Ln([],c,s),l=e<t?e:t,p=0;p<l;p++)f[p][p]=a;return f}})),_u=Tt("matAlgo01xDSid",["typed"],(e=>{var{typed:t}=e;return function(e,n,r,i){var u=e._data,o=e._size,a=e._datatype||e.getDataType(),s=n._values,c=n._index,f=n._ptr,l=n._size,p=n._datatype||void 0===n._data?n._datatype:n.getDataType();if(o.length!==l.length)throw new Tn(o.length,l.length);if(o[0]!==l[0]||o[1]!==l[1])throw new RangeError("Dimension mismatch. Matrix A ("+o+") must match Matrix B ("+l+")");if(!s)throw new Error("Cannot perform operation on Dense Matrix and Pattern Sparse Matrix");var h,m,d=o[0],v=o[1],g="string"==typeof a&&"mixed"!==a&&a===p?a:void 0,D=g?t.find(r,[g,g]):r,y=[];for(h=0;h<d;h++)y[h]=[];var E=[],w=[];for(m=0;m<v;m++){for(var b=m+1,x=f[m],A=f[m+1],C=x;C<A;C++)E[h=c[C]]=i?D(s[C],u[h][m]):D(u[h][m],s[C]),w[h]=b;for(h=0;h<d;h++)w[h]===b?y[h][m]=E[h]:y[h][m]=u[h][m]}return e.createDenseMatrix({data:y,size:[d,v],datatype:a===e._datatype&&p===n._datatype?g:void 0})}})),Mu=Tt("matAlgo10xSids",["typed","DenseMatrix"],(e=>{var{typed:t,DenseMatrix:n}=e;return function(e,r,i,u){var o=e._values,a=e._index,s=e._ptr,c=e._size,f=e._datatype;if(!o)throw new Error("Cannot perform operation on Pattern Sparse Matrix and Scalar value");var l,p=c[0],h=c[1],m=i;"string"==typeof f&&(l=f,r=t.convert(r,l),m=t.find(i,[l,l]));for(var d=[],v=[],g=[],D=0;D<h;D++){for(var y=D+1,E=s[D],w=s[D+1],b=E;b<w;b++){var x=a[b];v[x]=o[b],g[x]=y}for(var A=0;A<p;A++)0===D&&(d[A]=[]),g[A]===y?d[A][D]=u?m(r,v[A]):m(v[A],r):d[A][D]=r}return new n({data:d,size:[p,h],datatype:l})}}));function Su(e,t,n,r){if(!(this instanceof Su))throw new SyntaxError("Constructor must be called with the new operator");this.fn=e,this.count=t,this.min=n,this.max=r,this.message="Wrong number of arguments in function "+e+" ("+t+" provided, "+n+(null!=r?"-"+r:"")+" expected)",this.stack=(new Error).stack}Su.prototype=new Error,Su.prototype.constructor=Error,Su.prototype.name="ArgumentsError",Su.prototype.isArgumentsError=!0;var Ou="Number of decimals in function round must be an integer",Bu="round",Tu=Tt(Bu,["typed","config","matrix","equalScalar","zeros","BigNumber","DenseMatrix"],(e=>{var{typed:t,config:n,matrix:r,equalScalar:i,zeros:u,BigNumber:o,DenseMatrix:a}=e,s=du({typed:t,equalScalar:i}),c=vu({typed:t,DenseMatrix:a}),f=Du({typed:t});function l(e){return Math.abs(Kt(e).exponent)}return t(Bu,{number:function(e){var t=zr(e,l(n.epsilon));return zr(on(e,t,n.epsilon)?t:e)},"number, number":function(e,t){var r=l(n.epsilon);if(t>=r)return zr(e,t);var i=zr(e,r);return zr(on(e,i,n.epsilon)?i:e,t)},"number, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(Ou);return new o(e).toDecimalPlaces(t.toNumber())},Complex:function(e){return e.round()},"Complex, number":function(e,t){if(t%1)throw new TypeError(Ou);return e.round(t)},"Complex, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(Ou);var n=t.toNumber();return e.round(n)},BigNumber:function(e){var t=new o(e).toDecimalPlaces(l(n.epsilon));return(vi(e,t,n.epsilon)?t:e).toDecimalPlaces(0)},"BigNumber, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(Ou);var r=l(n.epsilon);if(t>=r)return e.toDecimalPlaces(t.toNumber());var i=e.toDecimalPlaces(r);return(vi(e,i,n.epsilon)?i:e).toDecimalPlaces(t.toNumber())},Fraction:function(e){return e.round()},"Fraction, number":function(e,t){if(t%1)throw new TypeError(Ou);return e.round(t)},"Fraction, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(Ou);return e.round(t.toNumber())},"Unit, number, Unit":t.referToSelf((e=>function(t,n,r){var i=t.toNumeric(r);return r.multiply(e(i,n))})),"Unit, BigNumber, Unit":t.referToSelf((e=>(t,n,r)=>e(t,n.toNumber(),r))),"Unit, Unit":t.referToSelf((e=>(t,n)=>e(t,0,n))),"Array | Matrix, number, Unit":t.referToSelf((e=>(t,n,r)=>vr(t,(t=>e(t,n,r)),!0))),"Array | Matrix, BigNumber, Unit":t.referToSelf((e=>(t,n,r)=>e(t,n.toNumber(),r))),"Array | Matrix, Unit":t.referToSelf((e=>(t,n)=>e(t,0,n))),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0))),"SparseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>s(t,n,e,!1))),"DenseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>f(t,n,e,!1))),"Array, number | BigNumber":t.referToSelf((e=>(t,n)=>f(r(t),n,e,!1).valueOf())),"number | Complex | BigNumber | Fraction, SparseMatrix":t.referToSelf((e=>(t,n)=>i(t,0)?u(n.size(),n.storage()):c(n,t,e,!0))),"number | Complex | BigNumber | Fraction, DenseMatrix":t.referToSelf((e=>(t,n)=>i(t,0)?u(n.size(),n.storage()):f(n,t,e,!0))),"number | Complex | BigNumber | Fraction, Array":t.referToSelf((e=>(t,n)=>f(r(n),t,e,!0).valueOf()))})})),Ru=Tt("matAlgo05xSfSf",["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return function(e,r,i){var u=e._values,o=e._index,a=e._ptr,s=e._size,c=e._datatype||void 0===e._data?e._datatype:e.getDataType(),f=r._values,l=r._index,p=r._ptr,h=r._size,m=r._datatype||void 0===r._data?r._datatype:r.getDataType();if(s.length!==h.length)throw new Tn(s.length,h.length);if(s[0]!==h[0]||s[1]!==h[1])throw new RangeError("Dimension mismatch. Matrix A ("+s+") must match Matrix B ("+h+")");var d,v=s[0],g=s[1],D=n,y=0,E=i;"string"==typeof c&&c===m&&"mixed"!==c&&(d=c,D=t.find(n,[d,d]),y=t.convert(0,d),E=t.find(i,[d,d]));var w,b,x,A,C=u&&f?[]:void 0,F=[],N=[],_=C?[]:void 0,M=C?[]:void 0,S=[],O=[];for(b=0;b<g;b++){N[b]=F.length;var B=b+1;for(x=a[b],A=a[b+1];x<A;x++)w=o[x],F.push(w),S[w]=B,_&&(_[w]=u[x]);for(x=p[b],A=p[b+1];x<A;x++)S[w=l[x]]!==B&&F.push(w),O[w]=B,M&&(M[w]=f[x]);if(C)for(x=N[b];x<F.length;){var T=S[w=F[x]],R=O[w];if(T===B||R===B){var I=E(T===B?_[w]:y,R===B?M[w]:y);D(I,y)?F.splice(x,1):(C.push(I),x++)}}}return N[g]=F.length,e.createSparseMatrix({values:C,index:F,ptr:N,size:[v,g],datatype:c===e._datatype&&m===r._datatype?d:void 0})}})),Iu="subtract",zu=Tt(Iu,["typed","matrix","equalScalar","subtractScalar","unaryMinus","DenseMatrix","concat"],(e=>{var{typed:t,matrix:n,equalScalar:r,subtractScalar:i,unaryMinus:u,DenseMatrix:o,concat:a}=e,s=_u({typed:t}),c=hu({typed:t}),f=Ru({typed:t,equalScalar:r}),l=Mu({typed:t,DenseMatrix:o}),p=vu({typed:t,DenseMatrix:o}),h=Eu({typed:t,matrix:n,concat:a});return t(Iu,{"any, any":i},h({elop:i,SS:f,DS:s,SD:c,Ss:p,sS:l}))})),Pu="unequal",Uu=(Tt(Pu,["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return t(Pu,{"any, any":function(e,t){return null===e?null!==t:null===t?null!==e:void 0===e?void 0!==t:void 0===t?void 0!==e:!n(e,t)}})})),Tt("matAlgo04xSidSid",["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return function(e,r,i){var u=e._values,o=e._index,a=e._ptr,s=e._size,c=e._datatype||void 0===e._data?e._datatype:e.getDataType(),f=r._values,l=r._index,p=r._ptr,h=r._size,m=r._datatype||void 0===r._data?r._datatype:r.getDataType();if(s.length!==h.length)throw new Tn(s.length,h.length);if(s[0]!==h[0]||s[1]!==h[1])throw new RangeError("Dimension mismatch. Matrix A ("+s+") must match Matrix B ("+h+")");var d,v=s[0],g=s[1],D=n,y=0,E=i;"string"==typeof c&&c===m&&"mixed"!==c&&(d=c,D=t.find(n,[d,d]),y=t.convert(0,d),E=t.find(i,[d,d]));var w,b,x,A,C,F=u&&f?[]:void 0,N=[],_=[],M=u&&f?[]:void 0,S=u&&f?[]:void 0,O=[],B=[];for(b=0;b<g;b++){_[b]=N.length;var T=b+1;for(A=a[b],C=a[b+1],x=A;x<C;x++)w=o[x],N.push(w),O[w]=T,M&&(M[w]=u[x]);for(A=p[b],C=p[b+1],x=A;x<C;x++)if(O[w=l[x]]===T){if(M){var R=E(M[w],f[x]);D(R,y)?O[w]=null:M[w]=R}}else N.push(w),B[w]=T,S&&(S[w]=f[x]);if(M&&S)for(x=_[b];x<N.length;)O[w=N[x]]===T?(F[x]=M[w],x++):B[w]===T?(F[x]=S[w],x++):N.splice(x,1)}return _[g]=N.length,e.createSparseMatrix({values:F,index:N,ptr:_,size:[v,g],datatype:c===e._datatype&&m===r._datatype?d:void 0})}}))),Lu=Tt("add",["typed","matrix","addScalar","equalScalar","DenseMatrix","SparseMatrix","concat"],(e=>{var{typed:t,matrix:n,addScalar:r,equalScalar:i,DenseMatrix:u,SparseMatrix:o,concat:a}=e,s=_u({typed:t}),c=Uu({typed:t,equalScalar:i}),f=Mu({typed:t,DenseMatrix:u}),l=Eu({typed:t,matrix:n,concat:a});return t("add",{"any, any":r,"any, any, ...any":t.referToSelf((e=>(t,n,r)=>{for(var i=e(t,n),u=0;u<r.length;u++)i=e(i,r[u]);return i}))},l({elop:r,DS:s,SS:c,Ss:f}))}));Bn.signature="any, any";var ju=Tt("dot",["typed","addScalar","multiplyScalar","conj","size"],(e=>{var{typed:t,addScalar:n,multiplyScalar:r,conj:i,size:u}=e;return t("dot",{"Array | DenseMatrix, Array | DenseMatrix":function(e,u){var s=o(e,u),c=Je(e)?e._data:e,f=Je(e)?e._datatype||e.getDataType():void 0,l=Je(u)?u._data:u,p=Je(u)?u._datatype||u.getDataType():void 0,h=2===a(e).length,m=2===a(u).length,d=n,v=r;if(f&&p&&f===p&&"string"==typeof f&&"mixed"!==f){var g=f;d=t.find(n,[g,g]),v=t.find(r,[g,g])}if(!h&&!m){for(var D=v(i(c[0]),l[0]),y=1;y<s;y++)D=d(D,v(i(c[y]),l[y]));return D}if(!h&&m){for(var E=v(i(c[0]),l[0][0]),w=1;w<s;w++)E=d(E,v(i(c[w]),l[w][0]));return E}if(h&&!m){for(var b=v(i(c[0][0]),l[0]),x=1;x<s;x++)b=d(b,v(i(c[x][0]),l[x]));return b}if(h&&m){for(var A=v(i(c[0][0]),l[0][0]),C=1;C<s;C++)A=d(A,v(i(c[C][0]),l[C][0]));return A}},"SparseMatrix, SparseMatrix":function(e,t){o(e,t);var i=e._index,u=e._values,a=t._index,s=t._values,c=0,f=n,l=r,p=0,h=0;for(;p<i.length&&h<a.length;){var m=i[p],d=a[h];m<d?p++:m>d?h++:m===d&&(c=f(c,l(u[p],s[h])),p++,h++)}return c}});function o(e,t){var n,r,i=a(e),u=a(t);if(1===i.length)n=i[0];else{if(2!==i.length||1!==i[1])throw new RangeError("Expected a column vector, instead got a matrix of size ("+i.join(", ")+")");n=i[0]}if(1===u.length)r=u[0];else{if(2!==u.length||1!==u[1])throw new RangeError("Expected a column vector, instead got a matrix of size ("+u.join(", ")+")");r=u[0]}if(n!==r)throw new RangeError("Vectors must have equal length ("+n+" != "+r+")");if(0===n)throw new RangeError("Cannot calculate the dot product of empty vectors");return n}function a(e){return Je(e)?e.size():u(e)}})),ku="floor",qu=["typed","config","round","matrix","equalScalar","zeros","DenseMatrix"],Hu=Tt(ku,["typed","config","round"],(e=>{var{typed:t,config:n,round:r}=e;return t(ku,{number:function(e){return on(e,r(e),n.epsilon)?r(e):Math.floor(e)},"number, number":function(e,t){if(on(e,r(e,t),n.epsilon))return r(e,t);var[i,u]="".concat(e,"e").split("e"),o=Math.floor(Number("".concat(i,"e").concat(Number(u)+t)));return[i,u]="".concat(o,"e").split("e"),Number("".concat(i,"e").concat(Number(u)-t))}})})),Gu=Tt(ku,qu,(e=>{var{typed:t,config:n,round:r,matrix:i,equalScalar:u,zeros:o,DenseMatrix:a}=e,s=du({typed:t,equalScalar:u}),c=vu({typed:t,DenseMatrix:a}),f=Du({typed:t}),l=Hu({typed:t,config:n,round:r});return t("floor",{number:l.signatures.number,"number,number":l.signatures["number,number"],Complex:function(e){return e.floor()},"Complex, number":function(e,t){return e.floor(t)},"Complex, BigNumber":function(e,t){return e.floor(t.toNumber())},BigNumber:function(e){return vi(e,r(e),n.epsilon)?r(e):e.floor()},"BigNumber, BigNumber":function(e,t){return vi(e,r(e,t),n.epsilon)?r(e,t):e.toDecimalPlaces(t.toNumber(),qe.ROUND_FLOOR)},Fraction:function(e){return e.floor()},"Fraction, number":function(e,t){return e.floor(t)},"Fraction, BigNumber":function(e,t){return e.floor(t.toNumber())},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0))),"Array, number | BigNumber":t.referToSelf((e=>(t,n)=>vr(t,(t=>e(t,n)),!0))),"SparseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>s(t,n,e,!1))),"DenseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>f(t,n,e,!1))),"number | Complex | Fraction | BigNumber, Array":t.referToSelf((e=>(t,n)=>f(i(n),t,e,!0).valueOf())),"number | Complex | Fraction | BigNumber, Matrix":t.referToSelf((e=>(t,n)=>u(t,0)?o(n.size(),n.storage()):"dense"===n.storage()?f(n,t,e,!0):c(n,t,e,!0)))})})),Vu="number | BigNumber | Fraction | Matrix | Array";"".concat(Vu,", ").concat(Vu,", ...").concat(Vu);var Yu="multiply",$u=Tt(Yu,["typed","matrix","addScalar","multiplyScalar","equalScalar","dot"],(e=>{var{typed:t,matrix:n,addScalar:r,multiplyScalar:i,equalScalar:u,dot:o}=e,a=du({typed:t,equalScalar:u}),s=Du({typed:t});function c(e,t){switch(e.length){case 1:switch(t.length){case 1:if(e[0]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Vectors must have the same length");break;case 2:if(e[0]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Vector length ("+e[0]+") must match Matrix rows ("+t[0]+")");break;default:throw new Error("Can only multiply a 1 or 2 dimensional matrix (Matrix B has "+t.length+" dimensions)")}break;case 2:switch(t.length){case 1:if(e[1]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Matrix columns ("+e[1]+") must match Vector length ("+t[0]+")");break;case 2:if(e[1]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Matrix A columns ("+e[1]+") must match Matrix B rows ("+t[0]+")");break;default:throw new Error("Can only multiply a 1 or 2 dimensional matrix (Matrix B has "+t.length+" dimensions)")}break;default:throw new Error("Can only multiply a 1 or 2 dimensional matrix (Matrix A has "+e.length+" dimensions)")}}function f(e,n){if("dense"!==n.storage())throw new Error("Support for SparseMatrix not implemented");return function(e,n){var u,o=e._data,a=e._size,s=e._datatype||e.getDataType(),c=n._data,f=n._size,l=n._datatype||n.getDataType(),p=a[0],h=f[1],m=r,d=i;s&&l&&s===l&&"string"==typeof s&&"mixed"!==s&&(u=s,m=t.find(r,[u,u]),d=t.find(i,[u,u]));for(var v=[],g=0;g<h;g++){for(var D=d(o[0],c[0][g]),y=1;y<p;y++)D=m(D,d(o[y],c[y][g]));v[g]=D}return e.createDenseMatrix({data:v,size:[h],datatype:s===e._datatype&&l===n._datatype?u:void 0})}(e,n)}var l=t("_multiplyMatrixVector",{"DenseMatrix, any":function(e,n){var u,o=e._data,a=e._size,s=e._datatype||e.getDataType(),c=n._data,f=n._datatype||n.getDataType(),l=a[0],p=a[1],h=r,m=i;s&&f&&s===f&&"string"==typeof s&&"mixed"!==s&&(u=s,h=t.find(r,[u,u]),m=t.find(i,[u,u]));for(var d=[],v=0;v<l;v++){for(var g=o[v],D=m(g[0],c[0]),y=1;y<p;y++)D=h(D,m(g[y],c[y]));d[v]=D}return e.createDenseMatrix({data:d,size:[l],datatype:s===e._datatype&&f===n._datatype?u:void 0})},"SparseMatrix, any":function(e,n){var o=e._values,a=e._index,s=e._ptr,c=e._datatype||void 0===e._data?e._datatype:e.getDataType();if(!o)throw new Error("Cannot multiply Pattern only Matrix times Dense Matrix");var f,l=n._data,p=n._datatype||n.getDataType(),h=e._size[0],m=n._size[0],d=[],v=[],g=[],D=r,y=i,E=u,w=0;c&&p&&c===p&&"string"==typeof c&&"mixed"!==c&&(f=c,D=t.find(r,[f,f]),y=t.find(i,[f,f]),E=t.find(u,[f,f]),w=t.convert(0,f));var b=[],x=[];g[0]=0;for(var A=0;A<m;A++){var C=l[A];if(!E(C,w))for(var F=s[A],N=s[A+1],_=F;_<N;_++){var M=a[_];x[M]?b[M]=D(b[M],y(C,o[_])):(x[M]=!0,v.push(M),b[M]=y(C,o[_]))}}for(var S=v.length,O=0;O<S;O++){var B=v[O];d[O]=b[B]}return g[1]=v.length,e.createSparseMatrix({values:d,index:v,ptr:g,size:[h,1],datatype:c===e._datatype&&p===n._datatype?f:void 0})}}),p=t("_multiplyMatrixMatrix",{"DenseMatrix, DenseMatrix":function(e,n){var u,o=e._data,a=e._size,s=e._datatype||e.getDataType(),c=n._data,f=n._size,l=n._datatype||n.getDataType(),p=a[0],h=a[1],m=f[1],d=r,v=i;s&&l&&s===l&&"string"==typeof s&&"mixed"!==s&&"mixed"!==s&&(u=s,d=t.find(r,[u,u]),v=t.find(i,[u,u]));for(var g=[],D=0;D<p;D++){var y=o[D];g[D]=[];for(var E=0;E<m;E++){for(var w=v(y[0],c[0][E]),b=1;b<h;b++)w=d(w,v(y[b],c[b][E]));g[D][E]=w}}return e.createDenseMatrix({data:g,size:[p,m],datatype:s===e._datatype&&l===n._datatype?u:void 0})},"DenseMatrix, SparseMatrix":function(e,n){var o=e._data,a=e._size,s=e._datatype||e.getDataType(),c=n._values,f=n._index,l=n._ptr,p=n._size,h=n._datatype||void 0===n._data?n._datatype:n.getDataType();if(!c)throw new Error("Cannot multiply Dense Matrix times Pattern only Matrix");var m,d=a[0],v=p[1],g=r,D=i,y=u,E=0;s&&h&&s===h&&"string"==typeof s&&"mixed"!==s&&(m=s,g=t.find(r,[m,m]),D=t.find(i,[m,m]),y=t.find(u,[m,m]),E=t.convert(0,m));for(var w=[],b=[],x=[],A=n.createSparseMatrix({values:w,index:b,ptr:x,size:[d,v],datatype:s===e._datatype&&h===n._datatype?m:void 0}),C=0;C<v;C++){x[C]=b.length;var F=l[C],N=l[C+1];if(N>F)for(var _=0,M=0;M<d;M++){for(var S=M+1,O=void 0,B=F;B<N;B++){var T=f[B];_!==S?(O=D(o[M][T],c[B]),_=S):O=g(O,D(o[M][T],c[B]))}_!==S||y(O,E)||(b.push(M),w.push(O))}}return x[v]=b.length,A},"SparseMatrix, DenseMatrix":function(e,n){var o=e._values,a=e._index,s=e._ptr,c=e._datatype||void 0===e._data?e._datatype:e.getDataType();if(!o)throw new Error("Cannot multiply Pattern only Matrix times Dense Matrix");var f,l=n._data,p=n._datatype||n.getDataType(),h=e._size[0],m=n._size[0],d=n._size[1],v=r,g=i,D=u,y=0;c&&p&&c===p&&"string"==typeof c&&"mixed"!==c&&(f=c,v=t.find(r,[f,f]),g=t.find(i,[f,f]),D=t.find(u,[f,f]),y=t.convert(0,f));for(var E=[],w=[],b=[],x=e.createSparseMatrix({values:E,index:w,ptr:b,size:[h,d],datatype:c===e._datatype&&p===n._datatype?f:void 0}),A=[],C=[],F=0;F<d;F++){b[F]=w.length;for(var N=F+1,_=0;_<m;_++){var M=l[_][F];if(!D(M,y))for(var S=s[_],O=s[_+1],B=S;B<O;B++){var T=a[B];C[T]!==N?(C[T]=N,w.push(T),A[T]=g(M,o[B])):A[T]=v(A[T],g(M,o[B]))}}for(var R=b[F],I=w.length,z=R;z<I;z++){var P=w[z];E[z]=A[P]}}return b[d]=w.length,x},"SparseMatrix, SparseMatrix":function(e,n){var u,o=e._values,a=e._index,s=e._ptr,c=e._datatype||void 0===e._data?e._datatype:e.getDataType(),f=n._values,l=n._index,p=n._ptr,h=n._datatype||void 0===n._data?n._datatype:n.getDataType(),m=e._size[0],d=n._size[1],v=o&&f,g=r,D=i;c&&h&&c===h&&"string"==typeof c&&"mixed"!==c&&(u=c,g=t.find(r,[u,u]),D=t.find(i,[u,u]));for(var y,E,w,b,x,A,C,F,N=v?[]:void 0,_=[],M=[],S=e.createSparseMatrix({values:N,index:_,ptr:M,size:[m,d],datatype:c===e._datatype&&h===n._datatype?u:void 0}),O=v?[]:void 0,B=[],T=0;T<d;T++){M[T]=_.length;var R=T+1;for(x=p[T],A=p[T+1],b=x;b<A;b++)if(F=l[b],v)for(E=s[F],w=s[F+1],y=E;y<w;y++)B[C=a[y]]!==R?(B[C]=R,_.push(C),O[C]=D(f[b],o[y])):O[C]=g(O[C],D(f[b],o[y]));else for(E=s[F],w=s[F+1],y=E;y<w;y++)B[C=a[y]]!==R&&(B[C]=R,_.push(C));if(v)for(var I=M[T],z=_.length,P=I;P<z;P++){var U=_[P];N[P]=O[U]}}return M[d]=_.length,S}});return t(Yu,i,{"Array, Array":t.referTo("Matrix, Matrix",(e=>(t,r)=>{c(In(t),In(r));var i=e(n(t),n(r));return Je(i)?i.valueOf():i})),"Matrix, Matrix":function(e,t){var n=e.size(),r=t.size();return c(n,r),1===n.length?1===r.length?function(e,t,n){if(0===n)throw new Error("Cannot multiply two empty vectors");return o(e,t)}(e,t,n[0]):f(e,t):1===r.length?l(e,t):p(e,t)},"Matrix, Array":t.referTo("Matrix,Matrix",(e=>(t,r)=>e(t,n(r)))),"Array, Matrix":t.referToSelf((e=>(t,r)=>e(n(t,r.storage()),r))),"SparseMatrix, any":function(e,t){return a(e,t,i,!1)},"DenseMatrix, any":function(e,t){return s(e,t,i,!1)},"any, SparseMatrix":function(e,t){return a(t,e,i,!0)},"any, DenseMatrix":function(e,t){return s(t,e,i,!0)},"Array, any":function(e,t){return s(n(e),t,i,!1).valueOf()},"any, Array":function(e,t){return s(n(t),e,i,!0).valueOf()},"any, any":i,"any, any, ...any":t.referToSelf((e=>(t,n,r)=>{for(var i=e(t,n),u=0;u<r.length;u++)i=e(i,r[u]);return i}))})}));var Wu="ceil",Zu=["typed","config","round","matrix","equalScalar","zeros","DenseMatrix"],Ju=Tt(Wu,["typed","config","round"],(e=>{var{typed:t,config:n,round:r}=e;return t(Wu,{number:function(e){return on(e,r(e),n.epsilon)?r(e):Math.ceil(e)},"number, number":function(e,t){if(on(e,r(e,t),n.epsilon))return r(e,t);var[i,u]="".concat(e,"e").split("e"),o=Math.ceil(Number("".concat(i,"e").concat(Number(u)+t)));return[i,u]="".concat(o,"e").split("e"),Number("".concat(i,"e").concat(Number(u)-t))}})})),Qu=Tt(Wu,Zu,(e=>{var{typed:t,config:n,round:r,matrix:i,equalScalar:u,zeros:o,DenseMatrix:a}=e,s=du({typed:t,equalScalar:u}),c=vu({typed:t,DenseMatrix:a}),f=Du({typed:t}),l=Ju({typed:t,config:n,round:r});return t("ceil",{number:l.signatures.number,"number,number":l.signatures["number,number"],Complex:function(e){return e.ceil()},"Complex, number":function(e,t){return e.ceil(t)},"Complex, BigNumber":function(e,t){return e.ceil(t.toNumber())},BigNumber:function(e){return vi(e,r(e),n.epsilon)?r(e):e.ceil()},"BigNumber, BigNumber":function(e,t){return vi(e,r(e,t),n.epsilon)?r(e,t):e.toDecimalPlaces(t.toNumber(),qe.ROUND_CEIL)},Fraction:function(e){return e.ceil()},"Fraction, number":function(e,t){return e.ceil(t)},"Fraction, BigNumber":function(e,t){return e.ceil(t.toNumber())},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0))),"Array, number | BigNumber":t.referToSelf((e=>(t,n)=>vr(t,(t=>e(t,n)),!0))),"SparseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>s(t,n,e,!1))),"DenseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>f(t,n,e,!1))),"number | Complex | Fraction | BigNumber, Array":t.referToSelf((e=>(t,n)=>f(i(n),t,e,!0).valueOf())),"number | Complex | Fraction | BigNumber, Matrix":t.referToSelf((e=>(t,n)=>u(t,0)?o(n.size(),n.storage()):"dense"===n.storage()?f(n,t,e,!0):c(n,t,e,!0)))})}));n(1880);var Xu=Tt("det",["typed","matrix","subtractScalar","multiply","divideScalar","isZero","unaryMinus"],(e=>{var{typed:t,matrix:n,subtractScalar:r,multiply:i,divideScalar:u,isZero:o,unaryMinus:a}=e;return t("det",{any:function(e){return Mt(e)},"Array | Matrix":function(e){var t;switch((t=Je(e)?e.size():Array.isArray(e)?(e=n(e)).size():[]).length){case 0:return Mt(e);case 1:if(1===t[0])return Mt(e.valueOf()[0]);if(0===t[0])return 1;throw new RangeError("Matrix must be square (size: "+_n(t)+")");case 2:var s=t[0],c=t[1];if(s===c)return function(e,t){if(1===t)return Mt(e[0][0]);if(2===t)return r(i(e[0][0],e[1][1]),i(e[1][0],e[0][1]));for(var n=!1,s=new Array(t).fill(0).map(((e,t)=>t)),c=0;c<t;c++){var f=s[c];if(o(e[f][c])){var l=void 0;for(l=c+1;l<t;l++)if(!o(e[s[l]][c])){f=s[l],s[l]=s[c],s[c]=f,n=!n;break}if(l===t)return e[f][c]}for(var p=e[f][c],h=0===c?1:e[s[c-1]][c-1],m=c+1;m<t;m++)for(var d=s[m],v=c+1;v<t;v++)e[d][v]=u(r(i(e[d][v],p),i(e[d][c],e[f][v])),h)}var g=e[s[t-1]][t-1];return n?a(g):g}(e.clone().valueOf(),s);if(0===c)return 1;throw new RangeError("Matrix must be square (size: "+_n(t)+")");default:throw new RangeError("Matrix must be two dimensional (size: "+_n(t)+")")}}})})),Ku="fix",eo=["typed","Complex","matrix","ceil","floor","equalScalar","zeros","DenseMatrix"],to=Tt(Ku,["typed","ceil","floor"],(e=>{var{typed:t,ceil:n,floor:r}=e;return t(Ku,{number:function(e){return e>0?r(e):n(e)},"number, number":function(e,t){return e>0?r(e,t):n(e,t)}})})),no=Tt(Ku,eo,(e=>{var{typed:t,Complex:n,matrix:r,ceil:i,floor:u,equalScalar:o,zeros:a,DenseMatrix:s}=e,c=vu({typed:t,DenseMatrix:s}),f=Du({typed:t}),l=to({typed:t,ceil:i,floor:u});return t("fix",{number:l.signatures.number,"number, number | BigNumber":l.signatures["number,number"],Complex:function(e){return new n(e.re>0?Math.floor(e.re):Math.ceil(e.re),e.im>0?Math.floor(e.im):Math.ceil(e.im))},"Complex, number":function(e,t){return new n(e.re>0?u(e.re,t):i(e.re,t),e.im>0?u(e.im,t):i(e.im,t))},"Complex, BigNumber":function(e,t){var r=t.toNumber();return new n(e.re>0?u(e.re,r):i(e.re,r),e.im>0?u(e.im,r):i(e.im,r))},BigNumber:function(e){return e.isNegative()?i(e):u(e)},"BigNumber, number | BigNumber":function(e,t){return e.isNegative()?i(e,t):u(e,t)},Fraction:function(e){return e.s<0?e.ceil():e.floor()},"Fraction, number | BigNumber":function(e,t){return e.s<0?i(e,t):u(e,t)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0))),"Array | Matrix, number | BigNumber":t.referToSelf((e=>(t,n)=>vr(t,(t=>e(t,n)),!0))),"number | Complex | Fraction | BigNumber, Array":t.referToSelf((e=>(t,n)=>f(r(n),t,e,!0).valueOf())),"number | Complex | Fraction | BigNumber, Matrix":t.referToSelf((e=>(t,n)=>o(t,0)?a(n.size(),n.storage()):"dense"===n.storage()?f(n,t,e,!0):c(n,t,e,!0)))})})),ro=Tt("inv",["typed","matrix","divideScalar","addScalar","multiply","unaryMinus","det","identity","abs"],(e=>{var{typed:t,matrix:n,divideScalar:r,addScalar:i,multiply:u,unaryMinus:o,det:a,identity:s,abs:c}=e;return t("inv",{"Array | Matrix":function(e){var t=Je(e)?e.size():In(e);switch(t.length){case 1:if(1===t[0])return Je(e)?n([r(1,e.valueOf()[0])]):[r(1,e[0])];throw new RangeError("Matrix must be square (size: "+_n(t)+")");case 2:var i=t[0],u=t[1];if(i===u)return Je(e)?n(f(e.valueOf(),i,u),e.storage()):f(e,i,u);throw new RangeError("Matrix must be square (size: "+_n(t)+")");default:throw new RangeError("Matrix must be two dimensional (size: "+_n(t)+")")}},any:function(e){return r(1,e)}});function f(e,t,n){var f,l,p,h,m;if(1===t){if(0===(h=e[0][0]))throw Error("Cannot calculate inverse, determinant is zero");return[[r(1,h)]]}if(2===t){var d=a(e);if(0===d)throw Error("Cannot calculate inverse, determinant is zero");return[[r(e[1][1],d),r(o(e[0][1]),d)],[r(o(e[1][0]),d),r(e[0][0],d)]]}var v=e.concat();for(f=0;f<t;f++)v[f]=v[f].concat();for(var g=s(t).valueOf(),D=0;D<n;D++){var y=c(v[D][D]),E=D;for(f=D+1;f<t;)c(v[f][D])>y&&(y=c(v[f][D]),E=f),f++;if(0===y)throw Error("Cannot calculate inverse, determinant is zero");(f=E)!==D&&(m=v[D],v[D]=v[f],v[f]=m,m=g[D],g[D]=g[f],g[f]=m);var w=v[D],b=g[D];for(f=0;f<t;f++){var x=v[f],A=g[f];if(f!==D){if(0!==x[D]){for(p=r(o(x[D]),w[D]),l=D;l<n;l++)x[l]=i(x[l],u(p,w[l]));for(l=0;l<n;l++)A[l]=i(A[l],u(p,b[l]))}}else{for(p=w[D],l=D;l<n;l++)x[l]=r(x[l],p);for(l=0;l<n;l++)A[l]=r(A[l],p)}}}return g}})),io=Tt("pow",["typed","config","identity","multiply","matrix","inv","fraction","number","Complex"],(e=>{var{typed:t,config:n,identity:r,multiply:i,matrix:u,inv:o,number:a,fraction:s,Complex:c}=e;return t("pow",{"number, number":f,"Complex, Complex":function(e,t){return e.pow(t)},"BigNumber, BigNumber":function(e,t){return t.isInteger()||e>=0||n.predictable?e.pow(t):new c(e.toNumber(),0).pow(t.toNumber(),0)},"Fraction, Fraction":function(e,t){var r=e.pow(t);if(null!=r)return r;if(n.predictable)throw new Error("Result of pow is non-rational and cannot be expressed as a fraction");return f(e.valueOf(),t.valueOf())},"Array, number":l,"Array, BigNumber":function(e,t){return l(e,t.toNumber())},"Matrix, number":p,"Matrix, BigNumber":function(e,t){return p(e,t.toNumber())},"Unit, number | BigNumber":function(e,t){return e.pow(t)}});function f(e,t){if(n.predictable&&!Gt(t)&&e<0)try{var r=s(t),i=a(r);if((t===i||Math.abs((t-i)/t)<1e-14)&&r.d%2==1)return(r.n%2==0?1:-1)*Math.pow(-e,t)}catch(e){}return n.predictable&&(e<-1&&t===1/0||e>-1&&e<0&&t===-1/0)?NaN:Gt(t)||e>=0||n.predictable?Ir(e,t):e*e<1&&t===1/0||e*e>1&&t===-1/0?0:new c(e,0).pow(t,0)}function l(e,t){if(!Gt(t))throw new TypeError("For A^b, b must be an integer (value is "+t+")");var n=In(e);if(2!==n.length)throw new Error("For A^b, A must be 2 dimensional (A has "+n.length+" dimensions)");if(n[0]!==n[1])throw new Error("For A^b, A must be square (size is "+n[0]+"x"+n[1]+")");if(t<0)try{return l(o(e),-t)}catch(e){if("Cannot calculate inverse, determinant is zero"===e.message)throw new TypeError("For A^b, when A is not invertible, b must be a positive integer (value is "+t+")");throw e}for(var u=r(n[0]).valueOf(),a=e;t>=1;)1&~t||(u=i(a,u)),t>>=1,a=i(a,a);return u}function p(e,t){return u(l(e.valueOf(),t))}}));function uo(e){return uo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},uo(e)}function oo(e){var t=function(e,t){if("object"!=uo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=uo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==uo(t)?t:t+""}function ao(e,t,n){return(t=oo(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function so(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function co(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?so(Object(n),!0).forEach((function(t){ao(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):so(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var fo=Tt("Unit",["?on","config","addScalar","subtractScalar","multiplyScalar","divideScalar","pow","abs","fix","round","equal","isNumeric","format","number","Complex","BigNumber","Fraction"],(t=>{var n,r,i,{on:u,config:o,addScalar:a,subtractScalar:s,multiplyScalar:c,divideScalar:f,pow:l,abs:p,fix:h,round:m,equal:d,isNumeric:v,format:g,number:D,Complex:y,BigNumber:E,Fraction:w}=t,b=D;function x(t,n){if(!(this instanceof x))throw new Error("Constructor must be called with the new operator");if(null!=t&&!v(t)&&!Ve(t))throw new TypeError("First parameter in Unit constructor must be number, BigNumber, Fraction, Complex, or undefined");if(this.fixPrefix=!1,this.skipAutomaticSimplification=!0,void 0===n)this.units=[],this.dimensions=R.map((e=>0));else if("string"==typeof n){var r=x.parse(n);this.units=r.units,this.dimensions=r.dimensions}else{if(!$e(n)||null!==n.value)throw new TypeError("Second parameter in Unit constructor must be a string or valueless Unit");this.fixPrefix=n.fixPrefix,this.skipAutomaticSimplification=n.skipAutomaticSimplification,this.dimensions=n.dimensions.slice(0),this.units=n.units.map((t=>e({},t)))}this.value=this._normalize(t)}function A(){for(;" "===i||"\t"===i;)F()}function C(e){return e>="0"&&e<="9"}function F(){r++,i=n.charAt(r)}function N(e){r=e,i=n.charAt(r)}function _(){var e="",t=r;if("+"===i?F():"-"===i&&(e+=i,F()),!function(e){return e>="0"&&e<="9"||"."===e}(i))return N(t),null;if("."===i){if(e+=i,F(),!C(i))return N(t),null}else{for(;C(i);)e+=i,F();"."===i&&(e+=i,F())}for(;C(i);)e+=i,F();if("E"===i||"e"===i){var n="",u=r;if(n+=i,F(),"+"!==i&&"-"!==i||(n+=i,F()),!C(i))return N(u),e;for(e+=n;C(i);)e+=i,F()}return e}function M(){for(var e="";C(i)||x.isValidAlpha(i);)e+=i,F();var t=e.charAt(0);return x.isValidAlpha(t)?e:null}function S(e){return i===e?(F(),e):null}Object.defineProperty(x,"name",{value:"Unit"}),x.prototype.constructor=x,x.prototype.type="Unit",x.prototype.isUnit=!0,x.parse=function(e,t){if(t=t||{},r=-1,i="","string"!=typeof(n=e))throw new TypeError("Invalid argument in Unit.parse, string expected");var u=new x;u.units=[];var a=1,s=!1;F(),A();var c=_(),f=null;if(c){if("BigNumber"===o.number)f=new E(c);else if("Fraction"===o.number)try{f=new w(c)}catch(e){f=parseFloat(c)}else f=parseFloat(c);A(),S("*")?(a=1,s=!0):S("/")&&(a=-1,s=!0)}for(var l=[],p=1;;){for(A();"("===i;)l.push(a),p*=a,a=1,F(),A();var h=void 0;if(!i)break;var m=i;if(null===(h=M()))throw new SyntaxError('Unexpected "'+m+'" in "'+n+'" at index '+r.toString());var d=O(h);if(null===d)throw new SyntaxError('Unit "'+h+'" not found.');var v=a*p;if(A(),S("^")){A();var g=_();if(null===g)throw new SyntaxError('In "'+e+'", "^" must be followed by a floating-point number');v*=g}u.units.push({unit:d.unit,prefix:d.prefix,power:v});for(var D=0;D<R.length;D++)u.dimensions[D]+=(d.unit.dimensions[D]||0)*v;for(A();")"===i;){if(0===l.length)throw new SyntaxError('Unmatched ")" in "'+n+'" at index '+r.toString());p/=l.pop(),F(),A()}if(s=!1,S("*")?(a=1,s=!0):S("/")?(a=-1,s=!0):a=1,d.unit.base){var y=d.unit.base.key;k.auto[y]={unit:d.unit,prefix:d.prefix}}}if(A(),i)throw new SyntaxError('Could not parse: "'+e+'"');if(s)throw new SyntaxError('Trailing characters: "'+e+'"');if(0!==l.length)throw new SyntaxError('Unmatched "(" in "'+n+'"');if(0===u.units.length&&!t.allowNoUnits)throw new SyntaxError('"'+e+'" contains no units');return u.value=void 0!==f?u._normalize(f):null,u},x.prototype.clone=function(){var e=new x;e.fixPrefix=this.fixPrefix,e.skipAutomaticSimplification=this.skipAutomaticSimplification,e.value=Mt(this.value),e.dimensions=this.dimensions.slice(0),e.units=[];for(var t=0;t<this.units.length;t++)for(var n in e.units[t]={},this.units[t])Bt(this.units[t],n)&&(e.units[t][n]=this.units[t][n]);return e},x.prototype.valueType=function(){return _t(this.value)},x.prototype._isDerived=function(){return 0!==this.units.length&&(this.units.length>1||Math.abs(this.units[0].power-1)>1e-15)},x.prototype._normalize=function(e){if(null==e||0===this.units.length)return e;for(var t=e,n=x._getNumberConverter(_t(e)),r=0;r<this.units.length;r++){var i=n(this.units[r].unit.value),u=n(this.units[r].prefix.value),o=n(this.units[r].power);t=c(t,l(c(i,u),o))}return t},x.prototype._denormalize=function(e,t){if(null==e||0===this.units.length)return e;for(var n=e,r=x._getNumberConverter(_t(e)),i=0;i<this.units.length;i++){var u=r(this.units[i].unit.value),o=r(this.units[i].prefix.value),a=r(this.units[i].power);n=f(n,l(c(u,o),a))}return n};var O=dn((e=>{if(Bt(U,e)){var t=U[e];return{unit:t,prefix:t.prefixes[""]}}for(var n in U)if(Bt(U,n)&&Nn(e,n)){var r=U[n],i=e.length-n.length,u=e.substring(0,i),o=Bt(r.prefixes,u)?r.prefixes[u]:void 0;if(void 0!==o)return{unit:r,prefix:o}}return null}),{hasher:e=>e[0],limit:100});function B(e){return e.equalBase(I.NONE)&&null!==e.value&&!o.predictable?e.value:e}x.isValuelessUnit=function(e){return null!==O(e)},x.prototype.hasBase=function(e){if("string"==typeof e&&(e=I[e]),!e)return!1;for(var t=0;t<R.length;t++)if(Math.abs((this.dimensions[t]||0)-(e.dimensions[t]||0))>1e-12)return!1;return!0},x.prototype.equalBase=function(e){for(var t=0;t<R.length;t++)if(Math.abs((this.dimensions[t]||0)-(e.dimensions[t]||0))>1e-12)return!1;return!0},x.prototype.equals=function(e){return this.equalBase(e)&&d(this.value,e.value)},x.prototype.multiply=function(e){for(var t=this.clone(),n=$e(e)?e:new x(e),r=0;r<R.length;r++)t.dimensions[r]=(this.dimensions[r]||0)+(n.dimensions[r]||0);for(var i=0;i<n.units.length;i++){var u=co({},n.units[i]);t.units.push(u)}if(null!==this.value||null!==n.value){var o=null===this.value?this._normalize(1):this.value,a=null===n.value?n._normalize(1):n.value;t.value=c(o,a)}else t.value=null;return $e(e)&&(t.skipAutomaticSimplification=!1),B(t)},x.prototype.divideInto=function(e){return new x(e).divide(this)},x.prototype.divide=function(e){for(var t=this.clone(),n=$e(e)?e:new x(e),r=0;r<R.length;r++)t.dimensions[r]=(this.dimensions[r]||0)-(n.dimensions[r]||0);for(var i=0;i<n.units.length;i++){var u=co(co({},n.units[i]),{},{power:-n.units[i].power});t.units.push(u)}if(null!==this.value||null!==n.value){var o=null===this.value?this._normalize(1):this.value,a=null===n.value?n._normalize(1):n.value;t.value=f(o,a)}else t.value=null;return $e(e)&&(t.skipAutomaticSimplification=!1),B(t)},x.prototype.pow=function(e){for(var t=this.clone(),n=0;n<R.length;n++)t.dimensions[n]=(this.dimensions[n]||0)*e;for(var r=0;r<t.units.length;r++)t.units[r].power*=e;return null!==t.value?t.value=l(t.value,e):t.value=null,t.skipAutomaticSimplification=!1,B(t)},x.prototype.abs=function(){var e=this.clone();if(null!==e.value)if(e._isDerived()||0===e.units.length||0===e.units[0].unit.offset)e.value=p(e.value);else{var t=e._numberConverter(),n=t(e.units[0].unit.value),r=t(e.units[0].unit.offset),i=c(n,r);e.value=s(p(a(e.value,i)),i)}for(var u in e.units)"VA"!==e.units[u].unit.name&&"VAR"!==e.units[u].unit.name||(e.units[u].unit=U.W);return e},x.prototype.to=function(e){var t,n=null===this.value?this._normalize(1):this.value;if("string"==typeof e)t=x.parse(e);else{if(!$e(e))throw new Error("String or Unit expected as parameter");t=e.clone()}if(!this.equalBase(t))throw new Error("Units do not match ('".concat(t.toString(),"' != '").concat(this.toString(),"')"));if(null!==t.value)throw new Error("Cannot convert to a unit with a value");if(null===this.value||this._isDerived()||0===this.units.length||0===t.units.length||this.units[0].unit.offset===t.units[0].unit.offset)t.value=Mt(n);else{var r=x._getNumberConverter(_t(n)),i=this.units[0].unit.value,u=this.units[0].unit.offset,o=c(i,u),f=t.units[0].unit.value,l=t.units[0].unit.offset,p=c(f,l);t.value=a(n,r(s(o,p)))}return t.fixPrefix=!0,t.skipAutomaticSimplification=!0,t},x.prototype.toNumber=function(e){return b(this.toNumeric(e))},x.prototype.toNumeric=function(e){var t;return(t=e?this.to(e):this.clone())._isDerived()||0===t.units.length?t._denormalize(t.value):t._denormalize(t.value,t.units[0].prefix.value)},x.prototype.toString=function(){return this.format()},x.prototype.toJSON=function(){return{mathjs:"Unit",value:this._denormalize(this.value),unit:this.units.length>0?this.formatUnits():null,fixPrefix:this.fixPrefix}},x.fromJSON=function(e){var t,n=new x(e.value,null!==(t=e.unit)&&void 0!==t?t:void 0);return n.fixPrefix=e.fixPrefix||!1,n},x.prototype.valueOf=x.prototype.toString,x.prototype.simplify=function(){var e,t,n=this.clone(),r=[];for(var i in q)if(Bt(q,i)&&n.hasBase(I[i])){e=i;break}if("NONE"===e)n.units=[];else if(e&&Bt(q,e)&&(t=q[e]),t)n.units=[{unit:t.unit,prefix:t.prefix,power:1}];else{for(var u=!1,o=0;o<R.length;o++){var a=R[o];Math.abs(n.dimensions[o]||0)>1e-12&&(Bt(q,a)?r.push({unit:q[a].unit,prefix:q[a].prefix,power:n.dimensions[o]||0}):u=!0)}r.length<n.units.length&&!u&&(n.units=r)}return n},x.prototype.toSI=function(){for(var e=this.clone(),t=[],n=0;n<R.length;n++){var r=R[n];if(Math.abs(e.dimensions[n]||0)>1e-12){if(!Bt(k.si,r))throw new Error("Cannot express custom unit "+r+" in SI units");t.push({unit:k.si[r].unit,prefix:k.si[r].prefix,power:e.dimensions[n]||0})}}return e.units=t,e.fixPrefix=!0,e.skipAutomaticSimplification=!0,null!==this.value?(e.value=null,this.to(e)):e},x.prototype.formatUnits=function(){for(var e="",t="",n=0,r=0,i=0;i<this.units.length;i++)this.units[i].power>0?(n++,e+=" "+this.units[i].prefix.name+this.units[i].unit.name,Math.abs(this.units[i].power-1)>1e-15&&(e+="^"+this.units[i].power)):this.units[i].power<0&&r++;if(r>0)for(var u=0;u<this.units.length;u++)this.units[u].power<0&&(n>0?(t+=" "+this.units[u].prefix.name+this.units[u].unit.name,Math.abs(this.units[u].power+1)>1e-15&&(t+="^"+-this.units[u].power)):(t+=" "+this.units[u].prefix.name+this.units[u].unit.name,t+="^"+this.units[u].power));e=e.substr(1),t=t.substr(1),n>1&&r>0&&(e="("+e+")"),r>1&&n>0&&(t="("+t+")");var o=e;return n>0&&r>0&&(o+=" / "),o+=t},x.prototype.format=function(e){var t=this.skipAutomaticSimplification||null===this.value?this.clone():this.simplify(),n=!1;for(var r in void 0!==t.value&&null!==t.value&&Ve(t.value)&&(n=Math.abs(t.value.re)<1e-14),t.units)Bt(t.units,r)&&t.units[r].unit&&("VA"===t.units[r].unit.name&&n?t.units[r].unit=U.VAR:"VAR"!==t.units[r].unit.name||n||(t.units[r].unit=U.VA));1!==t.units.length||t.fixPrefix||Math.abs(t.units[0].power-Math.round(t.units[0].power))<1e-14&&(t.units[0].prefix=t._bestPrefix());var i=t._denormalize(t.value),u=null!==t.value?g(i,e||{}):"",o=t.formatUnits();return t.value&&Ve(t.value)&&(u="("+u+")"),o.length>0&&u.length>0&&(u+=" "),u+=o},x.prototype._bestPrefix=function(){if(1!==this.units.length)throw new Error("Can only compute the best prefix for single units with integer powers, like kg, s^2, N^-1, and so forth!");if(Math.abs(this.units[0].power-Math.round(this.units[0].power))>=1e-14)throw new Error("Can only compute the best prefix for single units with integer powers, like kg, s^2, N^-1, and so forth!");var e=null!==this.value?p(this.value):0,t=p(this.units[0].unit.value),n=this.units[0].prefix;if(0===e)return n;var r=this.units[0].power,i=Math.log(e/Math.pow(n.value*t,r))/Math.LN10-1.2;if(i>-2.200001&&i<1.800001)return n;i=Math.abs(i);var u=this.units[0].unit.prefixes;for(var o in u)if(Bt(u,o)){var a=u[o];if(a.scientific){var s=Math.abs(Math.log(e/Math.pow(a.value*t,r))/Math.LN10-1.2);(s<i||s===i&&a.name.length<n.name.length)&&(n=a,i=s)}}return n},x.prototype.splitUnit=function(e){for(var t=this.clone(),n=[],r=0;r<e.length&&(t=t.to(e[r]),r!==e.length-1);r++){var i=t.toNumeric(),u=m(i),o=new x(d(u,i)?u:h(t.toNumeric()),e[r].toString());n.push(o),t=s(t,o)}for(var c=0,f=0;f<n.length;f++)c=a(c,n[f].value);return d(c,this.value)&&(t.value=0),n.push(t),n};var T={NONE:{"":{name:"",value:1,scientific:!0}},SHORT:{"":{name:"",value:1,scientific:!0},da:{name:"da",value:10,scientific:!1},h:{name:"h",value:100,scientific:!1},k:{name:"k",value:1e3,scientific:!0},M:{name:"M",value:1e6,scientific:!0},G:{name:"G",value:1e9,scientific:!0},T:{name:"T",value:1e12,scientific:!0},P:{name:"P",value:1e15,scientific:!0},E:{name:"E",value:1e18,scientific:!0},Z:{name:"Z",value:1e21,scientific:!0},Y:{name:"Y",value:1e24,scientific:!0},R:{name:"R",value:1e27,scientific:!0},Q:{name:"Q",value:1e30,scientific:!0},d:{name:"d",value:.1,scientific:!1},c:{name:"c",value:.01,scientific:!1},m:{name:"m",value:.001,scientific:!0},u:{name:"u",value:1e-6,scientific:!0},n:{name:"n",value:1e-9,scientific:!0},p:{name:"p",value:1e-12,scientific:!0},f:{name:"f",value:1e-15,scientific:!0},a:{name:"a",value:1e-18,scientific:!0},z:{name:"z",value:1e-21,scientific:!0},y:{name:"y",value:1e-24,scientific:!0},r:{name:"r",value:1e-27,scientific:!0},q:{name:"q",value:1e-30,scientific:!0}},LONG:{"":{name:"",value:1,scientific:!0},deca:{name:"deca",value:10,scientific:!1},hecto:{name:"hecto",value:100,scientific:!1},kilo:{name:"kilo",value:1e3,scientific:!0},mega:{name:"mega",value:1e6,scientific:!0},giga:{name:"giga",value:1e9,scientific:!0},tera:{name:"tera",value:1e12,scientific:!0},peta:{name:"peta",value:1e15,scientific:!0},exa:{name:"exa",value:1e18,scientific:!0},zetta:{name:"zetta",value:1e21,scientific:!0},yotta:{name:"yotta",value:1e24,scientific:!0},ronna:{name:"ronna",value:1e27,scientific:!0},quetta:{name:"quetta",value:1e30,scientific:!0},deci:{name:"deci",value:.1,scientific:!1},centi:{name:"centi",value:.01,scientific:!1},milli:{name:"milli",value:.001,scientific:!0},micro:{name:"micro",value:1e-6,scientific:!0},nano:{name:"nano",value:1e-9,scientific:!0},pico:{name:"pico",value:1e-12,scientific:!0},femto:{name:"femto",value:1e-15,scientific:!0},atto:{name:"atto",value:1e-18,scientific:!0},zepto:{name:"zepto",value:1e-21,scientific:!0},yocto:{name:"yocto",value:1e-24,scientific:!0},ronto:{name:"ronto",value:1e-27,scientific:!0},quecto:{name:"quecto",value:1e-30,scientific:!0}},SQUARED:{"":{name:"",value:1,scientific:!0},da:{name:"da",value:100,scientific:!1},h:{name:"h",value:1e4,scientific:!1},k:{name:"k",value:1e6,scientific:!0},M:{name:"M",value:1e12,scientific:!0},G:{name:"G",value:1e18,scientific:!0},T:{name:"T",value:1e24,scientific:!0},P:{name:"P",value:1e30,scientific:!0},E:{name:"E",value:1e36,scientific:!0},Z:{name:"Z",value:1e42,scientific:!0},Y:{name:"Y",value:1e48,scientific:!0},R:{name:"R",value:1e54,scientific:!0},Q:{name:"Q",value:1e60,scientific:!0},d:{name:"d",value:.01,scientific:!1},c:{name:"c",value:1e-4,scientific:!1},m:{name:"m",value:1e-6,scientific:!0},u:{name:"u",value:1e-12,scientific:!0},n:{name:"n",value:1e-18,scientific:!0},p:{name:"p",value:1e-24,scientific:!0},f:{name:"f",value:1e-30,scientific:!0},a:{name:"a",value:1e-36,scientific:!0},z:{name:"z",value:1e-42,scientific:!0},y:{name:"y",value:1e-48,scientific:!0},r:{name:"r",value:1e-54,scientific:!0},q:{name:"q",value:1e-60,scientific:!0}},CUBIC:{"":{name:"",value:1,scientific:!0},da:{name:"da",value:1e3,scientific:!1},h:{name:"h",value:1e6,scientific:!1},k:{name:"k",value:1e9,scientific:!0},M:{name:"M",value:1e18,scientific:!0},G:{name:"G",value:1e27,scientific:!0},T:{name:"T",value:1e36,scientific:!0},P:{name:"P",value:1e45,scientific:!0},E:{name:"E",value:1e54,scientific:!0},Z:{name:"Z",value:1e63,scientific:!0},Y:{name:"Y",value:1e72,scientific:!0},R:{name:"R",value:1e81,scientific:!0},Q:{name:"Q",value:1e90,scientific:!0},d:{name:"d",value:.001,scientific:!1},c:{name:"c",value:1e-6,scientific:!1},m:{name:"m",value:1e-9,scientific:!0},u:{name:"u",value:1e-18,scientific:!0},n:{name:"n",value:1e-27,scientific:!0},p:{name:"p",value:1e-36,scientific:!0},f:{name:"f",value:1e-45,scientific:!0},a:{name:"a",value:1e-54,scientific:!0},z:{name:"z",value:1e-63,scientific:!0},y:{name:"y",value:1e-72,scientific:!0},r:{name:"r",value:1e-81,scientific:!0},q:{name:"q",value:1e-90,scientific:!0}},BINARY_SHORT_SI:{"":{name:"",value:1,scientific:!0},k:{name:"k",value:1e3,scientific:!0},M:{name:"M",value:1e6,scientific:!0},G:{name:"G",value:1e9,scientific:!0},T:{name:"T",value:1e12,scientific:!0},P:{name:"P",value:1e15,scientific:!0},E:{name:"E",value:1e18,scientific:!0},Z:{name:"Z",value:1e21,scientific:!0},Y:{name:"Y",value:1e24,scientific:!0}},BINARY_SHORT_IEC:{"":{name:"",value:1,scientific:!0},Ki:{name:"Ki",value:1024,scientific:!0},Mi:{name:"Mi",value:Math.pow(1024,2),scientific:!0},Gi:{name:"Gi",value:Math.pow(1024,3),scientific:!0},Ti:{name:"Ti",value:Math.pow(1024,4),scientific:!0},Pi:{name:"Pi",value:Math.pow(1024,5),scientific:!0},Ei:{name:"Ei",value:Math.pow(1024,6),scientific:!0},Zi:{name:"Zi",value:Math.pow(1024,7),scientific:!0},Yi:{name:"Yi",value:Math.pow(1024,8),scientific:!0}},BINARY_LONG_SI:{"":{name:"",value:1,scientific:!0},kilo:{name:"kilo",value:1e3,scientific:!0},mega:{name:"mega",value:1e6,scientific:!0},giga:{name:"giga",value:1e9,scientific:!0},tera:{name:"tera",value:1e12,scientific:!0},peta:{name:"peta",value:1e15,scientific:!0},exa:{name:"exa",value:1e18,scientific:!0},zetta:{name:"zetta",value:1e21,scientific:!0},yotta:{name:"yotta",value:1e24,scientific:!0}},BINARY_LONG_IEC:{"":{name:"",value:1,scientific:!0},kibi:{name:"kibi",value:1024,scientific:!0},mebi:{name:"mebi",value:Math.pow(1024,2),scientific:!0},gibi:{name:"gibi",value:Math.pow(1024,3),scientific:!0},tebi:{name:"tebi",value:Math.pow(1024,4),scientific:!0},pebi:{name:"pebi",value:Math.pow(1024,5),scientific:!0},exi:{name:"exi",value:Math.pow(1024,6),scientific:!0},zebi:{name:"zebi",value:Math.pow(1024,7),scientific:!0},yobi:{name:"yobi",value:Math.pow(1024,8),scientific:!0}},BTU:{"":{name:"",value:1,scientific:!0},MM:{name:"MM",value:1e6,scientific:!0}}};T.SHORTLONG=e({},T.SHORT,T.LONG),T.BINARY_SHORT=e({},T.BINARY_SHORT_SI,T.BINARY_SHORT_IEC),T.BINARY_LONG=e({},T.BINARY_LONG_SI,T.BINARY_LONG_IEC);var R=["MASS","LENGTH","TIME","CURRENT","TEMPERATURE","LUMINOUS_INTENSITY","AMOUNT_OF_SUBSTANCE","ANGLE","BIT"],I={NONE:{dimensions:[0,0,0,0,0,0,0,0,0]},MASS:{dimensions:[1,0,0,0,0,0,0,0,0]},LENGTH:{dimensions:[0,1,0,0,0,0,0,0,0]},TIME:{dimensions:[0,0,1,0,0,0,0,0,0]},CURRENT:{dimensions:[0,0,0,1,0,0,0,0,0]},TEMPERATURE:{dimensions:[0,0,0,0,1,0,0,0,0]},LUMINOUS_INTENSITY:{dimensions:[0,0,0,0,0,1,0,0,0]},AMOUNT_OF_SUBSTANCE:{dimensions:[0,0,0,0,0,0,1,0,0]},FORCE:{dimensions:[1,1,-2,0,0,0,0,0,0]},SURFACE:{dimensions:[0,2,0,0,0,0,0,0,0]},VOLUME:{dimensions:[0,3,0,0,0,0,0,0,0]},ENERGY:{dimensions:[1,2,-2,0,0,0,0,0,0]},POWER:{dimensions:[1,2,-3,0,0,0,0,0,0]},PRESSURE:{dimensions:[1,-1,-2,0,0,0,0,0,0]},ELECTRIC_CHARGE:{dimensions:[0,0,1,1,0,0,0,0,0]},ELECTRIC_CAPACITANCE:{dimensions:[-1,-2,4,2,0,0,0,0,0]},ELECTRIC_POTENTIAL:{dimensions:[1,2,-3,-1,0,0,0,0,0]},ELECTRIC_RESISTANCE:{dimensions:[1,2,-3,-2,0,0,0,0,0]},ELECTRIC_INDUCTANCE:{dimensions:[1,2,-2,-2,0,0,0,0,0]},ELECTRIC_CONDUCTANCE:{dimensions:[-1,-2,3,2,0,0,0,0,0]},MAGNETIC_FLUX:{dimensions:[1,2,-2,-1,0,0,0,0,0]},MAGNETIC_FLUX_DENSITY:{dimensions:[1,0,-2,-1,0,0,0,0,0]},FREQUENCY:{dimensions:[0,0,-1,0,0,0,0,0,0]},ANGLE:{dimensions:[0,0,0,0,0,0,0,1,0]},BIT:{dimensions:[0,0,0,0,0,0,0,0,1]}};for(var z in I)Bt(I,z)&&(I[z].key=z);var P={name:"",base:{},value:1,offset:0,dimensions:R.map((e=>0))},U={meter:{name:"meter",base:I.LENGTH,prefixes:T.LONG,value:1,offset:0},inch:{name:"inch",base:I.LENGTH,prefixes:T.NONE,value:.0254,offset:0},foot:{name:"foot",base:I.LENGTH,prefixes:T.NONE,value:.3048,offset:0},yard:{name:"yard",base:I.LENGTH,prefixes:T.NONE,value:.9144,offset:0},mile:{name:"mile",base:I.LENGTH,prefixes:T.NONE,value:1609.344,offset:0},link:{name:"link",base:I.LENGTH,prefixes:T.NONE,value:.201168,offset:0},rod:{name:"rod",base:I.LENGTH,prefixes:T.NONE,value:5.0292,offset:0},chain:{name:"chain",base:I.LENGTH,prefixes:T.NONE,value:20.1168,offset:0},angstrom:{name:"angstrom",base:I.LENGTH,prefixes:T.NONE,value:1e-10,offset:0},m:{name:"m",base:I.LENGTH,prefixes:T.SHORT,value:1,offset:0},in:{name:"in",base:I.LENGTH,prefixes:T.NONE,value:.0254,offset:0},ft:{name:"ft",base:I.LENGTH,prefixes:T.NONE,value:.3048,offset:0},yd:{name:"yd",base:I.LENGTH,prefixes:T.NONE,value:.9144,offset:0},mi:{name:"mi",base:I.LENGTH,prefixes:T.NONE,value:1609.344,offset:0},li:{name:"li",base:I.LENGTH,prefixes:T.NONE,value:.201168,offset:0},rd:{name:"rd",base:I.LENGTH,prefixes:T.NONE,value:5.02921,offset:0},ch:{name:"ch",base:I.LENGTH,prefixes:T.NONE,value:20.1168,offset:0},mil:{name:"mil",base:I.LENGTH,prefixes:T.NONE,value:254e-7,offset:0},m2:{name:"m2",base:I.SURFACE,prefixes:T.SQUARED,value:1,offset:0},sqin:{name:"sqin",base:I.SURFACE,prefixes:T.NONE,value:64516e-8,offset:0},sqft:{name:"sqft",base:I.SURFACE,prefixes:T.NONE,value:.09290304,offset:0},sqyd:{name:"sqyd",base:I.SURFACE,prefixes:T.NONE,value:.83612736,offset:0},sqmi:{name:"sqmi",base:I.SURFACE,prefixes:T.NONE,value:2589988.110336,offset:0},sqrd:{name:"sqrd",base:I.SURFACE,prefixes:T.NONE,value:25.29295,offset:0},sqch:{name:"sqch",base:I.SURFACE,prefixes:T.NONE,value:404.6873,offset:0},sqmil:{name:"sqmil",base:I.SURFACE,prefixes:T.NONE,value:6.4516e-10,offset:0},acre:{name:"acre",base:I.SURFACE,prefixes:T.NONE,value:4046.86,offset:0},hectare:{name:"hectare",base:I.SURFACE,prefixes:T.NONE,value:1e4,offset:0},m3:{name:"m3",base:I.VOLUME,prefixes:T.CUBIC,value:1,offset:0},L:{name:"L",base:I.VOLUME,prefixes:T.SHORT,value:.001,offset:0},l:{name:"l",base:I.VOLUME,prefixes:T.SHORT,value:.001,offset:0},litre:{name:"litre",base:I.VOLUME,prefixes:T.LONG,value:.001,offset:0},cuin:{name:"cuin",base:I.VOLUME,prefixes:T.NONE,value:16387064e-12,offset:0},cuft:{name:"cuft",base:I.VOLUME,prefixes:T.NONE,value:.028316846592,offset:0},cuyd:{name:"cuyd",base:I.VOLUME,prefixes:T.NONE,value:.************,offset:0},teaspoon:{name:"teaspoon",base:I.VOLUME,prefixes:T.NONE,value:5e-6,offset:0},tablespoon:{name:"tablespoon",base:I.VOLUME,prefixes:T.NONE,value:15e-6,offset:0},drop:{name:"drop",base:I.VOLUME,prefixes:T.NONE,value:5e-8,offset:0},gtt:{name:"gtt",base:I.VOLUME,prefixes:T.NONE,value:5e-8,offset:0},minim:{name:"minim",base:I.VOLUME,prefixes:T.NONE,value:6.161152e-8,offset:0},fluiddram:{name:"fluiddram",base:I.VOLUME,prefixes:T.NONE,value:36966911e-13,offset:0},fluidounce:{name:"fluidounce",base:I.VOLUME,prefixes:T.NONE,value:2957353e-11,offset:0},gill:{name:"gill",base:I.VOLUME,prefixes:T.NONE,value:.0001182941,offset:0},cc:{name:"cc",base:I.VOLUME,prefixes:T.NONE,value:1e-6,offset:0},cup:{name:"cup",base:I.VOLUME,prefixes:T.NONE,value:.0002365882,offset:0},pint:{name:"pint",base:I.VOLUME,prefixes:T.NONE,value:.0004731765,offset:0},quart:{name:"quart",base:I.VOLUME,prefixes:T.NONE,value:.0009463529,offset:0},gallon:{name:"gallon",base:I.VOLUME,prefixes:T.NONE,value:.003785412,offset:0},beerbarrel:{name:"beerbarrel",base:I.VOLUME,prefixes:T.NONE,value:.1173478,offset:0},oilbarrel:{name:"oilbarrel",base:I.VOLUME,prefixes:T.NONE,value:.1589873,offset:0},hogshead:{name:"hogshead",base:I.VOLUME,prefixes:T.NONE,value:.238481,offset:0},fldr:{name:"fldr",base:I.VOLUME,prefixes:T.NONE,value:36966911e-13,offset:0},floz:{name:"floz",base:I.VOLUME,prefixes:T.NONE,value:2957353e-11,offset:0},gi:{name:"gi",base:I.VOLUME,prefixes:T.NONE,value:.0001182941,offset:0},cp:{name:"cp",base:I.VOLUME,prefixes:T.NONE,value:.0002365882,offset:0},pt:{name:"pt",base:I.VOLUME,prefixes:T.NONE,value:.0004731765,offset:0},qt:{name:"qt",base:I.VOLUME,prefixes:T.NONE,value:.0009463529,offset:0},gal:{name:"gal",base:I.VOLUME,prefixes:T.NONE,value:.003785412,offset:0},bbl:{name:"bbl",base:I.VOLUME,prefixes:T.NONE,value:.1173478,offset:0},obl:{name:"obl",base:I.VOLUME,prefixes:T.NONE,value:.1589873,offset:0},g:{name:"g",base:I.MASS,prefixes:T.SHORT,value:.001,offset:0},gram:{name:"gram",base:I.MASS,prefixes:T.LONG,value:.001,offset:0},ton:{name:"ton",base:I.MASS,prefixes:T.SHORT,value:907.18474,offset:0},t:{name:"t",base:I.MASS,prefixes:T.SHORT,value:1e3,offset:0},tonne:{name:"tonne",base:I.MASS,prefixes:T.LONG,value:1e3,offset:0},grain:{name:"grain",base:I.MASS,prefixes:T.NONE,value:6479891e-11,offset:0},dram:{name:"dram",base:I.MASS,prefixes:T.NONE,value:.0017718451953125,offset:0},ounce:{name:"ounce",base:I.MASS,prefixes:T.NONE,value:.028349523125,offset:0},poundmass:{name:"poundmass",base:I.MASS,prefixes:T.NONE,value:.45359237,offset:0},hundredweight:{name:"hundredweight",base:I.MASS,prefixes:T.NONE,value:45.359237,offset:0},stick:{name:"stick",base:I.MASS,prefixes:T.NONE,value:.115,offset:0},stone:{name:"stone",base:I.MASS,prefixes:T.NONE,value:6.35029318,offset:0},gr:{name:"gr",base:I.MASS,prefixes:T.NONE,value:6479891e-11,offset:0},dr:{name:"dr",base:I.MASS,prefixes:T.NONE,value:.0017718451953125,offset:0},oz:{name:"oz",base:I.MASS,prefixes:T.NONE,value:.028349523125,offset:0},lbm:{name:"lbm",base:I.MASS,prefixes:T.NONE,value:.45359237,offset:0},cwt:{name:"cwt",base:I.MASS,prefixes:T.NONE,value:45.359237,offset:0},s:{name:"s",base:I.TIME,prefixes:T.SHORT,value:1,offset:0},min:{name:"min",base:I.TIME,prefixes:T.NONE,value:60,offset:0},h:{name:"h",base:I.TIME,prefixes:T.NONE,value:3600,offset:0},second:{name:"second",base:I.TIME,prefixes:T.LONG,value:1,offset:0},sec:{name:"sec",base:I.TIME,prefixes:T.LONG,value:1,offset:0},minute:{name:"minute",base:I.TIME,prefixes:T.NONE,value:60,offset:0},hour:{name:"hour",base:I.TIME,prefixes:T.NONE,value:3600,offset:0},day:{name:"day",base:I.TIME,prefixes:T.NONE,value:86400,offset:0},week:{name:"week",base:I.TIME,prefixes:T.NONE,value:604800,offset:0},month:{name:"month",base:I.TIME,prefixes:T.NONE,value:2629800,offset:0},year:{name:"year",base:I.TIME,prefixes:T.NONE,value:31557600,offset:0},decade:{name:"decade",base:I.TIME,prefixes:T.NONE,value:315576e3,offset:0},century:{name:"century",base:I.TIME,prefixes:T.NONE,value:315576e4,offset:0},millennium:{name:"millennium",base:I.TIME,prefixes:T.NONE,value:315576e5,offset:0},hertz:{name:"Hertz",base:I.FREQUENCY,prefixes:T.LONG,value:1,offset:0,reciprocal:!0},Hz:{name:"Hz",base:I.FREQUENCY,prefixes:T.SHORT,value:1,offset:0,reciprocal:!0},rad:{name:"rad",base:I.ANGLE,prefixes:T.SHORT,value:1,offset:0},radian:{name:"radian",base:I.ANGLE,prefixes:T.LONG,value:1,offset:0},deg:{name:"deg",base:I.ANGLE,prefixes:T.SHORT,value:null,offset:0},degree:{name:"degree",base:I.ANGLE,prefixes:T.LONG,value:null,offset:0},grad:{name:"grad",base:I.ANGLE,prefixes:T.SHORT,value:null,offset:0},gradian:{name:"gradian",base:I.ANGLE,prefixes:T.LONG,value:null,offset:0},cycle:{name:"cycle",base:I.ANGLE,prefixes:T.NONE,value:null,offset:0},arcsec:{name:"arcsec",base:I.ANGLE,prefixes:T.NONE,value:null,offset:0},arcmin:{name:"arcmin",base:I.ANGLE,prefixes:T.NONE,value:null,offset:0},A:{name:"A",base:I.CURRENT,prefixes:T.SHORT,value:1,offset:0},ampere:{name:"ampere",base:I.CURRENT,prefixes:T.LONG,value:1,offset:0},K:{name:"K",base:I.TEMPERATURE,prefixes:T.SHORT,value:1,offset:0},degC:{name:"degC",base:I.TEMPERATURE,prefixes:T.SHORT,value:1,offset:273.15},degF:{name:"degF",base:I.TEMPERATURE,prefixes:T.SHORT,value:new w(5,9),offset:459.67},degR:{name:"degR",base:I.TEMPERATURE,prefixes:T.SHORT,value:new w(5,9),offset:0},kelvin:{name:"kelvin",base:I.TEMPERATURE,prefixes:T.LONG,value:1,offset:0},celsius:{name:"celsius",base:I.TEMPERATURE,prefixes:T.LONG,value:1,offset:273.15},fahrenheit:{name:"fahrenheit",base:I.TEMPERATURE,prefixes:T.LONG,value:new w(5,9),offset:459.67},rankine:{name:"rankine",base:I.TEMPERATURE,prefixes:T.LONG,value:new w(5,9),offset:0},mol:{name:"mol",base:I.AMOUNT_OF_SUBSTANCE,prefixes:T.SHORT,value:1,offset:0},mole:{name:"mole",base:I.AMOUNT_OF_SUBSTANCE,prefixes:T.LONG,value:1,offset:0},cd:{name:"cd",base:I.LUMINOUS_INTENSITY,prefixes:T.SHORT,value:1,offset:0},candela:{name:"candela",base:I.LUMINOUS_INTENSITY,prefixes:T.LONG,value:1,offset:0},N:{name:"N",base:I.FORCE,prefixes:T.SHORT,value:1,offset:0},newton:{name:"newton",base:I.FORCE,prefixes:T.LONG,value:1,offset:0},dyn:{name:"dyn",base:I.FORCE,prefixes:T.SHORT,value:1e-5,offset:0},dyne:{name:"dyne",base:I.FORCE,prefixes:T.LONG,value:1e-5,offset:0},lbf:{name:"lbf",base:I.FORCE,prefixes:T.NONE,value:4.4482216152605,offset:0},poundforce:{name:"poundforce",base:I.FORCE,prefixes:T.NONE,value:4.4482216152605,offset:0},kip:{name:"kip",base:I.FORCE,prefixes:T.LONG,value:4448.2216,offset:0},kilogramforce:{name:"kilogramforce",base:I.FORCE,prefixes:T.NONE,value:9.80665,offset:0},J:{name:"J",base:I.ENERGY,prefixes:T.SHORT,value:1,offset:0},joule:{name:"joule",base:I.ENERGY,prefixes:T.LONG,value:1,offset:0},erg:{name:"erg",base:I.ENERGY,prefixes:T.SHORTLONG,value:1e-7,offset:0},Wh:{name:"Wh",base:I.ENERGY,prefixes:T.SHORT,value:3600,offset:0},BTU:{name:"BTU",base:I.ENERGY,prefixes:T.BTU,value:1055.05585262,offset:0},eV:{name:"eV",base:I.ENERGY,prefixes:T.SHORT,value:1602176565e-28,offset:0},electronvolt:{name:"electronvolt",base:I.ENERGY,prefixes:T.LONG,value:1602176565e-28,offset:0},W:{name:"W",base:I.POWER,prefixes:T.SHORT,value:1,offset:0},watt:{name:"watt",base:I.POWER,prefixes:T.LONG,value:1,offset:0},hp:{name:"hp",base:I.POWER,prefixes:T.NONE,value:745.6998715386,offset:0},VAR:{name:"VAR",base:I.POWER,prefixes:T.SHORT,value:y.I,offset:0},VA:{name:"VA",base:I.POWER,prefixes:T.SHORT,value:1,offset:0},Pa:{name:"Pa",base:I.PRESSURE,prefixes:T.SHORT,value:1,offset:0},psi:{name:"psi",base:I.PRESSURE,prefixes:T.NONE,value:6894.75729276459,offset:0},atm:{name:"atm",base:I.PRESSURE,prefixes:T.NONE,value:101325,offset:0},bar:{name:"bar",base:I.PRESSURE,prefixes:T.SHORTLONG,value:1e5,offset:0},torr:{name:"torr",base:I.PRESSURE,prefixes:T.NONE,value:133.322,offset:0},mmHg:{name:"mmHg",base:I.PRESSURE,prefixes:T.NONE,value:133.322,offset:0},mmH2O:{name:"mmH2O",base:I.PRESSURE,prefixes:T.NONE,value:9.80665,offset:0},cmH2O:{name:"cmH2O",base:I.PRESSURE,prefixes:T.NONE,value:98.0665,offset:0},coulomb:{name:"coulomb",base:I.ELECTRIC_CHARGE,prefixes:T.LONG,value:1,offset:0},C:{name:"C",base:I.ELECTRIC_CHARGE,prefixes:T.SHORT,value:1,offset:0},farad:{name:"farad",base:I.ELECTRIC_CAPACITANCE,prefixes:T.LONG,value:1,offset:0},F:{name:"F",base:I.ELECTRIC_CAPACITANCE,prefixes:T.SHORT,value:1,offset:0},volt:{name:"volt",base:I.ELECTRIC_POTENTIAL,prefixes:T.LONG,value:1,offset:0},V:{name:"V",base:I.ELECTRIC_POTENTIAL,prefixes:T.SHORT,value:1,offset:0},ohm:{name:"ohm",base:I.ELECTRIC_RESISTANCE,prefixes:T.SHORTLONG,value:1,offset:0},henry:{name:"henry",base:I.ELECTRIC_INDUCTANCE,prefixes:T.LONG,value:1,offset:0},H:{name:"H",base:I.ELECTRIC_INDUCTANCE,prefixes:T.SHORT,value:1,offset:0},siemens:{name:"siemens",base:I.ELECTRIC_CONDUCTANCE,prefixes:T.LONG,value:1,offset:0},S:{name:"S",base:I.ELECTRIC_CONDUCTANCE,prefixes:T.SHORT,value:1,offset:0},weber:{name:"weber",base:I.MAGNETIC_FLUX,prefixes:T.LONG,value:1,offset:0},Wb:{name:"Wb",base:I.MAGNETIC_FLUX,prefixes:T.SHORT,value:1,offset:0},tesla:{name:"tesla",base:I.MAGNETIC_FLUX_DENSITY,prefixes:T.LONG,value:1,offset:0},T:{name:"T",base:I.MAGNETIC_FLUX_DENSITY,prefixes:T.SHORT,value:1,offset:0},b:{name:"b",base:I.BIT,prefixes:T.BINARY_SHORT,value:1,offset:0},bits:{name:"bits",base:I.BIT,prefixes:T.BINARY_LONG,value:1,offset:0},B:{name:"B",base:I.BIT,prefixes:T.BINARY_SHORT,value:8,offset:0},bytes:{name:"bytes",base:I.BIT,prefixes:T.BINARY_LONG,value:8,offset:0}},L={meters:"meter",inches:"inch",feet:"foot",yards:"yard",miles:"mile",links:"link",rods:"rod",chains:"chain",angstroms:"angstrom",lt:"l",litres:"litre",liter:"litre",liters:"litre",teaspoons:"teaspoon",tablespoons:"tablespoon",minims:"minim",fluiddrams:"fluiddram",fluidounces:"fluidounce",gills:"gill",cups:"cup",pints:"pint",quarts:"quart",gallons:"gallon",beerbarrels:"beerbarrel",oilbarrels:"oilbarrel",hogsheads:"hogshead",gtts:"gtt",grams:"gram",tons:"ton",tonnes:"tonne",grains:"grain",drams:"dram",ounces:"ounce",poundmasses:"poundmass",hundredweights:"hundredweight",sticks:"stick",lb:"lbm",lbs:"lbm",kips:"kip",kgf:"kilogramforce",acres:"acre",hectares:"hectare",sqfeet:"sqft",sqyard:"sqyd",sqmile:"sqmi",sqmiles:"sqmi",mmhg:"mmHg",mmh2o:"mmH2O",cmh2o:"cmH2O",seconds:"second",secs:"second",minutes:"minute",mins:"minute",hours:"hour",hr:"hour",hrs:"hour",days:"day",weeks:"week",months:"month",years:"year",decades:"decade",centuries:"century",millennia:"millennium",hertz:"hertz",radians:"radian",degrees:"degree",gradians:"gradian",cycles:"cycle",arcsecond:"arcsec",arcseconds:"arcsec",arcminute:"arcmin",arcminutes:"arcmin",BTUs:"BTU",watts:"watt",joules:"joule",amperes:"ampere",amps:"ampere",amp:"ampere",coulombs:"coulomb",volts:"volt",ohms:"ohm",farads:"farad",webers:"weber",teslas:"tesla",electronvolts:"electronvolt",moles:"mole",bit:"bits",byte:"bytes"};function j(e){if("BigNumber"===e.number){var t=gn(E);U.rad.value=new E(1),U.deg.value=t.div(180),U.grad.value=t.div(200),U.cycle.value=t.times(2),U.arcsec.value=t.div(648e3),U.arcmin.value=t.div(10800)}else U.rad.value=1,U.deg.value=Math.PI/180,U.grad.value=Math.PI/200,U.cycle.value=2*Math.PI,U.arcsec.value=Math.PI/648e3,U.arcmin.value=Math.PI/10800;U.radian.value=U.rad.value,U.degree.value=U.deg.value,U.gradian.value=U.grad.value}j(o),u&&u("config",(function(e,t){e.number!==t.number&&j(e)}));var k={si:{NONE:{unit:P,prefix:T.NONE[""]},LENGTH:{unit:U.m,prefix:T.SHORT[""]},MASS:{unit:U.g,prefix:T.SHORT.k},TIME:{unit:U.s,prefix:T.SHORT[""]},CURRENT:{unit:U.A,prefix:T.SHORT[""]},TEMPERATURE:{unit:U.K,prefix:T.SHORT[""]},LUMINOUS_INTENSITY:{unit:U.cd,prefix:T.SHORT[""]},AMOUNT_OF_SUBSTANCE:{unit:U.mol,prefix:T.SHORT[""]},ANGLE:{unit:U.rad,prefix:T.SHORT[""]},BIT:{unit:U.bits,prefix:T.SHORT[""]},FORCE:{unit:U.N,prefix:T.SHORT[""]},ENERGY:{unit:U.J,prefix:T.SHORT[""]},POWER:{unit:U.W,prefix:T.SHORT[""]},PRESSURE:{unit:U.Pa,prefix:T.SHORT[""]},ELECTRIC_CHARGE:{unit:U.C,prefix:T.SHORT[""]},ELECTRIC_CAPACITANCE:{unit:U.F,prefix:T.SHORT[""]},ELECTRIC_POTENTIAL:{unit:U.V,prefix:T.SHORT[""]},ELECTRIC_RESISTANCE:{unit:U.ohm,prefix:T.SHORT[""]},ELECTRIC_INDUCTANCE:{unit:U.H,prefix:T.SHORT[""]},ELECTRIC_CONDUCTANCE:{unit:U.S,prefix:T.SHORT[""]},MAGNETIC_FLUX:{unit:U.Wb,prefix:T.SHORT[""]},MAGNETIC_FLUX_DENSITY:{unit:U.T,prefix:T.SHORT[""]},FREQUENCY:{unit:U.Hz,prefix:T.SHORT[""]}}};k.cgs=JSON.parse(JSON.stringify(k.si)),k.cgs.LENGTH={unit:U.m,prefix:T.SHORT.c},k.cgs.MASS={unit:U.g,prefix:T.SHORT[""]},k.cgs.FORCE={unit:U.dyn,prefix:T.SHORT[""]},k.cgs.ENERGY={unit:U.erg,prefix:T.NONE[""]},k.us=JSON.parse(JSON.stringify(k.si)),k.us.LENGTH={unit:U.ft,prefix:T.NONE[""]},k.us.MASS={unit:U.lbm,prefix:T.NONE[""]},k.us.TEMPERATURE={unit:U.degF,prefix:T.NONE[""]},k.us.FORCE={unit:U.lbf,prefix:T.NONE[""]},k.us.ENERGY={unit:U.BTU,prefix:T.BTU[""]},k.us.POWER={unit:U.hp,prefix:T.NONE[""]},k.us.PRESSURE={unit:U.psi,prefix:T.NONE[""]},k.auto=JSON.parse(JSON.stringify(k.si));var q=k.auto;for(var H in x.setUnitSystem=function(e){if(!Bt(k,e))throw new Error("Unit system "+e+" does not exist. Choices are: "+Object.keys(k).join(", "));q=k[e]},x.getUnitSystem=function(){for(var e in k)if(Bt(k,e)&&k[e]===q)return e},x.typeConverters={BigNumber:function(e){return null!=e&&e.isFraction?new E(e.n).div(e.d).times(e.s):new E(e+"")},Fraction:function(e){return new w(e)},Complex:function(e){return e},number:function(e){return null!=e&&e.isFraction?D(e):e}},x.prototype._numberConverter=function(){var e=x.typeConverters[this.valueType()];if(e)return e;throw new TypeError('Unsupported Unit value type "'+this.valueType()+'"')},x._getNumberConverter=function(e){if(!x.typeConverters[e])throw new TypeError('Unsupported type "'+e+'"');return x.typeConverters[e]},U)if(Bt(U,H)){var G=U[H];G.dimensions=G.base.dimensions}for(var V in L)if(Bt(L,V)){var Y=U[L[V]],$={};for(var W in Y)Bt(Y,W)&&($[W]=Y[W]);$.name=V,U[V]=$}return x.isValidAlpha=function(e){return/^[a-zA-Z]$/.test(e)},x.createUnit=function(e,t){if("object"!=typeof e)throw new TypeError("createUnit expects first parameter to be of type 'Object'");if(t&&t.override)for(var n in e)if(Bt(e,n)&&x.deleteUnit(n),e[n].aliases)for(var r=0;r<e[n].aliases.length;r++)x.deleteUnit(e[n].aliases[r]);var i;for(var u in e)Bt(e,u)&&(i=x.createUnitSingle(u,e[u]));return i},x.createUnitSingle=function(e,t){if(null==t&&(t={}),"string"!=typeof e)throw new TypeError("createUnitSingle expects first parameter to be of type 'string'");if(Bt(U,e))throw new Error('Cannot create unit "'+e+'": a unit with that name already exists');!function(e){for(var t=0;t<e.length;t++){if(i=e.charAt(t),0===t&&!x.isValidAlpha(i))throw new Error('Invalid unit name (must begin with alpha character): "'+e+'"');if(t>0&&!x.isValidAlpha(i)&&!C(i))throw new Error('Invalid unit name (only alphanumeric characters are allowed): "'+e+'"')}}(e);var n,r,u,o=null,a=[],s=0;if(t&&"Unit"===t.type)o=t.clone();else if("string"==typeof t)""!==t&&(n=t);else{if("object"!=typeof t)throw new TypeError('Cannot create unit "'+e+'" from "'+t.toString()+'": expecting "string" or "Unit" or "Object"');n=t.definition,r=t.prefixes,s=t.offset,u=t.baseName,t.aliases&&(a=t.aliases.valueOf())}if(a)for(var c=0;c<a.length;c++)if(Bt(U,a[c]))throw new Error('Cannot create alias "'+a[c]+'": a unit with that name already exists');if(n&&"string"==typeof n&&!o)try{o=x.parse(n,{allowNoUnits:!0})}catch(t){throw t.message='Could not create unit "'+e+'" from "'+n+'": '+t.message,t}else n&&"Unit"===n.type&&(o=n.clone());a=a||[],s=s||0,r=r&&r.toUpperCase&&T[r.toUpperCase()]||T.NONE;var f={};if(o){f={name:e,value:o.value,dimensions:o.dimensions.slice(0),prefixes:r,offset:s};var l=!1;for(var p in I)if(Bt(I,p)){for(var h=!0,m=0;m<R.length;m++)if(Math.abs((f.dimensions[m]||0)-(I[p].dimensions[m]||0))>1e-12){h=!1;break}if(h){l=!0,f.base=I[p];break}}if(!l){u=u||e+"_STUFF";var d={dimensions:o.dimensions.slice(0)};d.key=u,I[u]=d,q[u]={unit:f,prefix:T.NONE[""]},f.base=I[u]}}else{if(u=u||e+"_STUFF",R.indexOf(u)>=0)throw new Error('Cannot create new base unit "'+e+'": a base unit with that name already exists (and cannot be overridden)');for(var v in R.push(u),I)Bt(I,v)&&(I[v].dimensions[R.length-1]=0);for(var g={dimensions:[]},D=0;D<R.length;D++)g.dimensions[D]=0;g.dimensions[R.length-1]=1,g.key=u,I[u]=g,f={name:e,value:1,dimensions:I[u].dimensions.slice(0),prefixes:r,offset:s,base:I[u]},q[u]={unit:f,prefix:T.NONE[""]}}x.UNITS[e]=f;for(var y=0;y<a.length;y++){var E=a[y],w={};for(var b in f)Bt(f,b)&&(w[b]=f[b]);w.name=E,x.UNITS[E]=w}return delete O.cache,new x(null,e)},x.deleteUnit=function(e){delete x.UNITS[e],delete O.cache},x.PREFIXES=T,x.BASE_DIMENSIONS=R,x.BASE_UNITS=I,x.UNIT_SYSTEMS=k,x.UNITS=U,x}),{isClass:!0}),lo=Tt("divide",["typed","matrix","multiply","equalScalar","divideScalar","inv"],(e=>{var{typed:t,matrix:n,multiply:r,equalScalar:i,divideScalar:u,inv:o}=e,a=du({typed:t,equalScalar:i}),s=Du({typed:t});return t("divide",St({"Array | Matrix, Array | Matrix":function(e,t){return r(e,o(t))},"DenseMatrix, any":function(e,t){return s(e,t,u,!1)},"SparseMatrix, any":function(e,t){return a(e,t,u,!1)},"Array, any":function(e,t){return s(n(e),t,u,!1).valueOf()},"any, Array | Matrix":function(e,t){return r(e,o(t))}},u.signatures))}));var po="unit",ho=Tt(po,["typed","Unit"],(e=>{var{typed:t,Unit:n}=e;return t(po,{Unit:function(e){return e.clone()},string:function(e){return n.isValuelessUnit(e)?new n(null,e):n.parse(e,{allowNoUnits:!0})},"number | BigNumber | Fraction | Complex, string | Unit":function(e,t){return new n(e,t)},"number | BigNumber | Fraction":function(e){return new n(e)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})}));var mo=It({config:u}),vo=hn({}),go=wn({}),Do=bn({}),yo=Kn({Matrix:Do}),Eo=pr({BigNumber:mo,Complex:vo,DenseMatrix:yo,Fraction:go}),wo=Pr({typed:Eo}),bo=ni({typed:Eo}),xo=ri({BigNumber:mo,typed:Eo}),Ao=di({typed:Eo}),Co=yi({config:u,typed:Eo}),Fo=wi({typed:Eo}),No=xi({typed:Eo}),_o=Si({typed:Eo}),Mo=Pi({typed:Eo}),So=Hi({typed:Eo}),Oo=Vi({Matrix:Do,equalScalar:Co,typed:Eo}),Bo=$i({typed:Eo}),To=Zi({typed:Eo}),Ro=Ji({Fraction:go,typed:Eo}),Io=Xi({typed:Eo}),zo=eu({DenseMatrix:yo,Matrix:Do,SparseMatrix:Oo,typed:Eo}),Po=iu({bignumber:xo,fraction:Ro,number:So}),Uo=ou({matrix:zo,config:u,typed:Eo}),Lo=su({BigNumber:mo,config:u,matrix:zo,typed:Eo}),jo=fu({isInteger:No,matrix:zo,typed:Eo}),ko=pu({numeric:Po,typed:Eo}),qo=bu({DenseMatrix:yo,concat:jo,equalScalar:Co,matrix:zo,typed:Eo}),Ho=Cu({isNumeric:Io,typed:Eo}),Go=Nu({BigNumber:mo,DenseMatrix:yo,SparseMatrix:Oo,config:u,matrix:zo,typed:Eo}),Vo=Tu({BigNumber:mo,DenseMatrix:yo,config:u,equalScalar:Co,matrix:zo,typed:Eo,zeros:Lo}),Yo=zu({DenseMatrix:yo,concat:jo,equalScalar:Co,matrix:zo,subtractScalar:Bo,typed:Eo,unaryMinus:To}),$o=Lu({DenseMatrix:yo,SparseMatrix:Oo,addScalar:bo,concat:jo,equalScalar:Co,matrix:zo,typed:Eo}),Wo=ju({addScalar:bo,conj:Ao,multiplyScalar:Mo,size:Uo,typed:Eo}),Zo=Gu({DenseMatrix:yo,config:u,equalScalar:Co,matrix:zo,round:Vo,typed:Eo,zeros:Lo}),Jo=$u({addScalar:bo,dot:Wo,equalScalar:Co,matrix:zo,multiplyScalar:Mo,typed:Eo}),Qo=Qu({DenseMatrix:yo,config:u,equalScalar:Co,matrix:zo,round:Vo,typed:Eo,zeros:Lo}),Xo=Xu({divideScalar:ko,isZero:_o,matrix:zo,multiply:Jo,subtractScalar:Bo,typed:Eo,unaryMinus:To}),Ko=no({Complex:vo,DenseMatrix:yo,ceil:Qo,equalScalar:Co,floor:Zo,matrix:zo,typed:Eo,zeros:Lo}),ea=ro({abs:wo,addScalar:bo,det:Xo,divideScalar:ko,identity:Go,matrix:zo,multiply:Jo,typed:Eo,unaryMinus:To}),ta=io({Complex:vo,config:u,fraction:Ro,identity:Go,inv:ea,matrix:zo,multiply:Jo,number:So,typed:Eo}),na=fo({BigNumber:mo,Complex:vo,Fraction:go,abs:wo,addScalar:bo,config:u,divideScalar:ko,equal:qo,fix:Ko,format:Fo,isNumeric:Io,multiplyScalar:Mo,number:So,pow:ta,round:Vo,subtractScalar:Bo}),ra=lo({divideScalar:ko,equalScalar:Co,inv:ea,matrix:zo,multiply:Jo,typed:Eo}),ia=ho({Unit:na,typed:Eo}),ua=n(1669),oa=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,u=t.length;i<u;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},aa={settings:{delayTimer:0,number:{precision:0,thousandsSep:",",decimalsSep:"."},currency:{symbol:"$",format:"%s%v",decimalsSep:".",thousandsSep:",",precision:2}},delay:function(e,t){return clearTimeout(this.settings.delayTimer),this.settings.delayTimer=setTimeout(e,t),this.settings.delayTimer},filterQuery:function(e,t){for(var n=e.split("&"),r=0;r<n.length;r++){var i=n[r].split("=");if(i[0]===t)return i[1]}return!1},filterByData:function(e,t,n){return void 0===n?e.filter((function(e,n){return void 0!==ua(n).data(t)})):e.filter((function(e,r){return ua(r).data(t)==n}))},addNotice:function(e,t,n,r){void 0===n&&(n=!1),void 0===r&&(r=5);var i=ua('<div class="notice-'.concat(e,' notice is-dismissible"><p><strong>').concat(t,"</strong></p></div>")).hide(),u=ua("<button />",{type:"button",class:"notice-dismiss"}),o=ua(".wp-header-end");o.siblings(".notice").remove(),o.before(i.append(u)),i.slideDown(100),u.on("click.wp-dismiss-notice",(function(e){e.preventDefault(),i.fadeTo(100,0,(function(){i.slideUp(100,(function(){i.remove()}))}))})),n&&setTimeout((function(){u.trigger("click.wp-dismiss-notice")}),1e3*r)},imagesLoaded:function(e){var t=e.find('img[src!=""]');if(!t.length)return ua.Deferred().resolve().promise();var n=[];return t.each((function(e,t){var r=ua.Deferred(),i=new Image;n.push(r),i.onload=function(){return r.resolve()},i.onerror=function(){return r.resolve()},i.src=ua(t).attr("src")})),ua.when.apply(ua,n)},getUrlParameter:function(e){if("undefined"!=typeof URLSearchParams)return new URLSearchParams(window.location.search).get(e);e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var t=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(window.location.search);return null===t?"":decodeURIComponent(t[1].replace(/\+/g," "))},getQueryParams:function(e){var t={};return new URLSearchParams(e).forEach((function(e,n){var r=decodeURIComponent(n),i=decodeURIComponent(e);r.endsWith("[]")?(r=r.replace("[]",""),t[r]||(t[r]=[]),t[r].push(i)):t[r]=i})),t},htmlDecode:function(e){var t=document.createElement("div");return t.innerHTML=e,t.childNodes[0].nodeValue},areEquivalent:function(e,t,n){void 0===n&&(n=!1);var r=Object.getOwnPropertyNames(e),i=Object.getOwnPropertyNames(t);if(r.length!=i.length)return!1;for(var u=0;u<r.length;u++){var o=r[u];if(n&&e[o]!==t[o]||!n&&e[o]!=t[o])return!1}return!0},toggleNodes:function(e,t){for(var n=0;n<e.length;n++)e[n].isExpanded="open"==t,e[n].children&&e[n].children.length>0&&this.toggleNodes(e[n].children,t)},formatNumber:function(e,t,n,r,i){void 0===t&&(t=this.settings.number.precision),void 0===n&&(n=this.settings.number.thousandsSep),void 0===r&&(r=this.settings.number.decimalsSep),void 0===i&&(i=1),e>999&&n===r&&!Number.isInteger(e)&&(n="");var u={minimumFractionDigits:t};return i&&(u.minimumSignificantDigits=i),e.toLocaleString("en",u).replace(new RegExp("\\,","g"),n).replace(new RegExp("\\."),r)},formatMoney:function(e,t,n,r,i,u){void 0===t&&(t=this.settings.currency.symbol),void 0===n&&(n=this.settings.currency.precision),void 0===r&&(r=this.settings.currency.thousandsSep),void 0===i&&(i=this.settings.currency.decimalsSep),void 0===u&&(u=this.settings.currency.format),this.isNumeric(e)||(e=this.unformat(e));var o=this.checkCurrencyFormat(u);return(e>0?o.pos:e<0?o.neg:o.zero).replace("%s",t).replace("%v",this.formatNumber(Math.abs(e),this.checkPrecision(n),r,i,null))},unformat:function(e,t){if(void 0===t&&(t=this.settings.number.decimalsSep),"number"==typeof e)return e;var n=new RegExp("[^0-9-".concat(t,"]"),"g"),r=parseFloat((""+e).replace(/\((.*)\)/,"-$1").replace(n,"").replace(t,"."));return isNaN(r)?0:r},checkPrecision:function(e,t){return void 0===t&&(t=0),e=Math.round(Math.abs(e)),isNaN(e)?t:e},checkCurrencyFormat:function(e){if("function"==typeof e)return e();if("string"==typeof e&&e.match("%v"))return{pos:e,neg:e.replace("-","").replace("%v","-%v"),zero:e};if(!e||"object"==typeof e&&(!e.pos||!e.pos.match("%v"))){var t=this.settings.currency.format;return"string"!=typeof t?t:this.settings.currency.format={pos:t,neg:t.replace("%v","-%v"),zero:t}}},countDecimals:function(e){return Math.floor(e)===e?0:e.toString().split(".")[1].length||0},multiplyDecimals:function(e,t){return So(Jo(xo(this.isNumeric(e)?e:0),xo(this.isNumeric(t)?t:0)))},divideDecimals:function(e,t){return So(ra(xo(this.isNumeric(e)?e:0),xo(this.isNumeric(t)?t:0)))},sumDecimals:function(e,t){return So($o(xo(this.isNumeric(e)?e:0),xo(this.isNumeric(t)?t:0)))},subtractDecimals:function(e,t){return So(Yo(xo(this.isNumeric(e)?e:0),xo(this.isNumeric(t)?t:0)))},isNumeric:function(e){return Ho(e)},round:function(e,t){return Vo(e,t)},convertUnit:function(e,t,n){return So(ia(e,t),n)},convertElemsToString:function(e){return ua("<div />").append(e).html()},mergeArrays:function(e,t){return Array.from(new Set(oa(oa([],e,!0),t,!0)))},restrictNumberInputValues:function(e){if("number"===e.attr("type")){var t=e.val(),n=parseFloat(t||"0"),r=e.attr("min"),i=e.attr("max"),u=parseFloat(r||"0"),o=parseFloat(i||"0");this.isNumeric(t)?void 0!==r&&n<u?e.val(u):void 0!==i&&n>o&&e.val(o):e.val(void 0!==r&&!isNaN(u)&&u>0?u:0)}},checkRTL:function(e){var t=!1;switch(ua('html[ dir="rtl" ]').length>0&&(t=!0),e){case"isRTL":case"reverse":return t;case"xSide":return t?"right":"left";default:return!1}},calcTaxesFromBase:function(e,t){var n,r=this,i=[0];return ua.each(t,(function(t,n){if("yes"===n.compound)return!0;i.push(r.divideDecimals(r.multiplyDecimals(e,n.rate),100))})),n=i.reduce((function(e,t){return r.sumDecimals(e,t)}),0),ua.each(t,(function(t,u){var o;if("no"===u.compound)return!0;o=r.divideDecimals(r.multiplyDecimals(r.sumDecimals(e,n),u.rate),100),i.push(o),n=r.sumDecimals(o,n)})),i.reduce((function(e,t){return r.sumDecimals(e,t)}),0)},pseudoClick:function(e,t,n){void 0===n&&(n="both");var r=!1,i=!1,u=t.get(0),o=parseInt(u.getBoundingClientRect().left.toString(),10),a=parseInt(u.getBoundingClientRect().top.toString(),10),s=e.clientX,c=e.clientY;if(["before","both"].includes(n)){var f=window.getComputedStyle(u,":before"),l=o+parseInt(f.getPropertyValue("left"),10),p=l+parseInt(f.width,10),h=a+parseInt(f.getPropertyValue("top"),10),m=h+parseInt(f.height,10);r=s>=l&&s<=p&&c>=h&&c<=m}if(["after","both"].includes(n)){var d=window.getComputedStyle(u,":after"),v=o+parseInt(d.getPropertyValue("left"),10),g=v+parseInt(d.width,10),D=a+parseInt(d.getPropertyValue("top"),10),y=D+parseInt(d.height,10);i=s>=v&&s<=g&&c>=D&&c<=y}switch(n){case"after":return i;case"before":return r;default:return{before:r,after:i}}},isElementInViewport:function(e){var t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom+80<=window.innerHeight&&t.right<=window.innerWidth}};const sa=aa;var ca=n(1669),fa=function(){function e(e){this.tooltip=e,this.$searchColumnWrapper=ca("#atum-search-by-column"),this.$searchColumnBtn=this.$searchColumnWrapper.find(".search-column-btn"),this.$searchColumnDropdown=this.$searchColumnWrapper.find("#search_column_dropdown"),this.$searchInput=this.$searchColumnWrapper.find("input[type=search]"),this.bindEvents(),this.tooltip.addTooltips(this.$searchColumnWrapper);var t=sa.getUrlParameter("atum_search_column"),n=this.$searchColumnDropdown.children("a");t?sa.filterByData(n,"value",t).trigger("click"):n.length<3?n.eq(1).trigger("click"):n.eq(0).trigger("click")}return e.prototype.bindEvents=function(){var e=this;this.$searchColumnBtn.on("click",(function(e){e.preventDefault(),e.stopPropagation(),ca(e.currentTarget).parent().find(".dropdown-menu").toggle()})),this.$searchColumnDropdown.on("click","a",(function(t){t.preventDefault();var n=ca(t.currentTarget),r=n.data("value"),i=n.text().trim(),u=e.$searchColumnDropdown.data("no-option");e.$searchInput.prop("disabled",!r),e.$searchColumnDropdown.children("input[type=hidden]").val(r),e.$searchColumnDropdown.hide().children("a.active").removeClass("active"),n.addClass("active"),e.$searchColumnBtn.attr("data-bs-original-title",u!==i?"".concat(u," ").concat(i):i),e.$searchColumnBtn.text(i),e.$searchColumnBtn.data("value",r),e.tooltip.destroyTooltips(e.$searchColumnWrapper),e.tooltip.addTooltips(e.$searchColumnWrapper),r&&e.$searchInput.trigger("focus").trigger("select")})),ca("body").on("click",(function(){return e.$searchColumnDropdown.hide()}))},e}();const la=fa;var pa=n(3029),ha=n.n(pa),ma=n(1669),da=function(){function e(e){void 0===e&&(e=!0),e&&this.addTooltips()}return e.prototype.addTooltips=function(e){var t=this;e||(e=ma("body")),e.find(".tips, .atum-tooltip").each((function(e,n){var r=ma(n),i=r.data("tip")||r.attr("title")||r.attr("data-bs-original-title");if(i){if(t.getInstance(r))return;new(ha())(r.get(0),{html:!0,title:i,container:"body",delay:{show:100,hide:200}}),r.on("inserted.bs.tooltip",(function(e){var t=ma(e.currentTarget).attr("aria-describedby");ma('.tooltip[class*="bs-tooltip-"]').not("#".concat(t)).remove()}))}}))},e.prototype.destroyTooltips=function(e){var t=this;e||(e=ma("body")),e.find(".tips, .atum-tooltip").each((function(e,n){var r=t.getInstance(ma(n));r&&r.dispose()}))},e.prototype.getInstance=function(e){return ha().getInstance(e.get(0))},e}();const va=da;n(1669)((function(e){var t=new va(!1);new la(t)}))})()})();