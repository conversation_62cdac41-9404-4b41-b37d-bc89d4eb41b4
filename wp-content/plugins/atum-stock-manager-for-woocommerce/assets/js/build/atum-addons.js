(()=>{var e={1170:(e,t,n)=>{"use strict";n.r(t),n.d(t,{afterMain:()=>E,afterRead:()=>y,afterWrite:()=>C,applyStyles:()=>T,arrow:()=>Q,auto:()=>a,basePlacements:()=>s,beforeMain:()=>w,beforeRead:()=>v,beforeWrite:()=>x,bottom:()=>i,clippingParents:()=>l,computeStyles:()=>ne,createPopper:()=>Te,createPopperBase:()=>Oe,createPopperLite:()=>Be,detectOverflow:()=>De,end:()=>f,eventListeners:()=>ie,flip:()=>ye,hide:()=>Ee,left:()=>u,main:()=>b,modifierPhases:()=>F,offset:()=>xe,placements:()=>g,popper:()=>h,popperGenerator:()=>Se,popperOffsets:()=>Ae,preventOverflow:()=>Ce,read:()=>D,reference:()=>d,right:()=>o,start:()=>c,top:()=>r,variationPlacements:()=>m,viewport:()=>p,write:()=>A});var r="top",i="bottom",o="right",u="left",a="auto",s=[r,i,o,u],c="start",f="end",l="clippingParents",p="viewport",h="popper",d="reference",m=s.reduce((function(e,t){return e.concat([t+"-"+c,t+"-"+f])}),[]),g=[].concat(s,[a]).reduce((function(e,t){return e.concat([t,t+"-"+c,t+"-"+f])}),[]),v="beforeRead",D="read",y="afterRead",w="beforeMain",b="main",E="afterMain",x="beforeWrite",A="write",C="afterWrite",F=[v,D,y,w,b,E,x,A,C];function N(e){return e?(e.nodeName||"").toLowerCase():null}function _(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function M(e){return e instanceof _(e).Element||e instanceof Element}function S(e){return e instanceof _(e).HTMLElement||e instanceof HTMLElement}function O(e){return"undefined"!=typeof ShadowRoot&&(e instanceof _(e).ShadowRoot||e instanceof ShadowRoot)}const T={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},i=t.elements[e];S(i)&&N(i)&&(Object.assign(i.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],i=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});S(r)&&N(r)&&(Object.assign(r.style,o),Object.keys(i).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]};function B(e){return e.split("-")[0]}var R=Math.max,I=Math.min,z=Math.round;function P(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function k(){return!/^((?!chrome|android).)*safari/i.test(P())}function j(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),i=1,o=1;t&&S(e)&&(i=e.offsetWidth>0&&z(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&z(r.height)/e.offsetHeight||1);var u=(M(e)?_(e):window).visualViewport,a=!k()&&n,s=(r.left+(a&&u?u.offsetLeft:0))/i,c=(r.top+(a&&u?u.offsetTop:0))/o,f=r.width/i,l=r.height/o;return{width:f,height:l,top:c,right:s+f,bottom:c+l,left:s,x:s,y:c}}function L(e){var t=j(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function U(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&O(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function q(e){return _(e).getComputedStyle(e)}function H(e){return["table","td","th"].indexOf(N(e))>=0}function G(e){return((M(e)?e.ownerDocument:e.document)||window.document).documentElement}function V(e){return"html"===N(e)?e:e.assignedSlot||e.parentNode||(O(e)?e.host:null)||G(e)}function Y(e){return S(e)&&"fixed"!==q(e).position?e.offsetParent:null}function W(e){for(var t=_(e),n=Y(e);n&&H(n)&&"static"===q(n).position;)n=Y(n);return n&&("html"===N(n)||"body"===N(n)&&"static"===q(n).position)?t:n||function(e){var t=/firefox/i.test(P());if(/Trident/i.test(P())&&S(e)&&"fixed"===q(e).position)return null;var n=V(e);for(O(n)&&(n=n.host);S(n)&&["html","body"].indexOf(N(n))<0;){var r=q(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function $(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Z(e,t,n){return R(e,I(t,n))}function J(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function X(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}const Q={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,a=e.name,c=e.options,f=n.elements.arrow,l=n.modifiersData.popperOffsets,p=B(n.placement),h=$(p),d=[u,o].indexOf(p)>=0?"height":"width";if(f&&l){var m=function(e,t){return J("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:X(e,s))}(c.padding,n),g=L(f),v="y"===h?r:u,D="y"===h?i:o,y=n.rects.reference[d]+n.rects.reference[h]-l[h]-n.rects.popper[d],w=l[h]-n.rects.reference[h],b=W(f),E=b?"y"===h?b.clientHeight||0:b.clientWidth||0:0,x=y/2-w/2,A=m[v],C=E-g[d]-m[D],F=E/2-g[d]/2+x,N=Z(A,F,C),_=h;n.modifiersData[a]=((t={})[_]=N,t.centerOffset=N-F,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&U(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function K(e){return e.split("-")[1]}var ee={top:"auto",right:"auto",bottom:"auto",left:"auto"};function te(e){var t,n=e.popper,a=e.popperRect,s=e.placement,c=e.variation,l=e.offsets,p=e.position,h=e.gpuAcceleration,d=e.adaptive,m=e.roundOffsets,g=e.isFixed,v=l.x,D=void 0===v?0:v,y=l.y,w=void 0===y?0:y,b="function"==typeof m?m({x:D,y:w}):{x:D,y:w};D=b.x,w=b.y;var E=l.hasOwnProperty("x"),x=l.hasOwnProperty("y"),A=u,C=r,F=window;if(d){var N=W(n),M="clientHeight",S="clientWidth";if(N===_(n)&&"static"!==q(N=G(n)).position&&"absolute"===p&&(M="scrollHeight",S="scrollWidth"),s===r||(s===u||s===o)&&c===f)C=i,w-=(g&&N===F&&F.visualViewport?F.visualViewport.height:N[M])-a.height,w*=h?1:-1;if(s===u||(s===r||s===i)&&c===f)A=o,D-=(g&&N===F&&F.visualViewport?F.visualViewport.width:N[S])-a.width,D*=h?1:-1}var O,T=Object.assign({position:p},d&&ee),B=!0===m?function(e,t){var n=e.x,r=e.y,i=t.devicePixelRatio||1;return{x:z(n*i)/i||0,y:z(r*i)/i||0}}({x:D,y:w},_(n)):{x:D,y:w};return D=B.x,w=B.y,h?Object.assign({},T,((O={})[C]=x?"0":"",O[A]=E?"0":"",O.transform=(F.devicePixelRatio||1)<=1?"translate("+D+"px, "+w+"px)":"translate3d("+D+"px, "+w+"px, 0)",O)):Object.assign({},T,((t={})[C]=x?w+"px":"",t[A]=E?D+"px":"",t.transform="",t))}const ne={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=void 0===r||r,o=n.adaptive,u=void 0===o||o,a=n.roundOffsets,s=void 0===a||a,c={placement:B(t.placement),variation:K(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,te(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:u,roundOffsets:s})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,te(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var re={passive:!0};const ie={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,o=void 0===i||i,u=r.resize,a=void 0===u||u,s=_(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach((function(e){e.addEventListener("scroll",n.update,re)})),a&&s.addEventListener("resize",n.update,re),function(){o&&c.forEach((function(e){e.removeEventListener("scroll",n.update,re)})),a&&s.removeEventListener("resize",n.update,re)}},data:{}};var oe={left:"right",right:"left",bottom:"top",top:"bottom"};function ue(e){return e.replace(/left|right|bottom|top/g,(function(e){return oe[e]}))}var ae={start:"end",end:"start"};function se(e){return e.replace(/start|end/g,(function(e){return ae[e]}))}function ce(e){var t=_(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function fe(e){return j(G(e)).left+ce(e).scrollLeft}function le(e){var t=q(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function pe(e){return["html","body","#document"].indexOf(N(e))>=0?e.ownerDocument.body:S(e)&&le(e)?e:pe(V(e))}function he(e,t){var n;void 0===t&&(t=[]);var r=pe(e),i=r===(null==(n=e.ownerDocument)?void 0:n.body),o=_(r),u=i?[o].concat(o.visualViewport||[],le(r)?r:[]):r,a=t.concat(u);return i?a:a.concat(he(V(u)))}function de(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function me(e,t,n){return t===p?de(function(e,t){var n=_(e),r=G(e),i=n.visualViewport,o=r.clientWidth,u=r.clientHeight,a=0,s=0;if(i){o=i.width,u=i.height;var c=k();(c||!c&&"fixed"===t)&&(a=i.offsetLeft,s=i.offsetTop)}return{width:o,height:u,x:a+fe(e),y:s}}(e,n)):M(t)?function(e,t){var n=j(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):de(function(e){var t,n=G(e),r=ce(e),i=null==(t=e.ownerDocument)?void 0:t.body,o=R(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),u=R(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-r.scrollLeft+fe(e),s=-r.scrollTop;return"rtl"===q(i||n).direction&&(a+=R(n.clientWidth,i?i.clientWidth:0)-o),{width:o,height:u,x:a,y:s}}(G(e)))}function ge(e,t,n,r){var i="clippingParents"===t?function(e){var t=he(V(e)),n=["absolute","fixed"].indexOf(q(e).position)>=0&&S(e)?W(e):e;return M(n)?t.filter((function(e){return M(e)&&U(e,n)&&"body"!==N(e)})):[]}(e):[].concat(t),o=[].concat(i,[n]),u=o[0],a=o.reduce((function(t,n){var i=me(e,n,r);return t.top=R(i.top,t.top),t.right=I(i.right,t.right),t.bottom=I(i.bottom,t.bottom),t.left=R(i.left,t.left),t}),me(e,u,r));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function ve(e){var t,n=e.reference,a=e.element,s=e.placement,l=s?B(s):null,p=s?K(s):null,h=n.x+n.width/2-a.width/2,d=n.y+n.height/2-a.height/2;switch(l){case r:t={x:h,y:n.y-a.height};break;case i:t={x:h,y:n.y+n.height};break;case o:t={x:n.x+n.width,y:d};break;case u:t={x:n.x-a.width,y:d};break;default:t={x:n.x,y:n.y}}var m=l?$(l):null;if(null!=m){var g="y"===m?"height":"width";switch(p){case c:t[m]=t[m]-(n[g]/2-a[g]/2);break;case f:t[m]=t[m]+(n[g]/2-a[g]/2)}}return t}function De(e,t){void 0===t&&(t={});var n=t,u=n.placement,a=void 0===u?e.placement:u,c=n.strategy,f=void 0===c?e.strategy:c,m=n.boundary,g=void 0===m?l:m,v=n.rootBoundary,D=void 0===v?p:v,y=n.elementContext,w=void 0===y?h:y,b=n.altBoundary,E=void 0!==b&&b,x=n.padding,A=void 0===x?0:x,C=J("number"!=typeof A?A:X(A,s)),F=w===h?d:h,N=e.rects.popper,_=e.elements[E?F:w],S=ge(M(_)?_:_.contextElement||G(e.elements.popper),g,D,f),O=j(e.elements.reference),T=ve({reference:O,element:N,strategy:"absolute",placement:a}),B=de(Object.assign({},N,T)),R=w===h?B:O,I={top:S.top-R.top+C.top,bottom:R.bottom-S.bottom+C.bottom,left:S.left-R.left+C.left,right:R.right-S.right+C.right},z=e.modifiersData.offset;if(w===h&&z){var P=z[a];Object.keys(I).forEach((function(e){var t=[o,i].indexOf(e)>=0?1:-1,n=[r,i].indexOf(e)>=0?"y":"x";I[e]+=P[n]*t}))}return I}const ye={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,f=e.name;if(!t.modifiersData[f]._skip){for(var l=n.mainAxis,p=void 0===l||l,h=n.altAxis,d=void 0===h||h,v=n.fallbackPlacements,D=n.padding,y=n.boundary,w=n.rootBoundary,b=n.altBoundary,E=n.flipVariations,x=void 0===E||E,A=n.allowedAutoPlacements,C=t.options.placement,F=B(C),N=v||(F===C||!x?[ue(C)]:function(e){if(B(e)===a)return[];var t=ue(e);return[se(e),t,se(t)]}(C)),_=[C].concat(N).reduce((function(e,n){return e.concat(B(n)===a?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=n.boundary,o=n.rootBoundary,u=n.padding,a=n.flipVariations,c=n.allowedAutoPlacements,f=void 0===c?g:c,l=K(r),p=l?a?m:m.filter((function(e){return K(e)===l})):s,h=p.filter((function(e){return f.indexOf(e)>=0}));0===h.length&&(h=p);var d=h.reduce((function(t,n){return t[n]=De(e,{placement:n,boundary:i,rootBoundary:o,padding:u})[B(n)],t}),{});return Object.keys(d).sort((function(e,t){return d[e]-d[t]}))}(t,{placement:n,boundary:y,rootBoundary:w,padding:D,flipVariations:x,allowedAutoPlacements:A}):n)}),[]),M=t.rects.reference,S=t.rects.popper,O=new Map,T=!0,R=_[0],I=0;I<_.length;I++){var z=_[I],P=B(z),k=K(z)===c,j=[r,i].indexOf(P)>=0,L=j?"width":"height",U=De(t,{placement:z,boundary:y,rootBoundary:w,altBoundary:b,padding:D}),q=j?k?o:u:k?i:r;M[L]>S[L]&&(q=ue(q));var H=ue(q),G=[];if(p&&G.push(U[P]<=0),d&&G.push(U[q]<=0,U[H]<=0),G.every((function(e){return e}))){R=z,T=!1;break}O.set(z,G)}if(T)for(var V=function(e){var t=_.find((function(t){var n=O.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return R=t,"break"},Y=x?3:1;Y>0;Y--){if("break"===V(Y))break}t.placement!==R&&(t.modifiersData[f]._skip=!0,t.placement=R,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function we(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function be(e){return[r,o,i,u].some((function(t){return e[t]>=0}))}const Ee={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,o=t.modifiersData.preventOverflow,u=De(t,{elementContext:"reference"}),a=De(t,{altBoundary:!0}),s=we(u,r),c=we(a,i,o),f=be(s),l=be(c);t.modifiersData[n]={referenceClippingOffsets:s,popperEscapeOffsets:c,isReferenceHidden:f,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":l})}};const xe={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,i=e.name,a=n.offset,s=void 0===a?[0,0]:a,c=g.reduce((function(e,n){return e[n]=function(e,t,n){var i=B(e),a=[u,r].indexOf(i)>=0?-1:1,s="function"==typeof n?n(Object.assign({},t,{placement:e})):n,c=s[0],f=s[1];return c=c||0,f=(f||0)*a,[u,o].indexOf(i)>=0?{x:f,y:c}:{x:c,y:f}}(n,t.rects,s),e}),{}),f=c[t.placement],l=f.x,p=f.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=p),t.modifiersData[i]=c}};const Ae={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=ve({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}};const Ce={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,a=e.name,s=n.mainAxis,f=void 0===s||s,l=n.altAxis,p=void 0!==l&&l,h=n.boundary,d=n.rootBoundary,m=n.altBoundary,g=n.padding,v=n.tether,D=void 0===v||v,y=n.tetherOffset,w=void 0===y?0:y,b=De(t,{boundary:h,rootBoundary:d,padding:g,altBoundary:m}),E=B(t.placement),x=K(t.placement),A=!x,C=$(E),F="x"===C?"y":"x",N=t.modifiersData.popperOffsets,_=t.rects.reference,M=t.rects.popper,S="function"==typeof w?w(Object.assign({},t.rects,{placement:t.placement})):w,O="number"==typeof S?{mainAxis:S,altAxis:S}:Object.assign({mainAxis:0,altAxis:0},S),T=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,z={x:0,y:0};if(N){if(f){var P,k="y"===C?r:u,j="y"===C?i:o,U="y"===C?"height":"width",q=N[C],H=q+b[k],G=q-b[j],V=D?-M[U]/2:0,Y=x===c?_[U]:M[U],J=x===c?-M[U]:-_[U],X=t.elements.arrow,Q=D&&X?L(X):{width:0,height:0},ee=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},te=ee[k],ne=ee[j],re=Z(0,_[U],Q[U]),ie=A?_[U]/2-V-re-te-O.mainAxis:Y-re-te-O.mainAxis,oe=A?-_[U]/2+V+re+ne+O.mainAxis:J+re+ne+O.mainAxis,ue=t.elements.arrow&&W(t.elements.arrow),ae=ue?"y"===C?ue.clientTop||0:ue.clientLeft||0:0,se=null!=(P=null==T?void 0:T[C])?P:0,ce=q+oe-se,fe=Z(D?I(H,q+ie-se-ae):H,q,D?R(G,ce):G);N[C]=fe,z[C]=fe-q}if(p){var le,pe="x"===C?r:u,he="x"===C?i:o,de=N[F],me="y"===F?"height":"width",ge=de+b[pe],ve=de-b[he],ye=-1!==[r,u].indexOf(E),we=null!=(le=null==T?void 0:T[F])?le:0,be=ye?ge:de-_[me]-M[me]-we+O.altAxis,Ee=ye?de+_[me]+M[me]-we-O.altAxis:ve,xe=D&&ye?function(e,t,n){var r=Z(e,t,n);return r>n?n:r}(be,de,Ee):Z(D?be:ge,de,D?Ee:ve);N[F]=xe,z[F]=xe-de}t.modifiersData[a]=z}},requiresIfExists:["offset"]};function Fe(e,t,n){void 0===n&&(n=!1);var r,i,o=S(t),u=S(t)&&function(e){var t=e.getBoundingClientRect(),n=z(t.width)/e.offsetWidth||1,r=z(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),a=G(t),s=j(e,u,n),c={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(o||!o&&!n)&&(("body"!==N(t)||le(a))&&(c=(r=t)!==_(r)&&S(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:ce(r)),S(t)?((f=j(t,!0)).x+=t.clientLeft,f.y+=t.clientTop):a&&(f.x=fe(a))),{x:s.left+c.scrollLeft-f.x,y:s.top+c.scrollTop-f.y,width:s.width,height:s.height}}function Ne(e){var t=new Map,n=new Set,r=[];function i(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&i(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||i(e)})),r}var _e={placement:"bottom",modifiers:[],strategy:"absolute"};function Me(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function Se(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,i=t.defaultOptions,o=void 0===i?_e:i;return function(e,t,n){void 0===n&&(n=o);var i,u,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},_e,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},s=[],c=!1,f={state:a,setOptions:function(n){var i="function"==typeof n?n(a.options):n;l(),a.options=Object.assign({},o,a.options,i),a.scrollParents={reference:M(e)?he(e):e.contextElement?he(e.contextElement):[],popper:he(t)};var u,c,p=function(e){var t=Ne(e);return F.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((u=[].concat(r,a.options.modifiers),c=u.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(c).map((function(e){return c[e]}))));return a.orderedModifiers=p.filter((function(e){return e.enabled})),a.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,i=e.effect;if("function"==typeof i){var o=i({state:a,name:t,instance:f,options:r}),u=function(){};s.push(o||u)}})),f.update()},forceUpdate:function(){if(!c){var e=a.elements,t=e.reference,n=e.popper;if(Me(t,n)){a.rects={reference:Fe(t,W(n),"fixed"===a.options.strategy),popper:L(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(e){return a.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<a.orderedModifiers.length;r++)if(!0!==a.reset){var i=a.orderedModifiers[r],o=i.fn,u=i.options,s=void 0===u?{}:u,l=i.name;"function"==typeof o&&(a=o({state:a,options:s,name:l,instance:f})||a)}else a.reset=!1,r=-1}}},update:(i=function(){return new Promise((function(e){f.forceUpdate(),e(a)}))},function(){return u||(u=new Promise((function(e){Promise.resolve().then((function(){u=void 0,e(i())}))}))),u}),destroy:function(){l(),c=!0}};if(!Me(e,t))return f;function l(){s.forEach((function(e){return e()})),s=[]}return f.setOptions(n).then((function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)})),f}}var Oe=Se(),Te=Se({defaultModifiers:[ie,Ae,ne,T,xe,ye,Ce,Q,Ee]}),Be=Se({defaultModifiers:[ie,Ae,ne,T]})},1234:()=>{},1377:function(e){!function(){"use strict";var t={s:1,n:0,d:1};function n(e,t){if(isNaN(e=parseInt(e,10)))throw c();return e*t}function r(e,t){if(0===t)throw s();var n=Object.create(a.prototype);n.s=e<0?-1:1;var r=u(e=e<0?-e:e,t);return n.n=e/r,n.d=t/r,n}function i(e){for(var t={},n=e,r=2,i=4;i<=n;){for(;n%r==0;)n/=r,t[r]=(t[r]||0)+1;i+=1+2*r++}return n!==e?n>1&&(t[n]=(t[n]||0)+1):t[e]=(t[e]||0)+1,t}var o=function(e,r){var i,o=0,u=1,a=1,l=0,p=0,h=0,d=1,m=1,g=0,v=1,D=1,y=1,w=1e7;if(null==e);else if(void 0!==r){if(a=(o=e)*(u=r),o%1!=0||u%1!=0)throw f()}else switch(typeof e){case"object":if("d"in e&&"n"in e)o=e.n,u=e.d,"s"in e&&(o*=e.s);else{if(!(0 in e))throw c();o=e[0],1 in e&&(u=e[1])}a=o*u;break;case"number":if(e<0&&(a=e,e=-e),e%1==0)o=e;else if(e>0){for(e>=1&&(e/=m=Math.pow(10,Math.floor(1+Math.log(e)/Math.LN10)));v<=w&&y<=w;){if(e===(i=(g+D)/(v+y))){v+y<=w?(o=g+D,u=v+y):y>v?(o=D,u=y):(o=g,u=v);break}e>i?(g+=D,v+=y):(D+=g,y+=v),v>w?(o=D,u=y):(o=g,u=v)}o*=m}else(isNaN(e)||isNaN(r))&&(u=o=NaN);break;case"string":if(null===(v=e.match(/\d+|./g)))throw c();if("-"===v[g]?(a=-1,g++):"+"===v[g]&&g++,v.length===g+1?p=n(v[g++],a):"."===v[g+1]||"."===v[g]?("."!==v[g]&&(l=n(v[g++],a)),(++g+1===v.length||"("===v[g+1]&&")"===v[g+3]||"'"===v[g+1]&&"'"===v[g+3])&&(p=n(v[g],a),d=Math.pow(10,v[g].length),g++),("("===v[g]&&")"===v[g+2]||"'"===v[g]&&"'"===v[g+2])&&(h=n(v[g+1],a),m=Math.pow(10,v[g+1].length)-1,g+=3)):"/"===v[g+1]||":"===v[g+1]?(p=n(v[g],a),d=n(v[g+2],1),g+=3):"/"===v[g+3]&&" "===v[g+1]&&(l=n(v[g],a),p=n(v[g+2],a),d=n(v[g+4],1),g+=5),v.length<=g){a=o=h+(u=d*m)*l+m*p;break}default:throw c()}if(0===u)throw s();t.s=a<0?-1:1,t.n=Math.abs(o),t.d=Math.abs(u)};function u(e,t){if(!e)return t;if(!t)return e;for(;;){if(!(e%=t))return t;if(!(t%=e))return e}}function a(e,n){if(o(e,n),!(this instanceof a))return r(t.s*t.n,t.d);e=u(t.d,t.n),this.s=t.s,this.n=t.n/e,this.d=t.d/e}var s=function(){return new Error("Division by Zero")},c=function(){return new Error("Invalid argument")},f=function(){return new Error("Parameters must be integer")};a.prototype={s:1,n:0,d:1,abs:function(){return r(this.n,this.d)},neg:function(){return r(-this.s*this.n,this.d)},add:function(e,n){return o(e,n),r(this.s*this.n*t.d+t.s*this.d*t.n,this.d*t.d)},sub:function(e,n){return o(e,n),r(this.s*this.n*t.d-t.s*this.d*t.n,this.d*t.d)},mul:function(e,n){return o(e,n),r(this.s*t.s*this.n*t.n,this.d*t.d)},div:function(e,n){return o(e,n),r(this.s*t.s*this.n*t.d,this.d*t.n)},clone:function(){return r(this.s*this.n,this.d)},mod:function(e,n){if(isNaN(this.n)||isNaN(this.d))return new a(NaN);if(void 0===e)return r(this.s*this.n%this.d,1);if(o(e,n),0===t.n&&0===this.d)throw s();return r(this.s*(t.d*this.n)%(t.n*this.d),t.d*this.d)},gcd:function(e,n){return o(e,n),r(u(t.n,this.n)*u(t.d,this.d),t.d*this.d)},lcm:function(e,n){return o(e,n),0===t.n&&0===this.n?r(0,1):r(t.n*this.n,u(t.n,this.n)*u(t.d,this.d))},ceil:function(e){return e=Math.pow(10,e||0),isNaN(this.n)||isNaN(this.d)?new a(NaN):r(Math.ceil(e*this.s*this.n/this.d),e)},floor:function(e){return e=Math.pow(10,e||0),isNaN(this.n)||isNaN(this.d)?new a(NaN):r(Math.floor(e*this.s*this.n/this.d),e)},round:function(e){return e=Math.pow(10,e||0),isNaN(this.n)||isNaN(this.d)?new a(NaN):r(Math.round(e*this.s*this.n/this.d),e)},inverse:function(){return r(this.s*this.d,this.n)},pow:function(e,n){if(o(e,n),1===t.d)return t.s<0?r(Math.pow(this.s*this.d,t.n),Math.pow(this.n,t.n)):r(Math.pow(this.s*this.n,t.n),Math.pow(this.d,t.n));if(this.s<0)return null;var u=i(this.n),a=i(this.d),s=1,c=1;for(var f in u)if("1"!==f){if("0"===f){s=0;break}if(u[f]*=t.n,u[f]%t.d!=0)return null;u[f]/=t.d,s*=Math.pow(f,u[f])}for(var f in a)if("1"!==f){if(a[f]*=t.n,a[f]%t.d!=0)return null;a[f]/=t.d,c*=Math.pow(f,a[f])}return t.s<0?r(c,s):r(s,c)},equals:function(e,n){return o(e,n),this.s*this.n*t.d==t.s*t.n*this.d},compare:function(e,n){o(e,n);var r=this.s*this.n*t.d-t.s*t.n*this.d;return(0<r)-(r<0)},simplify:function(e){if(isNaN(this.n)||isNaN(this.d))return this;e=e||.001;for(var t=this.abs(),n=t.toContinued(),i=1;i<n.length;i++){for(var o=r(n[i-1],1),u=i-2;u>=0;u--)o=o.inverse().add(n[u]);if(Math.abs(o.sub(t).valueOf())<e)return o.mul(this.s)}return this},divisible:function(e,n){return o(e,n),!(!(t.n*this.d)||this.n*t.d%(t.n*this.d))},valueOf:function(){return this.s*this.n/this.d},toFraction:function(e){var t,n="",r=this.n,i=this.d;return this.s<0&&(n+="-"),1===i?n+=r:(e&&(t=Math.floor(r/i))>0&&(n+=t,n+=" ",r%=i),n+=r,n+="/",n+=i),n},toLatex:function(e){var t,n="",r=this.n,i=this.d;return this.s<0&&(n+="-"),1===i?n+=r:(e&&(t=Math.floor(r/i))>0&&(n+=t,r%=i),n+="\\frac{",n+=r,n+="}{",n+=i,n+="}"),n},toContinued:function(){var e,t=this.n,n=this.d,r=[];if(isNaN(t)||isNaN(n))return r;do{r.push(Math.floor(t/n)),e=t%n,t=n,n=e}while(1!==t);return r},toString:function(e){var t=this.n,n=this.d;if(isNaN(t)||isNaN(n))return"NaN";e=e||15;var r=function(e,t){for(;t%2==0;t/=2);for(;t%5==0;t/=5);if(1===t)return 0;for(var n=10%t,r=1;1!==n;r++)if(n=10*n%t,r>2e3)return 0;return r}(0,n),i=function(e,t,n){for(var r=1,i=function(e,t,n){for(var r=1;t>0;e=e*e%n,t>>=1)1&t&&(r=r*e%n);return r}(10,n,t),o=0;o<300;o++){if(r===i)return o;r=10*r%t,i=10*i%t}return 0}(0,n,r),o=this.s<0?"-":"";if(o+=t/n|0,t%=n,(t*=10)&&(o+="."),r){for(var u=i;u--;)o+=t/n|0,t%=n,t*=10;o+="(";for(u=r;u--;)o+=t/n|0,t%=n,t*=10;o+=")"}else for(u=e;t&&u--;)o+=t/n|0,t%=n,t*=10;return o}},Object.defineProperty(a,"__esModule",{value:!0}),a.default=a,a.Fraction=a,e.exports=a}()},1669:e=>{"use strict";e.exports=jQuery},1880:e=>{e.exports=function e(t,n){"use strict";var r,i,o=/(^([+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,u=/(^[ ]*|[ ]*$)/g,a=/(^([\w ]+,?[\w ]+)?[\w ]+,?[\w ]+\d+:\d+(:\d+)?[\w ]?|^\d{1,4}[\/\-]\d{1,4}[\/\-]\d{1,4}|^\w+, \w+ \d+, \d{4})/,s=/^0x[0-9a-f]+$/i,c=/^0/,f=function(t){return e.insensitive&&(""+t).toLowerCase()||""+t},l=f(t).replace(u,"")||"",p=f(n).replace(u,"")||"",h=l.replace(o,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),d=p.replace(o,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),m=parseInt(l.match(s),16)||1!==h.length&&l.match(a)&&Date.parse(l),g=parseInt(p.match(s),16)||m&&p.match(a)&&Date.parse(p)||null;if(g){if(m<g)return-1;if(m>g)return 1}for(var v=0,D=Math.max(h.length,d.length);v<D;v++){if(r=!(h[v]||"").match(c)&&parseFloat(h[v])||h[v]||0,i=!(d[v]||"").match(c)&&parseFloat(d[v])||d[v]||0,isNaN(r)!==isNaN(i))return isNaN(r)?1:-1;if(typeof r!=typeof i&&(r+="",i+=""),r<i)return-1;if(r>i)return 1}return 0}},2105:function(e,t,n){e.exports=function(e,t){"use strict";class n{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(n,r){const i=t.isElement(r)?e.getDataAttribute(r,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...t.isElement(r)?e.getDataAttributes(r):{},..."object"==typeof n?n:{}}}_typeCheckConfig(e,n=this.constructor.DefaultType){for(const[r,i]of Object.entries(n)){const n=e[r],o=t.isElement(n)?"element":t.toType(n);if(!new RegExp(i).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${r}" provided type "${o}" but expected type "${i}".`)}}}return n}(n(2333),n(4035))},2333:function(e){e.exports=function(){"use strict";function e(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function t(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}return{setDataAttribute(e,n,r){e.setAttribute(`data-bs-${t(n)}`,r)},removeDataAttribute(e,n){e.removeAttribute(`data-bs-${t(n)}`)},getDataAttributes(t){if(!t)return{};const n={},r=Object.keys(t.dataset).filter((e=>e.startsWith("bs")&&!e.startsWith("bsConfig")));for(const i of r){let r=i.replace(/^bs/,"");r=r.charAt(0).toLowerCase()+r.slice(1),n[r]=e(t.dataset[i])}return n},getDataAttribute:(n,r)=>e(n.getAttribute(`data-bs-${t(r)}`))}}()},2369:function(e){e.exports=function(){"use strict";function e(){return!0}function t(){return!1}function n(){}const r="Argument is not a typed-function.";function i(){function o(e){return"object"==typeof e&&null!==e&&e.constructor===Object}const u=[{name:"number",test:function(e){return"number"==typeof e}},{name:"string",test:function(e){return"string"==typeof e}},{name:"boolean",test:function(e){return"boolean"==typeof e}},{name:"Function",test:function(e){return"function"==typeof e}},{name:"Array",test:Array.isArray},{name:"Date",test:function(e){return e instanceof Date}},{name:"RegExp",test:function(e){return e instanceof RegExp}},{name:"Object",test:o},{name:"null",test:function(e){return null===e}},{name:"undefined",test:function(e){return void 0===e}}],a={name:"any",test:e,isAny:!0};let s,c,f=0,l={createCount:0};function p(e){const t=s.get(e);if(t)return t;let n='Unknown type "'+e+'"';const r=e.toLowerCase();let i;for(i of c)if(i.toLowerCase()===r){n+='. Did you mean "'+i+'" ?';break}throw new TypeError(n)}function h(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"any";const n=t?p(t).index:c.length,r=[];for(let t=0;t<e.length;++t){if(!e[t]||"string"!=typeof e[t].name||"function"!=typeof e[t].test)throw new TypeError("Object with properties {name: string, test: function} expected");const i=e[t].name;if(s.has(i))throw new TypeError('Duplicate type name "'+i+'"');r.push(i),s.set(i,{name:i,test:e[t].test,isAny:e[t].isAny,index:n+t,conversionsTo:[]})}const i=c.slice(n);c=c.slice(0,n).concat(r).concat(i);for(let e=n+r.length;e<c.length;++e)s.get(c[e]).index=e}function d(){s=new Map,c=[],f=0,h([a],!1)}function m(){let e;for(e of c)s.get(e).conversionsTo=[];f=0}function g(e){const t=c.filter((t=>{const n=s.get(t);return!n.isAny&&n.test(e)}));return t.length?t:["any"]}function v(e){return e&&"function"==typeof e&&"_typedFunctionData"in e}function D(e,t,n){if(!v(e))throw new TypeError(r);const i=n&&n.exact,o=C(Array.isArray(t)?t.join(","):t),u=b(o);if(!i||u in e.signatures){const t=e._typedFunctionData.signatureMap.get(u);if(t)return t}const a=o.length;let s,c;if(i){let t;for(t in s=[],e.signatures)s.push(e._typedFunctionData.signatureMap.get(t))}else s=e._typedFunctionData.signatures;for(let e=0;e<a;++e){const t=o[e],n=[];let r;for(r of s){const i=M(r.params,e);if(i&&(!t.restParam||i.restParam)){if(!i.hasAny){const e=A(i);if(t.types.some((t=>!e.has(t.name))))continue}n.push(r)}}if(s=n,0===s.length)break}for(c of s)if(c.params.length<=a)return c;throw new TypeError("Signature not found (signature: "+(e.name||"unnamed")+"("+b(o,", ")+"))")}function y(e,t,n){return D(e,t,n).implementation}function w(e,t){const n=p(t);if(n.test(e))return e;const r=n.conversionsTo;if(0===r.length)throw new Error("There are no conversions to "+t+" defined.");for(let t=0;t<r.length;t++)if(p(r[t].from).test(e))return r[t].convert(e);throw new Error("Cannot convert "+e+" to "+t)}function b(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:",";return e.map((e=>e.name)).join(t)}function E(e){const t=0===e.indexOf("..."),n=(t?e.length>3?e.slice(3):"any":e).split("|").map((e=>p(e.trim())));let r=!1,i=t?"...":"";return{types:n.map((function(e){return r=e.isAny||r,i+=e.name+"|",{name:e.name,typeIndex:e.index,test:e.test,isAny:e.isAny,conversion:null,conversionIndex:-1}})),name:i.slice(0,-1),hasAny:r,hasConversion:!1,restParam:t}}function x(e){const t=k(e.types.map((e=>e.name)));let n=e.hasAny,r=e.name;const i=t.map((function(e){const t=p(e.from);return n=t.isAny||n,r+="|"+e.from,{name:e.from,typeIndex:t.index,test:t.test,isAny:t.isAny,conversion:e,conversionIndex:e.index}}));return{types:e.types.concat(i),name:r,hasAny:n,hasConversion:i.length>0,restParam:e.restParam}}function A(e){return e.typeSet||(e.typeSet=new Set,e.types.forEach((t=>e.typeSet.add(t.name)))),e.typeSet}function C(e){const t=[];if("string"!=typeof e)throw new TypeError("Signatures must be strings");const n=e.trim();if(""===n)return t;const r=n.split(",");for(let e=0;e<r.length;++e){const n=E(r[e].trim());if(n.restParam&&e!==r.length-1)throw new SyntaxError('Unexpected rest parameter "'+r[e]+'": only allowed for the last parameter');if(0===n.types.length)return null;t.push(n)}return t}function F(e){const t=J(e);return!!t&&t.restParam}function N(t){if(t&&0!==t.types.length){if(1===t.types.length)return p(t.types[0].name).test;if(2===t.types.length){const e=p(t.types[0].name).test,n=p(t.types[1].name).test;return function(t){return e(t)||n(t)}}{const e=t.types.map((function(e){return p(e.name).test}));return function(t){for(let n=0;n<e.length;n++)if(e[n](t))return!0;return!1}}}return e}function _(e){let t,n,r;if(F(e)){t=Z(e).map(N);const n=t.length,r=N(J(e)),i=function(e){for(let t=n;t<e.length;t++)if(!r(e[t]))return!1;return!0};return function(e){for(let n=0;n<t.length;n++)if(!t[n](e[n]))return!1;return i(e)&&e.length>=n+1}}return 0===e.length?function(e){return 0===e.length}:1===e.length?(n=N(e[0]),function(e){return n(e[0])&&1===e.length}):2===e.length?(n=N(e[0]),r=N(e[1]),function(e){return n(e[0])&&r(e[1])&&2===e.length}):(t=e.map(N),function(e){for(let n=0;n<t.length;n++)if(!t[n](e[n]))return!1;return e.length===t.length})}function M(e,t){return t<e.length?e[t]:F(e)?J(e):null}function S(e,t){const n=M(e,t);return n?A(n):new Set}function O(e){return null===e.conversion||void 0===e.conversion}function T(e,t){const n=new Set;return e.forEach((e=>{const r=S(e.params,t);let i;for(i of r)n.add(i)})),n.has("any")?["any"]:Array.from(n)}function B(e,t,n){let r,i;const o=e||"unnamed";let u,a=n;for(u=0;u<t.length;u++){const e=[];if(a.forEach((n=>{const r=N(M(n.params,u));(u<n.params.length||F(n.params))&&r(t[u])&&e.push(n)})),0===e.length){if(i=T(a,u),i.length>0){const e=g(t[u]);return r=new TypeError("Unexpected type of argument in function "+o+" (expected: "+i.join(" or ")+", actual: "+e.join(" | ")+", index: "+u+")"),r.data={category:"wrongType",fn:o,index:u,actual:e,expected:i},r}}else a=e}const s=a.map((function(e){return F(e.params)?1/0:e.params.length}));if(t.length<Math.min.apply(null,s))return i=T(a,u),r=new TypeError("Too few arguments in function "+o+" (expected: "+i.join(" or ")+", index: "+t.length+")"),r.data={category:"tooFewArgs",fn:o,index:t.length,expected:i},r;const c=Math.max.apply(null,s);if(t.length>c)return r=new TypeError("Too many arguments in function "+o+" (expected: "+c+", actual: "+t.length+")"),r.data={category:"tooManyArgs",fn:o,index:t.length,expectedLength:c},r;const f=[];for(let e=0;e<t.length;++e)f.push(g(t[e]).join("|"));return r=new TypeError('Arguments of type "'+f.join(", ")+'" do not match any of the defined signatures of function '+o+"."),r.data={category:"mismatch",actual:f},r}function R(e){let t=c.length+1;for(let n=0;n<e.types.length;n++)O(e.types[n])&&(t=Math.min(t,e.types[n].typeIndex));return t}function I(e){let t=f+1;for(let n=0;n<e.types.length;n++)O(e.types[n])||(t=Math.min(t,e.types[n].conversionIndex));return t}function z(e,t){if(e.hasAny){if(!t.hasAny)return 1}else if(t.hasAny)return-1;if(e.restParam){if(!t.restParam)return 1}else if(t.restParam)return-1;if(e.hasConversion){if(!t.hasConversion)return 1}else if(t.hasConversion)return-1;const n=R(e)-R(t);if(n<0)return-1;if(n>0)return 1;const r=I(e)-I(t);return r<0?-1:r>0?1:0}function P(e,t){const n=e.params,r=t.params,i=J(n),o=J(r),u=F(n),a=F(r);if(u&&i.hasAny){if(!a||!o.hasAny)return 1}else if(a&&o.hasAny)return-1;let s,c=0,f=0;for(s of n)s.hasAny&&++c,s.hasConversion&&++f;let l=0,p=0;for(s of r)s.hasAny&&++l,s.hasConversion&&++p;if(c!==l)return c-l;if(u&&i.hasConversion){if(!a||!o.hasConversion)return 1}else if(a&&o.hasConversion)return-1;if(f!==p)return f-p;if(u){if(!a)return 1}else if(a)return-1;const h=(n.length-r.length)*(u?-1:1);if(0!==h)return h;const d=[];let m,g=0;for(let e=0;e<n.length;++e){const t=z(n[e],r[e]);d.push(t),g+=t}if(0!==g)return g;for(m of d)if(0!==m)return m;return 0}function k(e){if(0===e.length)return[];const t=e.map(p);e.length>1&&t.sort(((e,t)=>e.index-t.index));let n=t[0].conversionsTo;if(1===e.length)return n;n=n.concat([]);const r=new Set(e);for(let e=1;e<t.length;++e){let i;for(i of t[e].conversionsTo)r.has(i.from)||(n.push(i),r.add(i.from))}return n}function j(e,t){let n=t;if(e.some((e=>e.hasConversion))){const r=F(e),i=e.map(L);n=function(){const e=[],n=r?arguments.length-1:arguments.length;for(let t=0;t<n;t++)e[t]=i[t](arguments[t]);return r&&(e[n]=arguments[n].map(i[n])),t.apply(this,e)}}let r=n;if(F(e)){const t=e.length-1;r=function(){return n.apply(this,X(arguments,0,t).concat([X(arguments,t)]))}}return r}function L(e){let t,n,r,i;const o=[],u=[];switch(e.types.forEach((function(e){e.conversion&&(o.push(p(e.conversion.from).test),u.push(e.conversion.convert))})),u.length){case 0:return function(e){return e};case 1:return t=o[0],r=u[0],function(e){return t(e)?r(e):e};case 2:return t=o[0],n=o[1],r=u[0],i=u[1],function(e){return t(e)?r(e):n(e)?i(e):e};default:return function(e){for(let t=0;t<u.length;t++)if(o[t](e))return u[t](e);return e}}}function U(e){function t(e,n,r){if(n<e.length){const i=e[n];let o=[];if(i.restParam){const e=i.types.filter(O);e.length<i.types.length&&o.push({types:e,name:"..."+e.map((e=>e.name)).join("|"),hasAny:e.some((e=>e.isAny)),hasConversion:!1,restParam:!0}),o.push(i)}else o=i.types.map((function(e){return{types:[e],name:e.name,hasAny:e.isAny,hasConversion:e.conversion,restParam:!1}}));return K(o,(function(i){return t(e,n+1,r.concat([i]))}))}return[r]}return t(e,0,[])}function q(e,t){const n=Math.max(e.length,t.length);for(let r=0;r<n;r++){const n=S(e,r),i=S(t,r);let o,u=!1;for(o of i)if(n.has(o)){u=!0;break}if(!u)return!1}const r=e.length,i=t.length,o=F(e),u=F(t);return o?u?r===i:i>=r:u?r>=i:r===i}function H(e){return e.map((e=>ie(e)?ne(e.referToSelf.callback):re(e)?te(e.referTo.references,e.referTo.callback):e))}function G(e,t,n){const r=[];let i;for(i of e){let e=n[i];if("number"!=typeof e)throw new TypeError('No definition for referenced signature "'+i+'"');if(e=t[e],"function"!=typeof e)return!1;r.push(e)}return r}function V(e,t,n){const r=H(e),i=new Array(r.length).fill(!1);let o=!0;for(;o;){o=!1;let e=!0;for(let u=0;u<r.length;++u){if(i[u])continue;const a=r[u];if(ie(a))r[u]=a.referToSelf.callback(n),r[u].referToSelf=a.referToSelf,i[u]=!0,e=!1;else if(re(a)){const n=G(a.referTo.references,r,t);n?(r[u]=a.referTo.callback.apply(this,n),r[u].referTo=a.referTo,i[u]=!0,e=!1):o=!0}}if(e&&o)throw new SyntaxError("Circular reference detected in resolving typed.referTo")}return r}function Y(e){const t=/\bthis(\(|\.signatures\b)/;Object.keys(e).forEach((n=>{const r=e[n];if(t.test(r.toString()))throw new SyntaxError("Using `this` to self-reference a function is deprecated since typed-function@3. Use typed.referTo and typed.referToSelf instead.")}))}function W(e,r){if(l.createCount++,0===Object.keys(r).length)throw new SyntaxError("No signatures provided");l.warnAgainstDeprecatedThis&&Y(r);const i=[],o=[],u={},a=[];let s;for(s in r){if(!Object.prototype.hasOwnProperty.call(r,s))continue;const e=C(s);if(!e)continue;i.forEach((function(t){if(q(t,e))throw new TypeError('Conflicting signatures "'+b(t)+'" and "'+b(e)+'".')})),i.push(e);const t=o.length;o.push(r[s]);const n=e.map(x);let c;for(c of U(n)){const e=b(c);a.push({params:c,name:e,fn:t}),c.every((e=>!e.hasConversion))&&(u[e]=t)}}a.sort(P);const c=V(o,u,se);let f;for(f in u)Object.prototype.hasOwnProperty.call(u,f)&&(u[f]=c[u[f]]);const p=[],h=new Map;for(f of a)h.has(f.name)||(f.fn=c[f.fn],p.push(f),h.set(f.name,f));const d=p[0]&&p[0].params.length<=2&&!F(p[0].params),m=p[1]&&p[1].params.length<=2&&!F(p[1].params),g=p[2]&&p[2].params.length<=2&&!F(p[2].params),v=p[3]&&p[3].params.length<=2&&!F(p[3].params),D=p[4]&&p[4].params.length<=2&&!F(p[4].params),y=p[5]&&p[5].params.length<=2&&!F(p[5].params),w=d&&m&&g&&v&&D&&y;for(let e=0;e<p.length;++e)p[e].test=_(p[e].params);const E=d?N(p[0].params[0]):t,A=m?N(p[1].params[0]):t,M=g?N(p[2].params[0]):t,S=v?N(p[3].params[0]):t,O=D?N(p[4].params[0]):t,T=y?N(p[5].params[0]):t,B=d?N(p[0].params[1]):t,R=m?N(p[1].params[1]):t,I=g?N(p[2].params[1]):t,z=v?N(p[3].params[1]):t,k=D?N(p[4].params[1]):t,L=y?N(p[5].params[1]):t;for(let e=0;e<p.length;++e)p[e].implementation=j(p[e].params,p[e].fn);const H=d?p[0].implementation:n,G=m?p[1].implementation:n,W=g?p[2].implementation:n,$=v?p[3].implementation:n,Z=D?p[4].implementation:n,J=y?p[5].implementation:n,X=d?p[0].params.length:-1,Q=m?p[1].params.length:-1,K=g?p[2].params.length:-1,ee=v?p[3].params.length:-1,te=D?p[4].params.length:-1,ne=y?p[5].params.length:-1,re=w?6:0,ie=p.length,oe=p.map((e=>e.test)),ue=p.map((e=>e.implementation)),ae=function(){for(let e=re;e<ie;e++)if(oe[e](arguments))return ue[e].apply(this,arguments);return l.onMismatch(e,arguments,p)};function se(e,t){return arguments.length===X&&E(e)&&B(t)?H.apply(this,arguments):arguments.length===Q&&A(e)&&R(t)?G.apply(this,arguments):arguments.length===K&&M(e)&&I(t)?W.apply(this,arguments):arguments.length===ee&&S(e)&&z(t)?$.apply(this,arguments):arguments.length===te&&O(e)&&k(t)?Z.apply(this,arguments):arguments.length===ne&&T(e)&&L(t)?J.apply(this,arguments):ae.apply(this,arguments)}try{Object.defineProperty(se,"name",{value:e})}catch(e){}return se.signatures=u,se._typedFunctionData={signatures:p,signatureMap:h},se}function $(e,t,n){throw B(e,t,n)}function Z(e){return X(e,0,e.length-1)}function J(e){return e[e.length-1]}function X(e,t,n){return Array.prototype.slice.call(e,t,n)}function Q(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return e[n]}function K(e,t){return Array.prototype.concat.apply([],e.map(t))}function ee(){const e=Z(arguments).map((e=>b(C(e)))),t=J(arguments);if("function"!=typeof t)throw new TypeError("Callback function expected as last argument");return te(e,t)}function te(e,t){return{referTo:{references:e,callback:t}}}function ne(e){if("function"!=typeof e)throw new TypeError("Callback function expected as first argument");return{referToSelf:{callback:e}}}function re(e){return e&&"object"==typeof e.referTo&&Array.isArray(e.referTo.references)&&"function"==typeof e.referTo.callback}function ie(e){return e&&"object"==typeof e.referToSelf&&"function"==typeof e.referToSelf.callback}function oe(e,t){if(!e)return t;if(t&&t!==e){const n=new Error("Function names do not match (expected: "+e+", actual: "+t+")");throw n.data={actual:t,expected:e},n}return e}function ue(e){let t;for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(v(e[n])||"string"==typeof e[n].signature)&&(t=oe(t,e[n].name));return t}function ae(e,t){let n;for(n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(n in e&&t[n]!==e[n]){const r=new Error('Signature "'+n+'" is defined twice');throw r.data={signature:n,sourceFunction:t[n],destFunction:e[n]},r}e[n]=t[n]}}d(),h(u);const se=l;function ce(e){if(!e||"string"!=typeof e.from||"string"!=typeof e.to||"function"!=typeof e.convert)throw new TypeError("Object with properties {from: string, to: string, convert: function} expected");if(e.to===e.from)throw new SyntaxError('Illegal to define conversion from "'+e.from+'" to itself.')}return l=function(e){const t="string"==typeof e;let n=t?e:"";const r={};for(let e=t?1:0;e<arguments.length;++e){const i=arguments[e];let u,a={};if("function"==typeof i?(u=i.name,"string"==typeof i.signature?a[i.signature]=i:v(i)&&(a=i.signatures)):o(i)&&(a=i,t||(u=ue(i))),0===Object.keys(a).length){const t=new TypeError("Argument to 'typed' at index "+e+" is not a (typed) function, nor an object with signatures as keys and functions as values.");throw t.data={index:e,argument:i},t}t||(n=oe(n,u)),ae(r,a)}return W(n||"",r)},l.create=i,l.createCount=se.createCount,l.onMismatch=$,l.throwMismatchError=$,l.createError=B,l.clear=d,l.clearConversions=m,l.addTypes=h,l._findType=p,l.referTo=ee,l.referToSelf=ne,l.convert=w,l.findSignature=D,l.find=y,l.isTypedFunction=v,l.warnAgainstDeprecatedThis=!0,l.addType=function(e,t){let n="any";!1!==t&&s.has("Object")&&(n="Object"),l.addTypes([e],n)},l.addConversion=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{override:!1};ce(e);const n=p(e.to),r=n.conversionsTo.find((t=>t.from===e.from));if(r){if(!t||!t.override)throw new Error('There is already a conversion from "'+e.from+'" to "'+n.name+'"');l.removeConversion({from:r.from,to:e.to,convert:r.convert})}n.conversionsTo.push({from:e.from,convert:e.convert,index:f++})},l.addConversions=function(e,t){e.forEach((e=>l.addConversion(e,t)))},l.removeConversion=function(e){ce(e);const t=p(e.to),n=Q(t.conversionsTo,(t=>t.from===e.from));if(!n)throw new Error("Attempt to remove nonexistent conversion from "+e.from+" to "+e.to);if(n.convert!==e.convert)throw new Error("Conversion to remove does not match existing conversion");const r=t.conversionsTo.indexOf(n);t.conversionsTo.splice(r,1)},l.resolve=function(e,t){if(!v(e))throw new TypeError(r);const n=e._typedFunctionData.signatures;for(let e=0;e<n.length;++e)if(n[e].test(t))return n[e];return null},l}return i()}()},2812:function(e,t){!function(e){"use strict";const t={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},n=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),r=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,i=(e,t)=>{const i=e.nodeName.toLowerCase();return t.includes(i)?!n.has(i)||Boolean(r.test(e.nodeValue)):t.filter((e=>e instanceof RegExp)).some((e=>e.test(i)))};function o(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);const r=(new window.DOMParser).parseFromString(e,"text/html"),o=[].concat(...r.body.querySelectorAll("*"));for(const e of o){const n=e.nodeName.toLowerCase();if(!Object.keys(t).includes(n)){e.remove();continue}const r=[].concat(...e.attributes),o=[].concat(t["*"]||[],t[n]||[]);for(const t of r)i(t,o)||e.removeAttribute(t.nodeName)}return r.body.innerHTML}e.DefaultAllowlist=t,e.sanitizeHtml=o,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(t)},3029:function(e,t,n){e.exports=function(e,t,n,r,i,o,u){"use strict";function a(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e)for(const n in e)if("default"!==n){const r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:()=>e[n]})}return t.default=e,Object.freeze(t)}const s=a(e),c="tooltip",f=new Set(["sanitize","allowList","sanitizeFn"]),l="fade",p="show",h=".tooltip-inner",d=".modal",m="hide.bs.modal",g="hover",v="focus",D="click",y="manual",w="hide",b="hidden",E="show",x="shown",A="inserted",C="click",F="focusin",N="focusout",_="mouseenter",M="mouseleave",S={AUTO:"auto",TOP:"top",RIGHT:i.isRTL()?"left":"right",BOTTOM:"bottom",LEFT:i.isRTL()?"right":"left"},O={allowList:o.DefaultAllowlist,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},T={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class B extends t{constructor(e,t){if(void 0===s)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return O}static get DefaultType(){return T}static get NAME(){return c}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),n.off(this._element.closest(d),m,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=n.trigger(this._element,this.constructor.eventName(E)),t=(i.findShadowRoot(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const r=this._getTipElement();this._element.setAttribute("aria-describedby",r.getAttribute("id"));const{container:o}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(o.append(r),n.trigger(this._element,this.constructor.eventName(A))),this._popper=this._createPopper(r),r.classList.add(p),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))n.on(e,"mouseover",i.noop);const u=()=>{n.trigger(this._element,this.constructor.eventName(x)),!1===this._isHovered&&this._leave(),this._isHovered=!1};this._queueCallback(u,this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(n.trigger(this._element,this.constructor.eventName(w)).defaultPrevented)return;if(this._getTipElement().classList.remove(p),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))n.off(e,"mouseover",i.noop);this._activeTrigger[D]=!1,this._activeTrigger[v]=!1,this._activeTrigger[g]=!1,this._isHovered=null;const e=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),n.trigger(this._element,this.constructor.eventName(b)))};this._queueCallback(e,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(l,p),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=i.getUID(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(l),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new u({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[h]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(l)}_isShown(){return this.tip&&this.tip.classList.contains(p)}_createPopper(e){const t=i.execute(this._config.placement,[this,e,this._element]),n=S[t.toUpperCase()];return s.createPopper(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return i.execute(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...i.execute(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if("click"===t)n.on(this._element,this.constructor.eventName(C),this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger[D]=!(t._isShown()&&t._activeTrigger[D]),t.toggle()}));else if(t!==y){const e=t===g?this.constructor.eventName(_):this.constructor.eventName(F),r=t===g?this.constructor.eventName(M):this.constructor.eventName(N);n.on(this._element,e,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?v:g]=!0,t._enter()})),n.on(this._element,r,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?v:g]=t._element.contains(e.relatedTarget),t._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},n.on(this._element.closest(d),m,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=r.getDataAttributes(this._element);for(const e of Object.keys(t))f.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:i.getElement(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each((function(){const t=B.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}return i.defineJQueryPlugin(B),B}(n(1170),n(9011),n(7956),n(2333),n(4035),n(2812),n(3982))},3031:function(e,t,n){var r;!function(e,i){function o(e){var t=this,n="";t.next=function(){var e=t.x^t.x>>>2;return t.x=t.y,t.y=t.z,t.z=t.w,t.w=t.v,(t.d=t.d+362437|0)+(t.v=t.v^t.v<<4^e^e<<1)|0},t.x=0,t.y=0,t.z=0,t.w=0,t.v=0,e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),r==n.length&&(t.d=t.x<<10^t.x>>>4),t.next()}function u(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t.v=e.v,t.d=e.d,t}function a(e,t){var n=new o(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&u(r,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.xorwow=a}(0,e=n.nmd(e),n.amdD)},3059:function(e,t){var n,r,i;r=[t],n=function(e){var t,n,r=window,i=document,o="mousemove touchmove",u="mouseup touchend",a="mousedown touchstart",s="mouseenter",c="click",f="EventListener",l="add"+f,p="remove"+f,h=4,d=[],m=function(e,f){for(e=0;e<d.length;)f=(f=d[e++]).container||f,a.split(" ").forEach((function(e,t){f[p](e,f["md"+t],0)})),f[p](c,f.mc,1),u.split(" ").forEach((function(e,t){r[p](e,f["mu"+t],0)})),o.split(" ").forEach((function(e,t){r[p](e,f["mm"+t],0)})),i[p](s,f.me,0);for(d=[].slice.call(i.getElementsByClassName("dragscroll")),e=0;e<d.length;)!function(e,f,p,d,m,v,D,y,w){a.split(" ").forEach((function(t,n){(w=e.container||e)[l](t,w["md"+n]=function(n){n.target.classList.contains("no-drag")||e.hasAttribute("nochilddrag")&&i.elementFromPoint(n.pageX,n.pageY)!=w||(g(n),D=1,v=0,d=f=n.clientX,m=p=n.clientY,"mousedown"===t&&(n.preventDefault(),n.stopPropagation()))},0)})),(w=e.container||e)[l](c,w.mc=function(e){if(v)e.preventDefault(),e.stopPropagation(),v=0,D=0;else if(e.target.href){var t=e.target.children[0];void 0!==t&&t.click()}},1),u.split(" ").forEach((function(t,n){r[l](t,w["mu"+n]=function(t){setTimeout((function(){e.classList.remove("dragging")}),100),D=0},0)})),i[l](s,w.me=function(e){e.buttonsPressed||(D=0)},0),o.split(" ").forEach((function(o,u){r[l](o,w["mm"+u]=function(r){D&&(g(r),e.classList.add("dragging"),!v&&(Math.abs(r.clientX-d)>h||Math.abs(r.clientY-m)>h)&&(v=!0),v&&((y=e.scroller||e).scrollLeft-=t=-f+(f=r.clientX),y.scrollTop-=n=-p+(p=r.clientY),e==i.body&&((y=i.documentElement).scrollLeft-=t,y.scrollTop-=n)))},0)}))}(d[e++])};function g(e){e.touches&&(e.clientX=e.touches[0].clientX,e.clientY=e.touches[0].clientY)}"complete"==i.readyState?m():r[l]("load",m,0),e.reset=m},void 0===(i="function"==typeof n?n.apply(t,r):n)||(e.exports=i)},3181:function(e,t,n){var r;!function(e,i){function o(e){var t=this,n="";t.x=0,t.y=0,t.z=0,t.w=0,t.next=function(){var e=t.x^t.x<<11;return t.x=t.y,t.y=t.z,t.z=t.w,t.w^=t.w>>>19^e^e>>>8},e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),t.next()}function u(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t}function a(e,t){var n=new o(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&u(r,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.xor128=a}(0,e=n.nmd(e),n.amdD)},3717:function(e,t,n){var r;!function(e,i){function o(e){var t=this,n="";t.next=function(){var e=t.b,n=t.c,r=t.d,i=t.a;return e=e<<25^e>>>7^n,n=n-r|0,r=r<<24^r>>>8^i,i=i-e|0,t.b=e=e<<20^e>>>12^n,t.c=n=n-r|0,t.d=r<<16^n>>>16^i,t.a=i-e|0},t.a=0,t.b=0,t.c=-1640531527,t.d=1367130551,e===Math.floor(e)?(t.a=e/4294967296|0,t.b=0|e):n+=e;for(var r=0;r<n.length+20;r++)t.b^=0|n.charCodeAt(r),t.next()}function u(e,t){return t.a=e.a,t.b=e.b,t.c=e.c,t.d=e.d,t}function a(e,t){var n=new o(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&u(r,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.tychei=a}(0,e=n.nmd(e),n.amdD)},3982:function(e,t,n){e.exports=function(e,t,n,r){"use strict";const i="TemplateFactory",o={allowList:n.DefaultAllowlist,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},u={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},a={entry:"(string|element|function|null)",selector:"(string|element)"};class s extends t{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return o}static get DefaultType(){return u}static get NAME(){return i}getContent(){return Object.values(this._config.content).map((e=>this._resolvePossibleFunction(e))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[t,n]of Object.entries(this._config.content))this._setContent(e,n,t);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},a)}_setContent(t,n,i){const o=e.findOne(i,t);o&&((n=this._resolvePossibleFunction(n))?r.isElement(n)?this._putElementInTemplate(r.getElement(n),o):this._config.html?o.innerHTML=this._maybeSanitize(n):o.textContent=n:o.remove())}_maybeSanitize(e){return this._config.sanitize?n.sanitizeHtml(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return r.execute(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}return s}(n(5411),n(2105),n(2812),n(4035))},4035:function(e,t){!function(e){"use strict";const t=1e6,n=1e3,r="transitionend",i=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,((e,t)=>`#${CSS.escape(t)}`))),e),o=e=>null==e?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),u=e=>{do{e+=Math.floor(Math.random()*t)}while(document.getElementById(e));return e},a=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:r}=window.getComputedStyle(e);const i=Number.parseFloat(t),o=Number.parseFloat(r);return i||o?(t=t.split(",")[0],r=r.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(r))*n):0},s=e=>{e.dispatchEvent(new Event(r))},c=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),f=e=>c(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(i(e)):null,l=e=>{if(!c(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const t=e.closest("summary");if(t&&t.parentNode!==n)return!1;if(null===t)return!1}return t},p=e=>!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),h=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?h(e.parentNode):null},d=()=>{},m=e=>{e.offsetHeight},g=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,v=[],D=e=>{"loading"===document.readyState?(v.length||document.addEventListener("DOMContentLoaded",(()=>{for(const e of v)e()})),v.push(e)):e()},y=()=>"rtl"===document.documentElement.dir,w=e=>{D((()=>{const t=g();if(t){const n=e.NAME,r=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=r,e.jQueryInterface)}}))},b=(e,t=[],n=e)=>"function"==typeof e?e.call(...t):n,E=(e,t,n=!0)=>{if(!n)return void b(e);const i=5,o=a(t)+i;let u=!1;const c=({target:n})=>{n===t&&(u=!0,t.removeEventListener(r,c),b(e))};t.addEventListener(r,c),setTimeout((()=>{u||s(t)}),o)},x=(e,t,n,r)=>{const i=e.length;let o=e.indexOf(t);return-1===o?!n&&r?e[i-1]:e[0]:(o+=n?1:-1,r&&(o=(o+i)%i),e[Math.max(0,Math.min(o,i-1))])};e.defineJQueryPlugin=w,e.execute=b,e.executeAfterTransition=E,e.findShadowRoot=h,e.getElement=f,e.getNextActiveElement=x,e.getTransitionDurationFromElement=a,e.getUID=u,e.getjQuery=g,e.isDisabled=p,e.isElement=c,e.isRTL=y,e.isVisible=l,e.noop=d,e.onDOMContentLoaded=D,e.parseSelector=i,e.reflow=m,e.toType=o,e.triggerTransitionEnd=s,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(t)},4801:function(e,t,n){var r;!function(i,o,u){var a,s=256,c=u.pow(s,6),f=u.pow(2,52),l=2*f,p=255;function h(e,t,n){var r=[],p=v(g((t=1==t?{entropy:!0}:t||{}).entropy?[e,D(o)]:null==e?function(){try{var e;return a&&(e=a.randomBytes)?e=e(s):(e=new Uint8Array(s),(i.crypto||i.msCrypto).getRandomValues(e)),D(e)}catch(e){var t=i.navigator,n=t&&t.plugins;return[+new Date,i,n,i.screen,D(o)]}}():e,3),r),h=new d(r),y=function(){for(var e=h.g(6),t=c,n=0;e<f;)e=(e+n)*s,t*=s,n=h.g(1);for(;e>=l;)e/=2,t/=2,n>>>=1;return(e+n)/t};return y.int32=function(){return 0|h.g(4)},y.quick=function(){return h.g(4)/4294967296},y.double=y,v(D(h.S),o),(t.pass||n||function(e,t,n,r){return r&&(r.S&&m(r,h),e.state=function(){return m(h,{})}),n?(u.random=e,t):e})(y,p,"global"in t?t.global:this==u,t.state)}function d(e){var t,n=e.length,r=this,i=0,o=r.i=r.j=0,u=r.S=[];for(n||(e=[n++]);i<s;)u[i]=i++;for(i=0;i<s;i++)u[i]=u[o=p&o+e[i%n]+(t=u[i])],u[o]=t;(r.g=function(e){for(var t,n=0,i=r.i,o=r.j,u=r.S;e--;)t=u[i=p&i+1],n=n*s+u[p&(u[i]=u[o=p&o+t])+(u[o]=t)];return r.i=i,r.j=o,n})(s)}function m(e,t){return t.i=e.i,t.j=e.j,t.S=e.S.slice(),t}function g(e,t){var n,r=[],i=typeof e;if(t&&"object"==i)for(n in e)try{r.push(g(e[n],t-1))}catch(e){}return r.length?r:"string"==i?e:e+"\0"}function v(e,t){for(var n,r=e+"",i=0;i<r.length;)t[p&i]=p&(n^=19*t[p&i])+r.charCodeAt(i++);return D(t)}function D(e){return String.fromCharCode.apply(0,e)}if(v(u.random(),o),e.exports){e.exports=h;try{a=n(1234)}catch(e){}}else void 0===(r=function(){return h}.call(t,n,t,e))||(e.exports=r)}("undefined"!=typeof self?self:this,[],Math)},5411:function(e,t,n){e.exports=function(e){"use strict";const t=t=>{let n=t.getAttribute("data-bs-target");if(!n||"#"===n){let e=t.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;e.includes("#")&&!e.startsWith("#")&&(e=`#${e.split("#")[1]}`),n=e&&"#"!==e?e.trim():null}return n?n.split(",").map((t=>e.parseSelector(t))).join(","):null},n={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter((e=>e.matches(t))),parents(e,t){const n=[];let r=e.parentNode.closest(t);for(;r;)n.push(r),r=r.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(t){const n=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((e=>`${e}:not([tabindex^="-"])`)).join(",");return this.find(n,t).filter((t=>!e.isDisabled(t)&&e.isVisible(t)))},getSelectorFromElement(e){const r=t(e);return r&&n.findOne(r)?r:null},getElementFromSelector(e){const r=t(e);return r?n.findOne(r):null},getMultipleElementsFromSelector(e){const r=t(e);return r?n.find(r):[]}};return n}(n(4035))},6833:function(e,t,n){var r;!function(e,i){function o(e){var t=this;t.next=function(){var e,n,r=t.w,i=t.X,o=t.i;return t.w=r=r+1640531527|0,n=i[o+34&127],e=i[o=o+1&127],n^=n<<13,e^=e<<17,n^=n>>>15,e^=e>>>12,n=i[o]=n^e,t.i=o,n+(r^r>>>16)|0},function(e,t){var n,r,i,o,u,a=[],s=128;for(t===(0|t)?(r=t,t=null):(t+="\0",r=0,s=Math.max(s,t.length)),i=0,o=-32;o<s;++o)t&&(r^=t.charCodeAt((o+32)%t.length)),0===o&&(u=r),r^=r<<10,r^=r>>>15,r^=r<<4,r^=r>>>13,o>=0&&(u=u+1640531527|0,i=0==(n=a[127&o]^=r+u)?i+1:0);for(i>=128&&(a[127&(t&&t.length||0)]=-1),i=127,o=512;o>0;--o)r=a[i+34&127],n=a[i=i+1&127],r^=r<<13,n^=n<<17,r^=r>>>15,n^=n>>>12,a[i]=r^n;e.w=u,e.X=a,e.i=i}(t,e)}function u(e,t){return t.i=e.i,t.w=e.w,t.X=e.X.slice(),t}function a(e,t){null==e&&(e=+new Date);var n=new o(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&(r.X&&u(r,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.xor4096=a}(0,e=n.nmd(e),n.amdD)},7180:function(e,t,n){var r;!function(e,i){function o(e){var t=this,n=function(){var e=4022871197,t=function(t){t=String(t);for(var n=0;n<t.length;n++){var r=.02519603282416938*(e+=t.charCodeAt(n));r-=e=r>>>0,e=(r*=e)>>>0,e+=4294967296*(r-=e)}return 2.3283064365386963e-10*(e>>>0)};return t}();t.next=function(){var e=2091639*t.s0+2.3283064365386963e-10*t.c;return t.s0=t.s1,t.s1=t.s2,t.s2=e-(t.c=0|e)},t.c=1,t.s0=n(" "),t.s1=n(" "),t.s2=n(" "),t.s0-=n(e),t.s0<0&&(t.s0+=1),t.s1-=n(e),t.s1<0&&(t.s1+=1),t.s2-=n(e),t.s2<0&&(t.s2+=1),n=null}function u(e,t){return t.c=e.c,t.s0=e.s0,t.s1=e.s1,t.s2=e.s2,t}function a(e,t){var n=new o(e),r=t&&t.state,i=n.next;return i.int32=function(){return 4294967296*n.next()|0},i.double=function(){return i()+11102230246251565e-32*(2097152*i()|0)},i.quick=i,r&&("object"==typeof r&&u(r,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.alea=a}(0,e=n.nmd(e),n.amdD)},7269:function(e){e.exports=function(){"use strict";const e=new Map;return{set(t,n,r){e.has(t)||e.set(t,new Map);const i=e.get(t);i.has(n)||0===i.size?i.set(n,r):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(i.keys())[0]}.`)},get:(t,n)=>e.has(t)&&e.get(t).get(n)||null,remove(t,n){if(!e.has(t))return;const r=e.get(t);r.delete(n),0===r.size&&e.delete(t)}}}()},7391:(e,t,n)=>{var r=n(7180),i=n(3181),o=n(3031),u=n(9067),a=n(6833),s=n(3717),c=n(4801);c.alea=r,c.xor128=i,c.xorwow=o,c.xorshift7=u,c.xor4096=a,c.tychei=s,e.exports=c},7956:function(e,t,n){e.exports=function(e){"use strict";const t=/[^.]*(?=\..*)\.|.*/,n=/\..*/,r=/::\d+$/,i={};let o=1;const u={mouseenter:"mouseover",mouseleave:"mouseout"},a=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function s(e,t){return t&&`${t}::${o++}`||e.uidEvent||o++}function c(e){const t=s(e);return e.uidEvent=t,i[t]=i[t]||{},i[t]}function f(e,t){return function n(r){return y(r,{delegateTarget:e}),n.oneOff&&D.off(e,r.type,t),t.apply(e,[r])}}function l(e,t,n){return function r(i){const o=e.querySelectorAll(t);for(let{target:u}=i;u&&u!==this;u=u.parentNode)for(const a of o)if(a===u)return y(i,{delegateTarget:u}),r.oneOff&&D.off(e,i.type,t,n),n.apply(u,[i])}}function p(e,t,n=null){return Object.values(e).find((e=>e.callable===t&&e.delegationSelector===n))}function h(e,t,n){const r="string"==typeof t,i=r?n:t||n;let o=v(e);return a.has(o)||(o=e),[r,i,o]}function d(e,n,r,i,o){if("string"!=typeof n||!e)return;let[a,d,m]=h(n,r,i);if(n in u){const e=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)};d=e(d)}const g=c(e),v=g[m]||(g[m]={}),D=p(v,d,a?r:null);if(D)return void(D.oneOff=D.oneOff&&o);const y=s(d,n.replace(t,"")),w=a?l(e,r,d):f(e,d);w.delegationSelector=a?r:null,w.callable=d,w.oneOff=o,w.uidEvent=y,v[y]=w,e.addEventListener(m,w,a)}function m(e,t,n,r,i){const o=p(t[n],r,i);o&&(e.removeEventListener(n,o,Boolean(i)),delete t[n][o.uidEvent])}function g(e,t,n,r){const i=t[n]||{};for(const[o,u]of Object.entries(i))o.includes(r)&&m(e,t,n,u.callable,u.delegationSelector)}function v(e){return e=e.replace(n,""),u[e]||e}const D={on(e,t,n,r){d(e,t,n,r,!1)},one(e,t,n,r){d(e,t,n,r,!0)},off(e,t,n,i){if("string"!=typeof t||!e)return;const[o,u,a]=h(t,n,i),s=a!==t,f=c(e),l=f[a]||{},p=t.startsWith(".");if(void 0===u){if(p)for(const n of Object.keys(f))g(e,f,n,t.slice(1));for(const[n,i]of Object.entries(l)){const o=n.replace(r,"");s&&!t.includes(o)||m(e,f,a,i.callable,i.delegationSelector)}}else{if(!Object.keys(l).length)return;m(e,f,a,u,o?n:null)}},trigger(t,n,r){if("string"!=typeof n||!t)return null;const i=e.getjQuery();let o=null,u=!0,a=!0,s=!1;n!==v(n)&&i&&(o=i.Event(n,r),i(t).trigger(o),u=!o.isPropagationStopped(),a=!o.isImmediatePropagationStopped(),s=o.isDefaultPrevented());const c=y(new Event(n,{bubbles:u,cancelable:!0}),r);return s&&c.preventDefault(),a&&t.dispatchEvent(c),c.defaultPrevented&&o&&o.preventDefault(),c}};function y(e,t={}){for(const[n,r]of Object.entries(t))try{e[n]=r}catch(t){Object.defineProperty(e,n,{configurable:!0,get:()=>r})}return e}return D}(n(4035))},9011:function(e,t,n){e.exports=function(e,t,n,r){"use strict";const i="5.3.7";class o extends n{constructor(t,n){super(),(t=r.getElement(t))&&(this._element=t,this._config=this._getConfig(n),e.set(this._element,this.constructor.DATA_KEY,this))}dispose(){e.remove(this._element,this.constructor.DATA_KEY),t.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){r.executeAfterTransition(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(t){return e.get(r.getElement(t),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return i}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}return o}(n(7269),n(7956),n(2105),n(4035))},9067:function(e,t,n){var r;!function(e,i){function o(e){var t=this;t.next=function(){var e,n,r=t.x,i=t.i;return e=r[i],n=(e^=e>>>7)^e<<24,n^=(e=r[i+1&7])^e>>>10,n^=(e=r[i+3&7])^e>>>3,n^=(e=r[i+4&7])^e<<7,e=r[i+7&7],n^=(e^=e<<13)^e<<9,r[i]=n,t.i=i+1&7,n},function(e,t){var n,r=[];if(t===(0|t))r[0]=t;else for(t=""+t,n=0;n<t.length;++n)r[7&n]=r[7&n]<<15^t.charCodeAt(n)+r[n+1&7]<<13;for(;r.length<8;)r.push(0);for(n=0;n<8&&0===r[n];++n);for(8==n?r[7]=-1:r[n],e.x=r,e.i=0,n=256;n>0;--n)e.next()}(t,e)}function u(e,t){return t.x=e.x.slice(),t.i=e.i,t}function a(e,t){null==e&&(e=+new Date);var n=new o(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&(r.x&&u(r,n),i.state=function(){return u(n,{})}),i}i&&i.exports?i.exports=a:n.amdD&&n.amdO?void 0===(r=function(){return a}.call(t,n,t,i))||(i.exports=r):this.xorshift7=a}(0,e=n.nmd(e),n.amdD)}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.loaded=!0,o.exports}n.amdD=function(){throw new Error("define cannot be used indirect")},n.amdO={},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{"use strict";const e={block:function(e,t){t=Object.assign({message:null,overlayCSS:{background:"rgba(0, 0, 0, 0.5)",opacity:1}},t),e.block(t)},unblock:function(e){e.unblock(),e.find(".blockUI").length&&e.find(".blockUI").remove()}};var t=n(3059),r=n.n(t);const i=Swal;var o=n.n(i),u=n(1669);const a=function(){function t(e,t){this.settings=e,this.trials=t,this.$addonsPage=u(".atum-addons"),this.$noResults=this.$addonsPage.find(".no-results"),this.prepareMenu(),this.initHorizontalDragScroll(),this.bindEvents()}return t.prototype.prepareMenu=function(){var e=this,t=this.$addonsPage.find(".nav-container-box");t.find("li").each((function(t,n){var r=u(n),i=r.data("status");"all"!==i&&(e.$addonsPage.find(".atum-addon.".concat(i)).length||e.$addonsPage.find(".atum-addon .actions.".concat(i)).length||r.hide())})),t.removeAttr("style")},t.prototype.bindEvents=function(){var t=this;this.$addonsPage.on("click",".nav-container-box li",(function(e){var n=u(e.currentTarget),r=n.find("span"),i=n.data("status"),o=u("#addons-search");"all"===i?o.parent().show():o.val("").parent().removeClass("is-searching").hide(),r.hasClass("active")||(n.siblings().find("span").removeClass("active"),r.addClass("active"),t.$addonsPage.find(".atum-addon").each((function(e,t){var n=u(t);n.toggle("all"===i||n.hasClass(i)||n.find(".actions").hasClass(i))})))})).on("click",".addon-key button",(function(e){e.preventDefault();var n,r=u(e.currentTarget);if(r.hasClass("cancel-action"))r.closest(".actions").children().slideToggle("fast");else if(r.hasClass("remove-license")){n=r.closest(".addon-key").find(".license-key").text();var i=r.closest(".actions").hasClass("trial");o().fire({title:t.settings.get(i?"trialDeactivation":"limitedDeactivations"),html:'<div style="padding:15px">'.concat(t.settings.get(i?"trialWillDisable":"allowedDeactivations"),"</div>"),icon:"warning",confirmButtonText:t.settings.get("continue"),cancelButtonText:t.settings.get("cancel"),showCancelButton:!0,showLoaderOnConfirm:!0,preConfirm:function(){return t.requestLicenseChange(r,n,!0)}})}else{if(!(n=r.siblings("input").val()))return t.showErrorAlert(t.settings.get("invalidKey")),!1;r.hasClass("install-atum-addon")?t.maybeInstallAddon(r):t.requestLicenseChange(r,n)}})).on("click",".show-key",(function(e){e.preventDefault(),u(e.currentTarget).closest(".actions").children().slideToggle("fast")})).on("click",".alert .refresh-status",(function(n){n.preventDefault();var r=u(n.currentTarget).closest(".atum-addon");u.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:"atum_refresh_license",security:t.settings.get("nonce"),addon:r.data("addon")},beforeSend:function(){return e.block(r)},success:function(n){!0===n.success?location.reload():(e.unblock(r),t.showErrorAlert(n.data))}})})).on("keyup paste search","#addons-search",(function(e){var n=u(e.currentTarget),r=n.val().toLowerCase(),i=t.$addonsPage.find(".atum-addon");if(t.$noResults.find(".no-results__term").text(r),r){n.parent().addClass("is-searching");var o=0;i.each((function(e,t){var n=u(t);n.text().toLowerCase().includes(r)?n.show():(n.hide(),o++)})),t.$noResults.toggle(o>=i.length)}else t.$noResults.hide(),t.$addonsPage.find(".nav-container-box .all").trigger("click"),i.show(),n.parent().removeClass("is-searching")})).on("click",".atum-addons-sidebar__toggle",(function(e){e.preventDefault();var n=u(e.currentTarget);n.closest(".atum-addons__sidebar").toggleClass("collapsed").closest(".atum-addons__wrap").toggleClass("with-collapsed");var r=n.find("span");r.text().trim()===t.settings.get("show")?r.text(t.settings.get("hide")):r.text(t.settings.get("show"))})).on("click",".atum-addons__nav-buttons .btn",(function(e){var t=u(e.currentTarget),n=u("#atum-addons-list");t.add(t.siblings()).toggleClass("btn-primary btn-outline-primary"),t.hasClass("grid-view")?n.addClass("atum-addons__grid-view"):n.removeClass("atum-addons__grid-view")}))},t.prototype.maybeInstallAddon=function(e){var t=this,n=e.closest(".atum-addon"),r=n.data("addon"),i=n.find(".addon-key input").val();u.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:"atum_validate_license",security:this.settings.get("nonce"),addon:r,key:i},beforeSend:function(){return t.beforeAjax(e)},success:function(n){!0===n.success?t.installAddon(r,i).then((function(e){return t.showSuccessAlert(e,t.settings.get("installed"))})).catch((function(e){return t.showErrorAlert(e)})).finally((function(){return t.afterAjax(e)})):(t.licenseChangeResponse(n.success,n.data,r,i),t.afterAjax(e))}})},t.prototype.installAddon=function(e,t){var n=this;return new Promise((function(r,i){u.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:"atum_install_addon",security:n.settings.get("nonce"),addon:e,key:t},success:function(e){!0===e.success?r(e.data):i(e.data)}})}))},t.prototype.requestLicenseChange=function(e,t,n){var r=this;return void 0===n&&(n=!1),new Promise((function(i){var o=e.closest(".atum-addon").data("addon");u.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:e.data("action"),security:r.settings.get("nonce"),addon:o,key:t},beforeSend:function(){return r.beforeAjax(e)},success:function(u){r.afterAjax(e),!0!==u.success&&"uninstall-trial"===u.data?r.licenseChangeResponse("uninstall-trial",u.data,o,t,n):r.licenseChangeResponse(u.success,u.data,o,t,n),i()}})}))},t.prototype.licenseChangeResponse=function(e,t,n,r,i){var a=this;switch(void 0===i&&(i=!1),e){case!1:i?o().showValidationMessage("<span>".concat(t,"</span>")):this.showErrorAlert(t);break;case!0:this.showSuccessAlert(t);break;case"activate":o().fire({title:this.settings.get("activation"),html:'<div style="padding:15px">'.concat(t,"</div>"),icon:"info",showCancelButton:!0,showLoaderOnConfirm:!0,confirmButtonText:this.settings.get("activate"),allowOutsideClick:!1,preConfirm:function(){return new Promise((function(e){u.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:"atum_activate_license",security:a.settings.get("nonce"),addon:n,key:r},success:function(u){!0!==u.success&&("uninstall-trial"===u.data?a.licenseChangeResponse("uninstall-trial",t,n,r,i):o().showValidationMessage("<span>".concat(u.data,"</span>"))),e()}})}))}}).then((function(e){e.isConfirmed&&o().fire({title:a.settings.get("activated"),html:'<div style="padding:15px">'.concat(a.settings.get("addonActivated"),"</div>"),icon:"success",confirmButtonText:a.settings.get("install"),cancelButtonText:a.settings.get("cancel"),showCancelButton:!0,showCloseButton:!0,showLoaderOnConfirm:!0,preConfirm:function(){return a.installAddon(n,r).then((function(e){return a.showSuccessAlert(e,a.settings.get("installed"))})).catch((function(e){return o().showValidationMessage("<span>".concat(e,"</span>"))}))}})}));break;case"trial":o().fire({icon:"info",title:this.settings.get("trial"),html:'<div style="padding:15px">'.concat(t,"</div>"),showCancelButton:!0,showCloseButton:!0,confirmButtonText:this.settings.get("agree"),cancelButtonText:this.settings.get("cancel"),reverseButtons:!0,showLoaderOnConfirm:!0,preConfirm:function(){return a.installAddon(n,r).then((function(e){return a.showSuccessAlert(e,a.settings.get("installed"))})).catch((function(e){return o().showValidationMessage("<span>".concat(e,"</span>"))}))}});break;case"extend":o().fire({icon:"info",title:this.settings.get("trialExpired"),html:'<div style="padding:15px">'.concat(t,"</div>"),showCancelButton:!0,showCloseButton:!0,confirmButtonText:this.settings.get("extend"),cancelButtonText:this.settings.get("cancel"),reverseButtons:!0,showLoaderOnConfirm:!0,preConfirm:function(){return a.trials.extendTrial(n,r,!0,(function(e){a.licenseChangeResponse(e.success,e.data,n,r,!0)}))}});break;case"uninstall-trial":o().fire({title:this.settings.get("activation"),html:this.settings.get("uninstallTrial"),icon:"info",showCancelButton:!0,showLoaderOnConfirm:!0,confirmButtonText:this.settings.get("uninstallIt"),cancelButtonText:this.settings.get("cancel"),allowOutsideClick:!1,preConfirm:function(){return new Promise((function(e){u.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:"atum_uninstall_trial",security:a.settings.get("nonce"),addon:n,key:r},success:function(t){!0!==t.success?o().showValidationMessage("<span>".concat(t.data,"</span>")):a.licenseChangeResponse("activate",a.settings.get("uninstalled"),n,r,i),e()}})}))}})}},t.prototype.showSuccessAlert=function(e,t,n){t||(t=this.settings.get("success")),o().fire({title:t,html:'<div style="padding:15px">'.concat(e,"</div>"),icon:"success",confirmButtonText:this.settings.get("ok")}).then((function(){return n?n():location.reload()}))},t.prototype.showErrorAlert=function(e){o().fire({title:this.settings.get("error"),html:'<div style="padding:15px">'.concat(e,"</div>"),icon:"error",confirmButtonText:this.settings.get("ok")})},t.prototype.beforeAjax=function(e){e.parent().find(".button, button").prop("disabled",!0),e.css("visibility","hidden").after('<div class="atum-loading"></div>'),e.siblings(":input").prop("disabled",!0)},t.prototype.afterAjax=function(e){e.siblings(".atum-loading").remove(),e.parent().find(".button, button").prop("disabled",!1),e.css("visibility","visible"),e.siblings(":input").prop("disabled",!1)},t.prototype.addMouseWheelSupport=function(){u(".nav-with-scroll-effect").off("wheel DOMMouseScroll").on("wheel DOMMouseScroll",(function(e){var t=u(e.currentTarget);if(!t.find(".overflow-opacity-effect-right").is(":hidden")||!t.find(".overflow-opacity-effect-left").is(":hidden")){var n=t.get(0),r=e.originalEvent;return(r.wheelDelta||r.detail)>0?n.scrollLeft-=60:n.scrollLeft+=60,!1}}))},t.prototype.initHorizontalDragScroll=function(){var e=this,t=u(".nav-with-scroll-effect");u(window).off("resize.atum").on("resize.atum",(function(){t.each((function(t,n){e.addHorizontalDragScroll(u(n))}))})).trigger("resize.atum"),t.css("visibility","visible").off("scroll.atum").on("scroll.atum",(function(t){e.addHorizontalDragScroll(u(t.currentTarget))})),this.addMouseWheelSupport(),r().reset()},t.prototype.addHorizontalDragScroll=function(e){if(e.length){var t=e.find(".overflow-opacity-effect-right"),n=e.find(".overflow-opacity-effect-left"),r=e.get(0);t.toggle(!this.navIsRight(r)),n.toggle(!this.navIsLeft(r)),e.css("cursor",n.is(":visible")||t.is(":visible")?"grab":"auto")}},t.prototype.navIsLeft=function(e){return 0===e.scrollLeft},t.prototype.navIsRight=function(e){var t=!Number.isInteger(e.scrollWidth)||!Number.isInteger(e.scrollLeft),n=Math.ceil(e.scrollWidth-e.scrollLeft),r=Math.ceil(parseFloat(u(e).outerWidth().toString()));return t?!(n>r)||n-1<=r:n<=r},t}();function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(null,arguments)}var c={epsilon:1e-12,matrix:"Matrix",number:"number",precision:64,predictable:!1,randomSeed:null},f=["Matrix","Array"],l=["number","BigNumber","Fraction"];var p=function(e){if(e)throw new Error("The global config is readonly. \nPlease create a mathjs instance if you want to change the default configuration. \nExample:\n\n  import { create, all } from 'mathjs';\n  const mathjs = create(all);\n  mathjs.config({ number: 'BigNumber' });\n");return Object.freeze(c)};s(p,c,{MATRIX_OPTIONS:f,NUMBER_OPTIONS:l});var h,d,m=9e15,g=1e9,v="0123456789abcdef",D="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",y="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",w={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-m,maxE:m,crypto:!1},b=!0,E="[DecimalError] ",x=E+"Invalid argument: ",A=E+"Precision limit exceeded",C=E+"crypto unavailable",F="[object Decimal]",N=Math.floor,_=Math.pow,M=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,S=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,O=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,T=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,B=1e7,R=D.length-1,I=y.length-1,z={toStringTag:F};function P(e){var t,n,r,i=e.length-1,o="",u=e[0];if(i>0){for(o+=u,t=1;t<i;t++)(n=7-(r=e[t]+"").length)&&(o+=$(n)),o+=r;(n=7-(r=(u=e[t])+"").length)&&(o+=$(n))}else if(0===u)return"0";for(;u%10==0;)u/=10;return o+u}function k(e,t,n){if(e!==~~e||e<t||e>n)throw Error(x+e)}function j(e,t,n,r){var i,o,u,a;for(o=e[0];o>=10;o/=10)--t;return--t<0?(t+=7,i=0):(i=Math.ceil((t+1)/7),t%=7),o=_(10,7-t),a=e[i]%o|0,null==r?t<3?(0==t?a=a/100|0:1==t&&(a=a/10|0),u=n<4&&99999==a||n>3&&49999==a||5e4==a||0==a):u=(n<4&&a+1==o||n>3&&a+1==o/2)&&(e[i+1]/o/100|0)==_(10,t-2)-1||(a==o/2||0==a)&&!(e[i+1]/o/100|0):t<4?(0==t?a=a/1e3|0:1==t?a=a/100|0:2==t&&(a=a/10|0),u=(r||n<4)&&9999==a||!r&&n>3&&4999==a):u=((r||n<4)&&a+1==o||!r&&n>3&&a+1==o/2)&&(e[i+1]/o/1e3|0)==_(10,t-3)-1,u}function L(e,t,n){for(var r,i,o=[0],u=0,a=e.length;u<a;){for(i=o.length;i--;)o[i]*=t;for(o[0]+=v.indexOf(e.charAt(u++)),r=0;r<o.length;r++)o[r]>n-1&&(void 0===o[r+1]&&(o[r+1]=0),o[r+1]+=o[r]/n|0,o[r]%=n)}return o.reverse()}z.absoluteValue=z.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),q(e)},z.ceil=function(){return q(new this.constructor(this),this.e+1,2)},z.clampedTo=z.clamp=function(e,t){var n=this,r=n.constructor;if(e=new r(e),t=new r(t),!e.s||!t.s)return new r(NaN);if(e.gt(t))throw Error(x+t);return n.cmp(e)<0?e:n.cmp(t)>0?t:new r(n)},z.comparedTo=z.cmp=function(e){var t,n,r,i,o=this,u=o.d,a=(e=new o.constructor(e)).d,s=o.s,c=e.s;if(!u||!a)return s&&c?s!==c?s:u===a?0:!u^s<0?1:-1:NaN;if(!u[0]||!a[0])return u[0]?s:a[0]?-c:0;if(s!==c)return s;if(o.e!==e.e)return o.e>e.e^s<0?1:-1;for(t=0,n=(r=u.length)<(i=a.length)?r:i;t<n;++t)if(u[t]!==a[t])return u[t]>a[t]^s<0?1:-1;return r===i?0:r>i^s<0?1:-1},z.cosine=z.cos=function(){var e,t,n=this,r=n.constructor;return n.d?n.d[0]?(e=r.precision,t=r.rounding,r.precision=e+Math.max(n.e,n.sd())+7,r.rounding=1,n=function(e,t){var n,r,i;if(t.isZero())return t;r=t.d.length,r<32?i=(1/ie(4,n=Math.ceil(r/3))).toString():(n=16,i="2.3283064365386962890625e-10");e.precision+=n,t=re(e,1,t.times(i),new e(1));for(var o=n;o--;){var u=t.times(t);t=u.times(u).minus(u).times(8).plus(1)}return e.precision-=n,t}(r,oe(r,n)),r.precision=e,r.rounding=t,q(2==d||3==d?n.neg():n,e,t,!0)):new r(1):new r(NaN)},z.cubeRoot=z.cbrt=function(){var e,t,n,r,i,o,u,a,s,c,f=this,l=f.constructor;if(!f.isFinite()||f.isZero())return new l(f);for(b=!1,(o=f.s*_(f.s*f,1/3))&&Math.abs(o)!=1/0?r=new l(o.toString()):(n=P(f.d),(o=((e=f.e)-n.length+1)%3)&&(n+=1==o||-2==o?"0":"00"),o=_(n,1/3),e=N((e+1)/3)-(e%3==(e<0?-1:2)),(r=new l(n=o==1/0?"5e"+e:(n=o.toExponential()).slice(0,n.indexOf("e")+1)+e)).s=f.s),u=(e=l.precision)+3;;)if(c=(s=(a=r).times(a).times(a)).plus(f),r=U(c.plus(f).times(a),c.plus(s),u+2,1),P(a.d).slice(0,u)===(n=P(r.d)).slice(0,u)){if("9999"!=(n=n.slice(u-3,u+1))&&(i||"4999"!=n)){+n&&(+n.slice(1)||"5"!=n.charAt(0))||(q(r,e+1,1),t=!r.times(r).times(r).eq(f));break}if(!i&&(q(a,e+1,0),a.times(a).times(a).eq(f))){r=a;break}u+=4,i=1}return b=!0,q(r,e,l.rounding,t)},z.decimalPlaces=z.dp=function(){var e,t=this.d,n=NaN;if(t){if(n=7*((e=t.length-1)-N(this.e/7)),e=t[e])for(;e%10==0;e/=10)n--;n<0&&(n=0)}return n},z.dividedBy=z.div=function(e){return U(this,new this.constructor(e))},z.dividedToIntegerBy=z.divToInt=function(e){var t=this.constructor;return q(U(this,new t(e),0,1,1),t.precision,t.rounding)},z.equals=z.eq=function(e){return 0===this.cmp(e)},z.floor=function(){return q(new this.constructor(this),this.e+1,3)},z.greaterThan=z.gt=function(e){return this.cmp(e)>0},z.greaterThanOrEqualTo=z.gte=function(e){var t=this.cmp(e);return 1==t||0===t},z.hyperbolicCosine=z.cosh=function(){var e,t,n,r,i,o=this,u=o.constructor,a=new u(1);if(!o.isFinite())return new u(o.s?1/0:NaN);if(o.isZero())return a;n=u.precision,r=u.rounding,u.precision=n+Math.max(o.e,o.sd())+4,u.rounding=1,(i=o.d.length)<32?t=(1/ie(4,e=Math.ceil(i/3))).toString():(e=16,t="2.3283064365386962890625e-10"),o=re(u,1,o.times(t),new u(1),!0);for(var s,c=e,f=new u(8);c--;)s=o.times(o),o=a.minus(s.times(f.minus(s.times(f))));return q(o,u.precision=n,u.rounding=r,!0)},z.hyperbolicSine=z.sinh=function(){var e,t,n,r,i=this,o=i.constructor;if(!i.isFinite()||i.isZero())return new o(i);if(t=o.precision,n=o.rounding,o.precision=t+Math.max(i.e,i.sd())+4,o.rounding=1,(r=i.d.length)<3)i=re(o,2,i,i,!0);else{e=(e=1.4*Math.sqrt(r))>16?16:0|e,i=re(o,2,i=i.times(1/ie(5,e)),i,!0);for(var u,a=new o(5),s=new o(16),c=new o(20);e--;)u=i.times(i),i=i.times(a.plus(u.times(s.times(u).plus(c))))}return o.precision=t,o.rounding=n,q(i,t,n,!0)},z.hyperbolicTangent=z.tanh=function(){var e,t,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+7,r.rounding=1,U(n.sinh(),n.cosh(),r.precision=e,r.rounding=t)):new r(n.s)},z.inverseCosine=z.acos=function(){var e=this,t=e.constructor,n=e.abs().cmp(1),r=t.precision,i=t.rounding;return-1!==n?0===n?e.isNeg()?Y(t,r,i):new t(0):new t(NaN):e.isZero()?Y(t,r+4,i).times(.5):(t.precision=r+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=r,t.rounding=i,e.times(2))},z.inverseHyperbolicCosine=z.acosh=function(){var e,t,n=this,r=n.constructor;return n.lte(1)?new r(n.eq(1)?0:NaN):n.isFinite()?(e=r.precision,t=r.rounding,r.precision=e+Math.max(Math.abs(n.e),n.sd())+4,r.rounding=1,b=!1,n=n.times(n).minus(1).sqrt().plus(n),b=!0,r.precision=e,r.rounding=t,n.ln()):new r(n)},z.inverseHyperbolicSine=z.asinh=function(){var e,t,n=this,r=n.constructor;return!n.isFinite()||n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+2*Math.max(Math.abs(n.e),n.sd())+6,r.rounding=1,b=!1,n=n.times(n).plus(1).sqrt().plus(n),b=!0,r.precision=e,r.rounding=t,n.ln())},z.inverseHyperbolicTangent=z.atanh=function(){var e,t,n,r,i=this,o=i.constructor;return i.isFinite()?i.e>=0?new o(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=o.precision,t=o.rounding,r=i.sd(),Math.max(r,e)<2*-i.e-1?q(new o(i),e,t,!0):(o.precision=n=r-i.e,i=U(i.plus(1),new o(1).minus(i),n+e,1),o.precision=e+4,o.rounding=1,i=i.ln(),o.precision=e,o.rounding=t,i.times(.5))):new o(NaN)},z.inverseSine=z.asin=function(){var e,t,n,r,i=this,o=i.constructor;return i.isZero()?new o(i):(t=i.abs().cmp(1),n=o.precision,r=o.rounding,-1!==t?0===t?((e=Y(o,n+4,r).times(.5)).s=i.s,e):new o(NaN):(o.precision=n+6,o.rounding=1,i=i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(),o.precision=n,o.rounding=r,i.times(2)))},z.inverseTangent=z.atan=function(){var e,t,n,r,i,o,u,a,s,c=this,f=c.constructor,l=f.precision,p=f.rounding;if(c.isFinite()){if(c.isZero())return new f(c);if(c.abs().eq(1)&&l+4<=I)return(u=Y(f,l+4,p).times(.25)).s=c.s,u}else{if(!c.s)return new f(NaN);if(l+4<=I)return(u=Y(f,l+4,p).times(.5)).s=c.s,u}for(f.precision=a=l+10,f.rounding=1,e=n=Math.min(28,a/7+2|0);e;--e)c=c.div(c.times(c).plus(1).sqrt().plus(1));for(b=!1,t=Math.ceil(a/7),r=1,s=c.times(c),u=new f(c),i=c;-1!==e;)if(i=i.times(s),o=u.minus(i.div(r+=2)),i=i.times(s),void 0!==(u=o.plus(i.div(r+=2))).d[t])for(e=t;u.d[e]===o.d[e]&&e--;);return n&&(u=u.times(2<<n-1)),b=!0,q(u,f.precision=l,f.rounding=p,!0)},z.isFinite=function(){return!!this.d},z.isInteger=z.isInt=function(){return!!this.d&&N(this.e/7)>this.d.length-2},z.isNaN=function(){return!this.s},z.isNegative=z.isNeg=function(){return this.s<0},z.isPositive=z.isPos=function(){return this.s>0},z.isZero=function(){return!!this.d&&0===this.d[0]},z.lessThan=z.lt=function(e){return this.cmp(e)<0},z.lessThanOrEqualTo=z.lte=function(e){return this.cmp(e)<1},z.logarithm=z.log=function(e){var t,n,r,i,o,u,a,s,c=this,f=c.constructor,l=f.precision,p=f.rounding;if(null==e)e=new f(10),t=!0;else{if(n=(e=new f(e)).d,e.s<0||!n||!n[0]||e.eq(1))return new f(NaN);t=e.eq(10)}if(n=c.d,c.s<0||!n||!n[0]||c.eq(1))return new f(n&&!n[0]?-1/0:1!=c.s?NaN:n?0:1/0);if(t)if(n.length>1)o=!0;else{for(i=n[0];i%10==0;)i/=10;o=1!==i}if(b=!1,u=K(c,a=l+5),r=t?V(f,a+10):K(e,a),j((s=U(u,r,a,1)).d,i=l,p))do{if(u=K(c,a+=10),r=t?V(f,a+10):K(e,a),s=U(u,r,a,1),!o){+P(s.d).slice(i+1,i+15)+1==1e14&&(s=q(s,l+1,0));break}}while(j(s.d,i+=10,p));return b=!0,q(s,l,p)},z.minus=z.sub=function(e){var t,n,r,i,o,u,a,s,c,f,l,p,h=this,d=h.constructor;if(e=new d(e),!h.d||!e.d)return h.s&&e.s?h.d?e.s=-e.s:e=new d(e.d||h.s!==e.s?h:NaN):e=new d(NaN),e;if(h.s!=e.s)return e.s=-e.s,h.plus(e);if(c=h.d,p=e.d,a=d.precision,s=d.rounding,!c[0]||!p[0]){if(p[0])e.s=-e.s;else{if(!c[0])return new d(3===s?-0:0);e=new d(h)}return b?q(e,a,s):e}if(n=N(e.e/7),f=N(h.e/7),c=c.slice(),o=f-n){for((l=o<0)?(t=c,o=-o,u=p.length):(t=p,n=f,u=c.length),o>(r=Math.max(Math.ceil(a/7),u)+2)&&(o=r,t.length=1),t.reverse(),r=o;r--;)t.push(0);t.reverse()}else{for((l=(r=c.length)<(u=p.length))&&(u=r),r=0;r<u;r++)if(c[r]!=p[r]){l=c[r]<p[r];break}o=0}for(l&&(t=c,c=p,p=t,e.s=-e.s),u=c.length,r=p.length-u;r>0;--r)c[u++]=0;for(r=p.length;r>o;){if(c[--r]<p[r]){for(i=r;i&&0===c[--i];)c[i]=B-1;--c[i],c[r]+=B}c[r]-=p[r]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(e.d=c,e.e=G(c,n),b?q(e,a,s):e):new d(3===s?-0:0)},z.modulo=z.mod=function(e){var t,n=this,r=n.constructor;return e=new r(e),!n.d||!e.s||e.d&&!e.d[0]?new r(NaN):!e.d||n.d&&!n.d[0]?q(new r(n),r.precision,r.rounding):(b=!1,9==r.modulo?(t=U(n,e.abs(),0,3,1)).s*=e.s:t=U(n,e,0,r.modulo,1),t=t.times(e),b=!0,n.minus(t))},z.naturalExponential=z.exp=function(){return Q(this)},z.naturalLogarithm=z.ln=function(){return K(this)},z.negated=z.neg=function(){var e=new this.constructor(this);return e.s=-e.s,q(e)},z.plus=z.add=function(e){var t,n,r,i,o,u,a,s,c,f,l=this,p=l.constructor;if(e=new p(e),!l.d||!e.d)return l.s&&e.s?l.d||(e=new p(e.d||l.s===e.s?l:NaN)):e=new p(NaN),e;if(l.s!=e.s)return e.s=-e.s,l.minus(e);if(c=l.d,f=e.d,a=p.precision,s=p.rounding,!c[0]||!f[0])return f[0]||(e=new p(l)),b?q(e,a,s):e;if(o=N(l.e/7),r=N(e.e/7),c=c.slice(),i=o-r){for(i<0?(n=c,i=-i,u=f.length):(n=f,r=o,u=c.length),i>(u=(o=Math.ceil(a/7))>u?o+1:u+1)&&(i=u,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((u=c.length)-(i=f.length)<0&&(i=u,n=f,f=c,c=n),t=0;i;)t=(c[--i]=c[i]+f[i]+t)/B|0,c[i]%=B;for(t&&(c.unshift(t),++r),u=c.length;0==c[--u];)c.pop();return e.d=c,e.e=G(c,r),b?q(e,a,s):e},z.precision=z.sd=function(e){var t,n=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(x+e);return n.d?(t=W(n.d),e&&n.e+1>t&&(t=n.e+1)):t=NaN,t},z.round=function(){var e=this,t=e.constructor;return q(new t(e),e.e+1,t.rounding)},z.sine=z.sin=function(){var e,t,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+Math.max(n.e,n.sd())+7,r.rounding=1,n=function(e,t){var n,r=t.d.length;if(r<3)return t.isZero()?t:re(e,2,t,t);n=(n=1.4*Math.sqrt(r))>16?16:0|n,t=t.times(1/ie(5,n)),t=re(e,2,t,t);for(var i,o=new e(5),u=new e(16),a=new e(20);n--;)i=t.times(t),t=t.times(o.plus(i.times(u.times(i).minus(a))));return t}(r,oe(r,n)),r.precision=e,r.rounding=t,q(d>2?n.neg():n,e,t,!0)):new r(NaN)},z.squareRoot=z.sqrt=function(){var e,t,n,r,i,o,u=this,a=u.d,s=u.e,c=u.s,f=u.constructor;if(1!==c||!a||!a[0])return new f(!c||c<0&&(!a||a[0])?NaN:a?u:1/0);for(b=!1,0==(c=Math.sqrt(+u))||c==1/0?(((t=P(a)).length+s)%2==0&&(t+="0"),c=Math.sqrt(t),s=N((s+1)/2)-(s<0||s%2),r=new f(t=c==1/0?"5e"+s:(t=c.toExponential()).slice(0,t.indexOf("e")+1)+s)):r=new f(c.toString()),n=(s=f.precision)+3;;)if(r=(o=r).plus(U(u,o,n+2,1)).times(.5),P(o.d).slice(0,n)===(t=P(r.d)).slice(0,n)){if("9999"!=(t=t.slice(n-3,n+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(q(r,s+1,1),e=!r.times(r).eq(u));break}if(!i&&(q(o,s+1,0),o.times(o).eq(u))){r=o;break}n+=4,i=1}return b=!0,q(r,s,f.rounding,e)},z.tangent=z.tan=function(){var e,t,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+10,r.rounding=1,(n=n.sin()).s=1,n=U(n,new r(1).minus(n.times(n)).sqrt(),e+10,0),r.precision=e,r.rounding=t,q(2==d||4==d?n.neg():n,e,t,!0)):new r(NaN)},z.times=z.mul=function(e){var t,n,r,i,o,u,a,s,c,f=this,l=f.constructor,p=f.d,h=(e=new l(e)).d;if(e.s*=f.s,!(p&&p[0]&&h&&h[0]))return new l(!e.s||p&&!p[0]&&!h||h&&!h[0]&&!p?NaN:p&&h?0*e.s:e.s/0);for(n=N(f.e/7)+N(e.e/7),(s=p.length)<(c=h.length)&&(o=p,p=h,h=o,u=s,s=c,c=u),o=[],r=u=s+c;r--;)o.push(0);for(r=c;--r>=0;){for(t=0,i=s+r;i>r;)a=o[i]+h[r]*p[i-r-1]+t,o[i--]=a%B|0,t=a/B|0;o[i]=(o[i]+t)%B|0}for(;!o[--u];)o.pop();return t?++n:o.shift(),e.d=o,e.e=G(o,n),b?q(e,l.precision,l.rounding):e},z.toBinary=function(e,t){return ue(this,2,e,t)},z.toDecimalPlaces=z.toDP=function(e,t){var n=this,r=n.constructor;return n=new r(n),void 0===e?n:(k(e,0,g),void 0===t?t=r.rounding:k(t,0,8),q(n,e+n.e+1,t))},z.toExponential=function(e,t){var n,r=this,i=r.constructor;return void 0===e?n=H(r,!0):(k(e,0,g),void 0===t?t=i.rounding:k(t,0,8),n=H(r=q(new i(r),e+1,t),!0,e+1)),r.isNeg()&&!r.isZero()?"-"+n:n},z.toFixed=function(e,t){var n,r,i=this,o=i.constructor;return void 0===e?n=H(i):(k(e,0,g),void 0===t?t=o.rounding:k(t,0,8),n=H(r=q(new o(i),e+i.e+1,t),!1,e+r.e+1)),i.isNeg()&&!i.isZero()?"-"+n:n},z.toFraction=function(e){var t,n,r,i,o,u,a,s,c,f,l,p,h=this,d=h.d,m=h.constructor;if(!d)return new m(h);if(c=n=new m(1),r=s=new m(0),u=(o=(t=new m(r)).e=W(d)-h.e-1)%7,t.d[0]=_(10,u<0?7+u:u),null==e)e=o>0?t:c;else{if(!(a=new m(e)).isInt()||a.lt(c))throw Error(x+a);e=a.gt(t)?o>0?t:c:a}for(b=!1,a=new m(P(d)),f=m.precision,m.precision=o=7*d.length*2;l=U(a,t,0,1,1),1!=(i=n.plus(l.times(r))).cmp(e);)n=r,r=i,i=c,c=s.plus(l.times(i)),s=i,i=t,t=a.minus(l.times(i)),a=i;return i=U(e.minus(n),r,0,1,1),s=s.plus(i.times(c)),n=n.plus(i.times(r)),s.s=c.s=h.s,p=U(c,r,o,1).minus(h).abs().cmp(U(s,n,o,1).minus(h).abs())<1?[c,r]:[s,n],m.precision=f,b=!0,p},z.toHexadecimal=z.toHex=function(e,t){return ue(this,16,e,t)},z.toNearest=function(e,t){var n=this,r=n.constructor;if(n=new r(n),null==e){if(!n.d)return n;e=new r(1),t=r.rounding}else{if(e=new r(e),void 0===t?t=r.rounding:k(t,0,8),!n.d)return e.s?n:e;if(!e.d)return e.s&&(e.s=n.s),e}return e.d[0]?(b=!1,n=U(n,e,0,t,1).times(e),b=!0,q(n)):(e.s=n.s,n=e),n},z.toNumber=function(){return+this},z.toOctal=function(e,t){return ue(this,8,e,t)},z.toPower=z.pow=function(e){var t,n,r,i,o,u,a=this,s=a.constructor,c=+(e=new s(e));if(!(a.d&&e.d&&a.d[0]&&e.d[0]))return new s(_(+a,c));if((a=new s(a)).eq(1))return a;if(r=s.precision,o=s.rounding,e.eq(1))return q(a,r,o);if((t=N(e.e/7))>=e.d.length-1&&(n=c<0?-c:c)<=9007199254740991)return i=Z(s,a,n,r),e.s<0?new s(1).div(i):q(i,r,o);if((u=a.s)<0){if(t<e.d.length-1)return new s(NaN);if(1&e.d[t]||(u=1),0==a.e&&1==a.d[0]&&1==a.d.length)return a.s=u,a}return(t=0!=(n=_(+a,c))&&isFinite(n)?new s(n+"").e:N(c*(Math.log("0."+P(a.d))/Math.LN10+a.e+1)))>s.maxE+1||t<s.minE-1?new s(t>0?u/0:0):(b=!1,s.rounding=a.s=1,n=Math.min(12,(t+"").length),(i=Q(e.times(K(a,r+n)),r)).d&&j((i=q(i,r+5,1)).d,r,o)&&(t=r+10,+P((i=q(Q(e.times(K(a,t+n)),t),t+5,1)).d).slice(r+1,r+15)+1==1e14&&(i=q(i,r+1,0))),i.s=u,b=!0,s.rounding=o,q(i,r,o))},z.toPrecision=function(e,t){var n,r=this,i=r.constructor;return void 0===e?n=H(r,r.e<=i.toExpNeg||r.e>=i.toExpPos):(k(e,1,g),void 0===t?t=i.rounding:k(t,0,8),n=H(r=q(new i(r),e,t),e<=r.e||r.e<=i.toExpNeg,e)),r.isNeg()&&!r.isZero()?"-"+n:n},z.toSignificantDigits=z.toSD=function(e,t){var n=this.constructor;return void 0===e?(e=n.precision,t=n.rounding):(k(e,1,g),void 0===t?t=n.rounding:k(t,0,8)),q(new n(this),e,t)},z.toString=function(){var e=this,t=e.constructor,n=H(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+n:n},z.truncated=z.trunc=function(){return q(new this.constructor(this),this.e+1,1)},z.valueOf=z.toJSON=function(){var e=this,t=e.constructor,n=H(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+n:n};var U=function(){function e(e,t,n){var r,i=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+i,e[o]=r%n|0,i=r/n|0;return i&&e.unshift(i),e}function t(e,t,n,r){var i,o;if(n!=r)o=n>r?1:-1;else for(i=o=0;i<n;i++)if(e[i]!=t[i]){o=e[i]>t[i]?1:-1;break}return o}function n(e,t,n,r){for(var i=0;n--;)e[n]-=i,i=e[n]<t[n]?1:0,e[n]=i*r+e[n]-t[n];for(;!e[0]&&e.length>1;)e.shift()}return function(r,i,o,u,a,s){var c,f,l,p,d,m,g,v,D,y,w,b,E,x,A,C,F,_,M,S,O=r.constructor,T=r.s==i.s?1:-1,R=r.d,I=i.d;if(!(R&&R[0]&&I&&I[0]))return new O(r.s&&i.s&&(R?!I||R[0]!=I[0]:I)?R&&0==R[0]||!I?0*T:T/0:NaN);for(s?(d=1,f=r.e-i.e):(s=B,d=7,f=N(r.e/d)-N(i.e/d)),M=I.length,F=R.length,y=(D=new O(T)).d=[],l=0;I[l]==(R[l]||0);l++);if(I[l]>(R[l]||0)&&f--,null==o?(x=o=O.precision,u=O.rounding):x=a?o+(r.e-i.e)+1:o,x<0)y.push(1),m=!0;else{if(x=x/d+2|0,l=0,1==M){for(p=0,I=I[0],x++;(l<F||p)&&x--;l++)A=p*s+(R[l]||0),y[l]=A/I|0,p=A%I|0;m=p||l<F}else{for((p=s/(I[0]+1)|0)>1&&(I=e(I,p,s),R=e(R,p,s),M=I.length,F=R.length),C=M,b=(w=R.slice(0,M)).length;b<M;)w[b++]=0;(S=I.slice()).unshift(0),_=I[0],I[1]>=s/2&&++_;do{p=0,(c=t(I,w,M,b))<0?(E=w[0],M!=b&&(E=E*s+(w[1]||0)),(p=E/_|0)>1?(p>=s&&(p=s-1),1==(c=t(g=e(I,p,s),w,v=g.length,b=w.length))&&(p--,n(g,M<v?S:I,v,s))):(0==p&&(c=p=1),g=I.slice()),(v=g.length)<b&&g.unshift(0),n(w,g,b,s),-1==c&&(c=t(I,w,M,b=w.length))<1&&(p++,n(w,M<b?S:I,b,s)),b=w.length):0===c&&(p++,w=[0]),y[l++]=p,c&&w[0]?w[b++]=R[C]||0:(w=[R[C]],b=1)}while((C++<F||void 0!==w[0])&&x--);m=void 0!==w[0]}y[0]||y.shift()}if(1==d)D.e=f,h=m;else{for(l=1,p=y[0];p>=10;p/=10)l++;D.e=l+f*d-1,q(D,a?o+D.e+1:o,u,m)}return D}}();function q(e,t,n,r){var i,o,u,a,s,c,f,l,p,h=e.constructor;e:if(null!=t){if(!(l=e.d))return e;for(i=1,a=l[0];a>=10;a/=10)i++;if((o=t-i)<0)o+=7,u=t,s=(f=l[p=0])/_(10,i-u-1)%10|0;else if((p=Math.ceil((o+1)/7))>=(a=l.length)){if(!r)break e;for(;a++<=p;)l.push(0);f=s=0,i=1,u=(o%=7)-7+1}else{for(f=a=l[p],i=1;a>=10;a/=10)i++;s=(u=(o%=7)-7+i)<0?0:f/_(10,i-u-1)%10|0}if(r=r||t<0||void 0!==l[p+1]||(u<0?f:f%_(10,i-u-1)),c=n<4?(s||r)&&(0==n||n==(e.s<0?3:2)):s>5||5==s&&(4==n||r||6==n&&(o>0?u>0?f/_(10,i-u):0:l[p-1])%10&1||n==(e.s<0?8:7)),t<1||!l[0])return l.length=0,c?(t-=e.e+1,l[0]=_(10,(7-t%7)%7),e.e=-t||0):l[0]=e.e=0,e;if(0==o?(l.length=p,a=1,p--):(l.length=p+1,a=_(10,7-o),l[p]=u>0?(f/_(10,i-u)%_(10,u)|0)*a:0),c)for(;;){if(0==p){for(o=1,u=l[0];u>=10;u/=10)o++;for(u=l[0]+=a,a=1;u>=10;u/=10)a++;o!=a&&(e.e++,l[0]==B&&(l[0]=1));break}if(l[p]+=a,l[p]!=B)break;l[p--]=0,a=1}for(o=l.length;0===l[--o];)l.pop()}return b&&(e.e>h.maxE?(e.d=null,e.e=NaN):e.e<h.minE&&(e.e=0,e.d=[0])),e}function H(e,t,n){if(!e.isFinite())return ee(e);var r,i=e.e,o=P(e.d),u=o.length;return t?(n&&(r=n-u)>0?o=o.charAt(0)+"."+o.slice(1)+$(r):u>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):i<0?(o="0."+$(-i-1)+o,n&&(r=n-u)>0&&(o+=$(r))):i>=u?(o+=$(i+1-u),n&&(r=n-i-1)>0&&(o=o+"."+$(r))):((r=i+1)<u&&(o=o.slice(0,r)+"."+o.slice(r)),n&&(r=n-u)>0&&(i+1===u&&(o+="."),o+=$(r))),o}function G(e,t){var n=e[0];for(t*=7;n>=10;n/=10)t++;return t}function V(e,t,n){if(t>R)throw b=!0,n&&(e.precision=n),Error(A);return q(new e(D),t,1,!0)}function Y(e,t,n){if(t>I)throw Error(A);return q(new e(y),t,n,!0)}function W(e){var t=e.length-1,n=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)n--;for(t=e[0];t>=10;t/=10)n++}return n}function $(e){for(var t="";e--;)t+="0";return t}function Z(e,t,n,r){var i,o=new e(1),u=Math.ceil(r/7+4);for(b=!1;;){if(n%2&&ae((o=o.times(t)).d,u)&&(i=!0),0===(n=N(n/2))){n=o.d.length-1,i&&0===o.d[n]&&++o.d[n];break}ae((t=t.times(t)).d,u)}return b=!0,o}function J(e){return 1&e.d[e.d.length-1]}function X(e,t,n){for(var r,i,o=new e(t[0]),u=0;++u<t.length;){if(!(i=new e(t[u])).s){o=i;break}((r=o.cmp(i))===n||0===r&&o.s===n)&&(o=i)}return o}function Q(e,t){var n,r,i,o,u,a,s,c=0,f=0,l=0,p=e.constructor,h=p.rounding,d=p.precision;if(!e.d||!e.d[0]||e.e>17)return new p(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(b=!1,s=d):s=t,a=new p(.03125);e.e>-2;)e=e.times(a),l+=5;for(s+=r=Math.log(_(2,l))/Math.LN10*2+5|0,n=o=u=new p(1),p.precision=s;;){if(o=q(o.times(e),s,1),n=n.times(++f),P((a=u.plus(U(o,n,s,1))).d).slice(0,s)===P(u.d).slice(0,s)){for(i=l;i--;)u=q(u.times(u),s,1);if(null!=t)return p.precision=d,u;if(!(c<3&&j(u.d,s-r,h,c)))return q(u,p.precision=d,h,b=!0);p.precision=s+=10,n=o=a=new p(1),f=0,c++}u=a}}function K(e,t){var n,r,i,o,u,a,s,c,f,l,p,h=1,d=e,m=d.d,g=d.constructor,v=g.rounding,D=g.precision;if(d.s<0||!m||!m[0]||!d.e&&1==m[0]&&1==m.length)return new g(m&&!m[0]?-1/0:1!=d.s?NaN:m?0:d);if(null==t?(b=!1,f=D):f=t,g.precision=f+=10,r=(n=P(m)).charAt(0),!(Math.abs(o=d.e)<15e14))return c=V(g,f+2,D).times(o+""),d=K(new g(r+"."+n.slice(1)),f-10).plus(c),g.precision=D,null==t?q(d,D,v,b=!0):d;for(;r<7&&1!=r||1==r&&n.charAt(1)>3;)r=(n=P((d=d.times(e)).d)).charAt(0),h++;for(o=d.e,r>1?(d=new g("0."+n),o++):d=new g(r+"."+n.slice(1)),l=d,s=u=d=U(d.minus(1),d.plus(1),f,1),p=q(d.times(d),f,1),i=3;;){if(u=q(u.times(p),f,1),P((c=s.plus(U(u,new g(i),f,1))).d).slice(0,f)===P(s.d).slice(0,f)){if(s=s.times(2),0!==o&&(s=s.plus(V(g,f+2,D).times(o+""))),s=U(s,new g(h),f,1),null!=t)return g.precision=D,s;if(!j(s.d,f-10,v,a))return q(s,g.precision=D,v,b=!0);g.precision=f+=10,c=u=d=U(l.minus(1),l.plus(1),f,1),p=q(d.times(d),f,1),i=a=1}s=c,i+=2}}function ee(e){return String(e.s*e.s/0)}function te(e,t){var n,r,i;for((n=t.indexOf("."))>-1&&(t=t.replace(".","")),(r=t.search(/e/i))>0?(n<0&&(n=r),n+=+t.slice(r+1),t=t.substring(0,r)):n<0&&(n=t.length),r=0;48===t.charCodeAt(r);r++);for(i=t.length;48===t.charCodeAt(i-1);--i);if(t=t.slice(r,i)){if(i-=r,e.e=n=n-r-1,e.d=[],r=(n+1)%7,n<0&&(r+=7),r<i){for(r&&e.d.push(+t.slice(0,r)),i-=7;r<i;)e.d.push(+t.slice(r,r+=7));r=7-(t=t.slice(r)).length}else r-=i;for(;r--;)t+="0";e.d.push(+t),b&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function ne(e,t){var n,r,i,o,u,a,s,c,f;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),T.test(t))return te(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(S.test(t))n=16,t=t.toLowerCase();else if(M.test(t))n=2;else{if(!O.test(t))throw Error(x+t);n=8}for((o=t.search(/p/i))>0?(s=+t.slice(o+1),t=t.substring(2,o)):t=t.slice(2),u=(o=t.indexOf("."))>=0,r=e.constructor,u&&(o=(a=(t=t.replace(".","")).length)-o,i=Z(r,new r(n),o,2*o)),o=f=(c=L(t,n,B)).length-1;0===c[o];--o)c.pop();return o<0?new r(0*e.s):(e.e=G(c,f),e.d=c,b=!1,u&&(e=U(e,i,4*a)),s&&(e=e.times(Math.abs(s)<54?_(2,s):$e.pow(2,s))),b=!0,e)}function re(e,t,n,r,i){var o,u,a,s,c=e.precision,f=Math.ceil(c/7);for(b=!1,s=n.times(n),a=new e(r);;){if(u=U(a.times(s),new e(t++*t++),c,1),a=i?r.plus(u):r.minus(u),r=U(u.times(s),new e(t++*t++),c,1),void 0!==(u=a.plus(r)).d[f]){for(o=f;u.d[o]===a.d[o]&&o--;);if(-1==o)break}o=a,a=r,r=u,u=o}return b=!0,u.d.length=f+1,u}function ie(e,t){for(var n=e;--t;)n*=e;return n}function oe(e,t){var n,r=t.s<0,i=Y(e,e.precision,1),o=i.times(.5);if((t=t.abs()).lte(o))return d=r?4:1,t;if((n=t.divToInt(i)).isZero())d=r?3:2;else{if((t=t.minus(n.times(i))).lte(o))return d=J(n)?r?2:3:r?4:1,t;d=J(n)?r?1:4:r?3:2}return t.minus(i).abs()}function ue(e,t,n,r){var i,o,u,a,s,c,f,l,p,d=e.constructor,m=void 0!==n;if(m?(k(n,1,g),void 0===r?r=d.rounding:k(r,0,8)):(n=d.precision,r=d.rounding),e.isFinite()){for(m?(i=2,16==t?n=4*n-3:8==t&&(n=3*n-2)):i=t,(u=(f=H(e)).indexOf("."))>=0&&(f=f.replace(".",""),(p=new d(1)).e=f.length-u,p.d=L(H(p),10,i),p.e=p.d.length),o=s=(l=L(f,10,i)).length;0==l[--s];)l.pop();if(l[0]){if(u<0?o--:((e=new d(e)).d=l,e.e=o,l=(e=U(e,p,n,r,0,i)).d,o=e.e,c=h),u=l[n],a=i/2,c=c||void 0!==l[n+1],c=r<4?(void 0!==u||c)&&(0===r||r===(e.s<0?3:2)):u>a||u===a&&(4===r||c||6===r&&1&l[n-1]||r===(e.s<0?8:7)),l.length=n,c)for(;++l[--n]>i-1;)l[n]=0,n||(++o,l.unshift(1));for(s=l.length;!l[s-1];--s);for(u=0,f="";u<s;u++)f+=v.charAt(l[u]);if(m){if(s>1)if(16==t||8==t){for(u=16==t?4:3,--s;s%u;s++)f+="0";for(s=(l=L(f,i,t)).length;!l[s-1];--s);for(u=1,f="1.";u<s;u++)f+=v.charAt(l[u])}else f=f.charAt(0)+"."+f.slice(1);f=f+(o<0?"p":"p+")+o}else if(o<0){for(;++o;)f="0"+f;f="0."+f}else if(++o>s)for(o-=s;o--;)f+="0";else o<s&&(f=f.slice(0,o)+"."+f.slice(o))}else f=m?"0p+0":"0";f=(16==t?"0x":2==t?"0b":8==t?"0o":"")+f}else f=ee(e);return e.s<0?"-"+f:f}function ae(e,t){if(e.length>t)return e.length=t,!0}function se(e){return new this(e).abs()}function ce(e){return new this(e).acos()}function fe(e){return new this(e).acosh()}function le(e,t){return new this(e).plus(t)}function pe(e){return new this(e).asin()}function he(e){return new this(e).asinh()}function de(e){return new this(e).atan()}function me(e){return new this(e).atanh()}function ge(e,t){e=new this(e),t=new this(t);var n,r=this.precision,i=this.rounding,o=r+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(n=t.s<0?Y(this,r,i):new this(0)).s=e.s:!e.d||t.isZero()?(n=Y(this,o,1).times(.5)).s=e.s:t.s<0?(this.precision=o,this.rounding=1,n=this.atan(U(e,t,o,1)),t=Y(this,o,1),this.precision=r,this.rounding=i,n=e.s<0?n.minus(t):n.plus(t)):n=this.atan(U(e,t,o,1)):(n=Y(this,o,1).times(t.s>0?.25:.75)).s=e.s:n=new this(NaN),n}function ve(e){return new this(e).cbrt()}function De(e){return q(e=new this(e),e.e+1,2)}function ye(e,t,n){return new this(e).clamp(t,n)}function we(e){if(!e||"object"!=typeof e)throw Error(E+"Object expected");var t,n,r,i=!0===e.defaults,o=["precision",1,g,"rounding",0,8,"toExpNeg",-m,0,"toExpPos",0,m,"maxE",0,m,"minE",-m,0,"modulo",0,9];for(t=0;t<o.length;t+=3)if(n=o[t],i&&(this[n]=w[n]),void 0!==(r=e[n])){if(!(N(r)===r&&r>=o[t+1]&&r<=o[t+2]))throw Error(x+n+": "+r);this[n]=r}if(n="crypto",i&&(this[n]=w[n]),void 0!==(r=e[n])){if(!0!==r&&!1!==r&&0!==r&&1!==r)throw Error(x+n+": "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw Error(C);this[n]=!0}else this[n]=!1}return this}function be(e){return new this(e).cos()}function Ee(e){return new this(e).cosh()}function xe(e,t){return new this(e).div(t)}function Ae(e){return new this(e).exp()}function Ce(e){return q(e=new this(e),e.e+1,3)}function Fe(){var e,t,n=new this(0);for(b=!1,e=0;e<arguments.length;)if((t=new this(arguments[e++])).d)n.d&&(n=n.plus(t.times(t)));else{if(t.s)return b=!0,new this(1/0);n=t}return b=!0,n.sqrt()}function Ne(e){return e instanceof $e||e&&e.toStringTag===F||!1}function _e(e){return new this(e).ln()}function Me(e,t){return new this(e).log(t)}function Se(e){return new this(e).log(2)}function Oe(e){return new this(e).log(10)}function Te(){return X(this,arguments,-1)}function Be(){return X(this,arguments,1)}function Re(e,t){return new this(e).mod(t)}function Ie(e,t){return new this(e).mul(t)}function ze(e,t){return new this(e).pow(t)}function Pe(e){var t,n,r,i,o=0,u=new this(1),a=[];if(void 0===e?e=this.precision:k(e,1,g),r=Math.ceil(e/7),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(r));o<r;)(i=t[o])>=429e7?t[o]=crypto.getRandomValues(new Uint32Array(1))[0]:a[o++]=i%1e7;else{if(!crypto.randomBytes)throw Error(C);for(t=crypto.randomBytes(r*=4);o<r;)(i=t[o]+(t[o+1]<<8)+(t[o+2]<<16)+((127&t[o+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,o):(a.push(i%1e7),o+=4);o=r/4}else for(;o<r;)a[o++]=1e7*Math.random()|0;for(e%=7,(r=a[--o])&&e&&(i=_(10,7-e),a[o]=(r/i|0)*i);0===a[o];o--)a.pop();if(o<0)n=0,a=[0];else{for(n=-1;0===a[0];n-=7)a.shift();for(r=1,i=a[0];i>=10;i/=10)r++;r<7&&(n-=7-r)}return u.e=n,u.d=a,u}function ke(e){return q(e=new this(e),e.e+1,this.rounding)}function je(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function Le(e){return new this(e).sin()}function Ue(e){return new this(e).sinh()}function qe(e){return new this(e).sqrt()}function He(e,t){return new this(e).sub(t)}function Ge(){var e=0,t=arguments,n=new this(t[e]);for(b=!1;n.s&&++e<t.length;)n=n.plus(t[e]);return b=!0,q(n,this.precision,this.rounding)}function Ve(e){return new this(e).tan()}function Ye(e){return new this(e).tanh()}function We(e){return q(e=new this(e),e.e+1,1)}z[Symbol.for("nodejs.util.inspect.custom")]=z.toString,z[Symbol.toStringTag]="Decimal";var $e=z.constructor=function e(t){var n,r,i;function o(e){var t,n,r,i=this;if(!(i instanceof o))return new o(e);if(i.constructor=o,Ne(e))return i.s=e.s,void(b?!e.d||e.e>o.maxE?(i.e=NaN,i.d=null):e.e<o.minE?(i.e=0,i.d=[0]):(i.e=e.e,i.d=e.d.slice()):(i.e=e.e,i.d=e.d?e.d.slice():e.d));if("number"===(r=typeof e)){if(0===e)return i.s=1/e<0?-1:1,i.e=0,void(i.d=[0]);if(e<0?(e=-e,i.s=-1):i.s=1,e===~~e&&e<1e7){for(t=0,n=e;n>=10;n/=10)t++;return void(b?t>o.maxE?(i.e=NaN,i.d=null):t<o.minE?(i.e=0,i.d=[0]):(i.e=t,i.d=[e]):(i.e=t,i.d=[e]))}return 0*e!=0?(e||(i.s=NaN),i.e=NaN,void(i.d=null)):te(i,e.toString())}if("string"===r)return 45===(n=e.charCodeAt(0))?(e=e.slice(1),i.s=-1):(43===n&&(e=e.slice(1)),i.s=1),T.test(e)?te(i,e):ne(i,e);if("bigint"===r)return e<0?(e=-e,i.s=-1):i.s=1,te(i,e.toString());throw Error(x+e)}if(o.prototype=z,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.EUCLID=9,o.config=o.set=we,o.clone=e,o.isDecimal=Ne,o.abs=se,o.acos=ce,o.acosh=fe,o.add=le,o.asin=pe,o.asinh=he,o.atan=de,o.atanh=me,o.atan2=ge,o.cbrt=ve,o.ceil=De,o.clamp=ye,o.cos=be,o.cosh=Ee,o.div=xe,o.exp=Ae,o.floor=Ce,o.hypot=Fe,o.ln=_e,o.log=Me,o.log10=Oe,o.log2=Se,o.max=Te,o.min=Be,o.mod=Re,o.mul=Ie,o.pow=ze,o.random=Pe,o.round=ke,o.sign=je,o.sin=Le,o.sinh=Ue,o.sqrt=qe,o.sub=He,o.sum=Ge,o.tan=Ve,o.tanh=Ye,o.trunc=We,void 0===t&&(t={}),t&&!0!==t.defaults)for(i=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],n=0;n<i.length;)t.hasOwnProperty(r=i[n++])||(t[r]=this[r]);return o.config(t),o}(w);D=new $e(D),y=new $e(y);const Ze=$e;function Je(e){return"number"==typeof e}function Xe(e){return!(!e||"object"!=typeof e||"function"!=typeof e.constructor)&&(!0===e.isBigNumber&&"object"==typeof e.constructor.prototype&&!0===e.constructor.prototype.isBigNumber||"function"==typeof e.constructor.isDecimal&&!0===e.constructor.isDecimal(e))}function Qe(e){return e&&"object"==typeof e&&!0===Object.getPrototypeOf(e).isComplex||!1}function Ke(e){return e&&"object"==typeof e&&!0===Object.getPrototypeOf(e).isFraction||!1}function et(e){return e&&!0===e.constructor.prototype.isUnit||!1}function tt(e){return"string"==typeof e}var nt=Array.isArray;function rt(e){return e&&!0===e.constructor.prototype.isMatrix||!1}function it(e){return Array.isArray(e)||rt(e)}function ot(e){return e&&e.isDenseMatrix&&!0===e.constructor.prototype.isMatrix||!1}function ut(e){return e&&e.isSparseMatrix&&!0===e.constructor.prototype.isMatrix||!1}function at(e){return e&&!0===e.constructor.prototype.isRange||!1}function st(e){return e&&!0===e.constructor.prototype.isIndex||!1}function ct(e){return"boolean"==typeof e}function ft(e){return e&&!0===e.constructor.prototype.isResultSet||!1}function lt(e){return e&&!0===e.constructor.prototype.isHelp||!1}function pt(e){return"function"==typeof e}function ht(e){return e instanceof Date}function dt(e){return e instanceof RegExp}function mt(e){return!(!e||"object"!=typeof e||e.constructor!==Object||Qe(e)||Ke(e))}function gt(e){return null===e}function vt(e){return void 0===e}function Dt(e){return e&&!0===e.isAccessorNode&&!0===e.constructor.prototype.isNode||!1}function yt(e){return e&&!0===e.isArrayNode&&!0===e.constructor.prototype.isNode||!1}function wt(e){return e&&!0===e.isAssignmentNode&&!0===e.constructor.prototype.isNode||!1}function bt(e){return e&&!0===e.isBlockNode&&!0===e.constructor.prototype.isNode||!1}function Et(e){return e&&!0===e.isConditionalNode&&!0===e.constructor.prototype.isNode||!1}function xt(e){return e&&!0===e.isConstantNode&&!0===e.constructor.prototype.isNode||!1}function At(e){return e&&!0===e.isFunctionAssignmentNode&&!0===e.constructor.prototype.isNode||!1}function Ct(e){return e&&!0===e.isFunctionNode&&!0===e.constructor.prototype.isNode||!1}function Ft(e){return e&&!0===e.isIndexNode&&!0===e.constructor.prototype.isNode||!1}function Nt(e){return e&&!0===e.isNode&&!0===e.constructor.prototype.isNode||!1}function _t(e){return e&&!0===e.isObjectNode&&!0===e.constructor.prototype.isNode||!1}function Mt(e){return e&&!0===e.isOperatorNode&&!0===e.constructor.prototype.isNode||!1}function St(e){return e&&!0===e.isParenthesisNode&&!0===e.constructor.prototype.isNode||!1}function Ot(e){return e&&!0===e.isRangeNode&&!0===e.constructor.prototype.isNode||!1}function Tt(e){return e&&!0===e.isRelationalNode&&!0===e.constructor.prototype.isNode||!1}function Bt(e){return e&&!0===e.isSymbolNode&&!0===e.constructor.prototype.isNode||!1}function Rt(e){return e&&!0===e.constructor.prototype.isChain||!1}function It(e){var t=typeof e;return"object"===t?null===e?"null":Xe(e)?"BigNumber":e.constructor&&e.constructor.name?e.constructor.name:"Object":t}function zt(e){var t=typeof e;if("number"===t||"string"===t||"boolean"===t||null==e)return e;if("function"==typeof e.clone)return e.clone();if(Array.isArray(e))return e.map((function(e){return zt(e)}));if(e instanceof Date)return new Date(e.valueOf());if(Xe(e))return e;if(mt(e))return function(e,t){var n={};for(var r in e)jt(e,r)&&(n[r]=t(e[r]));return n}(e,zt);throw new TypeError("Cannot clone: unknown type of value (value: ".concat(e,")"))}function Pt(e,t){for(var n in t)jt(t,n)&&(e[n]=t[n]);return e}function kt(e,t){var n,r,i;if(Array.isArray(e)){if(!Array.isArray(t))return!1;if(e.length!==t.length)return!1;for(r=0,i=e.length;r<i;r++)if(!kt(e[r],t[r]))return!1;return!0}if("function"==typeof e)return e===t;if(e instanceof Object){if(Array.isArray(t)||!(t instanceof Object))return!1;for(n in e)if(!(n in t)||!kt(e[n],t[n]))return!1;for(n in t)if(!(n in e))return!1;return!0}return e===t}function jt(e,t){return e&&Object.hasOwnProperty.call(e,t)}function Lt(e,t,n,r){function i(r){var i=function(e,t){for(var n={},r=0;r<t.length;r++){var i=t[r],o=e[i];void 0!==o&&(n[i]=o)}return n}(r,t.map(Ut));return function(e,t,n){var r=t.filter((e=>!function(e){return e&&"?"===e[0]}(e))).every((e=>void 0!==n[e]));if(!r){var i=t.filter((e=>void 0===n[e]));throw new Error('Cannot create function "'.concat(e,'", ')+"some dependencies are missing: ".concat(i.map((e=>'"'.concat(e,'"'))).join(", "),"."))}}(e,t,r),n(i)}return i.isFactory=!0,i.fn=e,i.dependencies=t.slice().sort(),r&&(i.meta=r),i}function Ut(e){return e&&"?"===e[0]?e.slice(1):e}var qt=Lt("BigNumber",["?on","config"],(e=>{var{on:t,config:n}=e,r=Ze.clone({precision:n.precision,modulo:Ze.EUCLID});return r.prototype=Object.create(r.prototype),r.prototype.type="BigNumber",r.prototype.isBigNumber=!0,r.prototype.toJSON=function(){return{mathjs:"BigNumber",value:this.toString()}},r.fromJSON=function(e){return new r(e.value)},t&&t("config",(function(e,t){e.precision!==t.precision&&r.config({precision:e.precision})})),r}),{isClass:!0});const Ht=Math.cosh||function(e){return Math.abs(e)<1e-9?1-e:.5*(Math.exp(e)+Math.exp(-e))},Gt=Math.sinh||function(e){return Math.abs(e)<1e-9?e:.5*(Math.exp(e)-Math.exp(-e))},Vt=function(e,t){return(e=Math.abs(e))<(t=Math.abs(t))&&([e,t]=[t,e]),e<1e8?Math.sqrt(e*e+t*t):(t/=e,e*Math.sqrt(1+t*t))},Yt=function(){throw SyntaxError("Invalid Param")};function Wt(e,t){const n=Math.abs(e),r=Math.abs(t);return 0===e?Math.log(r):0===t?Math.log(n):n<3e3&&r<3e3?.5*Math.log(e*e+t*t):(e*=.5,t*=.5,.5*Math.log(e*e+t*t)+Math.LN2)}const $t={re:0,im:0},Zt=function(e,t){const n=$t;if(null==e)n.re=n.im=0;else if(void 0!==t)n.re=e,n.im=t;else switch(typeof e){case"object":if("im"in e&&"re"in e)n.re=e.re,n.im=e.im;else if("abs"in e&&"arg"in e){if(!isFinite(e.abs)&&isFinite(e.arg))return Jt.INFINITY;n.re=e.abs*Math.cos(e.arg),n.im=e.abs*Math.sin(e.arg)}else if("r"in e&&"phi"in e){if(!isFinite(e.r)&&isFinite(e.phi))return Jt.INFINITY;n.re=e.r*Math.cos(e.phi),n.im=e.r*Math.sin(e.phi)}else 2===e.length?(n.re=e[0],n.im=e[1]):Yt();break;case"string":n.im=n.re=0;const t=e.replace(/_/g,"").match(/\d+\.?\d*e[+-]?\d+|\d+\.?\d*|\.\d+|./g);let r=1,i=0;null===t&&Yt();for(let e=0;e<t.length;e++){const o=t[e];" "===o||"\t"===o||"\n"===o||("+"===o?r++:"-"===o?i++:"i"===o||"I"===o?(r+i===0&&Yt()," "===t[e+1]||isNaN(t[e+1])?n.im+=parseFloat((i%2?"-":"")+"1"):(n.im+=parseFloat((i%2?"-":"")+t[e+1]),e++),r=i=0):((r+i===0||isNaN(o))&&Yt(),"i"===t[e+1]||"I"===t[e+1]?(n.im+=parseFloat((i%2?"-":"")+o),e++):n.re+=parseFloat((i%2?"-":"")+o),r=i=0))}r+i>0&&Yt();break;case"number":n.im=0,n.re=e;break;default:Yt()}return isNaN(n.re)||isNaN(n.im),n};function Jt(e,t){if(!(this instanceof Jt))return new Jt(e,t);const n=Zt(e,t);this.re=n.re,this.im=n.im}function Xt(e){return"boolean"==typeof e||!!isFinite(e)&&e===Math.round(e)}Jt.prototype={re:0,im:0,sign:function(){const e=Vt(this.re,this.im);return new Jt(this.re/e,this.im/e)},add:function(e,t){const n=Zt(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im));return r||i?r&&i?Jt.NAN:Jt.INFINITY:new Jt(this.re+n.re,this.im+n.im)},sub:function(e,t){const n=Zt(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im));return r||i?r&&i?Jt.NAN:Jt.INFINITY:new Jt(this.re-n.re,this.im-n.im)},mul:function(e,t){const n=Zt(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im)),o=0===this.re&&0===this.im,u=0===n.re&&0===n.im;return r&&u||i&&o?Jt.NAN:r||i?Jt.INFINITY:0===n.im&&0===this.im?new Jt(this.re*n.re,0):new Jt(this.re*n.re-this.im*n.im,this.re*n.im+this.im*n.re)},div:function(e,t){const n=Zt(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im)),o=0===this.re&&0===this.im,u=0===n.re&&0===n.im;if(o&&u||r&&i)return Jt.NAN;if(u||r)return Jt.INFINITY;if(o||i)return Jt.ZERO;if(0===n.im)return new Jt(this.re/n.re,this.im/n.re);if(Math.abs(n.re)<Math.abs(n.im)){const e=n.re/n.im,t=n.re*e+n.im;return new Jt((this.re*e+this.im)/t,(this.im*e-this.re)/t)}{const e=n.im/n.re,t=n.im*e+n.re;return new Jt((this.re+this.im*e)/t,(this.im-this.re*e)/t)}},pow:function(e,t){const n=Zt(e,t),r=0===this.re&&0===this.im;if(0===n.re&&0===n.im)return Jt.ONE;if(0===n.im){if(0===this.im&&this.re>0)return new Jt(Math.pow(this.re,n.re),0);if(0===this.re)switch((n.re%4+4)%4){case 0:return new Jt(Math.pow(this.im,n.re),0);case 1:return new Jt(0,Math.pow(this.im,n.re));case 2:return new Jt(-Math.pow(this.im,n.re),0);case 3:return new Jt(0,-Math.pow(this.im,n.re))}}if(r&&n.re>0)return Jt.ZERO;const i=Math.atan2(this.im,this.re),o=Wt(this.re,this.im);let u=Math.exp(n.re*o-n.im*i),a=n.im*o+n.re*i;return new Jt(u*Math.cos(a),u*Math.sin(a))},sqrt:function(){const e=this.re,t=this.im;if(0===t)return e>=0?new Jt(Math.sqrt(e),0):new Jt(0,Math.sqrt(-e));const n=Vt(e,t);let r=Math.sqrt(.5*(n+Math.abs(e))),i=Math.abs(t)/(2*r);return e>=0?new Jt(r,t<0?-i:i):new Jt(i,t<0?-r:r)},exp:function(){const e=Math.exp(this.re);return 0===this.im?new Jt(e,0):new Jt(e*Math.cos(this.im),e*Math.sin(this.im))},expm1:function(){const e=this.re,t=this.im;return new Jt(Math.expm1(e)*Math.cos(t)+function(e){const t=Math.PI/4;if(-t>e||e>t)return Math.cos(e)-1;const n=e*e;return n*(n*(n*(n*(n*(n*(n*(n/20922789888e3-1/87178291200)+1/479001600)-1/3628800)+1/40320)-1/720)+1/24)-.5)}(t),Math.exp(e)*Math.sin(t))},log:function(){const e=this.re,t=this.im;return 0===t&&e>0?new Jt(Math.log(e),0):new Jt(Wt(e,t),Math.atan2(t,e))},abs:function(){return Vt(this.re,this.im)},arg:function(){return Math.atan2(this.im,this.re)},sin:function(){const e=this.re,t=this.im;return new Jt(Math.sin(e)*Ht(t),Math.cos(e)*Gt(t))},cos:function(){const e=this.re,t=this.im;return new Jt(Math.cos(e)*Ht(t),-Math.sin(e)*Gt(t))},tan:function(){const e=2*this.re,t=2*this.im,n=Math.cos(e)+Ht(t);return new Jt(Math.sin(e)/n,Gt(t)/n)},cot:function(){const e=2*this.re,t=2*this.im,n=Math.cos(e)-Ht(t);return new Jt(-Math.sin(e)/n,Gt(t)/n)},sec:function(){const e=this.re,t=this.im,n=.5*Ht(2*t)+.5*Math.cos(2*e);return new Jt(Math.cos(e)*Ht(t)/n,Math.sin(e)*Gt(t)/n)},csc:function(){const e=this.re,t=this.im,n=.5*Ht(2*t)-.5*Math.cos(2*e);return new Jt(Math.sin(e)*Ht(t)/n,-Math.cos(e)*Gt(t)/n)},asin:function(){const e=this.re,t=this.im,n=new Jt(t*t-e*e+1,-2*e*t).sqrt(),r=new Jt(n.re-t,n.im+e).log();return new Jt(r.im,-r.re)},acos:function(){const e=this.re,t=this.im,n=new Jt(t*t-e*e+1,-2*e*t).sqrt(),r=new Jt(n.re-t,n.im+e).log();return new Jt(Math.PI/2-r.im,r.re)},atan:function(){const e=this.re,t=this.im;if(0===e){if(1===t)return new Jt(0,1/0);if(-1===t)return new Jt(0,-1/0)}const n=e*e+(1-t)*(1-t),r=new Jt((1-t*t-e*e)/n,-2*e/n).log();return new Jt(-.5*r.im,.5*r.re)},acot:function(){const e=this.re,t=this.im;if(0===t)return new Jt(Math.atan2(1,e),0);const n=e*e+t*t;return 0!==n?new Jt(e/n,-t/n).atan():new Jt(0!==e?e/0:0,0!==t?-t/0:0).atan()},asec:function(){const e=this.re,t=this.im;if(0===e&&0===t)return new Jt(0,1/0);const n=e*e+t*t;return 0!==n?new Jt(e/n,-t/n).acos():new Jt(0!==e?e/0:0,0!==t?-t/0:0).acos()},acsc:function(){const e=this.re,t=this.im;if(0===e&&0===t)return new Jt(Math.PI/2,1/0);const n=e*e+t*t;return 0!==n?new Jt(e/n,-t/n).asin():new Jt(0!==e?e/0:0,0!==t?-t/0:0).asin()},sinh:function(){const e=this.re,t=this.im;return new Jt(Gt(e)*Math.cos(t),Ht(e)*Math.sin(t))},cosh:function(){const e=this.re,t=this.im;return new Jt(Ht(e)*Math.cos(t),Gt(e)*Math.sin(t))},tanh:function(){const e=2*this.re,t=2*this.im,n=Ht(e)+Math.cos(t);return new Jt(Gt(e)/n,Math.sin(t)/n)},coth:function(){const e=2*this.re,t=2*this.im,n=Ht(e)-Math.cos(t);return new Jt(Gt(e)/n,-Math.sin(t)/n)},csch:function(){const e=this.re,t=this.im,n=Math.cos(2*t)-Ht(2*e);return new Jt(-2*Gt(e)*Math.cos(t)/n,2*Ht(e)*Math.sin(t)/n)},sech:function(){const e=this.re,t=this.im,n=Math.cos(2*t)+Ht(2*e);return new Jt(2*Ht(e)*Math.cos(t)/n,-2*Gt(e)*Math.sin(t)/n)},asinh:function(){let e=this.im;this.im=-this.re,this.re=e;const t=this.asin();return this.re=-this.im,this.im=e,e=t.re,t.re=-t.im,t.im=e,t},acosh:function(){const e=this.acos();if(e.im<=0){const t=e.re;e.re=-e.im,e.im=t}else{const t=e.im;e.im=-e.re,e.re=t}return e},atanh:function(){const e=this.re,t=this.im,n=e>1&&0===t,r=1-e,i=1+e,o=r*r+t*t,u=0!==o?new Jt((i*r-t*t)/o,(t*r+i*t)/o):new Jt(-1!==e?e/0:0,0!==t?t/0:0),a=u.re;return u.re=Wt(u.re,u.im)/2,u.im=Math.atan2(u.im,a)/2,n&&(u.im=-u.im),u},acoth:function(){const e=this.re,t=this.im;if(0===e&&0===t)return new Jt(0,Math.PI/2);const n=e*e+t*t;return 0!==n?new Jt(e/n,-t/n).atanh():new Jt(0!==e?e/0:0,0!==t?-t/0:0).atanh()},acsch:function(){const e=this.re,t=this.im;if(0===t)return new Jt(0!==e?Math.log(e+Math.sqrt(e*e+1)):1/0,0);const n=e*e+t*t;return 0!==n?new Jt(e/n,-t/n).asinh():new Jt(0!==e?e/0:0,0!==t?-t/0:0).asinh()},asech:function(){const e=this.re,t=this.im;if(this.isZero())return Jt.INFINITY;const n=e*e+t*t;return 0!==n?new Jt(e/n,-t/n).acosh():new Jt(0!==e?e/0:0,0!==t?-t/0:0).acosh()},inverse:function(){if(this.isZero())return Jt.INFINITY;if(this.isInfinite())return Jt.ZERO;const e=this.re,t=this.im,n=e*e+t*t;return new Jt(e/n,-t/n)},conjugate:function(){return new Jt(this.re,-this.im)},neg:function(){return new Jt(-this.re,-this.im)},ceil:function(e){return e=Math.pow(10,e||0),new Jt(Math.ceil(this.re*e)/e,Math.ceil(this.im*e)/e)},floor:function(e){return e=Math.pow(10,e||0),new Jt(Math.floor(this.re*e)/e,Math.floor(this.im*e)/e)},round:function(e){return e=Math.pow(10,e||0),new Jt(Math.round(this.re*e)/e,Math.round(this.im*e)/e)},equals:function(e,t){const n=Zt(e,t);return Math.abs(n.re-this.re)<=Jt.EPSILON&&Math.abs(n.im-this.im)<=Jt.EPSILON},clone:function(){return new Jt(this.re,this.im)},toString:function(){let e=this.re,t=this.im,n="";return this.isNaN()?"NaN":this.isInfinite()?"Infinity":(Math.abs(e)<Jt.EPSILON&&(e=0),Math.abs(t)<Jt.EPSILON&&(t=0),0===t?n+e:(0!==e?(n+=e,n+=" ",t<0?(t=-t,n+="-"):n+="+",n+=" "):t<0&&(t=-t,n+="-"),1!==t&&(n+=t),n+"i"))},toVector:function(){return[this.re,this.im]},valueOf:function(){return 0===this.im?this.re:null},isNaN:function(){return isNaN(this.re)||isNaN(this.im)},isZero:function(){return 0===this.im&&0===this.re},isFinite:function(){return isFinite(this.re)&&isFinite(this.im)},isInfinite:function(){return!this.isFinite()}},Jt.ZERO=new Jt(0,0),Jt.ONE=new Jt(1,0),Jt.I=new Jt(0,1),Jt.PI=new Jt(Math.PI,0),Jt.E=new Jt(Math.E,0),Jt.INFINITY=new Jt(1/0,1/0),Jt.NAN=new Jt(NaN,NaN),Jt.EPSILON=1e-15;var Qt=Math.sign||function(e){return e>0?1:e<0?-1:0},Kt=Math.log2||function(e){return Math.log(e)/Math.LN2},en=Math.log10||function(e){return Math.log(e)/Math.LN10},tn=(Math.log1p,Math.cbrt||function(e){if(0===e)return e;var t,n=e<0;return n&&(e=-e),t=isFinite(e)?(e/((t=Math.exp(Math.log(e)/3))*t)+2*t)/3:e,n?-t:t}),nn=Math.expm1||function(e){return e>=2e-4||e<=-2e-4?Math.exp(e)-1:e+e*e/2+e*e*e/6};function rn(e,t,n){var r={2:"0b",8:"0o",16:"0x"}[t],i="";if(n){if(n<1)throw new Error("size must be in greater than 0");if(!Xt(n))throw new Error("size must be an integer");if(e>2**(n-1)-1||e<-(2**(n-1)))throw new Error("Value must be in range [-2^".concat(n-1,", 2^").concat(n-1,"-1]"));if(!Xt(e))throw new Error("Value must be an integer");e<0&&(e+=2**n),i="i".concat(n)}var o="";return e<0&&(e=-e,o="-"),"".concat(o).concat(r).concat(e.toString(t)).concat(i)}function on(e,t){if("function"==typeof t)return t(e);if(e===1/0)return"Infinity";if(e===-1/0)return"-Infinity";if(isNaN(e))return"NaN";var{notation:n,precision:r,wordSize:i}=un(t);switch(n){case"fixed":return sn(e,r);case"exponential":return cn(e,r);case"engineering":return function(e,t){if(isNaN(e)||!isFinite(e))return String(e);var n=an(e),r=fn(n,t),i=r.exponent,o=r.coefficients,u=i%3==0?i:i<0?i-3-i%3:i-i%3;if(Je(t))for(;t>o.length||i-u+1>o.length;)o.push(0);else for(var a=Math.abs(i-u)-(o.length-1),s=0;s<a;s++)o.push(0);var c=Math.abs(i-u),f=1;for(;c>0;)f++,c--;var l=o.slice(f).join(""),p=Je(t)&&l.length||l.match(/[1-9]/)?"."+l:"",h=o.slice(0,f).join("")+p+"e"+(i>=0?"+":"")+u.toString();return r.sign+h}(e,r);case"bin":return rn(e,2,i);case"oct":return rn(e,8,i);case"hex":return rn(e,16,i);case"auto":return function(e,t,n){if(isNaN(e)||!isFinite(e))return String(e);var r=yn(null==n?void 0:n.lowerExp,-3),i=yn(null==n?void 0:n.upperExp,5),o=an(e),u=t?fn(o,t):o;if(u.exponent<r||u.exponent>=i)return cn(e,t);var a=u.coefficients,s=u.exponent;a.length<t&&(a=a.concat(ln(t-a.length))),a=a.concat(ln(s-a.length+1+(a.length<t?t-a.length:0)));var c=s>0?s:0;return c<(a=ln(-s).concat(a)).length-1&&a.splice(c+1,0,"."),u.sign+a.join("")}(e,r,t).replace(/((\.\d*?)(0+))($|e)/,(function(){var e=arguments[2],t=arguments[4];return"."!==e?e+t:t}));default:throw new Error('Unknown notation "'+n+'". Choose "auto", "exponential", "fixed", "bin", "oct", or "hex.')}}function un(e){var t,n,r="auto";if(void 0!==e)if(Je(e))t=e;else if(Xe(e))t=e.toNumber();else{if(!mt(e))throw new Error("Unsupported type of options, number, BigNumber, or object expected");void 0!==e.precision&&(t=Dn(e.precision,(()=>{throw new Error('Option "precision" must be a number or BigNumber')}))),void 0!==e.wordSize&&(n=Dn(e.wordSize,(()=>{throw new Error('Option "wordSize" must be a number or BigNumber')}))),e.notation&&(r=e.notation)}return{notation:r,precision:t,wordSize:n}}function an(e){var t=String(e).toLowerCase().match(/^(-?)(\d+\.?\d*)(e([+-]?\d+))?$/);if(!t)throw new SyntaxError("Invalid number "+e);var n=t[1],r=t[2],i=parseFloat(t[4]||"0"),o=r.indexOf(".");i+=-1!==o?o-1:r.length-1;var u=r.replace(".","").replace(/^0*/,(function(e){return i-=e.length,""})).replace(/0*$/,"").split("").map((function(e){return parseInt(e)}));return 0===u.length&&(u.push(0),i++),{sign:n,coefficients:u,exponent:i}}function sn(e,t){if(isNaN(e)||!isFinite(e))return String(e);var n=an(e),r="number"==typeof t?fn(n,n.exponent+1+t):n,i=r.coefficients,o=r.exponent+1,u=o+(t||0);return i.length<u&&(i=i.concat(ln(u-i.length))),o<0&&(i=ln(1-o).concat(i),o=1),o<i.length&&i.splice(o,0,0===o?"0.":"."),r.sign+i.join("")}function cn(e,t){if(isNaN(e)||!isFinite(e))return String(e);var n=an(e),r=t?fn(n,t):n,i=r.coefficients,o=r.exponent;i.length<t&&(i=i.concat(ln(t-i.length)));var u=i.shift();return r.sign+u+(i.length>0?"."+i.join(""):"")+"e"+(o>=0?"+":"")+o}function fn(e,t){for(var n={sign:e.sign,coefficients:e.coefficients,exponent:e.exponent},r=n.coefficients;t<=0;)r.unshift(0),n.exponent++,t++;if(r.length>t&&r.splice(t,r.length-t)[0]>=5){var i=t-1;for(r[i]++;10===r[i];)r.pop(),0===i&&(r.unshift(0),n.exponent++,i++),r[--i]++}return n}function ln(e){for(var t=[],n=0;n<e;n++)t.push(0);return t}var pn=Number.EPSILON||2220446049250313e-31;function hn(e,t,n){if(null==n)return e===t;if(e===t)return!0;if(isNaN(e)||isNaN(t))return!1;if(isFinite(e)&&isFinite(t)){var r=Math.abs(e-t);return r<=pn||r<=Math.max(Math.abs(e),Math.abs(t))*n}return!1}var dn=Math.acosh||function(e){return Math.log(Math.sqrt(e*e-1)+e)},mn=Math.asinh||function(e){return Math.log(Math.sqrt(e*e+1)+e)},gn=Math.atanh||function(e){return Math.log((1+e)/(1-e))/2},vn=(Math.cosh,Math.sinh||function(e){return(Math.exp(e)-Math.exp(-e))/2});Math.tanh;function Dn(e,t){return Je(e)?e:Xe(e)?e.toNumber():void t()}function yn(e,t){return Je(e)?e:Xe(e)?e.toNumber():t}var wn=Lt("Complex",[],(()=>(Object.defineProperty(Jt,"name",{value:"Complex"}),Jt.prototype.constructor=Jt,Jt.prototype.type="Complex",Jt.prototype.isComplex=!0,Jt.prototype.toJSON=function(){return{mathjs:"Complex",re:this.re,im:this.im}},Jt.prototype.toPolar=function(){return{r:this.abs(),phi:this.arg()}},Jt.prototype.format=function(e){var t=this.im,n=this.re,r=on(this.re,e),i=on(this.im,e),o=Je(e)?e:e?e.precision:null;if(null!==o){var u=Math.pow(10,-o);Math.abs(n/t)<u&&(n=0),Math.abs(t/n)<u&&(t=0)}return 0===t?r:0===n?1===t?"i":-1===t?"-i":i+"i":t<0?-1===t?r+" - i":r+" - "+i.substring(1)+"i":1===t?r+" + i":r+" + "+i+"i"},Jt.fromPolar=function(e){switch(arguments.length){case 1:var t=arguments[0];if("object"==typeof t)return Jt(t);throw new TypeError("Input has to be an object with r and phi keys.");case 2:var n=arguments[0],r=arguments[1];if(Je(n)){if(et(r)&&r.hasBase("ANGLE")&&(r=r.toNumber("rad")),Je(r))return new Jt({r:n,phi:r});throw new TypeError("Phi is not a number nor an angle unit.")}throw new TypeError("Radius r is not a number.");default:throw new SyntaxError("Wrong number of arguments in function fromPolar")}},Jt.prototype.valueOf=Jt.prototype.toString,Jt.fromJSON=function(e){return new Jt(e)},Jt.compare=function(e,t){return e.re>t.re?1:e.re<t.re?-1:e.im>t.im?1:e.im<t.im?-1:0},Jt)),{isClass:!0});function bn(e){var t=0,n=1,r=Object.create(null),i=Object.create(null),o=0,u=function(e){var u=i[e];if(u&&(delete r[u],delete i[e],--t,n===u)){if(!t)return o=0,void(n=1);for(;!Object.prototype.hasOwnProperty.call(r,++n););}};return e=Math.abs(e),{hit:function(a){var s=i[a],c=++o;if(r[c]=a,i[a]=c,!s){if(++t<=e)return;return a=r[n],u(a),a}if(delete r[s],n===s)for(;!Object.prototype.hasOwnProperty.call(r,++n););},delete:u,clear:function(){t=o=0,n=1,r=Object.create(null),i=Object.create(null)}}}function En(e){var{hasher:t,limit:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n=null==n?Number.POSITIVE_INFINITY:n,t=null==t?JSON.stringify:t,function r(){"object"!=typeof r.cache&&(r.cache={values:new Map,lru:bn(n||Number.POSITIVE_INFINITY)});for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];var u=t(i);if(r.cache.values.has(u))return r.cache.lru.hit(u),r.cache.values.get(u);var a=e.apply(e,i);return r.cache.values.set(u,a),r.cache.values.delete(r.cache.lru.hit(u)),a}}function xn(e){return Object.keys(e.signatures||{}).reduce((function(e,t){var n=(t.match(/,/g)||[]).length+1;return Math.max(e,n)}),-1)}En((function(e){return new e(1).exp()}),{hasher:Cn}),En((function(e){return new e(1).plus(new e(5).sqrt()).div(2)}),{hasher:Cn});var An=En((function(e){return e.acos(-1)}),{hasher:Cn});En((function(e){return An(e).times(2)}),{hasher:Cn});function Cn(e){return e[0].precision}Math.PI,Math.PI,Math.E;Fn("fineStructure",.0072973525693),Fn("weakMixingAngle",.2229),Fn("efimovFactor",22.7),Fn("sackurTetrode",-1.16487052358);function Fn(e,t){return Lt(e,["config","BigNumber"],(e=>{var{config:n,BigNumber:r}=e;return"BigNumber"===n.number?new r(t):t}))}var Nn=n(1377),_n=Lt("Fraction",[],(()=>(Object.defineProperty(Nn,"name",{value:"Fraction"}),Nn.prototype.constructor=Nn,Nn.prototype.type="Fraction",Nn.prototype.isFraction=!0,Nn.prototype.toJSON=function(){return{mathjs:"Fraction",n:this.s*this.n,d:this.d}},Nn.fromJSON=function(e){return new Nn(e)},Nn)),{isClass:!0}),Mn=Lt("Matrix",[],(()=>{function e(){if(!(this instanceof e))throw new SyntaxError("Constructor must be called with the new operator")}return e.prototype.type="Matrix",e.prototype.isMatrix=!0,e.prototype.storage=function(){throw new Error("Cannot invoke storage on a Matrix interface")},e.prototype.datatype=function(){throw new Error("Cannot invoke datatype on a Matrix interface")},e.prototype.create=function(e,t){throw new Error("Cannot invoke create on a Matrix interface")},e.prototype.subset=function(e,t,n){throw new Error("Cannot invoke subset on a Matrix interface")},e.prototype.get=function(e){throw new Error("Cannot invoke get on a Matrix interface")},e.prototype.set=function(e,t,n){throw new Error("Cannot invoke set on a Matrix interface")},e.prototype.resize=function(e,t){throw new Error("Cannot invoke resize on a Matrix interface")},e.prototype.reshape=function(e,t){throw new Error("Cannot invoke reshape on a Matrix interface")},e.prototype.clone=function(){throw new Error("Cannot invoke clone on a Matrix interface")},e.prototype.size=function(){throw new Error("Cannot invoke size on a Matrix interface")},e.prototype.map=function(e,t){throw new Error("Cannot invoke map on a Matrix interface")},e.prototype.forEach=function(e){throw new Error("Cannot invoke forEach on a Matrix interface")},e.prototype[Symbol.iterator]=function(){throw new Error("Cannot iterate a Matrix interface")},e.prototype.toArray=function(){throw new Error("Cannot invoke toArray on a Matrix interface")},e.prototype.valueOf=function(){throw new Error("Cannot invoke valueOf on a Matrix interface")},e.prototype.format=function(e){throw new Error("Cannot invoke format on a Matrix interface")},e.prototype.toString=function(){throw new Error("Cannot invoke toString on a Matrix interface")},e}),{isClass:!0});function Sn(e,t,n){var r=new(0,e.constructor)(2),i="";if(n){if(n<1)throw new Error("size must be in greater than 0");if(!Xt(n))throw new Error("size must be an integer");if(e.greaterThan(r.pow(n-1).sub(1))||e.lessThan(r.pow(n-1).mul(-1)))throw new Error("Value must be in range [-2^".concat(n-1,", 2^").concat(n-1,"-1]"));if(!e.isInteger())throw new Error("Value must be an integer");e.lessThan(0)&&(e=e.add(r.pow(n))),i="i".concat(n)}switch(t){case 2:return"".concat(e.toBinary()).concat(i);case 8:return"".concat(e.toOctal()).concat(i);case 16:return"".concat(e.toHexadecimal()).concat(i);default:throw new Error("Base ".concat(t," not supported "))}}function On(e,t){if("function"==typeof t)return t(e);if(!e.isFinite())return e.isNaN()?"NaN":e.gt(0)?"Infinity":"-Infinity";var{notation:n,precision:r,wordSize:i}=un(t);switch(n){case"fixed":return function(e,t){return e.toFixed(t)}(e,r);case"exponential":return Tn(e,r);case"engineering":return function(e,t){var n=e.e,r=n%3==0?n:n<0?n-3-n%3:n-n%3,i=e.mul(Math.pow(10,-r)),o=i.toPrecision(t);if(o.includes("e")){o=new(0,e.constructor)(o).toFixed()}return o+"e"+(n>=0?"+":"")+r.toString()}(e,r);case"bin":return Sn(e,2,i);case"oct":return Sn(e,8,i);case"hex":return Sn(e,16,i);case"auto":var o=Bn(null==t?void 0:t.lowerExp,-3),u=Bn(null==t?void 0:t.upperExp,5);if(e.isZero())return"0";var a=e.toSignificantDigits(r),s=a.e;return(s>=o&&s<u?a.toFixed():Tn(e,r)).replace(/((\.\d*?)(0+))($|e)/,(function(){var e=arguments[2],t=arguments[4];return"."!==e?e+t:t}));default:throw new Error('Unknown notation "'+n+'". Choose "auto", "exponential", "fixed", "bin", "oct", or "hex.')}}function Tn(e,t){return void 0!==t?e.toExponential(t-1):e.toExponential()}function Bn(e,t){return Je(e)?e:Xe(e)?e.toNumber():t}function Rn(e,t){var n=e.length-t.length,r=e.length;return e.substring(n,r)===t}function In(e,t){var n=function(e,t){if("number"==typeof e)return on(e,t);if(Xe(e))return On(e,t);if(function(e){return e&&"object"==typeof e&&"number"==typeof e.s&&"number"==typeof e.n&&"number"==typeof e.d||!1}(e))return t&&"decimal"===t.fraction?e.toString():e.s*e.n+"/"+e.d;if(Array.isArray(e))return kn(e,t);if(tt(e))return zn(e);if("function"==typeof e)return e.syntax?String(e.syntax):"function";if(e&&"object"==typeof e){return"function"==typeof e.format?e.format(t):e&&e.toString(t)!=={}.toString()?e.toString(t):"{"+Object.keys(e).map((n=>zn(n)+": "+In(e[n],t))).join(", ")+"}"}return String(e)}(e,t);return t&&"object"==typeof t&&"truncate"in t&&n.length>t.truncate?n.substring(0,t.truncate-3)+"...":n}function zn(e){for(var t=String(e),n="",r=0;r<t.length;){var i=t.charAt(r);n+=i in Pn?Pn[i]:i,r++}return'"'+n+'"'}var Pn={'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t"};function kn(e,t){if(Array.isArray(e)){for(var n="[",r=e.length,i=0;i<r;i++)0!==i&&(n+=", "),n+=kn(e[i],t);return n+="]"}return In(e,t)}function jn(e,t){if(!tt(e))throw new TypeError("Unexpected type of argument in function compareText (expected: string or Array or Matrix, actual: "+It(e)+", index: 0)");if(!tt(t))throw new TypeError("Unexpected type of argument in function compareText (expected: string or Array or Matrix, actual: "+It(t)+", index: 1)");return e===t?0:e>t?1:-1}function Ln(e,t,n){if(!(this instanceof Ln))throw new SyntaxError("Constructor must be called with the new operator");this.actual=e,this.expected=t,this.relation=n,this.message="Dimension mismatch ("+(Array.isArray(e)?"["+e.join(", ")+"]":e)+" "+(this.relation||"!=")+" "+(Array.isArray(t)?"["+t.join(", ")+"]":t)+")",this.stack=(new Error).stack}function Un(e,t,n){if(!(this instanceof Un))throw new SyntaxError("Constructor must be called with the new operator");this.index=e,arguments.length<3?(this.min=0,this.max=t):(this.min=t,this.max=n),void 0!==this.min&&this.index<this.min?this.message="Index out of range ("+this.index+" < "+this.min+")":void 0!==this.max&&this.index>=this.max?this.message="Index out of range ("+this.index+" > "+(this.max-1)+")":this.message="Index out of range ("+this.index+")",this.stack=(new Error).stack}function qn(e){for(var t=[];Array.isArray(e);)t.push(e.length),e=e[0];return t}function Hn(e,t,n){var r,i=e.length;if(i!==t[n])throw new Ln(i,t[n]);if(n<t.length-1){var o=n+1;for(r=0;r<i;r++){var u=e[r];if(!Array.isArray(u))throw new Ln(t.length-1,t.length,"<");Hn(e[r],t,o)}}else for(r=0;r<i;r++)if(Array.isArray(e[r]))throw new Ln(t.length+1,t.length,">")}function Gn(e,t){if(0===t.length){if(Array.isArray(e))throw new Ln(e.length,0)}else Hn(e,t,0)}function Vn(e,t){if(void 0!==e){if(!Je(e)||!Xt(e))throw new TypeError("Index must be an integer (value: "+e+")");if(e<0||"number"==typeof t&&e>=t)throw new Un(e,t)}}function Yn(e,t,n){if(!Array.isArray(t))throw new TypeError("Array expected");if(0===t.length)throw new Error("Resizing to scalar is not supported");return t.forEach((function(e){if(!Je(e)||!Xt(e)||e<0)throw new TypeError("Invalid size, must contain positive integers (size: "+In(t)+")")})),(Je(e)||Xe(e))&&(e=[e]),Wn(e,t,0,void 0!==n?n:0),e}function Wn(e,t,n,r){var i,o,u=e.length,a=t[n],s=Math.min(u,a);if(e.length=a,n<t.length-1){var c=n+1;for(i=0;i<s;i++)o=e[i],Array.isArray(o)||(o=[o],e[i]=o),Wn(o,t,c,r);for(i=s;i<a;i++)o=[],e[i]=o,Wn(o,t,c,r)}else{for(i=0;i<s;i++)for(;Array.isArray(e[i]);)e[i]=e[i][0];for(i=s;i<a;i++)e[i]=r}}function $n(e,t){var n=Kn(e),r=n.length;if(!Array.isArray(e)||!Array.isArray(t))throw new TypeError("Array expected");if(0===t.length)throw new Ln(0,r,"!=");var i=Jn(t=Zn(t,r));if(r!==i)throw new Ln(i,r,"!=");try{return function(e,t){for(var n,r=e,i=t.length-1;i>0;i--){var o=t[i];n=[];for(var u=r.length/o,a=0;a<u;a++)n.push(r.slice(a*o,(a+1)*o));r=n}return r}(n,t)}catch(e){if(e instanceof Ln)throw new Ln(i,r,"!=");throw e}}function Zn(e,t){var n=Jn(e),r=e.slice(),i=e.indexOf(-1);if(e.indexOf(-1,i+1)>=0)throw new Error("More than one wildcard in sizes");if(i>=0){if(!(t%n==0))throw new Error("Could not replace wildcard, since "+t+" is no multiple of "+-n);r[i]=-t/n}return r}function Jn(e){return e.reduce(((e,t)=>e*t),1)}function Xn(e,t,n,r){var i=r||qn(e);if(n)for(var o=0;o<n;o++)e=[e],i.unshift(1);for(e=Qn(e,t,0);i.length<t;)i.push(1);return e}function Qn(e,t,n){var r,i;if(Array.isArray(e)){var o=n+1;for(r=0,i=e.length;r<i;r++)e[r]=Qn(e[r],t,o)}else for(var u=n;u<t;u++)e=[e];return e}function Kn(e){if(!Array.isArray(e))return e;var t=[];return e.forEach((function e(n){Array.isArray(n)?n.forEach(e):t.push(n)})),t}function er(e,t){for(var n,r=0,i=0;i<e.length;i++){var o=e[i],u=Array.isArray(o);if(0===i&&u&&(r=o.length),u&&o.length!==r)return;var a=u?er(o,t):t(o);if(void 0===n)n=a;else if(n!==a)return"mixed"}return n}function tr(e,t,n,r){if(r<n){if(e.length!==t.length)throw new Ln(e.length,t.length);for(var i=[],o=0;o<e.length;o++)i[o]=tr(e[o],t[o],n,r+1);return i}return e.concat(t)}function nr(){var e=Array.prototype.slice.call(arguments,0,-1),t=Array.prototype.slice.call(arguments,-1);if(1===e.length)return e[0];if(e.length>1)return e.slice(1).reduce((function(e,n){return tr(e,n,t,0)}),e[0]);throw new Error("Wrong number of arguments in function concat")}function rr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var r=t.map((e=>e.length)),i=Math.max(...r),o=new Array(i).fill(null),u=0;u<t.length;u++)for(var a=t[u],s=r[u],c=0;c<s;c++){var f=i-s+c;a[c]>o[f]&&(o[f]=a[c])}for(var l=0;l<t.length;l++)ir(t[l],o);return o}function ir(e,t){for(var n=t.length,r=e.length,i=0;i<r;i++){var o=n-r+i;if(e[i]<t[o]&&e[i]>1||e[i]>t[o])throw new Error("shape missmatch: missmatch is found in arg with shape (".concat(e,") not possible to broadcast dimension ").concat(r," with size ").concat(e[i]," to size ").concat(t[o]))}}function or(e,t){var n=qn(e);if(kt(n,t))return e;ir(n,t);var r,i,o,u=rr(n,t),a=u.length,c=[...Array(a-n.length).fill(1),...n],f=function(e){return s([],e)}(e);n.length<a&&(n=qn(f=$n(f,c)));for(var l=0;l<a;l++)n[l]<u[l]&&(r=f,i=u[l],o=l,n=qn(f=nr(...Array(i).fill(r),o)));return f}Ln.prototype=new RangeError,Ln.prototype.constructor=RangeError,Ln.prototype.name="DimensionError",Ln.prototype.isDimensionError=!0,Un.prototype=new RangeError,Un.prototype.constructor=RangeError,Un.prototype.name="IndexError",Un.prototype.isIndexError=!0;var ur=Lt("DenseMatrix",["Matrix"],(e=>{var{Matrix:t}=e;function n(e,t){if(!(this instanceof n))throw new SyntaxError("Constructor must be called with the new operator");if(t&&!tt(t))throw new Error("Invalid datatype: "+t);if(rt(e))"DenseMatrix"===e.type?(this._data=zt(e._data),this._size=zt(e._size),this._datatype=t||e._datatype):(this._data=e.toArray(),this._size=e.size(),this._datatype=t||e._datatype);else if(e&&nt(e.data)&&nt(e.size))this._data=e.data,this._size=e.size,Gn(this._data,this._size),this._datatype=t||e.datatype;else if(nt(e))this._data=a(e),this._size=qn(this._data),Gn(this._data,this._size),this._datatype=t;else{if(e)throw new TypeError("Unsupported type of data ("+It(e)+")");this._data=[],this._size=[0],this._datatype=t}}function r(e,t,n,i){var o=i===n-1,u=t.dimension(i);return o?u.map((function(t){return Vn(t,e.length),e[t]})).valueOf():u.map((function(o){return Vn(o,e.length),r(e[o],t,n,i+1)})).valueOf()}function i(e,t,n,r,o){var u=o===r-1,a=t.dimension(o);u?a.forEach((function(t,r){Vn(t),e[t]=n[r[0]]})):a.forEach((function(u,a){Vn(u),i(e[u],t,n[a[0]],r,o+1)}))}function o(e,t,n){if(0===t.length){for(var r=e._data;nt(r);)r=r[0];return r}return e._size=t.slice(0),e._data=Yn(e._data,e._size,n),e}function u(e,t,n){for(var r=e._size.slice(0),i=!1;r.length<t.length;)r.push(0),i=!0;for(var u=0,a=t.length;u<a;u++)t[u]>r[u]&&(r[u]=t[u],i=!0);i&&o(e,r,n)}function a(e){return rt(e)?a(e.valueOf()):nt(e)?e.map(a):e}return n.prototype=new t,n.prototype.createDenseMatrix=function(e,t){return new n(e,t)},Object.defineProperty(n,"name",{value:"DenseMatrix"}),n.prototype.constructor=n,n.prototype.type="DenseMatrix",n.prototype.isDenseMatrix=!0,n.prototype.getDataType=function(){return er(this._data,It)},n.prototype.storage=function(){return"dense"},n.prototype.datatype=function(){return this._datatype},n.prototype.create=function(e,t){return new n(e,t)},n.prototype.subset=function(e,t,o){switch(arguments.length){case 1:return function(e,t){if(!st(t))throw new TypeError("Invalid index");var i=t.isScalar();if(i)return e.get(t.min());var o=t.size();if(o.length!==e._size.length)throw new Ln(o.length,e._size.length);for(var u=t.min(),a=t.max(),s=0,c=e._size.length;s<c;s++)Vn(u[s],e._size[s]),Vn(a[s],e._size[s]);return new n(r(e._data,t,o.length,0),e._datatype)}(this,e);case 2:case 3:return function(e,t,n,r){if(!t||!0!==t.isIndex)throw new TypeError("Invalid index");var o,a=t.size(),s=t.isScalar();rt(n)?(o=n.size(),n=n.valueOf()):o=qn(n);if(s){if(0!==o.length)throw new TypeError("Scalar expected");e.set(t.min(),n,r)}else{if(!kt(o,a))try{o=qn(n=0===o.length?or([n],a):or(n,a))}catch(e){}if(a.length<e._size.length)throw new Ln(a.length,e._size.length,"<");if(o.length<a.length){for(var c=0,f=0;1===a[c]&&1===o[c];)c++;for(;1===a[c];)f++,c++;n=Xn(n,a.length,f,o)}if(!kt(a,o))throw new Ln(a,o,">");var l=t.max().map((function(e){return e+1}));u(e,l,r);var p=a.length,h=0;i(e._data,t,n,p,h)}return e}(this,e,t,o);default:throw new SyntaxError("Wrong number of arguments")}},n.prototype.get=function(e){if(!nt(e))throw new TypeError("Array expected");if(e.length!==this._size.length)throw new Ln(e.length,this._size.length);for(var t=0;t<e.length;t++)Vn(e[t],this._size[t]);for(var n=this._data,r=0,i=e.length;r<i;r++){var o=e[r];Vn(o,n.length),n=n[o]}return n},n.prototype.set=function(e,t,n){if(!nt(e))throw new TypeError("Array expected");if(e.length<this._size.length)throw new Ln(e.length,this._size.length,"<");var r,i,o,a=e.map((function(e){return e+1}));u(this,a,n);var s=this._data;for(r=0,i=e.length-1;r<i;r++)Vn(o=e[r],s.length),s=s[o];return Vn(o=e[e.length-1],s.length),s[o]=t,this},n.prototype.resize=function(e,t,n){if(!it(e))throw new TypeError("Array or Matrix expected");var r=e.valueOf().map((e=>Array.isArray(e)&&1===e.length?e[0]:e));return o(n?this.clone():this,r,t)},n.prototype.reshape=function(e,t){var n=t?this.clone():this;n._data=$n(n._data,e);var r=n._size.reduce(((e,t)=>e*t));return n._size=Zn(e,r),n},n.prototype.clone=function(){return new n({data:zt(this._data),size:zt(this._size),datatype:this._datatype})},n.prototype.size=function(){return this._size.slice(0)},n.prototype.map=function(e){var t=this,r=xn(e),i=function n(i,o){return nt(i)?i.map((function(e,t){return n(e,o.concat(t))})):1===r?e(i):2===r?e(i,o):e(i,o,t)}(this._data,[]);return new n(i,void 0!==this._datatype?er(i,It):void 0)},n.prototype.forEach=function(e){var t=this;!function n(r,i){nt(r)?r.forEach((function(e,t){n(e,i.concat(t))})):e(r,i,t)}(this._data,[])},n.prototype[Symbol.iterator]=function*(){yield*function*e(t,n){if(nt(t))for(var r=0;r<t.length;r++)yield*e(t[r],n.concat(r));else yield{value:t,index:n}}(this._data,[])},n.prototype.rows=function(){var e=[];if(2!==this.size().length)throw new TypeError("Rows can only be returned for a 2D matrix.");var t=this._data;for(var r of t)e.push(new n([r],this._datatype));return e},n.prototype.columns=function(){var e=this,t=[],r=this.size();if(2!==r.length)throw new TypeError("Rows can only be returned for a 2D matrix.");for(var i=this._data,o=function(r){var o=i.map((e=>[e[r]]));t.push(new n(o,e._datatype))},u=0;u<r[1];u++)o(u);return t},n.prototype.toArray=function(){return zt(this._data)},n.prototype.valueOf=function(){return this._data},n.prototype.format=function(e){return In(this._data,e)},n.prototype.toString=function(){return In(this._data)},n.prototype.toJSON=function(){return{mathjs:"DenseMatrix",data:this._data,size:this._size,datatype:this._datatype}},n.prototype.diagonal=function(e){if(e){if(Xe(e)&&(e=e.toNumber()),!Je(e)||!Xt(e))throw new TypeError("The parameter k must be an integer number")}else e=0;for(var t=e>0?e:0,r=e<0?-e:0,i=this._size[0],o=this._size[1],u=Math.min(i-r,o-t),a=[],s=0;s<u;s++)a[s]=this._data[s+r][s+t];return new n({data:a,size:[u],datatype:this._datatype})},n.diagonal=function(e,t,r,i){if(!nt(e))throw new TypeError("Array expected, size parameter");if(2!==e.length)throw new Error("Only two dimensions matrix are supported");if(e=e.map((function(e){if(Xe(e)&&(e=e.toNumber()),!Je(e)||!Xt(e)||e<1)throw new Error("Size values must be positive integers");return e})),r){if(Xe(r)&&(r=r.toNumber()),!Je(r)||!Xt(r))throw new TypeError("The parameter k must be an integer number")}else r=0;var o,u=r>0?r:0,a=r<0?-r:0,s=e[0],c=e[1],f=Math.min(s-a,c-u);if(nt(t)){if(t.length!==f)throw new Error("Invalid value array length");o=function(e){return t[e]}}else if(rt(t)){var l=t.size();if(1!==l.length||l[0]!==f)throw new Error("Invalid matrix length");o=function(e){return t.get([e])}}else o=function(){return t};i||(i=Xe(o(0))?o(0).mul(0):0);var p=[];if(e.length>0){p=Yn(p,e,i);for(var h=0;h<f;h++)p[h+a][h+u]=o(h)}return new n({data:p,size:[s,c]})},n.fromJSON=function(e){return new n(e)},n.prototype.swapRows=function(e,t){if(!(Je(e)&&Xt(e)&&Je(t)&&Xt(t)))throw new Error("Row index must be positive integers");if(2!==this._size.length)throw new Error("Only two dimensional matrix is supported");return Vn(e,this._size[0]),Vn(t,this._size[0]),n._swapRows(e,t,this._data),this},n._swapRows=function(e,t,n){var r=n[e];n[e]=n[t],n[t]=r},n}),{isClass:!0}),ar=n(2369);function sr(e,t){if(pr(e)&&fr(e,t))return e[t];if("function"==typeof e[t]&&lr(e,t))throw new Error('Cannot access method "'+t+'" as a property');throw new Error('No access to property "'+t+'"')}function cr(e,t,n){if(pr(e)&&fr(e,t))return e[t]=n,n;throw new Error('No access to property "'+t+'"')}function fr(e,t){return!(!e||"object"!=typeof e)&&(!!jt(hr,t)||!(t in Object.prototype)&&!(t in Function.prototype))}function lr(e,t){return null!=e&&"function"==typeof e[t]&&(!(jt(e,t)&&Object.getPrototypeOf&&t in Object.getPrototypeOf(e))&&(!!jt(dr,t)||!(t in Object.prototype)&&!(t in Function.prototype)))}function pr(e){return"object"==typeof e&&e&&e.constructor===Object}var hr={length:!0,name:!0},dr={toString:!0,valueOf:!0,toLocaleString:!0};class mr{constructor(e){this.wrappedObject=e,this[Symbol.iterator]=this.entries}keys(){return Object.keys(this.wrappedObject).values()}get(e){return sr(this.wrappedObject,e)}set(e,t){return cr(this.wrappedObject,e,t),this}has(e){return t=this.wrappedObject,e in t;var t}entries(){return gr(this.keys(),(e=>[e,this.get(e)]))}forEach(e){for(var t of this.keys())e(this.get(t),t,this)}delete(e){delete this.wrappedObject[e]}clear(){for(var e of this.keys())this.delete(e)}get size(){return Object.keys(this.wrappedObject).length}}function gr(e,t){return{next:()=>{var n=e.next();return n.done?n:{value:t(n.value),done:!1}}}}function vr(e){return!!e&&(e instanceof Map||e instanceof mr||"function"==typeof e.set&&"function"==typeof e.get&&"function"==typeof e.keys&&"function"==typeof e.has)}var Dr=function(){return Dr=ar.create,ar},yr=Lt("typed",["?BigNumber","?Complex","?DenseMatrix","?Fraction"],(function(e){var{BigNumber:t,Complex:n,DenseMatrix:r,Fraction:i}=e,o=Dr();return o.clear(),o.addTypes([{name:"number",test:Je},{name:"Complex",test:Qe},{name:"BigNumber",test:Xe},{name:"Fraction",test:Ke},{name:"Unit",test:et},{name:"identifier",test:e=>tt&&/^(?:[A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDCD0-\uDCEB\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDCD0-\uDCEB\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])*$/.test(e)},{name:"string",test:tt},{name:"Chain",test:Rt},{name:"Array",test:nt},{name:"Matrix",test:rt},{name:"DenseMatrix",test:ot},{name:"SparseMatrix",test:ut},{name:"Range",test:at},{name:"Index",test:st},{name:"boolean",test:ct},{name:"ResultSet",test:ft},{name:"Help",test:lt},{name:"function",test:pt},{name:"Date",test:ht},{name:"RegExp",test:dt},{name:"null",test:gt},{name:"undefined",test:vt},{name:"AccessorNode",test:Dt},{name:"ArrayNode",test:yt},{name:"AssignmentNode",test:wt},{name:"BlockNode",test:bt},{name:"ConditionalNode",test:Et},{name:"ConstantNode",test:xt},{name:"FunctionNode",test:Ct},{name:"FunctionAssignmentNode",test:At},{name:"IndexNode",test:Ft},{name:"Node",test:Nt},{name:"ObjectNode",test:_t},{name:"OperatorNode",test:Mt},{name:"ParenthesisNode",test:St},{name:"RangeNode",test:Ot},{name:"RelationalNode",test:Tt},{name:"SymbolNode",test:Bt},{name:"Map",test:vr},{name:"Object",test:mt}]),o.addConversions([{from:"number",to:"BigNumber",convert:function(e){if(t||wr(e),e.toExponential().replace(/e.*$/,"").replace(/^0\.?0*|\./,"").length>15)throw new TypeError("Cannot implicitly convert a number with >15 significant digits to BigNumber (value: "+e+"). Use function bignumber(x) to convert to BigNumber.");return new t(e)}},{from:"number",to:"Complex",convert:function(e){return n||br(e),new n(e,0)}},{from:"BigNumber",to:"Complex",convert:function(e){return n||br(e),new n(e.toNumber(),0)}},{from:"Fraction",to:"BigNumber",convert:function(e){throw new TypeError("Cannot implicitly convert a Fraction to BigNumber or vice versa. Use function bignumber(x) to convert to BigNumber or fraction(x) to convert to Fraction.")}},{from:"Fraction",to:"Complex",convert:function(e){return n||br(e),new n(e.valueOf(),0)}},{from:"number",to:"Fraction",convert:function(e){i||Er(e);var t=new i(e);if(t.valueOf()!==e)throw new TypeError("Cannot implicitly convert a number to a Fraction when there will be a loss of precision (value: "+e+"). Use function fraction(x) to convert to Fraction.");return t}},{from:"string",to:"number",convert:function(e){var t=Number(e);if(isNaN(t))throw new Error('Cannot convert "'+e+'" to a number');return t}},{from:"string",to:"BigNumber",convert:function(e){t||wr(e);try{return new t(e)}catch(t){throw new Error('Cannot convert "'+e+'" to BigNumber')}}},{from:"string",to:"Fraction",convert:function(e){i||Er(e);try{return new i(e)}catch(t){throw new Error('Cannot convert "'+e+'" to Fraction')}}},{from:"string",to:"Complex",convert:function(e){n||br(e);try{return new n(e)}catch(t){throw new Error('Cannot convert "'+e+'" to Complex')}}},{from:"boolean",to:"number",convert:function(e){return+e}},{from:"boolean",to:"BigNumber",convert:function(e){return t||wr(e),new t(+e)}},{from:"boolean",to:"Fraction",convert:function(e){return i||Er(e),new i(+e)}},{from:"boolean",to:"string",convert:function(e){return String(e)}},{from:"Array",to:"Matrix",convert:function(e){return r||function(){throw new Error("Cannot convert array into a Matrix: no class 'DenseMatrix' provided")}(),new r(e)}},{from:"Matrix",to:"Array",convert:function(e){return e.valueOf()}}]),o.onMismatch=(e,t,n)=>{var r=o.createError(e,t,n);if(["wrongType","mismatch"].includes(r.data.category)&&1===t.length&&it(t[0])&&n.some((e=>!e.params.includes(",")))){var i=new TypeError("Function '".concat(e,"' doesn't apply to matrices. To call it ")+"elementwise on a matrix 'M', try 'map(M, ".concat(e,")'."));throw i.data=r.data,i}throw r},o.onMismatch=(e,t,n)=>{var r=o.createError(e,t,n);if(["wrongType","mismatch"].includes(r.data.category)&&1===t.length&&it(t[0])&&n.some((e=>!e.params.includes(",")))){var i=new TypeError("Function '".concat(e,"' doesn't apply to matrices. To call it ")+"elementwise on a matrix 'M', try 'map(M, ".concat(e,")'."));throw i.data=r.data,i}throw r},o}));function wr(e){throw new Error("Cannot convert value ".concat(e," into a BigNumber: no class 'BigNumber' provided"))}function br(e){throw new Error("Cannot convert value ".concat(e," into a Complex number: no class 'Complex' provided"))}function Er(e){throw new Error("Cannot convert value ".concat(e," into a Fraction, no class 'Fraction' provided."))}function xr(e,t,n){return e&&"function"==typeof e.map?e.map((function(e){return xr(e,t,n)})):t(e)}var Ar="number",Cr="number, number";function Fr(e){return Math.abs(e)}function Nr(e,t){return e+t}function _r(e,t){return e-t}function Mr(e,t){return e*t}function Sr(e){return-e}function Or(e){return e}function Tr(e){return tn(e)}function Br(e){return e*e*e}function Rr(e){return Math.exp(e)}function Ir(e){return nn(e)}function zr(e,t){if(!Xt(e)||!Xt(t))throw new Error("Parameters in function lcm must be integer numbers");if(0===e||0===t)return 0;for(var n,r=e*t;0!==t;)t=e%(n=t),e=n;return Math.abs(r/e)}function Pr(e){return en(e)}function kr(e){return Kt(e)}function jr(e){return Qt(e)}function Lr(e){return e*e}function Ur(e,t){var n,r,i,o=0,u=1,a=1,s=0;if(!Xt(e)||!Xt(t))throw new Error("Parameters in function xgcd must be integer numbers");for(;t;)i=e-(r=Math.floor(e/t))*t,n=o,o=u-r*o,u=n,n=a,a=s-r*a,s=n,e=t,t=i;return e<0?[-e,-u,-s]:[e,e?u:0,s]}function qr(e,t){return e*e<1&&t===1/0||e*e>1&&t===-1/0?0:Math.pow(e,t)}function Hr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!Xt(t)||t<0||t>15)throw new Error("Number of decimals in function round must be an integer from 0 to 15 inclusive");return parseFloat(sn(e,t))}Fr.signature=Ar,Nr.signature=Cr,_r.signature=Cr,Mr.signature=Cr,Sr.signature=Ar,Or.signature=Ar,Tr.signature=Ar,Br.signature=Ar,Rr.signature=Ar,Ir.signature=Ar,zr.signature=Cr,Pr.signature=Ar,kr.signature=Ar,jr.signature=Ar,Lr.signature=Ar,Ur.signature=Cr,qr.signature=Cr;var Gr=Lt("abs",["typed"],(e=>{var{typed:t}=e;return t("abs",{number:Fr,"Complex | BigNumber | Fraction | Unit":e=>e.abs(),"Array | Matrix":t.referToSelf((e=>t=>xr(t,e,!0)))})})),Vr="number";function Yr(e){return dn(e)}function Wr(e){return Math.atan(1/e)}function $r(e){return isFinite(e)?(Math.log((e+1)/e)+Math.log(e/(e-1)))/2:0}function Zr(e){return Math.asin(1/e)}function Jr(e){var t=1/e;return Math.log(t+Math.sqrt(t*t+1))}function Xr(e){return Math.acos(1/e)}function Qr(e){var t=1/e,n=Math.sqrt(t*t-1);return Math.log(n+t)}function Kr(e){return mn(e)}function ei(e){return gn(e)}function ti(e){return 1/Math.tan(e)}function ni(e){var t=Math.exp(2*e);return(t+1)/(t-1)}function ri(e){return 1/Math.sin(e)}function ii(e){return 0===e?Number.POSITIVE_INFINITY:Math.abs(2/(Math.exp(e)-Math.exp(-e)))*Qt(e)}function oi(e){return 1/Math.cos(e)}function ui(e){return 2/(Math.exp(e)+Math.exp(-e))}function ai(e){return vn(e)}Yr.signature=Vr,Wr.signature=Vr,$r.signature=Vr,Zr.signature=Vr,Jr.signature=Vr,Xr.signature=Vr,Qr.signature=Vr,Kr.signature=Vr,ei.signature=Vr,ti.signature=Vr,ni.signature=Vr,ri.signature=Vr,ii.signature=Vr,oi.signature=Vr,ui.signature=Vr,ai.signature=Vr;var si="addScalar",ci=Lt(si,["typed"],(e=>{var{typed:t}=e;return t(si,{"number, number":Nr,"Complex, Complex":function(e,t){return e.add(t)},"BigNumber, BigNumber":function(e,t){return e.plus(t)},"Fraction, Fraction":function(e,t){return e.add(t)},"Unit, Unit":t.referToSelf((e=>(n,r)=>{if(null===n.value||void 0===n.value)throw new Error("Parameter x contains a unit with undefined value");if(null===r.value||void 0===r.value)throw new Error("Parameter y contains a unit with undefined value");if(!n.equalBase(r))throw new Error("Units do not match");var i=n.clone();return i.value=t.find(e,[i.valueType(),r.valueType()])(i.value,r.value),i.fixPrefix=!1,i}))})})),fi=Lt("bignumber",["typed","BigNumber"],(e=>{var{typed:t,BigNumber:n}=e;return t("bignumber",{"":function(){return new n(0)},number:function(e){return new n(e+"")},string:function(e){var t=e.match(/(0[box][0-9a-fA-F]*)i([0-9]*)/);if(t){var r=t[2],i=n(t[1]),o=new n(2).pow(Number(r));if(i.gt(o.sub(1)))throw new SyntaxError('String "'.concat(e,'" is out of range'));var u=new n(2).pow(Number(r)-1);return i.gte(u)?i.sub(o):i}return new n(e)},BigNumber:function(e){return e},Unit:t.referToSelf((e=>t=>{var n=t.clone();return n.value=e(t.value),n})),Fraction:function(e){return new n(e.n).div(e.d).times(e.s)},null:function(e){return new n(0)},"Array | Matrix":t.referToSelf((e=>t=>xr(t,e)))})}));var li="number, number";function pi(e,t){if(!Xt(e)||!Xt(t))throw new Error("Integers expected in function bitAnd");return e&t}function hi(e){if(!Xt(e))throw new Error("Integer expected in function bitNot");return~e}function di(e,t){if(!Xt(e)||!Xt(t))throw new Error("Integers expected in function bitOr");return e|t}function mi(e,t){if(!Xt(e)||!Xt(t))throw new Error("Integers expected in function bitXor");return e^t}function gi(e,t){if(!Xt(e)||!Xt(t))throw new Error("Integers expected in function leftShift");return e<<t}function vi(e,t){if(!Xt(e)||!Xt(t))throw new Error("Integers expected in function rightArithShift");return e>>t}function Di(e,t){if(!Xt(e)||!Xt(t))throw new Error("Integers expected in function rightLogShift");return e>>>t}pi.signature=li,hi.signature="number",di.signature=li,mi.signature=li,gi.signature=li,vi.signature=li,Di.signature=li;function yi(e,t){if(t<e)return 1;if(t===e)return t;var n=t+e>>1;return yi(e,n)*yi(n+1,t)}function wi(e,t){if(!Xt(e)||e<0)throw new TypeError("Positive integer value expected in function combinations");if(!Xt(t)||t<0)throw new TypeError("Positive integer value expected in function combinations");if(t>e)throw new TypeError("k must be less than or equal to n");for(var n=e-t,r=1,i=2,o=t<n?t:n,u=t<n?n+1:t+1;u<=e;++u)for(r*=u;i<=o&&r%i==0;)r/=i,++i;return i<=o&&(r/=yi(i,o)),r}wi.signature="number, number";var bi="conj",Ei=Lt(bi,["typed"],(e=>{var{typed:t}=e;return t(bi,{"number | BigNumber | Fraction":e=>e,Complex:e=>e.conjugate(),"Array | Matrix":t.referToSelf((e=>t=>xr(t,e)))})}));function xi(e,t,n){if(null==n)return e.eq(t);if(e.eq(t))return!0;if(e.isNaN()||t.isNaN())return!1;if(e.isFinite()&&t.isFinite()){var r=e.minus(t).abs();if(r.isZero())return!0;var i=e.constructor.max(e.abs(),t.abs());return r.lte(i.times(n))}return!1}var Ai=Lt("compareUnits",["typed"],(e=>{var{typed:t}=e;return{"Unit, Unit":t.referToSelf((e=>(n,r)=>{if(!n.equalBase(r))throw new Error("Cannot compare units with different base");return t.find(e,[n.valueType(),r.valueType()])(n.value,r.value)}))}})),Ci="equalScalar",Fi=Lt(Ci,["typed","config"],(e=>{var{typed:t,config:n}=e,r=Ai({typed:t});return t(Ci,{"boolean, boolean":function(e,t){return e===t},"number, number":function(e,t){return hn(e,t,n.epsilon)},"BigNumber, BigNumber":function(e,t){return e.eq(t)||xi(e,t,n.epsilon)},"Fraction, Fraction":function(e,t){return e.equals(t)},"Complex, Complex":function(e,t){return function(e,t,n){return hn(e.re,t.re,n)&&hn(e.im,t.im,n)}(e,t,n.epsilon)}},r)}));Lt(Ci,["typed","config"],(e=>{var{typed:t,config:n}=e;return t(Ci,{"number, number":function(e,t){return hn(e,t,n.epsilon)}})})),Math.pow(2,53);var Ni="format",_i=Lt(Ni,["typed"],(e=>{var{typed:t}=e;return t(Ni,{any:In,"any, Object | function | number | BigNumber":In})})),Mi=(Lt("hex",["typed","format"],(e=>{var{typed:t,format:n}=e;return t("hex",{"number | BigNumber":function(e){return n(e,{notation:"hex"})},"number | BigNumber, number | BigNumber":function(e,t){return n(e,{notation:"hex",wordSize:t})}})})),"isInteger"),Si=Lt(Mi,["typed"],(e=>{var{typed:t}=e;return t(Mi,{number:Xt,BigNumber:function(e){return e.isInt()},Fraction:function(e){return 1===e.d&&isFinite(e.n)},"Array | Matrix":t.referToSelf((e=>t=>xr(t,e)))})})),Oi="number";function Ti(e){return e<0}function Bi(e){return e>0}function Ri(e){return 0===e}function Ii(e){return Number.isNaN(e)}Ti.signature=Oi,Bi.signature=Oi,Ri.signature=Oi,Ii.signature=Oi;var zi="isZero",Pi=Lt(zi,["typed"],(e=>{var{typed:t}=e;return t(zi,{number:Ri,BigNumber:function(e){return e.isZero()},Complex:function(e){return 0===e.re&&0===e.im},Fraction:function(e){return 1===e.d&&0===e.n},Unit:t.referToSelf((e=>n=>t.find(e,n.valueType())(n.value))),"Array | Matrix":t.referToSelf((e=>t=>xr(t,e)))})}));function ki(e){var t;if(Xt(e))return e<=0?isFinite(e)?1/0:NaN:e>171?1/0:yi(1,e-1);if(e<.5)return Math.PI/(Math.sin(Math.PI*e)*ki(1-e));if(e>=171.35)return 1/0;if(e>85){var n=e*e,r=n*e,i=r*e,o=i*e;return Math.sqrt(2*Math.PI/e)*Math.pow(e/Math.E,e)*(1+1/(12*e)+1/(288*n)-139/(51840*r)-571/(2488320*i)+163879/(209018880*o)+5246819/(75246796800*o*e))}--e,t=Li[0];for(var u=1;u<Li.length;++u)t+=Li[u]/(e+u);var a=e+ji+.5;return Math.sqrt(2*Math.PI)*Math.pow(a,e+.5)*Math.exp(-a)*t}ki.signature="number";var ji=4.7421875,Li=[.9999999999999971,57.15623566586292,-59.59796035547549,14.136097974741746,-.4919138160976202,3399464998481189e-20,4652362892704858e-20,-9837447530487956e-20,.0001580887032249125,-.00021026444172410488,.00021743961811521265,-.0001643181065367639,8441822398385275e-20,-26190838401581408e-21,36899182659531625e-22],Ui=.9189385332046728,qi=[1.000000000190015,76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18];function Hi(e){if(e<0)return NaN;if(0===e)return 1/0;if(!isFinite(e))return e;if(e<.5)return Math.log(Math.PI/Math.sin(Math.PI*e))-Hi(1-e);for(var t=(e-=1)+5+.5,n=qi[0],r=6;r>=1;r--)n+=qi[r]/(e+r);return Ui+(e+.5)*Math.log(t)-t+Math.log(n)}Hi.signature="number";var Gi=Lt("multiplyScalar",["typed"],(e=>{var{typed:t}=e;return t("multiplyScalar",{"number, number":Mr,"Complex, Complex":function(e,t){return e.mul(t)},"BigNumber, BigNumber":function(e,t){return e.times(t)},"Fraction, Fraction":function(e,t){return e.mul(t)},"number | Fraction | BigNumber | Complex, Unit":(e,t)=>t.multiply(e),"Unit, number | Fraction | BigNumber | Complex | Unit":(e,t)=>e.multiply(t)})})),Vi="number, number";function Yi(e){return!e}function Wi(e,t){return!(!e&&!t)}function $i(e,t){return!!e!=!!t}function Zi(e,t){return!(!e||!t)}Yi.signature="number",Wi.signature=Vi,$i.signature=Vi,Zi.signature=Vi;var Ji=Lt("number",["typed"],(e=>{var{typed:t}=e,n=t("number",{"":function(){return 0},number:function(e){return e},string:function(e){if("NaN"===e)return NaN;var t,n,r=(n=(t=e).match(/(0[box])([0-9a-fA-F]*)\.([0-9a-fA-F]*)/))?{input:t,radix:{"0b":2,"0o":8,"0x":16}[n[1]],integerPart:n[2],fractionalPart:n[3]}:null;if(r)return function(e){for(var t=parseInt(e.integerPart,e.radix),n=0,r=0;r<e.fractionalPart.length;r++)n+=parseInt(e.fractionalPart[r],e.radix)/Math.pow(e.radix,r+1);var i=t+n;if(isNaN(i))throw new SyntaxError('String "'+e.input+'" is not a valid number');return i}(r);var i=0,o=e.match(/(0[box][0-9a-fA-F]*)i([0-9]*)/);o&&(i=Number(o[2]),e=o[1]);var u=Number(e);if(isNaN(u))throw new SyntaxError('String "'+e+'" is not a valid number');if(o){if(u>2**i-1)throw new SyntaxError('String "'.concat(e,'" is out of range'));u>=2**(i-1)&&(u-=2**i)}return u},BigNumber:function(e){return e.toNumber()},Fraction:function(e){return e.valueOf()},Unit:t.referToSelf((e=>t=>{var n=t.clone();return n.value=e(t.value),n})),null:function(e){return 0},"Unit, string | Unit":function(e,t){return e.toNumber(t)},"Array | Matrix":t.referToSelf((e=>t=>xr(t,e)))});return n.fromJSON=function(e){return parseFloat(e.value)},n})),Xi=(Lt("oct",["typed","format"],(e=>{var{typed:t,format:n}=e;return t("oct",{"number | BigNumber":function(e){return n(e,{notation:"oct"})},"number | BigNumber, number | BigNumber":function(e,t){return n(e,{notation:"oct",wordSize:t})}})})),n(7391));Date.now();var Qi=Lt("SparseMatrix",["typed","equalScalar","Matrix"],(e=>{var{typed:t,equalScalar:n,Matrix:r}=e;function i(e,t){if(!(this instanceof i))throw new SyntaxError("Constructor must be called with the new operator");if(t&&!tt(t))throw new Error("Invalid datatype: "+t);if(rt(e))!function(e,t,n){"SparseMatrix"===t.type?(e._values=t._values?zt(t._values):void 0,e._index=zt(t._index),e._ptr=zt(t._ptr),e._size=zt(t._size),e._datatype=n||t._datatype):o(e,t.valueOf(),n||t._datatype)}(this,e,t);else if(e&&nt(e.index)&&nt(e.ptr)&&nt(e.size))this._values=e.values,this._index=e.index,this._ptr=e.ptr,this._size=e.size,this._datatype=t||e.datatype;else if(nt(e))o(this,e,t);else{if(e)throw new TypeError("Unsupported type of data ("+It(e)+")");this._values=[],this._index=[],this._ptr=[0],this._size=[0,0],this._datatype=t}}function o(e,r,i){e._values=[],e._index=[],e._ptr=[],e._datatype=i;var o=r.length,u=0,a=n,s=0;if(tt(i)&&(a=t.find(n,[i,i])||n,s=t.convert(0,i)),o>0){var c=0;do{e._ptr.push(e._index.length);for(var f=0;f<o;f++){var l=r[f];if(nt(l)){if(0===c&&u<l.length&&(u=l.length),c<l.length){var p=l[c];a(p,s)||(e._values.push(p),e._index.push(f))}}else 0===c&&u<1&&(u=1),a(l,s)||(e._values.push(l),e._index.push(f))}c++}while(c<u)}e._ptr.push(e._index.length),e._size=[o,u]}function u(e,t,n,r){if(n-t==0)return n;for(var i=t;i<n;i++)if(r[i]===e)return i;return t}function a(e,t,n,r,i,o,u){i.splice(e,0,r),o.splice(e,0,t);for(var a=n+1;a<u.length;a++)u[a]++}function s(e,r,i,o){var u=o||0,a=n,s=0;tt(e._datatype)&&(a=t.find(n,[e._datatype,e._datatype])||n,s=t.convert(0,e._datatype),u=t.convert(u,e._datatype));var c,f,l,p=!a(u,s),h=e._size[0],d=e._size[1];if(i>d){for(f=d;f<i;f++)if(e._ptr[f]=e._values.length,p)for(c=0;c<h;c++)e._values.push(u),e._index.push(c);e._ptr[i]=e._values.length}else i<d&&(e._ptr.splice(i+1,d-i),e._values.splice(e._ptr[i],e._values.length),e._index.splice(e._ptr[i],e._index.length));if(d=i,r>h){if(p){var m=0;for(f=0;f<d;f++){e._ptr[f]=e._ptr[f]+m,l=e._ptr[f+1]+m;var g=0;for(c=h;c<r;c++,g++)e._values.splice(l+g,0,u),e._index.splice(l+g,0,c),m++}e._ptr[d]=e._values.length}}else if(r<h){var v=0;for(f=0;f<d;f++){e._ptr[f]=e._ptr[f]-v;var D=e._ptr[f],y=e._ptr[f+1]-v;for(l=D;l<y;l++)(c=e._index[l])>r-1&&(e._values.splice(l,1),e._index.splice(l,1),v++)}e._ptr[f]=e._values.length}return e._size[0]=r,e._size[1]=i,e}function c(e,t,n,r,i){var o,u,a=r[0],s=r[1],c=[];for(o=0;o<a;o++)for(c[o]=[],u=0;u<s;u++)c[o][u]=0;for(u=0;u<s;u++)for(var f=n[u],l=n[u+1],p=f;p<l;p++)c[o=t[p]][u]=e?i?zt(e[p]):e[p]:1;return c}return i.prototype=new r,i.prototype.createSparseMatrix=function(e,t){return new i(e,t)},Object.defineProperty(i,"name",{value:"SparseMatrix"}),i.prototype.constructor=i,i.prototype.type="SparseMatrix",i.prototype.isSparseMatrix=!0,i.prototype.getDataType=function(){return er(this._values,It)},i.prototype.storage=function(){return"sparse"},i.prototype.datatype=function(){return this._datatype},i.prototype.create=function(e,t){return new i(e,t)},i.prototype.density=function(){var e=this._size[0],t=this._size[1];return 0!==e&&0!==t?this._index.length/(e*t):0},i.prototype.subset=function(e,t,n){if(!this._values)throw new Error("Cannot invoke subset on a Pattern only matrix");switch(arguments.length){case 1:return function(e,t){if(!st(t))throw new TypeError("Invalid index");if(t.isScalar())return e.get(t.min());var n,r,o,u,a=t.size();if(a.length!==e._size.length)throw new Ln(a.length,e._size.length);var s=t.min(),c=t.max();for(n=0,r=e._size.length;n<r;n++)Vn(s[n],e._size[n]),Vn(c[n],e._size[n]);var f=e._values,l=e._index,p=e._ptr,h=t.dimension(0),d=t.dimension(1),m=[],g=[];h.forEach((function(e,t){g[e]=t[0],m[e]=!0}));var v=f?[]:void 0,D=[],y=[];return d.forEach((function(e){for(y.push(D.length),o=p[e],u=p[e+1];o<u;o++)n=l[o],!0===m[n]&&(D.push(g[n]),v&&v.push(f[o]))})),y.push(D.length),new i({values:v,index:D,ptr:y,size:a,datatype:e._datatype})}(this,e);case 2:case 3:return function(e,t,n,r){if(!t||!0!==t.isIndex)throw new TypeError("Invalid index");var i,o=t.size(),u=t.isScalar();rt(n)?(i=n.size(),n=n.toArray()):i=qn(n);if(u){if(0!==i.length)throw new TypeError("Scalar expected");e.set(t.min(),n,r)}else{if(1!==o.length&&2!==o.length)throw new Ln(o.length,e._size.length,"<");if(i.length<o.length){for(var a=0,s=0;1===o[a]&&1===i[a];)a++;for(;1===o[a];)s++,a++;n=Xn(n,o.length,s,i)}if(!kt(o,i))throw new Ln(o,i,">");if(1===o.length){t.dimension(0).forEach((function(t,i){Vn(t),e.set([t,0],n[i[0]],r)}))}else{var c=t.dimension(0),f=t.dimension(1);c.forEach((function(t,i){Vn(t),f.forEach((function(o,u){Vn(o),e.set([t,o],n[i[0]][u[0]],r)}))}))}}return e}(this,e,t,n);default:throw new SyntaxError("Wrong number of arguments")}},i.prototype.get=function(e){if(!nt(e))throw new TypeError("Array expected");if(e.length!==this._size.length)throw new Ln(e.length,this._size.length);if(!this._values)throw new Error("Cannot invoke get on a Pattern only matrix");var t=e[0],n=e[1];Vn(t,this._size[0]),Vn(n,this._size[1]);var r=u(t,this._ptr[n],this._ptr[n+1],this._index);return r<this._ptr[n+1]&&this._index[r]===t?this._values[r]:0},i.prototype.set=function(e,r,i){if(!nt(e))throw new TypeError("Array expected");if(e.length!==this._size.length)throw new Ln(e.length,this._size.length);if(!this._values)throw new Error("Cannot invoke set on a Pattern only matrix");var o=e[0],c=e[1],f=this._size[0],l=this._size[1],p=n,h=0;tt(this._datatype)&&(p=t.find(n,[this._datatype,this._datatype])||n,h=t.convert(0,this._datatype)),(o>f-1||c>l-1)&&(s(this,Math.max(o+1,f),Math.max(c+1,l),i),f=this._size[0],l=this._size[1]),Vn(o,f),Vn(c,l);var d=u(o,this._ptr[c],this._ptr[c+1],this._index);return d<this._ptr[c+1]&&this._index[d]===o?p(r,h)?function(e,t,n,r,i){n.splice(e,1),r.splice(e,1);for(var o=t+1;o<i.length;o++)i[o]--}(d,c,this._values,this._index,this._ptr):this._values[d]=r:p(r,h)||a(d,o,c,r,this._values,this._index,this._ptr),this},i.prototype.resize=function(e,t,n){if(!it(e))throw new TypeError("Array or Matrix expected");var r=e.valueOf().map((e=>Array.isArray(e)&&1===e.length?e[0]:e));if(2!==r.length)throw new Error("Only two dimensions matrix are supported");return r.forEach((function(e){if(!Je(e)||!Xt(e)||e<0)throw new TypeError("Invalid size, must contain positive integers (size: "+In(r)+")")})),s(n?this.clone():this,r[0],r[1],t)},i.prototype.reshape=function(e,t){if(!nt(e))throw new TypeError("Array expected");if(2!==e.length)throw new Error("Sparse matrices can only be reshaped in two dimensions");e.forEach((function(t){if(!Je(t)||!Xt(t)||t<=-2||0===t)throw new TypeError("Invalid size, must contain positive integers or -1 (size: "+In(e)+")")}));var n=this._size[0]*this._size[1];if(n!==(e=Zn(e,n))[0]*e[1])throw new Error("Reshaping sparse matrix will result in the wrong number of elements");var r=t?this.clone():this;if(this._size[0]===e[0]&&this._size[1]===e[1])return r;for(var i=[],o=0;o<r._ptr.length;o++)for(var s=0;s<r._ptr[o+1]-r._ptr[o];s++)i.push(o);for(var c=r._values.slice(),f=r._index.slice(),l=0;l<r._index.length;l++){var p=f[l],h=i[l],d=p*r._size[1]+h;i[l]=d%e[1],f[l]=Math.floor(d/e[1])}r._values.length=0,r._index.length=0,r._ptr.length=e[1]+1,r._size=e.slice();for(var m=0;m<r._ptr.length;m++)r._ptr[m]=0;for(var g=0;g<c.length;g++){var v=f[g],D=i[g],y=c[g];a(u(v,r._ptr[D],r._ptr[D+1],r._index),v,D,y,r._values,r._index,r._ptr)}return r},i.prototype.clone=function(){return new i({values:this._values?zt(this._values):void 0,index:zt(this._index),ptr:zt(this._ptr),size:zt(this._size),datatype:this._datatype})},i.prototype.size=function(){return this._size.slice(0)},i.prototype.map=function(e,r){if(!this._values)throw new Error("Cannot invoke map on a Pattern only matrix");var o=this,u=this._size[0],a=this._size[1],s=xn(e);return function(e,r,o,u,a,s,c){var f=[],l=[],p=[],h=n,d=0;tt(e._datatype)&&(h=t.find(n,[e._datatype,e._datatype])||n,d=t.convert(0,e._datatype));for(var m=function(e,t,n){e=s(e,t,n),h(e,d)||(f.push(e),l.push(t))},g=u;g<=a;g++){p.push(f.length);var v=e._ptr[g],D=e._ptr[g+1];if(c)for(var y=v;y<D;y++){var w=e._index[y];w>=r&&w<=o&&m(e._values[y],w-r,g-u)}else{for(var b={},E=v;E<D;E++){b[e._index[E]]=e._values[E]}for(var x=r;x<=o;x++){m(x in b?b[x]:0,x-r,g-u)}}}return p.push(f.length),new i({values:f,index:l,ptr:p,size:[o-r+1,a-u+1]})}(this,0,u-1,0,a-1,(function(t,n,r){return 1===s?e(t):2===s?e(t,[n,r]):e(t,[n,r],o)}),r)},i.prototype.forEach=function(e,t){if(!this._values)throw new Error("Cannot invoke forEach on a Pattern only matrix");for(var n=this._size[0],r=this._size[1],i=0;i<r;i++){var o=this._ptr[i],u=this._ptr[i+1];if(t)for(var a=o;a<u;a++){var s=this._index[a];e(this._values[a],[s,i],this)}else{for(var c={},f=o;f<u;f++){c[this._index[f]]=this._values[f]}for(var l=0;l<n;l++){e(l in c?c[l]:0,[l,i],this)}}}},i.prototype[Symbol.iterator]=function*(){if(!this._values)throw new Error("Cannot iterate a Pattern only matrix");for(var e=this._size[1],t=0;t<e;t++)for(var n=this._ptr[t],r=this._ptr[t+1],i=n;i<r;i++){var o=this._index[i];yield{value:this._values[i],index:[o,t]}}},i.prototype.toArray=function(){return c(this._values,this._index,this._ptr,this._size,!0)},i.prototype.valueOf=function(){return c(this._values,this._index,this._ptr,this._size,!1)},i.prototype.format=function(e){for(var t=this._size[0],n=this._size[1],r=this.density(),i="Sparse Matrix ["+In(t,e)+" x "+In(n,e)+"] density: "+In(r,e)+"\n",o=0;o<n;o++)for(var u=this._ptr[o],a=this._ptr[o+1],s=u;s<a;s++){i+="\n    ("+In(this._index[s],e)+", "+In(o,e)+") ==> "+(this._values?In(this._values[s],e):"X")}return i},i.prototype.toString=function(){return In(this.toArray())},i.prototype.toJSON=function(){return{mathjs:"SparseMatrix",values:this._values,index:this._index,ptr:this._ptr,size:this._size,datatype:this._datatype}},i.prototype.diagonal=function(e){if(e){if(Xe(e)&&(e=e.toNumber()),!Je(e)||!Xt(e))throw new TypeError("The parameter k must be an integer number")}else e=0;var t=e>0?e:0,n=e<0?-e:0,r=this._size[0],o=this._size[1],u=Math.min(r-n,o-t),a=[],s=[],c=[];c[0]=0;for(var f=t;f<o&&a.length<u;f++)for(var l=this._ptr[f],p=this._ptr[f+1],h=l;h<p;h++){var d=this._index[h];if(d===f-t+n){a.push(this._values[h]),s[a.length-1]=d-n;break}}return c.push(a.length),new i({values:a,index:s,ptr:c,size:[u,1]})},i.fromJSON=function(e){return new i(e)},i.diagonal=function(e,r,o,u,a){if(!nt(e))throw new TypeError("Array expected, size parameter");if(2!==e.length)throw new Error("Only two dimensions matrix are supported");if(e=e.map((function(e){if(Xe(e)&&(e=e.toNumber()),!Je(e)||!Xt(e)||e<1)throw new Error("Size values must be positive integers");return e})),o){if(Xe(o)&&(o=o.toNumber()),!Je(o)||!Xt(o))throw new TypeError("The parameter k must be an integer number")}else o=0;var s=n,c=0;tt(a)&&(s=t.find(n,[a,a])||n,c=t.convert(0,a));var f,l=o>0?o:0,p=o<0?-o:0,h=e[0],d=e[1],m=Math.min(h-p,d-l);if(nt(r)){if(r.length!==m)throw new Error("Invalid value array length");f=function(e){return r[e]}}else if(rt(r)){var g=r.size();if(1!==g.length||g[0]!==m)throw new Error("Invalid matrix length");f=function(e){return r.get([e])}}else f=function(){return r};for(var v=[],D=[],y=[],w=0;w<d;w++){y.push(v.length);var b=w-l;if(b>=0&&b<m){var E=f(b);s(E,c)||(D.push(b+p),v.push(E))}}return y.push(v.length),new i({values:v,index:D,ptr:y,size:[h,d]})},i.prototype.swapRows=function(e,t){if(!(Je(e)&&Xt(e)&&Je(t)&&Xt(t)))throw new Error("Row index must be positive integers");if(2!==this._size.length)throw new Error("Only two dimensional matrix is supported");return Vn(e,this._size[0]),Vn(t,this._size[0]),i._swapRows(e,t,this._size[1],this._values,this._index,this._ptr),this},i._forEachRow=function(e,t,n,r,i){for(var o=r[e],u=r[e+1],a=o;a<u;a++)i(n[a],t[a])},i._swapRows=function(e,t,n,r,i,o){for(var a=0;a<n;a++){var s=o[a],c=o[a+1],f=u(e,s,c,i),l=u(t,s,c,i);if(f<c&&l<c&&i[f]===e&&i[l]===t){if(r){var p=r[f];r[f]=r[l],r[l]=p}}else if(f<c&&i[f]===e&&(l>=c||i[l]!==t)){var h=r?r[f]:void 0;i.splice(l,0,t),r&&r.splice(l,0,h),i.splice(l<=f?f+1:f,1),r&&r.splice(l<=f?f+1:f,1)}else if(l<c&&i[l]===t&&(f>=c||i[f]!==e)){var d=r?r[l]:void 0;i.splice(f,0,e),r&&r.splice(f,0,d),i.splice(f<=l?l+1:l,1),r&&r.splice(f<=l?l+1:l,1)}}},i}),{isClass:!0}),Ki="subtractScalar",eo=Lt(Ki,["typed"],(e=>{var{typed:t}=e;return t(Ki,{"number, number":_r,"Complex, Complex":function(e,t){return e.sub(t)},"BigNumber, BigNumber":function(e,t){return e.minus(t)},"Fraction, Fraction":function(e,t){return e.sub(t)},"Unit, Unit":t.referToSelf((e=>(n,r)=>{if(null===n.value||void 0===n.value)throw new Error("Parameter x contains a unit with undefined value");if(null===r.value||void 0===r.value)throw new Error("Parameter y contains a unit with undefined value");if(!n.equalBase(r))throw new Error("Units do not match");var i=n.clone();return i.value=t.find(e,[i.valueType(),r.valueType()])(i.value,r.value),i.fixPrefix=!1,i}))})}));Lt("bin",["typed","format"],(e=>{var{typed:t,format:n}=e;return t("bin",{"number | BigNumber":function(e){return n(e,{notation:"bin"})},"number | BigNumber, number | BigNumber":function(e,t){return n(e,{notation:"bin",wordSize:t})}})}));var to="unaryMinus",no=Lt(to,["typed"],(e=>{var{typed:t}=e;return t(to,{number:Sr,"Complex | BigNumber | Fraction":e=>e.neg(),Unit:t.referToSelf((e=>n=>{var r=n.clone();return r.value=t.find(e,r.valueType())(n.value),r})),"Array | Matrix":t.referToSelf((e=>t=>xr(t,e,!0)))})})),ro=Lt("fraction",["typed","Fraction"],(e=>{var{typed:t,Fraction:n}=e;return t("fraction",{number:function(e){if(!isFinite(e)||isNaN(e))throw new Error(e+" cannot be represented as a fraction");return new n(e)},string:function(e){return new n(e)},"number, number":function(e,t){return new n(e,t)},null:function(e){return new n(0)},BigNumber:function(e){return new n(e.toString())},Fraction:function(e){return e},Unit:t.referToSelf((e=>t=>{var n=t.clone();return n.value=e(t.value),n})),Object:function(e){return new n(e)},"Array | Matrix":t.referToSelf((e=>t=>xr(t,e)))})})),io="isNumeric",oo=Lt(io,["typed"],(e=>{var{typed:t}=e;return t(io,{"number | BigNumber | Fraction | boolean":()=>!0,"Complex | Unit | string | null | undefined | Node":()=>!1,"Array | Matrix":t.referToSelf((e=>t=>xr(t,e)))})})),uo="matrix",ao=Lt(uo,["typed","Matrix","DenseMatrix","SparseMatrix"],(e=>{var{typed:t,Matrix:n,DenseMatrix:r,SparseMatrix:i}=e;return t(uo,{"":function(){return o([])},string:function(e){return o([],e)},"string, string":function(e,t){return o([],e,t)},Array:function(e){return o(e)},Matrix:function(e){return o(e,e.storage())},"Array | Matrix, string":o,"Array | Matrix, string, string":o});function o(e,t,n){if("dense"===t||"default"===t||void 0===t)return new r(e,n);if("sparse"===t)return new i(e,n);throw new TypeError("Unknown matrix type "+JSON.stringify(t)+".")}}));function so(){throw new Error('No "bignumber" implementation available')}function co(){throw new Error('No "fraction" implementation available')}function fo(){throw new Error('No "matrix" implementation available')}var lo=Lt("numeric",["number","?bignumber","?fraction"],(e=>{var{number:t,bignumber:n,fraction:r}=e,i={string:!0,number:!0,BigNumber:!0,Fraction:!0},o={number:e=>t(e),BigNumber:n?e=>n(e):so,Fraction:r?e=>r(e):co};return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"number";if(void 0!==(arguments.length>2?arguments[2]:void 0))throw new SyntaxError("numeric() takes one or two arguments");var n=It(e);if(!(n in i))throw new TypeError("Cannot convert "+e+' of type "'+n+'"; valid input types are '+Object.keys(i).join(", "));if(!(t in o))throw new TypeError("Cannot convert "+e+' to type "'+t+'"; valid output types are '+Object.keys(o).join(", "));return t===n?e:o[t](e)}}));var po="size",ho=Lt(po,["typed","config","?matrix"],(e=>{var{typed:t,config:n,matrix:r}=e;return t(po,{Matrix:function(e){return e.create(e.size())},Array:qn,string:function(e){return"Array"===n.matrix?[e.length]:r([e.length])},"number | Complex | BigNumber | Unit | boolean | null":function(e){return"Array"===n.matrix?[]:r?r([]):fo()}})})),mo="zeros",go=Lt(mo,["typed","config","matrix","BigNumber"],(e=>{var{typed:t,config:n,matrix:r,BigNumber:i}=e;return t(mo,{"":function(){return"Array"===n.matrix?o([]):o([],"default")},"...number | BigNumber | string":function(e){if("string"==typeof e[e.length-1]){var t=e.pop();return o(e,t)}return"Array"===n.matrix?o(e):o(e,"default")},Array:o,Matrix:function(e){var t=e.storage();return o(e.valueOf(),t)},"Array | Matrix, string":function(e,t){return o(e.valueOf(),t)}});function o(e,t){var n=function(e){var t=!1;return e.forEach((function(e,n,r){Xe(e)&&(t=!0,r[n]=e.toNumber())})),t}(e),o=n?new i(0):0;if(function(e){e.forEach((function(e){if("number"!=typeof e||!Xt(e)||e<0)throw new Error("Parameters in function zeros must be positive integers")}))}(e),t){var u=r(t);return e.length>0?u.resize(e,o):u}var a=[];return e.length>0?Yn(a,e,o):a}})),vo="concat",Do=Lt(vo,["typed","matrix","isInteger"],(e=>{var{typed:t,matrix:n,isInteger:r}=e;return t(vo,{"...Array | Matrix | number | BigNumber":function(e){var t,i,o=e.length,u=-1,a=!1,s=[];for(t=0;t<o;t++){var c=e[t];if(rt(c)&&(a=!0),Je(c)||Xe(c)){if(t!==o-1)throw new Error("Dimension must be specified as last argument");if(i=u,u=c.valueOf(),!r(u))throw new TypeError("Integer number expected for dimension");if(u<0||t>0&&u>i)throw new Un(u,i+1)}else{var f=zt(c).valueOf(),l=qn(f);if(s[t]=f,i=u,u=l.length-1,t>0&&u!==i)throw new Ln(i+1,u+1)}}if(0===s.length)throw new SyntaxError("At least one matrix expected");for(var p=s.shift();s.length;)p=nr(p,s.shift(),u);return a?n(p):p},"...string":function(e){return e.join("")}})})),yo="divideScalar",wo=Lt(yo,["typed","numeric"],(e=>{var{typed:t,numeric:n}=e;return t(yo,{"number, number":function(e,t){return e/t},"Complex, Complex":function(e,t){return e.div(t)},"BigNumber, BigNumber":function(e,t){return e.div(t)},"Fraction, Fraction":function(e,t){return e.div(t)},"Unit, number | Complex | Fraction | BigNumber | Unit":(e,t)=>e.divide(t),"number | Fraction | Complex | BigNumber, Unit":(e,t)=>t.divideInto(e)})})),bo=Lt("matAlgo03xDSf",["typed"],(e=>{var{typed:t}=e;return function(e,n,r,i){var o=e._data,u=e._size,a=e._datatype||e.getDataType(),s=n._values,c=n._index,f=n._ptr,l=n._size,p=n._datatype||void 0===n._data?n._datatype:n.getDataType();if(u.length!==l.length)throw new Ln(u.length,l.length);if(u[0]!==l[0]||u[1]!==l[1])throw new RangeError("Dimension mismatch. Matrix A ("+u+") must match Matrix B ("+l+")");if(!s)throw new Error("Cannot perform operation on Dense Matrix and Pattern Sparse Matrix");var h,d=u[0],m=u[1],g=0,v=r;"string"==typeof a&&a===p&&"mixed"!==a&&(h=a,g=t.convert(0,h),v=t.find(r,[h,h]));for(var D=[],y=0;y<d;y++)D[y]=[];for(var w=[],b=[],E=0;E<m;E++){for(var x=E+1,A=f[E],C=f[E+1],F=A;F<C;F++){var N=c[F];w[N]=i?v(s[F],o[N][E]):v(o[N][E],s[F]),b[N]=x}for(var _=0;_<d;_++)b[_]===x?D[_][E]=w[_]:D[_][E]=i?v(g,o[_][E]):v(o[_][E],g)}return e.createDenseMatrix({data:D,size:[d,m],datatype:a===e._datatype&&p===n._datatype?h:void 0})}})),Eo=Lt("matAlgo07xSSf",["typed","DenseMatrix"],(e=>{var{typed:t,DenseMatrix:n}=e;return function(e,i,o){var u=e._size,a=e._datatype||void 0===e._data?e._datatype:e.getDataType(),s=i._size,c=i._datatype||void 0===i._data?i._datatype:i.getDataType();if(u.length!==s.length)throw new Ln(u.length,s.length);if(u[0]!==s[0]||u[1]!==s[1])throw new RangeError("Dimension mismatch. Matrix A ("+u+") must match Matrix B ("+s+")");var f,l,p,h=u[0],d=u[1],m=0,g=o;"string"==typeof a&&a===c&&"mixed"!==a&&(f=a,m=t.convert(0,f),g=t.find(o,[f,f]));var v=[];for(l=0;l<h;l++)v[l]=[];var D=[],y=[],w=[],b=[];for(p=0;p<d;p++){var E=p+1;for(r(e,p,w,D,E),r(i,p,b,y,E),l=0;l<h;l++){var x=w[l]===E?D[l]:m,A=b[l]===E?y[l]:m;v[l][p]=g(x,A)}}return new n({data:v,size:[h,d],datatype:a===e._datatype&&c===i._datatype?f:void 0})};function r(e,t,n,r,i){for(var o=e._values,u=e._index,a=e._ptr,s=a[t],c=a[t+1];s<c;s++){var f=u[s];n[f]=i,r[f]=o[s]}}})),xo=Lt("matAlgo11xS0s",["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return function(e,r,i,o){var u=e._values,a=e._index,s=e._ptr,c=e._size,f=e._datatype;if(!u)throw new Error("Cannot perform operation on Pattern Sparse Matrix and Scalar value");var l,p=c[0],h=c[1],d=n,m=0,g=i;"string"==typeof f&&(l=f,d=t.find(n,[l,l]),m=t.convert(0,l),r=t.convert(r,l),g=t.find(i,[l,l]));for(var v=[],D=[],y=[],w=0;w<h;w++){y[w]=D.length;for(var b=s[w],E=s[w+1],x=b;x<E;x++){var A=a[x],C=o?g(r,u[x]):g(u[x],r);d(C,m)||(D.push(A),v.push(C))}}return y[h]=D.length,e.createSparseMatrix({values:v,index:D,ptr:y,size:[p,h],datatype:l})}})),Ao=Lt("matAlgo12xSfs",["typed","DenseMatrix"],(e=>{var{typed:t,DenseMatrix:n}=e;return function(e,r,i,o){var u=e._values,a=e._index,s=e._ptr,c=e._size,f=e._datatype;if(!u)throw new Error("Cannot perform operation on Pattern Sparse Matrix and Scalar value");var l,p=c[0],h=c[1],d=i;"string"==typeof f&&(l=f,r=t.convert(r,l),d=t.find(i,[l,l]));for(var m=[],g=[],v=[],D=0;D<h;D++){for(var y=D+1,w=s[D],b=s[D+1],E=w;E<b;E++){var x=a[E];g[x]=u[E],v[x]=y}for(var A=0;A<p;A++)0===D&&(m[A]=[]),v[A]===y?m[A][D]=o?d(r,g[A]):d(g[A],r):m[A][D]=o?d(r,0):d(0,r)}return new n({data:m,size:[p,h],datatype:l})}})),Co=Lt("matAlgo13xDD",["typed"],(e=>{var{typed:t}=e;return function(e,r,i){var o,u=e._data,a=e._size,s=e._datatype,c=r._data,f=r._size,l=r._datatype,p=[];if(a.length!==f.length)throw new Ln(a.length,f.length);for(var h=0;h<a.length;h++){if(a[h]!==f[h])throw new RangeError("Dimension mismatch. Matrix A ("+a+") must match Matrix B ("+f+")");p[h]=a[h]}var d=i;"string"==typeof s&&s===l&&(o=s,d=t.find(i,[o,o]));var m=p.length>0?n(d,0,p,p[0],u,c):[];return e.createDenseMatrix({data:m,size:p,datatype:o})};function n(e,t,r,i,o,u){var a=[];if(t===r.length-1)for(var s=0;s<i;s++)a[s]=e(o[s],u[s]);else for(var c=0;c<i;c++)a[c]=n(e,t+1,r,r[t+1],o[c],u[c]);return a}})),Fo=Lt("matAlgo14xDs",["typed"],(e=>{var{typed:t}=e;return function(e,r,i,o){var u,a=e._data,s=e._size,c=e._datatype,f=i;"string"==typeof c&&(u=c,r=t.convert(r,u),f=t.find(i,[u,u]));var l=s.length>0?n(f,0,s,s[0],a,r,o):[];return e.createDenseMatrix({data:l,size:zt(s),datatype:u})};function n(e,t,r,i,o,u,a){var s=[];if(t===r.length-1)for(var c=0;c<i;c++)s[c]=a?e(u,o[c]):e(o[c],u);else for(var f=0;f<i;f++)s[f]=n(e,t+1,r,r[t+1],o[f],u,a);return s}})),No=Lt("broadcast",["concat"],(e=>{var{concat:t}=e;return function(e,t){var i=Math.max(e._size.length,t._size.length);if(e._size.length===t._size.length&&e._size.every(((e,n)=>e===t._size[n])))return[e,t];for(var o=n(e._size,i,0),u=n(t._size,i,0),a=[],s=0;s<i;s++)a[s]=Math.max(o[s],u[s]);ir(o,a),ir(u,a);var c=e.clone(),f=t.clone();c._size.length<i?c.reshape(n(c._size,i,1)):f._size.length<i&&f.reshape(n(f._size,i,1));for(var l=0;l<i;l++)c._size[l]<a[l]&&(c=r(c,a[l],l)),f._size[l]<a[l]&&(f=r(f,a[l],l));return[c,f]};function n(e,t,n){return[...Array(t-e.length).fill(n),...e]}function r(e,n,r){return t(...Array(n).fill(e),r)}})),_o=Lt("matrixAlgorithmSuite",["typed","matrix","concat"],(e=>{var{typed:t,matrix:n,concat:r}=e,i=Co({typed:t}),o=Fo({typed:t}),u=No({concat:r});return function(e){var r,a=e.elop,s=e.SD||e.DS;a?(r={"DenseMatrix, DenseMatrix":(e,t)=>i(...u(e,t),a),"Array, Array":(e,t)=>i(...u(n(e),n(t)),a).valueOf(),"Array, DenseMatrix":(e,t)=>i(...u(n(e),t),a),"DenseMatrix, Array":(e,t)=>i(...u(e,n(t)),a)},e.SS&&(r["SparseMatrix, SparseMatrix"]=(t,n)=>e.SS(...u(t,n),a,!1)),e.DS&&(r["DenseMatrix, SparseMatrix"]=(t,n)=>e.DS(...u(t,n),a,!1),r["Array, SparseMatrix"]=(t,r)=>e.DS(...u(n(t),r),a,!1)),s&&(r["SparseMatrix, DenseMatrix"]=(e,t)=>s(...u(t,e),a,!0),r["SparseMatrix, Array"]=(e,t)=>s(...u(n(t),e),a,!0))):(r={"DenseMatrix, DenseMatrix":t.referToSelf((e=>(t,n)=>i(...u(t,n),e))),"Array, Array":t.referToSelf((e=>(t,r)=>i(...u(n(t),n(r)),e).valueOf())),"Array, DenseMatrix":t.referToSelf((e=>(t,r)=>i(...u(n(t),r),e))),"DenseMatrix, Array":t.referToSelf((e=>(t,r)=>i(...u(t,n(r)),e)))},e.SS&&(r["SparseMatrix, SparseMatrix"]=t.referToSelf((t=>(n,r)=>e.SS(...u(n,r),t,!1)))),e.DS&&(r["DenseMatrix, SparseMatrix"]=t.referToSelf((t=>(n,r)=>e.DS(...u(n,r),t,!1))),r["Array, SparseMatrix"]=t.referToSelf((t=>(r,i)=>e.DS(...u(n(r),i),t,!1)))),s&&(r["SparseMatrix, DenseMatrix"]=t.referToSelf((e=>(t,n)=>s(...u(n,t),e,!0))),r["SparseMatrix, Array"]=t.referToSelf((e=>(t,r)=>s(...u(n(r),t),e,!0)))));var c=e.scalar||"any";(e.Ds||e.Ss)&&(a?(r["DenseMatrix,"+c]=(e,t)=>o(e,t,a,!1),r[c+", DenseMatrix"]=(e,t)=>o(t,e,a,!0),r["Array,"+c]=(e,t)=>o(n(e),t,a,!1).valueOf(),r[c+", Array"]=(e,t)=>o(n(t),e,a,!0).valueOf()):(r["DenseMatrix,"+c]=t.referToSelf((e=>(t,n)=>o(t,n,e,!1))),r[c+", DenseMatrix"]=t.referToSelf((e=>(t,n)=>o(n,t,e,!0))),r["Array,"+c]=t.referToSelf((e=>(t,r)=>o(n(t),r,e,!1).valueOf())),r[c+", Array"]=t.referToSelf((e=>(t,r)=>o(n(r),t,e,!0).valueOf()))));var f=void 0!==e.sS?e.sS:e.Ss;return a?(e.Ss&&(r["SparseMatrix,"+c]=(t,n)=>e.Ss(t,n,a,!1)),f&&(r[c+", SparseMatrix"]=(e,t)=>f(t,e,a,!0))):(e.Ss&&(r["SparseMatrix,"+c]=t.referToSelf((t=>(n,r)=>e.Ss(n,r,t,!1)))),f&&(r[c+", SparseMatrix"]=t.referToSelf((e=>(t,n)=>f(n,t,e,!0))))),a&&a.signatures&&Pt(r,a.signatures),r}})),Mo="equal",So=Lt(Mo,["typed","matrix","equalScalar","DenseMatrix","concat"],(e=>{var{typed:t,matrix:n,equalScalar:r,DenseMatrix:i,concat:o}=e,u=bo({typed:t}),a=Eo({typed:t,DenseMatrix:i}),s=Ao({typed:t,DenseMatrix:i}),c=_o({typed:t,matrix:n,concat:o});return t(Mo,Oo({typed:t,equalScalar:r}),c({elop:r,SS:a,DS:u,Ss:s}))})),Oo=Lt(Mo,["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return t(Mo,{"any, any":function(e,t){return null===e?null===t:null===t?null===e:void 0===e?void 0===t:void 0===t?void 0===e:n(e,t)}})})),To="hasNumericValue",Bo=Lt(To,["typed","isNumeric"],(e=>{var{typed:t,isNumeric:n}=e;return t(To,{boolean:()=>!0,string:function(e){return e.trim().length>0&&!isNaN(Number(e))},any:function(e){return n(e)}})})),Ro="identity",Io=Lt(Ro,["typed","config","matrix","BigNumber","DenseMatrix","SparseMatrix"],(e=>{var{typed:t,config:n,matrix:r,BigNumber:i,DenseMatrix:o,SparseMatrix:u}=e;return t(Ro,{"":function(){return"Matrix"===n.matrix?r([]):[]},string:function(e){return r(e)},"number | BigNumber":function(e){return s(e,e,"Matrix"===n.matrix?"dense":void 0)},"number | BigNumber, string":function(e,t){return s(e,e,t)},"number | BigNumber, number | BigNumber":function(e,t){return s(e,t,"Matrix"===n.matrix?"dense":void 0)},"number | BigNumber, number | BigNumber, string":function(e,t,n){return s(e,t,n)},Array:function(e){return a(e)},"Array, string":function(e,t){return a(e,t)},Matrix:function(e){return a(e.valueOf(),e.storage())},"Matrix, string":function(e,t){return a(e.valueOf(),t)}});function a(e,t){switch(e.length){case 0:return t?r(t):[];case 1:return s(e[0],e[0],t);case 2:return s(e[0],e[1],t);default:throw new Error("Vector containing two values expected")}}function s(e,t,n){var r=Xe(e)||Xe(t)?i:null;if(Xe(e)&&(e=e.toNumber()),Xe(t)&&(t=t.toNumber()),!Xt(e)||e<1)throw new Error("Parameters in function identity must be positive integers");if(!Xt(t)||t<1)throw new Error("Parameters in function identity must be positive integers");var a=r?new i(1):1,s=r?new r(0):0,c=[e,t];if(n){if("sparse"===n)return u.diagonal(c,a,0,s);if("dense"===n)return o.diagonal(c,a,0,s);throw new TypeError('Unknown matrix type "'.concat(n,'"'))}for(var f=Yn([],c,s),l=e<t?e:t,p=0;p<l;p++)f[p][p]=a;return f}})),zo=Lt("matAlgo01xDSid",["typed"],(e=>{var{typed:t}=e;return function(e,n,r,i){var o=e._data,u=e._size,a=e._datatype||e.getDataType(),s=n._values,c=n._index,f=n._ptr,l=n._size,p=n._datatype||void 0===n._data?n._datatype:n.getDataType();if(u.length!==l.length)throw new Ln(u.length,l.length);if(u[0]!==l[0]||u[1]!==l[1])throw new RangeError("Dimension mismatch. Matrix A ("+u+") must match Matrix B ("+l+")");if(!s)throw new Error("Cannot perform operation on Dense Matrix and Pattern Sparse Matrix");var h,d,m=u[0],g=u[1],v="string"==typeof a&&"mixed"!==a&&a===p?a:void 0,D=v?t.find(r,[v,v]):r,y=[];for(h=0;h<m;h++)y[h]=[];var w=[],b=[];for(d=0;d<g;d++){for(var E=d+1,x=f[d],A=f[d+1],C=x;C<A;C++)w[h=c[C]]=i?D(s[C],o[h][d]):D(o[h][d],s[C]),b[h]=E;for(h=0;h<m;h++)b[h]===E?y[h][d]=w[h]:y[h][d]=o[h][d]}return e.createDenseMatrix({data:y,size:[m,g],datatype:a===e._datatype&&p===n._datatype?v:void 0})}})),Po=Lt("matAlgo10xSids",["typed","DenseMatrix"],(e=>{var{typed:t,DenseMatrix:n}=e;return function(e,r,i,o){var u=e._values,a=e._index,s=e._ptr,c=e._size,f=e._datatype;if(!u)throw new Error("Cannot perform operation on Pattern Sparse Matrix and Scalar value");var l,p=c[0],h=c[1],d=i;"string"==typeof f&&(l=f,r=t.convert(r,l),d=t.find(i,[l,l]));for(var m=[],g=[],v=[],D=0;D<h;D++){for(var y=D+1,w=s[D],b=s[D+1],E=w;E<b;E++){var x=a[E];g[x]=u[E],v[x]=y}for(var A=0;A<p;A++)0===D&&(m[A]=[]),v[A]===y?m[A][D]=o?d(r,g[A]):d(g[A],r):m[A][D]=r}return new n({data:m,size:[p,h],datatype:l})}}));function ko(e,t,n,r){if(!(this instanceof ko))throw new SyntaxError("Constructor must be called with the new operator");this.fn=e,this.count=t,this.min=n,this.max=r,this.message="Wrong number of arguments in function "+e+" ("+t+" provided, "+n+(null!=r?"-"+r:"")+" expected)",this.stack=(new Error).stack}ko.prototype=new Error,ko.prototype.constructor=Error,ko.prototype.name="ArgumentsError",ko.prototype.isArgumentsError=!0;var jo="Number of decimals in function round must be an integer",Lo="round",Uo=Lt(Lo,["typed","config","matrix","equalScalar","zeros","BigNumber","DenseMatrix"],(e=>{var{typed:t,config:n,matrix:r,equalScalar:i,zeros:o,BigNumber:u,DenseMatrix:a}=e,s=xo({typed:t,equalScalar:i}),c=Ao({typed:t,DenseMatrix:a}),f=Fo({typed:t});function l(e){return Math.abs(an(e).exponent)}return t(Lo,{number:function(e){var t=Hr(e,l(n.epsilon));return Hr(hn(e,t,n.epsilon)?t:e)},"number, number":function(e,t){var r=l(n.epsilon);if(t>=r)return Hr(e,t);var i=Hr(e,r);return Hr(hn(e,i,n.epsilon)?i:e,t)},"number, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(jo);return new u(e).toDecimalPlaces(t.toNumber())},Complex:function(e){return e.round()},"Complex, number":function(e,t){if(t%1)throw new TypeError(jo);return e.round(t)},"Complex, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(jo);var n=t.toNumber();return e.round(n)},BigNumber:function(e){var t=new u(e).toDecimalPlaces(l(n.epsilon));return(xi(e,t,n.epsilon)?t:e).toDecimalPlaces(0)},"BigNumber, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(jo);var r=l(n.epsilon);if(t>=r)return e.toDecimalPlaces(t.toNumber());var i=e.toDecimalPlaces(r);return(xi(e,i,n.epsilon)?i:e).toDecimalPlaces(t.toNumber())},Fraction:function(e){return e.round()},"Fraction, number":function(e,t){if(t%1)throw new TypeError(jo);return e.round(t)},"Fraction, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(jo);return e.round(t.toNumber())},"Unit, number, Unit":t.referToSelf((e=>function(t,n,r){var i=t.toNumeric(r);return r.multiply(e(i,n))})),"Unit, BigNumber, Unit":t.referToSelf((e=>(t,n,r)=>e(t,n.toNumber(),r))),"Unit, Unit":t.referToSelf((e=>(t,n)=>e(t,0,n))),"Array | Matrix, number, Unit":t.referToSelf((e=>(t,n,r)=>xr(t,(t=>e(t,n,r)),!0))),"Array | Matrix, BigNumber, Unit":t.referToSelf((e=>(t,n,r)=>e(t,n.toNumber(),r))),"Array | Matrix, Unit":t.referToSelf((e=>(t,n)=>e(t,0,n))),"Array | Matrix":t.referToSelf((e=>t=>xr(t,e,!0))),"SparseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>s(t,n,e,!1))),"DenseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>f(t,n,e,!1))),"Array, number | BigNumber":t.referToSelf((e=>(t,n)=>f(r(t),n,e,!1).valueOf())),"number | Complex | BigNumber | Fraction, SparseMatrix":t.referToSelf((e=>(t,n)=>i(t,0)?o(n.size(),n.storage()):c(n,t,e,!0))),"number | Complex | BigNumber | Fraction, DenseMatrix":t.referToSelf((e=>(t,n)=>i(t,0)?o(n.size(),n.storage()):f(n,t,e,!0))),"number | Complex | BigNumber | Fraction, Array":t.referToSelf((e=>(t,n)=>f(r(n),t,e,!0).valueOf()))})})),qo=Lt("matAlgo05xSfSf",["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return function(e,r,i){var o=e._values,u=e._index,a=e._ptr,s=e._size,c=e._datatype||void 0===e._data?e._datatype:e.getDataType(),f=r._values,l=r._index,p=r._ptr,h=r._size,d=r._datatype||void 0===r._data?r._datatype:r.getDataType();if(s.length!==h.length)throw new Ln(s.length,h.length);if(s[0]!==h[0]||s[1]!==h[1])throw new RangeError("Dimension mismatch. Matrix A ("+s+") must match Matrix B ("+h+")");var m,g=s[0],v=s[1],D=n,y=0,w=i;"string"==typeof c&&c===d&&"mixed"!==c&&(m=c,D=t.find(n,[m,m]),y=t.convert(0,m),w=t.find(i,[m,m]));var b,E,x,A,C=o&&f?[]:void 0,F=[],N=[],_=C?[]:void 0,M=C?[]:void 0,S=[],O=[];for(E=0;E<v;E++){N[E]=F.length;var T=E+1;for(x=a[E],A=a[E+1];x<A;x++)b=u[x],F.push(b),S[b]=T,_&&(_[b]=o[x]);for(x=p[E],A=p[E+1];x<A;x++)S[b=l[x]]!==T&&F.push(b),O[b]=T,M&&(M[b]=f[x]);if(C)for(x=N[E];x<F.length;){var B=S[b=F[x]],R=O[b];if(B===T||R===T){var I=w(B===T?_[b]:y,R===T?M[b]:y);D(I,y)?F.splice(x,1):(C.push(I),x++)}}}return N[v]=F.length,e.createSparseMatrix({values:C,index:F,ptr:N,size:[g,v],datatype:c===e._datatype&&d===r._datatype?m:void 0})}})),Ho="subtract",Go=Lt(Ho,["typed","matrix","equalScalar","subtractScalar","unaryMinus","DenseMatrix","concat"],(e=>{var{typed:t,matrix:n,equalScalar:r,subtractScalar:i,unaryMinus:o,DenseMatrix:u,concat:a}=e,s=zo({typed:t}),c=bo({typed:t}),f=qo({typed:t,equalScalar:r}),l=Po({typed:t,DenseMatrix:u}),p=Ao({typed:t,DenseMatrix:u}),h=_o({typed:t,matrix:n,concat:a});return t(Ho,{"any, any":i},h({elop:i,SS:f,DS:s,SD:c,Ss:p,sS:l}))})),Vo="unequal",Yo=(Lt(Vo,["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return t(Vo,{"any, any":function(e,t){return null===e?null!==t:null===t?null!==e:void 0===e?void 0!==t:void 0===t?void 0!==e:!n(e,t)}})})),Lt("matAlgo04xSidSid",["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return function(e,r,i){var o=e._values,u=e._index,a=e._ptr,s=e._size,c=e._datatype||void 0===e._data?e._datatype:e.getDataType(),f=r._values,l=r._index,p=r._ptr,h=r._size,d=r._datatype||void 0===r._data?r._datatype:r.getDataType();if(s.length!==h.length)throw new Ln(s.length,h.length);if(s[0]!==h[0]||s[1]!==h[1])throw new RangeError("Dimension mismatch. Matrix A ("+s+") must match Matrix B ("+h+")");var m,g=s[0],v=s[1],D=n,y=0,w=i;"string"==typeof c&&c===d&&"mixed"!==c&&(m=c,D=t.find(n,[m,m]),y=t.convert(0,m),w=t.find(i,[m,m]));var b,E,x,A,C,F=o&&f?[]:void 0,N=[],_=[],M=o&&f?[]:void 0,S=o&&f?[]:void 0,O=[],T=[];for(E=0;E<v;E++){_[E]=N.length;var B=E+1;for(A=a[E],C=a[E+1],x=A;x<C;x++)b=u[x],N.push(b),O[b]=B,M&&(M[b]=o[x]);for(A=p[E],C=p[E+1],x=A;x<C;x++)if(O[b=l[x]]===B){if(M){var R=w(M[b],f[x]);D(R,y)?O[b]=null:M[b]=R}}else N.push(b),T[b]=B,S&&(S[b]=f[x]);if(M&&S)for(x=_[E];x<N.length;)O[b=N[x]]===B?(F[x]=M[b],x++):T[b]===B?(F[x]=S[b],x++):N.splice(x,1)}return _[v]=N.length,e.createSparseMatrix({values:F,index:N,ptr:_,size:[g,v],datatype:c===e._datatype&&d===r._datatype?m:void 0})}}))),Wo=Lt("add",["typed","matrix","addScalar","equalScalar","DenseMatrix","SparseMatrix","concat"],(e=>{var{typed:t,matrix:n,addScalar:r,equalScalar:i,DenseMatrix:o,SparseMatrix:u,concat:a}=e,s=zo({typed:t}),c=Yo({typed:t,equalScalar:i}),f=Po({typed:t,DenseMatrix:o}),l=_o({typed:t,matrix:n,concat:a});return t("add",{"any, any":r,"any, any, ...any":t.referToSelf((e=>(t,n,r)=>{for(var i=e(t,n),o=0;o<r.length;o++)i=e(i,r[o]);return i}))},l({elop:r,DS:s,SS:c,Ss:f}))}));jn.signature="any, any";var $o=Lt("dot",["typed","addScalar","multiplyScalar","conj","size"],(e=>{var{typed:t,addScalar:n,multiplyScalar:r,conj:i,size:o}=e;return t("dot",{"Array | DenseMatrix, Array | DenseMatrix":function(e,o){var s=u(e,o),c=rt(e)?e._data:e,f=rt(e)?e._datatype||e.getDataType():void 0,l=rt(o)?o._data:o,p=rt(o)?o._datatype||o.getDataType():void 0,h=2===a(e).length,d=2===a(o).length,m=n,g=r;if(f&&p&&f===p&&"string"==typeof f&&"mixed"!==f){var v=f;m=t.find(n,[v,v]),g=t.find(r,[v,v])}if(!h&&!d){for(var D=g(i(c[0]),l[0]),y=1;y<s;y++)D=m(D,g(i(c[y]),l[y]));return D}if(!h&&d){for(var w=g(i(c[0]),l[0][0]),b=1;b<s;b++)w=m(w,g(i(c[b]),l[b][0]));return w}if(h&&!d){for(var E=g(i(c[0][0]),l[0]),x=1;x<s;x++)E=m(E,g(i(c[x][0]),l[x]));return E}if(h&&d){for(var A=g(i(c[0][0]),l[0][0]),C=1;C<s;C++)A=m(A,g(i(c[C][0]),l[C][0]));return A}},"SparseMatrix, SparseMatrix":function(e,t){u(e,t);var i=e._index,o=e._values,a=t._index,s=t._values,c=0,f=n,l=r,p=0,h=0;for(;p<i.length&&h<a.length;){var d=i[p],m=a[h];d<m?p++:d>m?h++:d===m&&(c=f(c,l(o[p],s[h])),p++,h++)}return c}});function u(e,t){var n,r,i=a(e),o=a(t);if(1===i.length)n=i[0];else{if(2!==i.length||1!==i[1])throw new RangeError("Expected a column vector, instead got a matrix of size ("+i.join(", ")+")");n=i[0]}if(1===o.length)r=o[0];else{if(2!==o.length||1!==o[1])throw new RangeError("Expected a column vector, instead got a matrix of size ("+o.join(", ")+")");r=o[0]}if(n!==r)throw new RangeError("Vectors must have equal length ("+n+" != "+r+")");if(0===n)throw new RangeError("Cannot calculate the dot product of empty vectors");return n}function a(e){return rt(e)?e.size():o(e)}})),Zo="floor",Jo=["typed","config","round","matrix","equalScalar","zeros","DenseMatrix"],Xo=Lt(Zo,["typed","config","round"],(e=>{var{typed:t,config:n,round:r}=e;return t(Zo,{number:function(e){return hn(e,r(e),n.epsilon)?r(e):Math.floor(e)},"number, number":function(e,t){if(hn(e,r(e,t),n.epsilon))return r(e,t);var[i,o]="".concat(e,"e").split("e"),u=Math.floor(Number("".concat(i,"e").concat(Number(o)+t)));return[i,o]="".concat(u,"e").split("e"),Number("".concat(i,"e").concat(Number(o)-t))}})})),Qo=Lt(Zo,Jo,(e=>{var{typed:t,config:n,round:r,matrix:i,equalScalar:o,zeros:u,DenseMatrix:a}=e,s=xo({typed:t,equalScalar:o}),c=Ao({typed:t,DenseMatrix:a}),f=Fo({typed:t}),l=Xo({typed:t,config:n,round:r});return t("floor",{number:l.signatures.number,"number,number":l.signatures["number,number"],Complex:function(e){return e.floor()},"Complex, number":function(e,t){return e.floor(t)},"Complex, BigNumber":function(e,t){return e.floor(t.toNumber())},BigNumber:function(e){return xi(e,r(e),n.epsilon)?r(e):e.floor()},"BigNumber, BigNumber":function(e,t){return xi(e,r(e,t),n.epsilon)?r(e,t):e.toDecimalPlaces(t.toNumber(),Ze.ROUND_FLOOR)},Fraction:function(e){return e.floor()},"Fraction, number":function(e,t){return e.floor(t)},"Fraction, BigNumber":function(e,t){return e.floor(t.toNumber())},"Array | Matrix":t.referToSelf((e=>t=>xr(t,e,!0))),"Array, number | BigNumber":t.referToSelf((e=>(t,n)=>xr(t,(t=>e(t,n)),!0))),"SparseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>s(t,n,e,!1))),"DenseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>f(t,n,e,!1))),"number | Complex | Fraction | BigNumber, Array":t.referToSelf((e=>(t,n)=>f(i(n),t,e,!0).valueOf())),"number | Complex | Fraction | BigNumber, Matrix":t.referToSelf((e=>(t,n)=>o(t,0)?u(n.size(),n.storage()):"dense"===n.storage()?f(n,t,e,!0):c(n,t,e,!0)))})})),Ko="number | BigNumber | Fraction | Matrix | Array";"".concat(Ko,", ").concat(Ko,", ...").concat(Ko);var eu="multiply",tu=Lt(eu,["typed","matrix","addScalar","multiplyScalar","equalScalar","dot"],(e=>{var{typed:t,matrix:n,addScalar:r,multiplyScalar:i,equalScalar:o,dot:u}=e,a=xo({typed:t,equalScalar:o}),s=Fo({typed:t});function c(e,t){switch(e.length){case 1:switch(t.length){case 1:if(e[0]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Vectors must have the same length");break;case 2:if(e[0]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Vector length ("+e[0]+") must match Matrix rows ("+t[0]+")");break;default:throw new Error("Can only multiply a 1 or 2 dimensional matrix (Matrix B has "+t.length+" dimensions)")}break;case 2:switch(t.length){case 1:if(e[1]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Matrix columns ("+e[1]+") must match Vector length ("+t[0]+")");break;case 2:if(e[1]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Matrix A columns ("+e[1]+") must match Matrix B rows ("+t[0]+")");break;default:throw new Error("Can only multiply a 1 or 2 dimensional matrix (Matrix B has "+t.length+" dimensions)")}break;default:throw new Error("Can only multiply a 1 or 2 dimensional matrix (Matrix A has "+e.length+" dimensions)")}}function f(e,n){if("dense"!==n.storage())throw new Error("Support for SparseMatrix not implemented");return function(e,n){var o,u=e._data,a=e._size,s=e._datatype||e.getDataType(),c=n._data,f=n._size,l=n._datatype||n.getDataType(),p=a[0],h=f[1],d=r,m=i;s&&l&&s===l&&"string"==typeof s&&"mixed"!==s&&(o=s,d=t.find(r,[o,o]),m=t.find(i,[o,o]));for(var g=[],v=0;v<h;v++){for(var D=m(u[0],c[0][v]),y=1;y<p;y++)D=d(D,m(u[y],c[y][v]));g[v]=D}return e.createDenseMatrix({data:g,size:[h],datatype:s===e._datatype&&l===n._datatype?o:void 0})}(e,n)}var l=t("_multiplyMatrixVector",{"DenseMatrix, any":function(e,n){var o,u=e._data,a=e._size,s=e._datatype||e.getDataType(),c=n._data,f=n._datatype||n.getDataType(),l=a[0],p=a[1],h=r,d=i;s&&f&&s===f&&"string"==typeof s&&"mixed"!==s&&(o=s,h=t.find(r,[o,o]),d=t.find(i,[o,o]));for(var m=[],g=0;g<l;g++){for(var v=u[g],D=d(v[0],c[0]),y=1;y<p;y++)D=h(D,d(v[y],c[y]));m[g]=D}return e.createDenseMatrix({data:m,size:[l],datatype:s===e._datatype&&f===n._datatype?o:void 0})},"SparseMatrix, any":function(e,n){var u=e._values,a=e._index,s=e._ptr,c=e._datatype||void 0===e._data?e._datatype:e.getDataType();if(!u)throw new Error("Cannot multiply Pattern only Matrix times Dense Matrix");var f,l=n._data,p=n._datatype||n.getDataType(),h=e._size[0],d=n._size[0],m=[],g=[],v=[],D=r,y=i,w=o,b=0;c&&p&&c===p&&"string"==typeof c&&"mixed"!==c&&(f=c,D=t.find(r,[f,f]),y=t.find(i,[f,f]),w=t.find(o,[f,f]),b=t.convert(0,f));var E=[],x=[];v[0]=0;for(var A=0;A<d;A++){var C=l[A];if(!w(C,b))for(var F=s[A],N=s[A+1],_=F;_<N;_++){var M=a[_];x[M]?E[M]=D(E[M],y(C,u[_])):(x[M]=!0,g.push(M),E[M]=y(C,u[_]))}}for(var S=g.length,O=0;O<S;O++){var T=g[O];m[O]=E[T]}return v[1]=g.length,e.createSparseMatrix({values:m,index:g,ptr:v,size:[h,1],datatype:c===e._datatype&&p===n._datatype?f:void 0})}}),p=t("_multiplyMatrixMatrix",{"DenseMatrix, DenseMatrix":function(e,n){var o,u=e._data,a=e._size,s=e._datatype||e.getDataType(),c=n._data,f=n._size,l=n._datatype||n.getDataType(),p=a[0],h=a[1],d=f[1],m=r,g=i;s&&l&&s===l&&"string"==typeof s&&"mixed"!==s&&"mixed"!==s&&(o=s,m=t.find(r,[o,o]),g=t.find(i,[o,o]));for(var v=[],D=0;D<p;D++){var y=u[D];v[D]=[];for(var w=0;w<d;w++){for(var b=g(y[0],c[0][w]),E=1;E<h;E++)b=m(b,g(y[E],c[E][w]));v[D][w]=b}}return e.createDenseMatrix({data:v,size:[p,d],datatype:s===e._datatype&&l===n._datatype?o:void 0})},"DenseMatrix, SparseMatrix":function(e,n){var u=e._data,a=e._size,s=e._datatype||e.getDataType(),c=n._values,f=n._index,l=n._ptr,p=n._size,h=n._datatype||void 0===n._data?n._datatype:n.getDataType();if(!c)throw new Error("Cannot multiply Dense Matrix times Pattern only Matrix");var d,m=a[0],g=p[1],v=r,D=i,y=o,w=0;s&&h&&s===h&&"string"==typeof s&&"mixed"!==s&&(d=s,v=t.find(r,[d,d]),D=t.find(i,[d,d]),y=t.find(o,[d,d]),w=t.convert(0,d));for(var b=[],E=[],x=[],A=n.createSparseMatrix({values:b,index:E,ptr:x,size:[m,g],datatype:s===e._datatype&&h===n._datatype?d:void 0}),C=0;C<g;C++){x[C]=E.length;var F=l[C],N=l[C+1];if(N>F)for(var _=0,M=0;M<m;M++){for(var S=M+1,O=void 0,T=F;T<N;T++){var B=f[T];_!==S?(O=D(u[M][B],c[T]),_=S):O=v(O,D(u[M][B],c[T]))}_!==S||y(O,w)||(E.push(M),b.push(O))}}return x[g]=E.length,A},"SparseMatrix, DenseMatrix":function(e,n){var u=e._values,a=e._index,s=e._ptr,c=e._datatype||void 0===e._data?e._datatype:e.getDataType();if(!u)throw new Error("Cannot multiply Pattern only Matrix times Dense Matrix");var f,l=n._data,p=n._datatype||n.getDataType(),h=e._size[0],d=n._size[0],m=n._size[1],g=r,v=i,D=o,y=0;c&&p&&c===p&&"string"==typeof c&&"mixed"!==c&&(f=c,g=t.find(r,[f,f]),v=t.find(i,[f,f]),D=t.find(o,[f,f]),y=t.convert(0,f));for(var w=[],b=[],E=[],x=e.createSparseMatrix({values:w,index:b,ptr:E,size:[h,m],datatype:c===e._datatype&&p===n._datatype?f:void 0}),A=[],C=[],F=0;F<m;F++){E[F]=b.length;for(var N=F+1,_=0;_<d;_++){var M=l[_][F];if(!D(M,y))for(var S=s[_],O=s[_+1],T=S;T<O;T++){var B=a[T];C[B]!==N?(C[B]=N,b.push(B),A[B]=v(M,u[T])):A[B]=g(A[B],v(M,u[T]))}}for(var R=E[F],I=b.length,z=R;z<I;z++){var P=b[z];w[z]=A[P]}}return E[m]=b.length,x},"SparseMatrix, SparseMatrix":function(e,n){var o,u=e._values,a=e._index,s=e._ptr,c=e._datatype||void 0===e._data?e._datatype:e.getDataType(),f=n._values,l=n._index,p=n._ptr,h=n._datatype||void 0===n._data?n._datatype:n.getDataType(),d=e._size[0],m=n._size[1],g=u&&f,v=r,D=i;c&&h&&c===h&&"string"==typeof c&&"mixed"!==c&&(o=c,v=t.find(r,[o,o]),D=t.find(i,[o,o]));for(var y,w,b,E,x,A,C,F,N=g?[]:void 0,_=[],M=[],S=e.createSparseMatrix({values:N,index:_,ptr:M,size:[d,m],datatype:c===e._datatype&&h===n._datatype?o:void 0}),O=g?[]:void 0,T=[],B=0;B<m;B++){M[B]=_.length;var R=B+1;for(x=p[B],A=p[B+1],E=x;E<A;E++)if(F=l[E],g)for(w=s[F],b=s[F+1],y=w;y<b;y++)T[C=a[y]]!==R?(T[C]=R,_.push(C),O[C]=D(f[E],u[y])):O[C]=v(O[C],D(f[E],u[y]));else for(w=s[F],b=s[F+1],y=w;y<b;y++)T[C=a[y]]!==R&&(T[C]=R,_.push(C));if(g)for(var I=M[B],z=_.length,P=I;P<z;P++){var k=_[P];N[P]=O[k]}}return M[m]=_.length,S}});return t(eu,i,{"Array, Array":t.referTo("Matrix, Matrix",(e=>(t,r)=>{c(qn(t),qn(r));var i=e(n(t),n(r));return rt(i)?i.valueOf():i})),"Matrix, Matrix":function(e,t){var n=e.size(),r=t.size();return c(n,r),1===n.length?1===r.length?function(e,t,n){if(0===n)throw new Error("Cannot multiply two empty vectors");return u(e,t)}(e,t,n[0]):f(e,t):1===r.length?l(e,t):p(e,t)},"Matrix, Array":t.referTo("Matrix,Matrix",(e=>(t,r)=>e(t,n(r)))),"Array, Matrix":t.referToSelf((e=>(t,r)=>e(n(t,r.storage()),r))),"SparseMatrix, any":function(e,t){return a(e,t,i,!1)},"DenseMatrix, any":function(e,t){return s(e,t,i,!1)},"any, SparseMatrix":function(e,t){return a(t,e,i,!0)},"any, DenseMatrix":function(e,t){return s(t,e,i,!0)},"Array, any":function(e,t){return s(n(e),t,i,!1).valueOf()},"any, Array":function(e,t){return s(n(t),e,i,!0).valueOf()},"any, any":i,"any, any, ...any":t.referToSelf((e=>(t,n,r)=>{for(var i=e(t,n),o=0;o<r.length;o++)i=e(i,r[o]);return i}))})}));var nu="ceil",ru=["typed","config","round","matrix","equalScalar","zeros","DenseMatrix"],iu=Lt(nu,["typed","config","round"],(e=>{var{typed:t,config:n,round:r}=e;return t(nu,{number:function(e){return hn(e,r(e),n.epsilon)?r(e):Math.ceil(e)},"number, number":function(e,t){if(hn(e,r(e,t),n.epsilon))return r(e,t);var[i,o]="".concat(e,"e").split("e"),u=Math.ceil(Number("".concat(i,"e").concat(Number(o)+t)));return[i,o]="".concat(u,"e").split("e"),Number("".concat(i,"e").concat(Number(o)-t))}})})),ou=Lt(nu,ru,(e=>{var{typed:t,config:n,round:r,matrix:i,equalScalar:o,zeros:u,DenseMatrix:a}=e,s=xo({typed:t,equalScalar:o}),c=Ao({typed:t,DenseMatrix:a}),f=Fo({typed:t}),l=iu({typed:t,config:n,round:r});return t("ceil",{number:l.signatures.number,"number,number":l.signatures["number,number"],Complex:function(e){return e.ceil()},"Complex, number":function(e,t){return e.ceil(t)},"Complex, BigNumber":function(e,t){return e.ceil(t.toNumber())},BigNumber:function(e){return xi(e,r(e),n.epsilon)?r(e):e.ceil()},"BigNumber, BigNumber":function(e,t){return xi(e,r(e,t),n.epsilon)?r(e,t):e.toDecimalPlaces(t.toNumber(),Ze.ROUND_CEIL)},Fraction:function(e){return e.ceil()},"Fraction, number":function(e,t){return e.ceil(t)},"Fraction, BigNumber":function(e,t){return e.ceil(t.toNumber())},"Array | Matrix":t.referToSelf((e=>t=>xr(t,e,!0))),"Array, number | BigNumber":t.referToSelf((e=>(t,n)=>xr(t,(t=>e(t,n)),!0))),"SparseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>s(t,n,e,!1))),"DenseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>f(t,n,e,!1))),"number | Complex | Fraction | BigNumber, Array":t.referToSelf((e=>(t,n)=>f(i(n),t,e,!0).valueOf())),"number | Complex | Fraction | BigNumber, Matrix":t.referToSelf((e=>(t,n)=>o(t,0)?u(n.size(),n.storage()):"dense"===n.storage()?f(n,t,e,!0):c(n,t,e,!0)))})}));n(1880);var uu=Lt("det",["typed","matrix","subtractScalar","multiply","divideScalar","isZero","unaryMinus"],(e=>{var{typed:t,matrix:n,subtractScalar:r,multiply:i,divideScalar:o,isZero:u,unaryMinus:a}=e;return t("det",{any:function(e){return zt(e)},"Array | Matrix":function(e){var t;switch((t=rt(e)?e.size():Array.isArray(e)?(e=n(e)).size():[]).length){case 0:return zt(e);case 1:if(1===t[0])return zt(e.valueOf()[0]);if(0===t[0])return 1;throw new RangeError("Matrix must be square (size: "+In(t)+")");case 2:var s=t[0],c=t[1];if(s===c)return function(e,t){if(1===t)return zt(e[0][0]);if(2===t)return r(i(e[0][0],e[1][1]),i(e[1][0],e[0][1]));for(var n=!1,s=new Array(t).fill(0).map(((e,t)=>t)),c=0;c<t;c++){var f=s[c];if(u(e[f][c])){var l=void 0;for(l=c+1;l<t;l++)if(!u(e[s[l]][c])){f=s[l],s[l]=s[c],s[c]=f,n=!n;break}if(l===t)return e[f][c]}for(var p=e[f][c],h=0===c?1:e[s[c-1]][c-1],d=c+1;d<t;d++)for(var m=s[d],g=c+1;g<t;g++)e[m][g]=o(r(i(e[m][g],p),i(e[m][c],e[f][g])),h)}var v=e[s[t-1]][t-1];return n?a(v):v}(e.clone().valueOf(),s);if(0===c)return 1;throw new RangeError("Matrix must be square (size: "+In(t)+")");default:throw new RangeError("Matrix must be two dimensional (size: "+In(t)+")")}}})})),au="fix",su=["typed","Complex","matrix","ceil","floor","equalScalar","zeros","DenseMatrix"],cu=Lt(au,["typed","ceil","floor"],(e=>{var{typed:t,ceil:n,floor:r}=e;return t(au,{number:function(e){return e>0?r(e):n(e)},"number, number":function(e,t){return e>0?r(e,t):n(e,t)}})})),fu=Lt(au,su,(e=>{var{typed:t,Complex:n,matrix:r,ceil:i,floor:o,equalScalar:u,zeros:a,DenseMatrix:s}=e,c=Ao({typed:t,DenseMatrix:s}),f=Fo({typed:t}),l=cu({typed:t,ceil:i,floor:o});return t("fix",{number:l.signatures.number,"number, number | BigNumber":l.signatures["number,number"],Complex:function(e){return new n(e.re>0?Math.floor(e.re):Math.ceil(e.re),e.im>0?Math.floor(e.im):Math.ceil(e.im))},"Complex, number":function(e,t){return new n(e.re>0?o(e.re,t):i(e.re,t),e.im>0?o(e.im,t):i(e.im,t))},"Complex, BigNumber":function(e,t){var r=t.toNumber();return new n(e.re>0?o(e.re,r):i(e.re,r),e.im>0?o(e.im,r):i(e.im,r))},BigNumber:function(e){return e.isNegative()?i(e):o(e)},"BigNumber, number | BigNumber":function(e,t){return e.isNegative()?i(e,t):o(e,t)},Fraction:function(e){return e.s<0?e.ceil():e.floor()},"Fraction, number | BigNumber":function(e,t){return e.s<0?i(e,t):o(e,t)},"Array | Matrix":t.referToSelf((e=>t=>xr(t,e,!0))),"Array | Matrix, number | BigNumber":t.referToSelf((e=>(t,n)=>xr(t,(t=>e(t,n)),!0))),"number | Complex | Fraction | BigNumber, Array":t.referToSelf((e=>(t,n)=>f(r(n),t,e,!0).valueOf())),"number | Complex | Fraction | BigNumber, Matrix":t.referToSelf((e=>(t,n)=>u(t,0)?a(n.size(),n.storage()):"dense"===n.storage()?f(n,t,e,!0):c(n,t,e,!0)))})})),lu=Lt("inv",["typed","matrix","divideScalar","addScalar","multiply","unaryMinus","det","identity","abs"],(e=>{var{typed:t,matrix:n,divideScalar:r,addScalar:i,multiply:o,unaryMinus:u,det:a,identity:s,abs:c}=e;return t("inv",{"Array | Matrix":function(e){var t=rt(e)?e.size():qn(e);switch(t.length){case 1:if(1===t[0])return rt(e)?n([r(1,e.valueOf()[0])]):[r(1,e[0])];throw new RangeError("Matrix must be square (size: "+In(t)+")");case 2:var i=t[0],o=t[1];if(i===o)return rt(e)?n(f(e.valueOf(),i,o),e.storage()):f(e,i,o);throw new RangeError("Matrix must be square (size: "+In(t)+")");default:throw new RangeError("Matrix must be two dimensional (size: "+In(t)+")")}},any:function(e){return r(1,e)}});function f(e,t,n){var f,l,p,h,d;if(1===t){if(0===(h=e[0][0]))throw Error("Cannot calculate inverse, determinant is zero");return[[r(1,h)]]}if(2===t){var m=a(e);if(0===m)throw Error("Cannot calculate inverse, determinant is zero");return[[r(e[1][1],m),r(u(e[0][1]),m)],[r(u(e[1][0]),m),r(e[0][0],m)]]}var g=e.concat();for(f=0;f<t;f++)g[f]=g[f].concat();for(var v=s(t).valueOf(),D=0;D<n;D++){var y=c(g[D][D]),w=D;for(f=D+1;f<t;)c(g[f][D])>y&&(y=c(g[f][D]),w=f),f++;if(0===y)throw Error("Cannot calculate inverse, determinant is zero");(f=w)!==D&&(d=g[D],g[D]=g[f],g[f]=d,d=v[D],v[D]=v[f],v[f]=d);var b=g[D],E=v[D];for(f=0;f<t;f++){var x=g[f],A=v[f];if(f!==D){if(0!==x[D]){for(p=r(u(x[D]),b[D]),l=D;l<n;l++)x[l]=i(x[l],o(p,b[l]));for(l=0;l<n;l++)A[l]=i(A[l],o(p,E[l]))}}else{for(p=b[D],l=D;l<n;l++)x[l]=r(x[l],p);for(l=0;l<n;l++)A[l]=r(A[l],p)}}}return v}})),pu=Lt("pow",["typed","config","identity","multiply","matrix","inv","fraction","number","Complex"],(e=>{var{typed:t,config:n,identity:r,multiply:i,matrix:o,inv:u,number:a,fraction:s,Complex:c}=e;return t("pow",{"number, number":f,"Complex, Complex":function(e,t){return e.pow(t)},"BigNumber, BigNumber":function(e,t){return t.isInteger()||e>=0||n.predictable?e.pow(t):new c(e.toNumber(),0).pow(t.toNumber(),0)},"Fraction, Fraction":function(e,t){var r=e.pow(t);if(null!=r)return r;if(n.predictable)throw new Error("Result of pow is non-rational and cannot be expressed as a fraction");return f(e.valueOf(),t.valueOf())},"Array, number":l,"Array, BigNumber":function(e,t){return l(e,t.toNumber())},"Matrix, number":p,"Matrix, BigNumber":function(e,t){return p(e,t.toNumber())},"Unit, number | BigNumber":function(e,t){return e.pow(t)}});function f(e,t){if(n.predictable&&!Xt(t)&&e<0)try{var r=s(t),i=a(r);if((t===i||Math.abs((t-i)/t)<1e-14)&&r.d%2==1)return(r.n%2==0?1:-1)*Math.pow(-e,t)}catch(e){}return n.predictable&&(e<-1&&t===1/0||e>-1&&e<0&&t===-1/0)?NaN:Xt(t)||e>=0||n.predictable?qr(e,t):e*e<1&&t===1/0||e*e>1&&t===-1/0?0:new c(e,0).pow(t,0)}function l(e,t){if(!Xt(t))throw new TypeError("For A^b, b must be an integer (value is "+t+")");var n=qn(e);if(2!==n.length)throw new Error("For A^b, A must be 2 dimensional (A has "+n.length+" dimensions)");if(n[0]!==n[1])throw new Error("For A^b, A must be square (size is "+n[0]+"x"+n[1]+")");if(t<0)try{return l(u(e),-t)}catch(e){if("Cannot calculate inverse, determinant is zero"===e.message)throw new TypeError("For A^b, when A is not invertible, b must be a positive integer (value is "+t+")");throw e}for(var o=r(n[0]).valueOf(),a=e;t>=1;)1&~t||(o=i(a,o)),t>>=1,a=i(a,a);return o}function p(e,t){return o(l(e.valueOf(),t))}}));function hu(e){return hu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},hu(e)}function du(e){var t=function(e,t){if("object"!=hu(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=hu(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hu(t)?t:t+""}function mu(e,t,n){return(t=du(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function gu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function vu(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?gu(Object(n),!0).forEach((function(t){mu(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gu(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Du=Lt("Unit",["?on","config","addScalar","subtractScalar","multiplyScalar","divideScalar","pow","abs","fix","round","equal","isNumeric","format","number","Complex","BigNumber","Fraction"],(e=>{var t,n,r,{on:i,config:o,addScalar:u,subtractScalar:a,multiplyScalar:c,divideScalar:f,pow:l,abs:p,fix:h,round:d,equal:m,isNumeric:g,format:v,number:D,Complex:y,BigNumber:w,Fraction:b}=e,E=D;function x(e,t){if(!(this instanceof x))throw new Error("Constructor must be called with the new operator");if(null!=e&&!g(e)&&!Qe(e))throw new TypeError("First parameter in Unit constructor must be number, BigNumber, Fraction, Complex, or undefined");if(this.fixPrefix=!1,this.skipAutomaticSimplification=!0,void 0===t)this.units=[],this.dimensions=R.map((e=>0));else if("string"==typeof t){var n=x.parse(t);this.units=n.units,this.dimensions=n.dimensions}else{if(!et(t)||null!==t.value)throw new TypeError("Second parameter in Unit constructor must be a string or valueless Unit");this.fixPrefix=t.fixPrefix,this.skipAutomaticSimplification=t.skipAutomaticSimplification,this.dimensions=t.dimensions.slice(0),this.units=t.units.map((e=>s({},e)))}this.value=this._normalize(e)}function A(){for(;" "===r||"\t"===r;)F()}function C(e){return e>="0"&&e<="9"}function F(){n++,r=t.charAt(n)}function N(e){n=e,r=t.charAt(n)}function _(){var e="",t=n;if("+"===r?F():"-"===r&&(e+=r,F()),!function(e){return e>="0"&&e<="9"||"."===e}(r))return N(t),null;if("."===r){if(e+=r,F(),!C(r))return N(t),null}else{for(;C(r);)e+=r,F();"."===r&&(e+=r,F())}for(;C(r);)e+=r,F();if("E"===r||"e"===r){var i="",o=n;if(i+=r,F(),"+"!==r&&"-"!==r||(i+=r,F()),!C(r))return N(o),e;for(e+=i;C(r);)e+=r,F()}return e}function M(){for(var e="";C(r)||x.isValidAlpha(r);)e+=r,F();var t=e.charAt(0);return x.isValidAlpha(t)?e:null}function S(e){return r===e?(F(),e):null}Object.defineProperty(x,"name",{value:"Unit"}),x.prototype.constructor=x,x.prototype.type="Unit",x.prototype.isUnit=!0,x.parse=function(e,i){if(i=i||{},n=-1,r="","string"!=typeof(t=e))throw new TypeError("Invalid argument in Unit.parse, string expected");var u=new x;u.units=[];var a=1,s=!1;F(),A();var c=_(),f=null;if(c){if("BigNumber"===o.number)f=new w(c);else if("Fraction"===o.number)try{f=new b(c)}catch(e){f=parseFloat(c)}else f=parseFloat(c);A(),S("*")?(a=1,s=!0):S("/")&&(a=-1,s=!0)}for(var l=[],p=1;;){for(A();"("===r;)l.push(a),p*=a,a=1,F(),A();var h=void 0;if(!r)break;var d=r;if(null===(h=M()))throw new SyntaxError('Unexpected "'+d+'" in "'+t+'" at index '+n.toString());var m=O(h);if(null===m)throw new SyntaxError('Unit "'+h+'" not found.');var g=a*p;if(A(),S("^")){A();var v=_();if(null===v)throw new SyntaxError('In "'+e+'", "^" must be followed by a floating-point number');g*=v}u.units.push({unit:m.unit,prefix:m.prefix,power:g});for(var D=0;D<R.length;D++)u.dimensions[D]+=(m.unit.dimensions[D]||0)*g;for(A();")"===r;){if(0===l.length)throw new SyntaxError('Unmatched ")" in "'+t+'" at index '+n.toString());p/=l.pop(),F(),A()}if(s=!1,S("*")?(a=1,s=!0):S("/")?(a=-1,s=!0):a=1,m.unit.base){var y=m.unit.base.key;U.auto[y]={unit:m.unit,prefix:m.prefix}}}if(A(),r)throw new SyntaxError('Could not parse: "'+e+'"');if(s)throw new SyntaxError('Trailing characters: "'+e+'"');if(0!==l.length)throw new SyntaxError('Unmatched "(" in "'+t+'"');if(0===u.units.length&&!i.allowNoUnits)throw new SyntaxError('"'+e+'" contains no units');return u.value=void 0!==f?u._normalize(f):null,u},x.prototype.clone=function(){var e=new x;e.fixPrefix=this.fixPrefix,e.skipAutomaticSimplification=this.skipAutomaticSimplification,e.value=zt(this.value),e.dimensions=this.dimensions.slice(0),e.units=[];for(var t=0;t<this.units.length;t++)for(var n in e.units[t]={},this.units[t])jt(this.units[t],n)&&(e.units[t][n]=this.units[t][n]);return e},x.prototype.valueType=function(){return It(this.value)},x.prototype._isDerived=function(){return 0!==this.units.length&&(this.units.length>1||Math.abs(this.units[0].power-1)>1e-15)},x.prototype._normalize=function(e){if(null==e||0===this.units.length)return e;for(var t=e,n=x._getNumberConverter(It(e)),r=0;r<this.units.length;r++){var i=n(this.units[r].unit.value),o=n(this.units[r].prefix.value),u=n(this.units[r].power);t=c(t,l(c(i,o),u))}return t},x.prototype._denormalize=function(e,t){if(null==e||0===this.units.length)return e;for(var n=e,r=x._getNumberConverter(It(e)),i=0;i<this.units.length;i++){var o=r(this.units[i].unit.value),u=r(this.units[i].prefix.value),a=r(this.units[i].power);n=f(n,l(c(o,u),a))}return n};var O=En((e=>{if(jt(k,e)){var t=k[e];return{unit:t,prefix:t.prefixes[""]}}for(var n in k)if(jt(k,n)&&Rn(e,n)){var r=k[n],i=e.length-n.length,o=e.substring(0,i),u=jt(r.prefixes,o)?r.prefixes[o]:void 0;if(void 0!==u)return{unit:r,prefix:u}}return null}),{hasher:e=>e[0],limit:100});function T(e){return e.equalBase(I.NONE)&&null!==e.value&&!o.predictable?e.value:e}x.isValuelessUnit=function(e){return null!==O(e)},x.prototype.hasBase=function(e){if("string"==typeof e&&(e=I[e]),!e)return!1;for(var t=0;t<R.length;t++)if(Math.abs((this.dimensions[t]||0)-(e.dimensions[t]||0))>1e-12)return!1;return!0},x.prototype.equalBase=function(e){for(var t=0;t<R.length;t++)if(Math.abs((this.dimensions[t]||0)-(e.dimensions[t]||0))>1e-12)return!1;return!0},x.prototype.equals=function(e){return this.equalBase(e)&&m(this.value,e.value)},x.prototype.multiply=function(e){for(var t=this.clone(),n=et(e)?e:new x(e),r=0;r<R.length;r++)t.dimensions[r]=(this.dimensions[r]||0)+(n.dimensions[r]||0);for(var i=0;i<n.units.length;i++){var o=vu({},n.units[i]);t.units.push(o)}if(null!==this.value||null!==n.value){var u=null===this.value?this._normalize(1):this.value,a=null===n.value?n._normalize(1):n.value;t.value=c(u,a)}else t.value=null;return et(e)&&(t.skipAutomaticSimplification=!1),T(t)},x.prototype.divideInto=function(e){return new x(e).divide(this)},x.prototype.divide=function(e){for(var t=this.clone(),n=et(e)?e:new x(e),r=0;r<R.length;r++)t.dimensions[r]=(this.dimensions[r]||0)-(n.dimensions[r]||0);for(var i=0;i<n.units.length;i++){var o=vu(vu({},n.units[i]),{},{power:-n.units[i].power});t.units.push(o)}if(null!==this.value||null!==n.value){var u=null===this.value?this._normalize(1):this.value,a=null===n.value?n._normalize(1):n.value;t.value=f(u,a)}else t.value=null;return et(e)&&(t.skipAutomaticSimplification=!1),T(t)},x.prototype.pow=function(e){for(var t=this.clone(),n=0;n<R.length;n++)t.dimensions[n]=(this.dimensions[n]||0)*e;for(var r=0;r<t.units.length;r++)t.units[r].power*=e;return null!==t.value?t.value=l(t.value,e):t.value=null,t.skipAutomaticSimplification=!1,T(t)},x.prototype.abs=function(){var e=this.clone();if(null!==e.value)if(e._isDerived()||0===e.units.length||0===e.units[0].unit.offset)e.value=p(e.value);else{var t=e._numberConverter(),n=t(e.units[0].unit.value),r=t(e.units[0].unit.offset),i=c(n,r);e.value=a(p(u(e.value,i)),i)}for(var o in e.units)"VA"!==e.units[o].unit.name&&"VAR"!==e.units[o].unit.name||(e.units[o].unit=k.W);return e},x.prototype.to=function(e){var t,n=null===this.value?this._normalize(1):this.value;if("string"==typeof e)t=x.parse(e);else{if(!et(e))throw new Error("String or Unit expected as parameter");t=e.clone()}if(!this.equalBase(t))throw new Error("Units do not match ('".concat(t.toString(),"' != '").concat(this.toString(),"')"));if(null!==t.value)throw new Error("Cannot convert to a unit with a value");if(null===this.value||this._isDerived()||0===this.units.length||0===t.units.length||this.units[0].unit.offset===t.units[0].unit.offset)t.value=zt(n);else{var r=x._getNumberConverter(It(n)),i=this.units[0].unit.value,o=this.units[0].unit.offset,s=c(i,o),f=t.units[0].unit.value,l=t.units[0].unit.offset,p=c(f,l);t.value=u(n,r(a(s,p)))}return t.fixPrefix=!0,t.skipAutomaticSimplification=!0,t},x.prototype.toNumber=function(e){return E(this.toNumeric(e))},x.prototype.toNumeric=function(e){var t;return(t=e?this.to(e):this.clone())._isDerived()||0===t.units.length?t._denormalize(t.value):t._denormalize(t.value,t.units[0].prefix.value)},x.prototype.toString=function(){return this.format()},x.prototype.toJSON=function(){return{mathjs:"Unit",value:this._denormalize(this.value),unit:this.units.length>0?this.formatUnits():null,fixPrefix:this.fixPrefix}},x.fromJSON=function(e){var t,n=new x(e.value,null!==(t=e.unit)&&void 0!==t?t:void 0);return n.fixPrefix=e.fixPrefix||!1,n},x.prototype.valueOf=x.prototype.toString,x.prototype.simplify=function(){var e,t,n=this.clone(),r=[];for(var i in q)if(jt(q,i)&&n.hasBase(I[i])){e=i;break}if("NONE"===e)n.units=[];else if(e&&jt(q,e)&&(t=q[e]),t)n.units=[{unit:t.unit,prefix:t.prefix,power:1}];else{for(var o=!1,u=0;u<R.length;u++){var a=R[u];Math.abs(n.dimensions[u]||0)>1e-12&&(jt(q,a)?r.push({unit:q[a].unit,prefix:q[a].prefix,power:n.dimensions[u]||0}):o=!0)}r.length<n.units.length&&!o&&(n.units=r)}return n},x.prototype.toSI=function(){for(var e=this.clone(),t=[],n=0;n<R.length;n++){var r=R[n];if(Math.abs(e.dimensions[n]||0)>1e-12){if(!jt(U.si,r))throw new Error("Cannot express custom unit "+r+" in SI units");t.push({unit:U.si[r].unit,prefix:U.si[r].prefix,power:e.dimensions[n]||0})}}return e.units=t,e.fixPrefix=!0,e.skipAutomaticSimplification=!0,null!==this.value?(e.value=null,this.to(e)):e},x.prototype.formatUnits=function(){for(var e="",t="",n=0,r=0,i=0;i<this.units.length;i++)this.units[i].power>0?(n++,e+=" "+this.units[i].prefix.name+this.units[i].unit.name,Math.abs(this.units[i].power-1)>1e-15&&(e+="^"+this.units[i].power)):this.units[i].power<0&&r++;if(r>0)for(var o=0;o<this.units.length;o++)this.units[o].power<0&&(n>0?(t+=" "+this.units[o].prefix.name+this.units[o].unit.name,Math.abs(this.units[o].power+1)>1e-15&&(t+="^"+-this.units[o].power)):(t+=" "+this.units[o].prefix.name+this.units[o].unit.name,t+="^"+this.units[o].power));e=e.substr(1),t=t.substr(1),n>1&&r>0&&(e="("+e+")"),r>1&&n>0&&(t="("+t+")");var u=e;return n>0&&r>0&&(u+=" / "),u+=t},x.prototype.format=function(e){var t=this.skipAutomaticSimplification||null===this.value?this.clone():this.simplify(),n=!1;for(var r in void 0!==t.value&&null!==t.value&&Qe(t.value)&&(n=Math.abs(t.value.re)<1e-14),t.units)jt(t.units,r)&&t.units[r].unit&&("VA"===t.units[r].unit.name&&n?t.units[r].unit=k.VAR:"VAR"!==t.units[r].unit.name||n||(t.units[r].unit=k.VA));1!==t.units.length||t.fixPrefix||Math.abs(t.units[0].power-Math.round(t.units[0].power))<1e-14&&(t.units[0].prefix=t._bestPrefix());var i=t._denormalize(t.value),o=null!==t.value?v(i,e||{}):"",u=t.formatUnits();return t.value&&Qe(t.value)&&(o="("+o+")"),u.length>0&&o.length>0&&(o+=" "),o+=u},x.prototype._bestPrefix=function(){if(1!==this.units.length)throw new Error("Can only compute the best prefix for single units with integer powers, like kg, s^2, N^-1, and so forth!");if(Math.abs(this.units[0].power-Math.round(this.units[0].power))>=1e-14)throw new Error("Can only compute the best prefix for single units with integer powers, like kg, s^2, N^-1, and so forth!");var e=null!==this.value?p(this.value):0,t=p(this.units[0].unit.value),n=this.units[0].prefix;if(0===e)return n;var r=this.units[0].power,i=Math.log(e/Math.pow(n.value*t,r))/Math.LN10-1.2;if(i>-2.200001&&i<1.800001)return n;i=Math.abs(i);var o=this.units[0].unit.prefixes;for(var u in o)if(jt(o,u)){var a=o[u];if(a.scientific){var s=Math.abs(Math.log(e/Math.pow(a.value*t,r))/Math.LN10-1.2);(s<i||s===i&&a.name.length<n.name.length)&&(n=a,i=s)}}return n},x.prototype.splitUnit=function(e){for(var t=this.clone(),n=[],r=0;r<e.length&&(t=t.to(e[r]),r!==e.length-1);r++){var i=t.toNumeric(),o=d(i),s=new x(m(o,i)?o:h(t.toNumeric()),e[r].toString());n.push(s),t=a(t,s)}for(var c=0,f=0;f<n.length;f++)c=u(c,n[f].value);return m(c,this.value)&&(t.value=0),n.push(t),n};var B={NONE:{"":{name:"",value:1,scientific:!0}},SHORT:{"":{name:"",value:1,scientific:!0},da:{name:"da",value:10,scientific:!1},h:{name:"h",value:100,scientific:!1},k:{name:"k",value:1e3,scientific:!0},M:{name:"M",value:1e6,scientific:!0},G:{name:"G",value:1e9,scientific:!0},T:{name:"T",value:1e12,scientific:!0},P:{name:"P",value:1e15,scientific:!0},E:{name:"E",value:1e18,scientific:!0},Z:{name:"Z",value:1e21,scientific:!0},Y:{name:"Y",value:1e24,scientific:!0},R:{name:"R",value:1e27,scientific:!0},Q:{name:"Q",value:1e30,scientific:!0},d:{name:"d",value:.1,scientific:!1},c:{name:"c",value:.01,scientific:!1},m:{name:"m",value:.001,scientific:!0},u:{name:"u",value:1e-6,scientific:!0},n:{name:"n",value:1e-9,scientific:!0},p:{name:"p",value:1e-12,scientific:!0},f:{name:"f",value:1e-15,scientific:!0},a:{name:"a",value:1e-18,scientific:!0},z:{name:"z",value:1e-21,scientific:!0},y:{name:"y",value:1e-24,scientific:!0},r:{name:"r",value:1e-27,scientific:!0},q:{name:"q",value:1e-30,scientific:!0}},LONG:{"":{name:"",value:1,scientific:!0},deca:{name:"deca",value:10,scientific:!1},hecto:{name:"hecto",value:100,scientific:!1},kilo:{name:"kilo",value:1e3,scientific:!0},mega:{name:"mega",value:1e6,scientific:!0},giga:{name:"giga",value:1e9,scientific:!0},tera:{name:"tera",value:1e12,scientific:!0},peta:{name:"peta",value:1e15,scientific:!0},exa:{name:"exa",value:1e18,scientific:!0},zetta:{name:"zetta",value:1e21,scientific:!0},yotta:{name:"yotta",value:1e24,scientific:!0},ronna:{name:"ronna",value:1e27,scientific:!0},quetta:{name:"quetta",value:1e30,scientific:!0},deci:{name:"deci",value:.1,scientific:!1},centi:{name:"centi",value:.01,scientific:!1},milli:{name:"milli",value:.001,scientific:!0},micro:{name:"micro",value:1e-6,scientific:!0},nano:{name:"nano",value:1e-9,scientific:!0},pico:{name:"pico",value:1e-12,scientific:!0},femto:{name:"femto",value:1e-15,scientific:!0},atto:{name:"atto",value:1e-18,scientific:!0},zepto:{name:"zepto",value:1e-21,scientific:!0},yocto:{name:"yocto",value:1e-24,scientific:!0},ronto:{name:"ronto",value:1e-27,scientific:!0},quecto:{name:"quecto",value:1e-30,scientific:!0}},SQUARED:{"":{name:"",value:1,scientific:!0},da:{name:"da",value:100,scientific:!1},h:{name:"h",value:1e4,scientific:!1},k:{name:"k",value:1e6,scientific:!0},M:{name:"M",value:1e12,scientific:!0},G:{name:"G",value:1e18,scientific:!0},T:{name:"T",value:1e24,scientific:!0},P:{name:"P",value:1e30,scientific:!0},E:{name:"E",value:1e36,scientific:!0},Z:{name:"Z",value:1e42,scientific:!0},Y:{name:"Y",value:1e48,scientific:!0},R:{name:"R",value:1e54,scientific:!0},Q:{name:"Q",value:1e60,scientific:!0},d:{name:"d",value:.01,scientific:!1},c:{name:"c",value:1e-4,scientific:!1},m:{name:"m",value:1e-6,scientific:!0},u:{name:"u",value:1e-12,scientific:!0},n:{name:"n",value:1e-18,scientific:!0},p:{name:"p",value:1e-24,scientific:!0},f:{name:"f",value:1e-30,scientific:!0},a:{name:"a",value:1e-36,scientific:!0},z:{name:"z",value:1e-42,scientific:!0},y:{name:"y",value:1e-48,scientific:!0},r:{name:"r",value:1e-54,scientific:!0},q:{name:"q",value:1e-60,scientific:!0}},CUBIC:{"":{name:"",value:1,scientific:!0},da:{name:"da",value:1e3,scientific:!1},h:{name:"h",value:1e6,scientific:!1},k:{name:"k",value:1e9,scientific:!0},M:{name:"M",value:1e18,scientific:!0},G:{name:"G",value:1e27,scientific:!0},T:{name:"T",value:1e36,scientific:!0},P:{name:"P",value:1e45,scientific:!0},E:{name:"E",value:1e54,scientific:!0},Z:{name:"Z",value:1e63,scientific:!0},Y:{name:"Y",value:1e72,scientific:!0},R:{name:"R",value:1e81,scientific:!0},Q:{name:"Q",value:1e90,scientific:!0},d:{name:"d",value:.001,scientific:!1},c:{name:"c",value:1e-6,scientific:!1},m:{name:"m",value:1e-9,scientific:!0},u:{name:"u",value:1e-18,scientific:!0},n:{name:"n",value:1e-27,scientific:!0},p:{name:"p",value:1e-36,scientific:!0},f:{name:"f",value:1e-45,scientific:!0},a:{name:"a",value:1e-54,scientific:!0},z:{name:"z",value:1e-63,scientific:!0},y:{name:"y",value:1e-72,scientific:!0},r:{name:"r",value:1e-81,scientific:!0},q:{name:"q",value:1e-90,scientific:!0}},BINARY_SHORT_SI:{"":{name:"",value:1,scientific:!0},k:{name:"k",value:1e3,scientific:!0},M:{name:"M",value:1e6,scientific:!0},G:{name:"G",value:1e9,scientific:!0},T:{name:"T",value:1e12,scientific:!0},P:{name:"P",value:1e15,scientific:!0},E:{name:"E",value:1e18,scientific:!0},Z:{name:"Z",value:1e21,scientific:!0},Y:{name:"Y",value:1e24,scientific:!0}},BINARY_SHORT_IEC:{"":{name:"",value:1,scientific:!0},Ki:{name:"Ki",value:1024,scientific:!0},Mi:{name:"Mi",value:Math.pow(1024,2),scientific:!0},Gi:{name:"Gi",value:Math.pow(1024,3),scientific:!0},Ti:{name:"Ti",value:Math.pow(1024,4),scientific:!0},Pi:{name:"Pi",value:Math.pow(1024,5),scientific:!0},Ei:{name:"Ei",value:Math.pow(1024,6),scientific:!0},Zi:{name:"Zi",value:Math.pow(1024,7),scientific:!0},Yi:{name:"Yi",value:Math.pow(1024,8),scientific:!0}},BINARY_LONG_SI:{"":{name:"",value:1,scientific:!0},kilo:{name:"kilo",value:1e3,scientific:!0},mega:{name:"mega",value:1e6,scientific:!0},giga:{name:"giga",value:1e9,scientific:!0},tera:{name:"tera",value:1e12,scientific:!0},peta:{name:"peta",value:1e15,scientific:!0},exa:{name:"exa",value:1e18,scientific:!0},zetta:{name:"zetta",value:1e21,scientific:!0},yotta:{name:"yotta",value:1e24,scientific:!0}},BINARY_LONG_IEC:{"":{name:"",value:1,scientific:!0},kibi:{name:"kibi",value:1024,scientific:!0},mebi:{name:"mebi",value:Math.pow(1024,2),scientific:!0},gibi:{name:"gibi",value:Math.pow(1024,3),scientific:!0},tebi:{name:"tebi",value:Math.pow(1024,4),scientific:!0},pebi:{name:"pebi",value:Math.pow(1024,5),scientific:!0},exi:{name:"exi",value:Math.pow(1024,6),scientific:!0},zebi:{name:"zebi",value:Math.pow(1024,7),scientific:!0},yobi:{name:"yobi",value:Math.pow(1024,8),scientific:!0}},BTU:{"":{name:"",value:1,scientific:!0},MM:{name:"MM",value:1e6,scientific:!0}}};B.SHORTLONG=s({},B.SHORT,B.LONG),B.BINARY_SHORT=s({},B.BINARY_SHORT_SI,B.BINARY_SHORT_IEC),B.BINARY_LONG=s({},B.BINARY_LONG_SI,B.BINARY_LONG_IEC);var R=["MASS","LENGTH","TIME","CURRENT","TEMPERATURE","LUMINOUS_INTENSITY","AMOUNT_OF_SUBSTANCE","ANGLE","BIT"],I={NONE:{dimensions:[0,0,0,0,0,0,0,0,0]},MASS:{dimensions:[1,0,0,0,0,0,0,0,0]},LENGTH:{dimensions:[0,1,0,0,0,0,0,0,0]},TIME:{dimensions:[0,0,1,0,0,0,0,0,0]},CURRENT:{dimensions:[0,0,0,1,0,0,0,0,0]},TEMPERATURE:{dimensions:[0,0,0,0,1,0,0,0,0]},LUMINOUS_INTENSITY:{dimensions:[0,0,0,0,0,1,0,0,0]},AMOUNT_OF_SUBSTANCE:{dimensions:[0,0,0,0,0,0,1,0,0]},FORCE:{dimensions:[1,1,-2,0,0,0,0,0,0]},SURFACE:{dimensions:[0,2,0,0,0,0,0,0,0]},VOLUME:{dimensions:[0,3,0,0,0,0,0,0,0]},ENERGY:{dimensions:[1,2,-2,0,0,0,0,0,0]},POWER:{dimensions:[1,2,-3,0,0,0,0,0,0]},PRESSURE:{dimensions:[1,-1,-2,0,0,0,0,0,0]},ELECTRIC_CHARGE:{dimensions:[0,0,1,1,0,0,0,0,0]},ELECTRIC_CAPACITANCE:{dimensions:[-1,-2,4,2,0,0,0,0,0]},ELECTRIC_POTENTIAL:{dimensions:[1,2,-3,-1,0,0,0,0,0]},ELECTRIC_RESISTANCE:{dimensions:[1,2,-3,-2,0,0,0,0,0]},ELECTRIC_INDUCTANCE:{dimensions:[1,2,-2,-2,0,0,0,0,0]},ELECTRIC_CONDUCTANCE:{dimensions:[-1,-2,3,2,0,0,0,0,0]},MAGNETIC_FLUX:{dimensions:[1,2,-2,-1,0,0,0,0,0]},MAGNETIC_FLUX_DENSITY:{dimensions:[1,0,-2,-1,0,0,0,0,0]},FREQUENCY:{dimensions:[0,0,-1,0,0,0,0,0,0]},ANGLE:{dimensions:[0,0,0,0,0,0,0,1,0]},BIT:{dimensions:[0,0,0,0,0,0,0,0,1]}};for(var z in I)jt(I,z)&&(I[z].key=z);var P={name:"",base:{},value:1,offset:0,dimensions:R.map((e=>0))},k={meter:{name:"meter",base:I.LENGTH,prefixes:B.LONG,value:1,offset:0},inch:{name:"inch",base:I.LENGTH,prefixes:B.NONE,value:.0254,offset:0},foot:{name:"foot",base:I.LENGTH,prefixes:B.NONE,value:.3048,offset:0},yard:{name:"yard",base:I.LENGTH,prefixes:B.NONE,value:.9144,offset:0},mile:{name:"mile",base:I.LENGTH,prefixes:B.NONE,value:1609.344,offset:0},link:{name:"link",base:I.LENGTH,prefixes:B.NONE,value:.201168,offset:0},rod:{name:"rod",base:I.LENGTH,prefixes:B.NONE,value:5.0292,offset:0},chain:{name:"chain",base:I.LENGTH,prefixes:B.NONE,value:20.1168,offset:0},angstrom:{name:"angstrom",base:I.LENGTH,prefixes:B.NONE,value:1e-10,offset:0},m:{name:"m",base:I.LENGTH,prefixes:B.SHORT,value:1,offset:0},in:{name:"in",base:I.LENGTH,prefixes:B.NONE,value:.0254,offset:0},ft:{name:"ft",base:I.LENGTH,prefixes:B.NONE,value:.3048,offset:0},yd:{name:"yd",base:I.LENGTH,prefixes:B.NONE,value:.9144,offset:0},mi:{name:"mi",base:I.LENGTH,prefixes:B.NONE,value:1609.344,offset:0},li:{name:"li",base:I.LENGTH,prefixes:B.NONE,value:.201168,offset:0},rd:{name:"rd",base:I.LENGTH,prefixes:B.NONE,value:5.02921,offset:0},ch:{name:"ch",base:I.LENGTH,prefixes:B.NONE,value:20.1168,offset:0},mil:{name:"mil",base:I.LENGTH,prefixes:B.NONE,value:254e-7,offset:0},m2:{name:"m2",base:I.SURFACE,prefixes:B.SQUARED,value:1,offset:0},sqin:{name:"sqin",base:I.SURFACE,prefixes:B.NONE,value:64516e-8,offset:0},sqft:{name:"sqft",base:I.SURFACE,prefixes:B.NONE,value:.09290304,offset:0},sqyd:{name:"sqyd",base:I.SURFACE,prefixes:B.NONE,value:.83612736,offset:0},sqmi:{name:"sqmi",base:I.SURFACE,prefixes:B.NONE,value:2589988.110336,offset:0},sqrd:{name:"sqrd",base:I.SURFACE,prefixes:B.NONE,value:25.29295,offset:0},sqch:{name:"sqch",base:I.SURFACE,prefixes:B.NONE,value:404.6873,offset:0},sqmil:{name:"sqmil",base:I.SURFACE,prefixes:B.NONE,value:6.4516e-10,offset:0},acre:{name:"acre",base:I.SURFACE,prefixes:B.NONE,value:4046.86,offset:0},hectare:{name:"hectare",base:I.SURFACE,prefixes:B.NONE,value:1e4,offset:0},m3:{name:"m3",base:I.VOLUME,prefixes:B.CUBIC,value:1,offset:0},L:{name:"L",base:I.VOLUME,prefixes:B.SHORT,value:.001,offset:0},l:{name:"l",base:I.VOLUME,prefixes:B.SHORT,value:.001,offset:0},litre:{name:"litre",base:I.VOLUME,prefixes:B.LONG,value:.001,offset:0},cuin:{name:"cuin",base:I.VOLUME,prefixes:B.NONE,value:16387064e-12,offset:0},cuft:{name:"cuft",base:I.VOLUME,prefixes:B.NONE,value:.028316846592,offset:0},cuyd:{name:"cuyd",base:I.VOLUME,prefixes:B.NONE,value:.************,offset:0},teaspoon:{name:"teaspoon",base:I.VOLUME,prefixes:B.NONE,value:5e-6,offset:0},tablespoon:{name:"tablespoon",base:I.VOLUME,prefixes:B.NONE,value:15e-6,offset:0},drop:{name:"drop",base:I.VOLUME,prefixes:B.NONE,value:5e-8,offset:0},gtt:{name:"gtt",base:I.VOLUME,prefixes:B.NONE,value:5e-8,offset:0},minim:{name:"minim",base:I.VOLUME,prefixes:B.NONE,value:6.161152e-8,offset:0},fluiddram:{name:"fluiddram",base:I.VOLUME,prefixes:B.NONE,value:36966911e-13,offset:0},fluidounce:{name:"fluidounce",base:I.VOLUME,prefixes:B.NONE,value:2957353e-11,offset:0},gill:{name:"gill",base:I.VOLUME,prefixes:B.NONE,value:.0001182941,offset:0},cc:{name:"cc",base:I.VOLUME,prefixes:B.NONE,value:1e-6,offset:0},cup:{name:"cup",base:I.VOLUME,prefixes:B.NONE,value:.0002365882,offset:0},pint:{name:"pint",base:I.VOLUME,prefixes:B.NONE,value:.0004731765,offset:0},quart:{name:"quart",base:I.VOLUME,prefixes:B.NONE,value:.0009463529,offset:0},gallon:{name:"gallon",base:I.VOLUME,prefixes:B.NONE,value:.003785412,offset:0},beerbarrel:{name:"beerbarrel",base:I.VOLUME,prefixes:B.NONE,value:.1173478,offset:0},oilbarrel:{name:"oilbarrel",base:I.VOLUME,prefixes:B.NONE,value:.1589873,offset:0},hogshead:{name:"hogshead",base:I.VOLUME,prefixes:B.NONE,value:.238481,offset:0},fldr:{name:"fldr",base:I.VOLUME,prefixes:B.NONE,value:36966911e-13,offset:0},floz:{name:"floz",base:I.VOLUME,prefixes:B.NONE,value:2957353e-11,offset:0},gi:{name:"gi",base:I.VOLUME,prefixes:B.NONE,value:.0001182941,offset:0},cp:{name:"cp",base:I.VOLUME,prefixes:B.NONE,value:.0002365882,offset:0},pt:{name:"pt",base:I.VOLUME,prefixes:B.NONE,value:.0004731765,offset:0},qt:{name:"qt",base:I.VOLUME,prefixes:B.NONE,value:.0009463529,offset:0},gal:{name:"gal",base:I.VOLUME,prefixes:B.NONE,value:.003785412,offset:0},bbl:{name:"bbl",base:I.VOLUME,prefixes:B.NONE,value:.1173478,offset:0},obl:{name:"obl",base:I.VOLUME,prefixes:B.NONE,value:.1589873,offset:0},g:{name:"g",base:I.MASS,prefixes:B.SHORT,value:.001,offset:0},gram:{name:"gram",base:I.MASS,prefixes:B.LONG,value:.001,offset:0},ton:{name:"ton",base:I.MASS,prefixes:B.SHORT,value:907.18474,offset:0},t:{name:"t",base:I.MASS,prefixes:B.SHORT,value:1e3,offset:0},tonne:{name:"tonne",base:I.MASS,prefixes:B.LONG,value:1e3,offset:0},grain:{name:"grain",base:I.MASS,prefixes:B.NONE,value:6479891e-11,offset:0},dram:{name:"dram",base:I.MASS,prefixes:B.NONE,value:.0017718451953125,offset:0},ounce:{name:"ounce",base:I.MASS,prefixes:B.NONE,value:.028349523125,offset:0},poundmass:{name:"poundmass",base:I.MASS,prefixes:B.NONE,value:.45359237,offset:0},hundredweight:{name:"hundredweight",base:I.MASS,prefixes:B.NONE,value:45.359237,offset:0},stick:{name:"stick",base:I.MASS,prefixes:B.NONE,value:.115,offset:0},stone:{name:"stone",base:I.MASS,prefixes:B.NONE,value:6.35029318,offset:0},gr:{name:"gr",base:I.MASS,prefixes:B.NONE,value:6479891e-11,offset:0},dr:{name:"dr",base:I.MASS,prefixes:B.NONE,value:.0017718451953125,offset:0},oz:{name:"oz",base:I.MASS,prefixes:B.NONE,value:.028349523125,offset:0},lbm:{name:"lbm",base:I.MASS,prefixes:B.NONE,value:.45359237,offset:0},cwt:{name:"cwt",base:I.MASS,prefixes:B.NONE,value:45.359237,offset:0},s:{name:"s",base:I.TIME,prefixes:B.SHORT,value:1,offset:0},min:{name:"min",base:I.TIME,prefixes:B.NONE,value:60,offset:0},h:{name:"h",base:I.TIME,prefixes:B.NONE,value:3600,offset:0},second:{name:"second",base:I.TIME,prefixes:B.LONG,value:1,offset:0},sec:{name:"sec",base:I.TIME,prefixes:B.LONG,value:1,offset:0},minute:{name:"minute",base:I.TIME,prefixes:B.NONE,value:60,offset:0},hour:{name:"hour",base:I.TIME,prefixes:B.NONE,value:3600,offset:0},day:{name:"day",base:I.TIME,prefixes:B.NONE,value:86400,offset:0},week:{name:"week",base:I.TIME,prefixes:B.NONE,value:604800,offset:0},month:{name:"month",base:I.TIME,prefixes:B.NONE,value:2629800,offset:0},year:{name:"year",base:I.TIME,prefixes:B.NONE,value:31557600,offset:0},decade:{name:"decade",base:I.TIME,prefixes:B.NONE,value:315576e3,offset:0},century:{name:"century",base:I.TIME,prefixes:B.NONE,value:315576e4,offset:0},millennium:{name:"millennium",base:I.TIME,prefixes:B.NONE,value:315576e5,offset:0},hertz:{name:"Hertz",base:I.FREQUENCY,prefixes:B.LONG,value:1,offset:0,reciprocal:!0},Hz:{name:"Hz",base:I.FREQUENCY,prefixes:B.SHORT,value:1,offset:0,reciprocal:!0},rad:{name:"rad",base:I.ANGLE,prefixes:B.SHORT,value:1,offset:0},radian:{name:"radian",base:I.ANGLE,prefixes:B.LONG,value:1,offset:0},deg:{name:"deg",base:I.ANGLE,prefixes:B.SHORT,value:null,offset:0},degree:{name:"degree",base:I.ANGLE,prefixes:B.LONG,value:null,offset:0},grad:{name:"grad",base:I.ANGLE,prefixes:B.SHORT,value:null,offset:0},gradian:{name:"gradian",base:I.ANGLE,prefixes:B.LONG,value:null,offset:0},cycle:{name:"cycle",base:I.ANGLE,prefixes:B.NONE,value:null,offset:0},arcsec:{name:"arcsec",base:I.ANGLE,prefixes:B.NONE,value:null,offset:0},arcmin:{name:"arcmin",base:I.ANGLE,prefixes:B.NONE,value:null,offset:0},A:{name:"A",base:I.CURRENT,prefixes:B.SHORT,value:1,offset:0},ampere:{name:"ampere",base:I.CURRENT,prefixes:B.LONG,value:1,offset:0},K:{name:"K",base:I.TEMPERATURE,prefixes:B.SHORT,value:1,offset:0},degC:{name:"degC",base:I.TEMPERATURE,prefixes:B.SHORT,value:1,offset:273.15},degF:{name:"degF",base:I.TEMPERATURE,prefixes:B.SHORT,value:new b(5,9),offset:459.67},degR:{name:"degR",base:I.TEMPERATURE,prefixes:B.SHORT,value:new b(5,9),offset:0},kelvin:{name:"kelvin",base:I.TEMPERATURE,prefixes:B.LONG,value:1,offset:0},celsius:{name:"celsius",base:I.TEMPERATURE,prefixes:B.LONG,value:1,offset:273.15},fahrenheit:{name:"fahrenheit",base:I.TEMPERATURE,prefixes:B.LONG,value:new b(5,9),offset:459.67},rankine:{name:"rankine",base:I.TEMPERATURE,prefixes:B.LONG,value:new b(5,9),offset:0},mol:{name:"mol",base:I.AMOUNT_OF_SUBSTANCE,prefixes:B.SHORT,value:1,offset:0},mole:{name:"mole",base:I.AMOUNT_OF_SUBSTANCE,prefixes:B.LONG,value:1,offset:0},cd:{name:"cd",base:I.LUMINOUS_INTENSITY,prefixes:B.SHORT,value:1,offset:0},candela:{name:"candela",base:I.LUMINOUS_INTENSITY,prefixes:B.LONG,value:1,offset:0},N:{name:"N",base:I.FORCE,prefixes:B.SHORT,value:1,offset:0},newton:{name:"newton",base:I.FORCE,prefixes:B.LONG,value:1,offset:0},dyn:{name:"dyn",base:I.FORCE,prefixes:B.SHORT,value:1e-5,offset:0},dyne:{name:"dyne",base:I.FORCE,prefixes:B.LONG,value:1e-5,offset:0},lbf:{name:"lbf",base:I.FORCE,prefixes:B.NONE,value:4.4482216152605,offset:0},poundforce:{name:"poundforce",base:I.FORCE,prefixes:B.NONE,value:4.4482216152605,offset:0},kip:{name:"kip",base:I.FORCE,prefixes:B.LONG,value:4448.2216,offset:0},kilogramforce:{name:"kilogramforce",base:I.FORCE,prefixes:B.NONE,value:9.80665,offset:0},J:{name:"J",base:I.ENERGY,prefixes:B.SHORT,value:1,offset:0},joule:{name:"joule",base:I.ENERGY,prefixes:B.LONG,value:1,offset:0},erg:{name:"erg",base:I.ENERGY,prefixes:B.SHORTLONG,value:1e-7,offset:0},Wh:{name:"Wh",base:I.ENERGY,prefixes:B.SHORT,value:3600,offset:0},BTU:{name:"BTU",base:I.ENERGY,prefixes:B.BTU,value:1055.05585262,offset:0},eV:{name:"eV",base:I.ENERGY,prefixes:B.SHORT,value:1602176565e-28,offset:0},electronvolt:{name:"electronvolt",base:I.ENERGY,prefixes:B.LONG,value:1602176565e-28,offset:0},W:{name:"W",base:I.POWER,prefixes:B.SHORT,value:1,offset:0},watt:{name:"watt",base:I.POWER,prefixes:B.LONG,value:1,offset:0},hp:{name:"hp",base:I.POWER,prefixes:B.NONE,value:745.6998715386,offset:0},VAR:{name:"VAR",base:I.POWER,prefixes:B.SHORT,value:y.I,offset:0},VA:{name:"VA",base:I.POWER,prefixes:B.SHORT,value:1,offset:0},Pa:{name:"Pa",base:I.PRESSURE,prefixes:B.SHORT,value:1,offset:0},psi:{name:"psi",base:I.PRESSURE,prefixes:B.NONE,value:6894.75729276459,offset:0},atm:{name:"atm",base:I.PRESSURE,prefixes:B.NONE,value:101325,offset:0},bar:{name:"bar",base:I.PRESSURE,prefixes:B.SHORTLONG,value:1e5,offset:0},torr:{name:"torr",base:I.PRESSURE,prefixes:B.NONE,value:133.322,offset:0},mmHg:{name:"mmHg",base:I.PRESSURE,prefixes:B.NONE,value:133.322,offset:0},mmH2O:{name:"mmH2O",base:I.PRESSURE,prefixes:B.NONE,value:9.80665,offset:0},cmH2O:{name:"cmH2O",base:I.PRESSURE,prefixes:B.NONE,value:98.0665,offset:0},coulomb:{name:"coulomb",base:I.ELECTRIC_CHARGE,prefixes:B.LONG,value:1,offset:0},C:{name:"C",base:I.ELECTRIC_CHARGE,prefixes:B.SHORT,value:1,offset:0},farad:{name:"farad",base:I.ELECTRIC_CAPACITANCE,prefixes:B.LONG,value:1,offset:0},F:{name:"F",base:I.ELECTRIC_CAPACITANCE,prefixes:B.SHORT,value:1,offset:0},volt:{name:"volt",base:I.ELECTRIC_POTENTIAL,prefixes:B.LONG,value:1,offset:0},V:{name:"V",base:I.ELECTRIC_POTENTIAL,prefixes:B.SHORT,value:1,offset:0},ohm:{name:"ohm",base:I.ELECTRIC_RESISTANCE,prefixes:B.SHORTLONG,value:1,offset:0},henry:{name:"henry",base:I.ELECTRIC_INDUCTANCE,prefixes:B.LONG,value:1,offset:0},H:{name:"H",base:I.ELECTRIC_INDUCTANCE,prefixes:B.SHORT,value:1,offset:0},siemens:{name:"siemens",base:I.ELECTRIC_CONDUCTANCE,prefixes:B.LONG,value:1,offset:0},S:{name:"S",base:I.ELECTRIC_CONDUCTANCE,prefixes:B.SHORT,value:1,offset:0},weber:{name:"weber",base:I.MAGNETIC_FLUX,prefixes:B.LONG,value:1,offset:0},Wb:{name:"Wb",base:I.MAGNETIC_FLUX,prefixes:B.SHORT,value:1,offset:0},tesla:{name:"tesla",base:I.MAGNETIC_FLUX_DENSITY,prefixes:B.LONG,value:1,offset:0},T:{name:"T",base:I.MAGNETIC_FLUX_DENSITY,prefixes:B.SHORT,value:1,offset:0},b:{name:"b",base:I.BIT,prefixes:B.BINARY_SHORT,value:1,offset:0},bits:{name:"bits",base:I.BIT,prefixes:B.BINARY_LONG,value:1,offset:0},B:{name:"B",base:I.BIT,prefixes:B.BINARY_SHORT,value:8,offset:0},bytes:{name:"bytes",base:I.BIT,prefixes:B.BINARY_LONG,value:8,offset:0}},j={meters:"meter",inches:"inch",feet:"foot",yards:"yard",miles:"mile",links:"link",rods:"rod",chains:"chain",angstroms:"angstrom",lt:"l",litres:"litre",liter:"litre",liters:"litre",teaspoons:"teaspoon",tablespoons:"tablespoon",minims:"minim",fluiddrams:"fluiddram",fluidounces:"fluidounce",gills:"gill",cups:"cup",pints:"pint",quarts:"quart",gallons:"gallon",beerbarrels:"beerbarrel",oilbarrels:"oilbarrel",hogsheads:"hogshead",gtts:"gtt",grams:"gram",tons:"ton",tonnes:"tonne",grains:"grain",drams:"dram",ounces:"ounce",poundmasses:"poundmass",hundredweights:"hundredweight",sticks:"stick",lb:"lbm",lbs:"lbm",kips:"kip",kgf:"kilogramforce",acres:"acre",hectares:"hectare",sqfeet:"sqft",sqyard:"sqyd",sqmile:"sqmi",sqmiles:"sqmi",mmhg:"mmHg",mmh2o:"mmH2O",cmh2o:"cmH2O",seconds:"second",secs:"second",minutes:"minute",mins:"minute",hours:"hour",hr:"hour",hrs:"hour",days:"day",weeks:"week",months:"month",years:"year",decades:"decade",centuries:"century",millennia:"millennium",hertz:"hertz",radians:"radian",degrees:"degree",gradians:"gradian",cycles:"cycle",arcsecond:"arcsec",arcseconds:"arcsec",arcminute:"arcmin",arcminutes:"arcmin",BTUs:"BTU",watts:"watt",joules:"joule",amperes:"ampere",amps:"ampere",amp:"ampere",coulombs:"coulomb",volts:"volt",ohms:"ohm",farads:"farad",webers:"weber",teslas:"tesla",electronvolts:"electronvolt",moles:"mole",bit:"bits",byte:"bytes"};function L(e){if("BigNumber"===e.number){var t=An(w);k.rad.value=new w(1),k.deg.value=t.div(180),k.grad.value=t.div(200),k.cycle.value=t.times(2),k.arcsec.value=t.div(648e3),k.arcmin.value=t.div(10800)}else k.rad.value=1,k.deg.value=Math.PI/180,k.grad.value=Math.PI/200,k.cycle.value=2*Math.PI,k.arcsec.value=Math.PI/648e3,k.arcmin.value=Math.PI/10800;k.radian.value=k.rad.value,k.degree.value=k.deg.value,k.gradian.value=k.grad.value}L(o),i&&i("config",(function(e,t){e.number!==t.number&&L(e)}));var U={si:{NONE:{unit:P,prefix:B.NONE[""]},LENGTH:{unit:k.m,prefix:B.SHORT[""]},MASS:{unit:k.g,prefix:B.SHORT.k},TIME:{unit:k.s,prefix:B.SHORT[""]},CURRENT:{unit:k.A,prefix:B.SHORT[""]},TEMPERATURE:{unit:k.K,prefix:B.SHORT[""]},LUMINOUS_INTENSITY:{unit:k.cd,prefix:B.SHORT[""]},AMOUNT_OF_SUBSTANCE:{unit:k.mol,prefix:B.SHORT[""]},ANGLE:{unit:k.rad,prefix:B.SHORT[""]},BIT:{unit:k.bits,prefix:B.SHORT[""]},FORCE:{unit:k.N,prefix:B.SHORT[""]},ENERGY:{unit:k.J,prefix:B.SHORT[""]},POWER:{unit:k.W,prefix:B.SHORT[""]},PRESSURE:{unit:k.Pa,prefix:B.SHORT[""]},ELECTRIC_CHARGE:{unit:k.C,prefix:B.SHORT[""]},ELECTRIC_CAPACITANCE:{unit:k.F,prefix:B.SHORT[""]},ELECTRIC_POTENTIAL:{unit:k.V,prefix:B.SHORT[""]},ELECTRIC_RESISTANCE:{unit:k.ohm,prefix:B.SHORT[""]},ELECTRIC_INDUCTANCE:{unit:k.H,prefix:B.SHORT[""]},ELECTRIC_CONDUCTANCE:{unit:k.S,prefix:B.SHORT[""]},MAGNETIC_FLUX:{unit:k.Wb,prefix:B.SHORT[""]},MAGNETIC_FLUX_DENSITY:{unit:k.T,prefix:B.SHORT[""]},FREQUENCY:{unit:k.Hz,prefix:B.SHORT[""]}}};U.cgs=JSON.parse(JSON.stringify(U.si)),U.cgs.LENGTH={unit:k.m,prefix:B.SHORT.c},U.cgs.MASS={unit:k.g,prefix:B.SHORT[""]},U.cgs.FORCE={unit:k.dyn,prefix:B.SHORT[""]},U.cgs.ENERGY={unit:k.erg,prefix:B.NONE[""]},U.us=JSON.parse(JSON.stringify(U.si)),U.us.LENGTH={unit:k.ft,prefix:B.NONE[""]},U.us.MASS={unit:k.lbm,prefix:B.NONE[""]},U.us.TEMPERATURE={unit:k.degF,prefix:B.NONE[""]},U.us.FORCE={unit:k.lbf,prefix:B.NONE[""]},U.us.ENERGY={unit:k.BTU,prefix:B.BTU[""]},U.us.POWER={unit:k.hp,prefix:B.NONE[""]},U.us.PRESSURE={unit:k.psi,prefix:B.NONE[""]},U.auto=JSON.parse(JSON.stringify(U.si));var q=U.auto;for(var H in x.setUnitSystem=function(e){if(!jt(U,e))throw new Error("Unit system "+e+" does not exist. Choices are: "+Object.keys(U).join(", "));q=U[e]},x.getUnitSystem=function(){for(var e in U)if(jt(U,e)&&U[e]===q)return e},x.typeConverters={BigNumber:function(e){return null!=e&&e.isFraction?new w(e.n).div(e.d).times(e.s):new w(e+"")},Fraction:function(e){return new b(e)},Complex:function(e){return e},number:function(e){return null!=e&&e.isFraction?D(e):e}},x.prototype._numberConverter=function(){var e=x.typeConverters[this.valueType()];if(e)return e;throw new TypeError('Unsupported Unit value type "'+this.valueType()+'"')},x._getNumberConverter=function(e){if(!x.typeConverters[e])throw new TypeError('Unsupported type "'+e+'"');return x.typeConverters[e]},k)if(jt(k,H)){var G=k[H];G.dimensions=G.base.dimensions}for(var V in j)if(jt(j,V)){var Y=k[j[V]],W={};for(var $ in Y)jt(Y,$)&&(W[$]=Y[$]);W.name=V,k[V]=W}return x.isValidAlpha=function(e){return/^[a-zA-Z]$/.test(e)},x.createUnit=function(e,t){if("object"!=typeof e)throw new TypeError("createUnit expects first parameter to be of type 'Object'");if(t&&t.override)for(var n in e)if(jt(e,n)&&x.deleteUnit(n),e[n].aliases)for(var r=0;r<e[n].aliases.length;r++)x.deleteUnit(e[n].aliases[r]);var i;for(var o in e)jt(e,o)&&(i=x.createUnitSingle(o,e[o]));return i},x.createUnitSingle=function(e,t){if(null==t&&(t={}),"string"!=typeof e)throw new TypeError("createUnitSingle expects first parameter to be of type 'string'");if(jt(k,e))throw new Error('Cannot create unit "'+e+'": a unit with that name already exists');!function(e){for(var t=0;t<e.length;t++){if(r=e.charAt(t),0===t&&!x.isValidAlpha(r))throw new Error('Invalid unit name (must begin with alpha character): "'+e+'"');if(t>0&&!x.isValidAlpha(r)&&!C(r))throw new Error('Invalid unit name (only alphanumeric characters are allowed): "'+e+'"')}}(e);var n,i,o,u=null,a=[],s=0;if(t&&"Unit"===t.type)u=t.clone();else if("string"==typeof t)""!==t&&(n=t);else{if("object"!=typeof t)throw new TypeError('Cannot create unit "'+e+'" from "'+t.toString()+'": expecting "string" or "Unit" or "Object"');n=t.definition,i=t.prefixes,s=t.offset,o=t.baseName,t.aliases&&(a=t.aliases.valueOf())}if(a)for(var c=0;c<a.length;c++)if(jt(k,a[c]))throw new Error('Cannot create alias "'+a[c]+'": a unit with that name already exists');if(n&&"string"==typeof n&&!u)try{u=x.parse(n,{allowNoUnits:!0})}catch(t){throw t.message='Could not create unit "'+e+'" from "'+n+'": '+t.message,t}else n&&"Unit"===n.type&&(u=n.clone());a=a||[],s=s||0,i=i&&i.toUpperCase&&B[i.toUpperCase()]||B.NONE;var f={};if(u){f={name:e,value:u.value,dimensions:u.dimensions.slice(0),prefixes:i,offset:s};var l=!1;for(var p in I)if(jt(I,p)){for(var h=!0,d=0;d<R.length;d++)if(Math.abs((f.dimensions[d]||0)-(I[p].dimensions[d]||0))>1e-12){h=!1;break}if(h){l=!0,f.base=I[p];break}}if(!l){o=o||e+"_STUFF";var m={dimensions:u.dimensions.slice(0)};m.key=o,I[o]=m,q[o]={unit:f,prefix:B.NONE[""]},f.base=I[o]}}else{if(o=o||e+"_STUFF",R.indexOf(o)>=0)throw new Error('Cannot create new base unit "'+e+'": a base unit with that name already exists (and cannot be overridden)');for(var g in R.push(o),I)jt(I,g)&&(I[g].dimensions[R.length-1]=0);for(var v={dimensions:[]},D=0;D<R.length;D++)v.dimensions[D]=0;v.dimensions[R.length-1]=1,v.key=o,I[o]=v,f={name:e,value:1,dimensions:I[o].dimensions.slice(0),prefixes:i,offset:s,base:I[o]},q[o]={unit:f,prefix:B.NONE[""]}}x.UNITS[e]=f;for(var y=0;y<a.length;y++){var w=a[y],b={};for(var E in f)jt(f,E)&&(b[E]=f[E]);b.name=w,x.UNITS[w]=b}return delete O.cache,new x(null,e)},x.deleteUnit=function(e){delete x.UNITS[e],delete O.cache},x.PREFIXES=B,x.BASE_DIMENSIONS=R,x.BASE_UNITS=I,x.UNIT_SYSTEMS=U,x.UNITS=k,x}),{isClass:!0}),yu=Lt("divide",["typed","matrix","multiply","equalScalar","divideScalar","inv"],(e=>{var{typed:t,matrix:n,multiply:r,equalScalar:i,divideScalar:o,inv:u}=e,a=xo({typed:t,equalScalar:i}),s=Fo({typed:t});return t("divide",Pt({"Array | Matrix, Array | Matrix":function(e,t){return r(e,u(t))},"DenseMatrix, any":function(e,t){return s(e,t,o,!1)},"SparseMatrix, any":function(e,t){return a(e,t,o,!1)},"Array, any":function(e,t){return s(n(e),t,o,!1).valueOf()},"any, Array | Matrix":function(e,t){return r(e,u(t))}},o.signatures))}));var wu="unit",bu=Lt(wu,["typed","Unit"],(e=>{var{typed:t,Unit:n}=e;return t(wu,{Unit:function(e){return e.clone()},string:function(e){return n.isValuelessUnit(e)?new n(null,e):n.parse(e,{allowNoUnits:!0})},"number | BigNumber | Fraction | Complex, string | Unit":function(e,t){return new n(e,t)},"number | BigNumber | Fraction":function(e){return new n(e)},"Array | Matrix":t.referToSelf((e=>t=>xr(t,e)))})}));var Eu=qt({config:p}),xu=wn({}),Au=_n({}),Cu=Mn({}),Fu=ur({Matrix:Cu}),Nu=yr({BigNumber:Eu,Complex:xu,DenseMatrix:Fu,Fraction:Au}),_u=Gr({typed:Nu}),Mu=ci({typed:Nu}),Su=fi({BigNumber:Eu,typed:Nu}),Ou=Ei({typed:Nu}),Tu=Fi({config:p,typed:Nu}),Bu=_i({typed:Nu}),Ru=Si({typed:Nu}),Iu=Pi({typed:Nu}),zu=Gi({typed:Nu}),Pu=Ji({typed:Nu}),ku=Qi({Matrix:Cu,equalScalar:Tu,typed:Nu}),ju=eo({typed:Nu}),Lu=no({typed:Nu}),Uu=ro({Fraction:Au,typed:Nu}),qu=oo({typed:Nu}),Hu=ao({DenseMatrix:Fu,Matrix:Cu,SparseMatrix:ku,typed:Nu}),Gu=lo({bignumber:Su,fraction:Uu,number:Pu}),Vu=ho({matrix:Hu,config:p,typed:Nu}),Yu=go({BigNumber:Eu,config:p,matrix:Hu,typed:Nu}),Wu=Do({isInteger:Ru,matrix:Hu,typed:Nu}),$u=wo({numeric:Gu,typed:Nu}),Zu=So({DenseMatrix:Fu,concat:Wu,equalScalar:Tu,matrix:Hu,typed:Nu}),Ju=Bo({isNumeric:qu,typed:Nu}),Xu=Io({BigNumber:Eu,DenseMatrix:Fu,SparseMatrix:ku,config:p,matrix:Hu,typed:Nu}),Qu=Uo({BigNumber:Eu,DenseMatrix:Fu,config:p,equalScalar:Tu,matrix:Hu,typed:Nu,zeros:Yu}),Ku=Go({DenseMatrix:Fu,concat:Wu,equalScalar:Tu,matrix:Hu,subtractScalar:ju,typed:Nu,unaryMinus:Lu}),ea=Wo({DenseMatrix:Fu,SparseMatrix:ku,addScalar:Mu,concat:Wu,equalScalar:Tu,matrix:Hu,typed:Nu}),ta=$o({addScalar:Mu,conj:Ou,multiplyScalar:zu,size:Vu,typed:Nu}),na=Qo({DenseMatrix:Fu,config:p,equalScalar:Tu,matrix:Hu,round:Qu,typed:Nu,zeros:Yu}),ra=tu({addScalar:Mu,dot:ta,equalScalar:Tu,matrix:Hu,multiplyScalar:zu,typed:Nu}),ia=ou({DenseMatrix:Fu,config:p,equalScalar:Tu,matrix:Hu,round:Qu,typed:Nu,zeros:Yu}),oa=uu({divideScalar:$u,isZero:Iu,matrix:Hu,multiply:ra,subtractScalar:ju,typed:Nu,unaryMinus:Lu}),ua=fu({Complex:xu,DenseMatrix:Fu,ceil:ia,equalScalar:Tu,floor:na,matrix:Hu,typed:Nu,zeros:Yu}),aa=lu({abs:_u,addScalar:Mu,det:oa,divideScalar:$u,identity:Xu,matrix:Hu,multiply:ra,typed:Nu,unaryMinus:Lu}),sa=pu({Complex:xu,config:p,fraction:Uu,identity:Xu,inv:aa,matrix:Hu,multiply:ra,number:Pu,typed:Nu}),ca=Du({BigNumber:Eu,Complex:xu,Fraction:Au,abs:_u,addScalar:Mu,config:p,divideScalar:$u,equal:Zu,fix:ua,format:Bu,isNumeric:qu,multiplyScalar:zu,number:Pu,pow:sa,round:Qu,subtractScalar:ju}),fa=yu({divideScalar:$u,equalScalar:Tu,inv:aa,matrix:Hu,multiply:ra,typed:Nu}),la=bu({Unit:ca,typed:Nu}),pa=n(1669),ha=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},da={settings:{delayTimer:0,number:{precision:0,thousandsSep:",",decimalsSep:"."},currency:{symbol:"$",format:"%s%v",decimalsSep:".",thousandsSep:",",precision:2}},delay:function(e,t){return clearTimeout(this.settings.delayTimer),this.settings.delayTimer=setTimeout(e,t),this.settings.delayTimer},filterQuery:function(e,t){for(var n=e.split("&"),r=0;r<n.length;r++){var i=n[r].split("=");if(i[0]===t)return i[1]}return!1},filterByData:function(e,t,n){return void 0===n?e.filter((function(e,n){return void 0!==pa(n).data(t)})):e.filter((function(e,r){return pa(r).data(t)==n}))},addNotice:function(e,t,n,r){void 0===n&&(n=!1),void 0===r&&(r=5);var i=pa('<div class="notice-'.concat(e,' notice is-dismissible"><p><strong>').concat(t,"</strong></p></div>")).hide(),o=pa("<button />",{type:"button",class:"notice-dismiss"}),u=pa(".wp-header-end");u.siblings(".notice").remove(),u.before(i.append(o)),i.slideDown(100),o.on("click.wp-dismiss-notice",(function(e){e.preventDefault(),i.fadeTo(100,0,(function(){i.slideUp(100,(function(){i.remove()}))}))})),n&&setTimeout((function(){o.trigger("click.wp-dismiss-notice")}),1e3*r)},imagesLoaded:function(e){var t=e.find('img[src!=""]');if(!t.length)return pa.Deferred().resolve().promise();var n=[];return t.each((function(e,t){var r=pa.Deferred(),i=new Image;n.push(r),i.onload=function(){return r.resolve()},i.onerror=function(){return r.resolve()},i.src=pa(t).attr("src")})),pa.when.apply(pa,n)},getUrlParameter:function(e){if("undefined"!=typeof URLSearchParams)return new URLSearchParams(window.location.search).get(e);e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var t=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(window.location.search);return null===t?"":decodeURIComponent(t[1].replace(/\+/g," "))},getQueryParams:function(e){var t={};return new URLSearchParams(e).forEach((function(e,n){var r=decodeURIComponent(n),i=decodeURIComponent(e);r.endsWith("[]")?(r=r.replace("[]",""),t[r]||(t[r]=[]),t[r].push(i)):t[r]=i})),t},htmlDecode:function(e){var t=document.createElement("div");return t.innerHTML=e,t.childNodes[0].nodeValue},areEquivalent:function(e,t,n){void 0===n&&(n=!1);var r=Object.getOwnPropertyNames(e),i=Object.getOwnPropertyNames(t);if(r.length!=i.length)return!1;for(var o=0;o<r.length;o++){var u=r[o];if(n&&e[u]!==t[u]||!n&&e[u]!=t[u])return!1}return!0},toggleNodes:function(e,t){for(var n=0;n<e.length;n++)e[n].isExpanded="open"==t,e[n].children&&e[n].children.length>0&&this.toggleNodes(e[n].children,t)},formatNumber:function(e,t,n,r,i){void 0===t&&(t=this.settings.number.precision),void 0===n&&(n=this.settings.number.thousandsSep),void 0===r&&(r=this.settings.number.decimalsSep),void 0===i&&(i=1),e>999&&n===r&&!Number.isInteger(e)&&(n="");var o={minimumFractionDigits:t};return i&&(o.minimumSignificantDigits=i),e.toLocaleString("en",o).replace(new RegExp("\\,","g"),n).replace(new RegExp("\\."),r)},formatMoney:function(e,t,n,r,i,o){void 0===t&&(t=this.settings.currency.symbol),void 0===n&&(n=this.settings.currency.precision),void 0===r&&(r=this.settings.currency.thousandsSep),void 0===i&&(i=this.settings.currency.decimalsSep),void 0===o&&(o=this.settings.currency.format),this.isNumeric(e)||(e=this.unformat(e));var u=this.checkCurrencyFormat(o);return(e>0?u.pos:e<0?u.neg:u.zero).replace("%s",t).replace("%v",this.formatNumber(Math.abs(e),this.checkPrecision(n),r,i,null))},unformat:function(e,t){if(void 0===t&&(t=this.settings.number.decimalsSep),"number"==typeof e)return e;var n=new RegExp("[^0-9-".concat(t,"]"),"g"),r=parseFloat((""+e).replace(/\((.*)\)/,"-$1").replace(n,"").replace(t,"."));return isNaN(r)?0:r},checkPrecision:function(e,t){return void 0===t&&(t=0),e=Math.round(Math.abs(e)),isNaN(e)?t:e},checkCurrencyFormat:function(e){if("function"==typeof e)return e();if("string"==typeof e&&e.match("%v"))return{pos:e,neg:e.replace("-","").replace("%v","-%v"),zero:e};if(!e||"object"==typeof e&&(!e.pos||!e.pos.match("%v"))){var t=this.settings.currency.format;return"string"!=typeof t?t:this.settings.currency.format={pos:t,neg:t.replace("%v","-%v"),zero:t}}},countDecimals:function(e){return Math.floor(e)===e?0:e.toString().split(".")[1].length||0},multiplyDecimals:function(e,t){return Pu(ra(Su(this.isNumeric(e)?e:0),Su(this.isNumeric(t)?t:0)))},divideDecimals:function(e,t){return Pu(fa(Su(this.isNumeric(e)?e:0),Su(this.isNumeric(t)?t:0)))},sumDecimals:function(e,t){return Pu(ea(Su(this.isNumeric(e)?e:0),Su(this.isNumeric(t)?t:0)))},subtractDecimals:function(e,t){return Pu(Ku(Su(this.isNumeric(e)?e:0),Su(this.isNumeric(t)?t:0)))},isNumeric:function(e){return Ju(e)},round:function(e,t){return Qu(e,t)},convertUnit:function(e,t,n){return Pu(la(e,t),n)},convertElemsToString:function(e){return pa("<div />").append(e).html()},mergeArrays:function(e,t){return Array.from(new Set(ha(ha([],e,!0),t,!0)))},restrictNumberInputValues:function(e){if("number"===e.attr("type")){var t=e.val(),n=parseFloat(t||"0"),r=e.attr("min"),i=e.attr("max"),o=parseFloat(r||"0"),u=parseFloat(i||"0");this.isNumeric(t)?void 0!==r&&n<o?e.val(o):void 0!==i&&n>u&&e.val(u):e.val(void 0!==r&&!isNaN(o)&&o>0?o:0)}},checkRTL:function(e){var t=!1;switch(pa('html[ dir="rtl" ]').length>0&&(t=!0),e){case"isRTL":case"reverse":return t;case"xSide":return t?"right":"left";default:return!1}},calcTaxesFromBase:function(e,t){var n,r=this,i=[0];return pa.each(t,(function(t,n){if("yes"===n.compound)return!0;i.push(r.divideDecimals(r.multiplyDecimals(e,n.rate),100))})),n=i.reduce((function(e,t){return r.sumDecimals(e,t)}),0),pa.each(t,(function(t,o){var u;if("no"===o.compound)return!0;u=r.divideDecimals(r.multiplyDecimals(r.sumDecimals(e,n),o.rate),100),i.push(u),n=r.sumDecimals(u,n)})),i.reduce((function(e,t){return r.sumDecimals(e,t)}),0)},pseudoClick:function(e,t,n){void 0===n&&(n="both");var r=!1,i=!1,o=t.get(0),u=parseInt(o.getBoundingClientRect().left.toString(),10),a=parseInt(o.getBoundingClientRect().top.toString(),10),s=e.clientX,c=e.clientY;if(["before","both"].includes(n)){var f=window.getComputedStyle(o,":before"),l=u+parseInt(f.getPropertyValue("left"),10),p=l+parseInt(f.width,10),h=a+parseInt(f.getPropertyValue("top"),10),d=h+parseInt(f.height,10);r=s>=l&&s<=p&&c>=h&&c<=d}if(["after","both"].includes(n)){var m=window.getComputedStyle(o,":after"),g=u+parseInt(m.getPropertyValue("left"),10),v=g+parseInt(m.width,10),D=a+parseInt(m.getPropertyValue("top"),10),y=D+parseInt(m.height,10);i=s>=g&&s<=v&&c>=D&&c<=y}switch(n){case"after":return i;case"before":return r;default:return{before:r,after:i}}},isElementInViewport:function(e){var t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom+80<=window.innerHeight&&t.right<=window.innerWidth}};const ma=da;var ga=n(1669),va=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function u(e){try{s(r.next(e))}catch(e){o(e)}}function a(e){try{s(r.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(u,a)}s((r=r.apply(e,t||[])).next())}))},Da=function(e,t){var n,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},u=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return u.next=a(0),u.throw=a(1),u.return=a(2),"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function a(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;u&&(u=0,a[0]&&(o=0)),o;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},ya=function(){function e(e,t,n){this.settings=e,this.addonsPage=t,this.tooltip=n;var r=this.settings.get("autoInstallData");r&&(this.autoInstallParams=ma.getQueryParams(r),this.run())}return e.prototype.run=function(){var e=this,t='\n\t\t\t<div class="atum-modal-content">\n\t\t\t\t<div class="note">'.concat(this.settings.get("toBeInstalled"),'</div>\n\t\t\t\t<hr>\n\t\t\t\t<ul class="auto-install-list">\n\t\t');this.autoInstallParams.key.forEach((function(n,r){t+='\n\t\t\t\t<li data-addon="'.concat(e.autoInstallParams.addon[r],'">\n\t\t\t\t\t<i class="atum-icon atmi-cloud-download atum-tooltip"></i>\n\t\t\t\t\t').concat(e.autoInstallParams.addon[r]," <code>(").concat(e.settings.get("key"),": ").concat(n,")</code>\n\t\t\t\t</li>\n\t\t\t")})),t+="</ul></div>",o().fire({title:this.settings.get("autoInstaller"),html:t,customClass:{container:"atum-modal",popup:"auto-installer-modal"},confirmButtonText:this.settings.get("install"),cancelButtonText:this.settings.get("cancel"),showCancelButton:!0,showCloseButton:!0,reverseButtons:!0,allowOutsideClick:function(){return!o().isLoading()},allowEscapeKey:function(){return!o().isLoading()},showLoaderOnConfirm:!0,preConfirm:function(){return new Promise((function(t,n){return va(e,void 0,void 0,(function(){var e,r,i=this;return Da(this,(function(o){for(r in e=[],this.autoInstallParams.key)e.push(this.maybeInstallAddon(this.autoInstallParams.addon[r],this.autoInstallParams.key[r]));return Promise.all(e).then((function(){i.addonsPage.showSuccessAlert(i.settings.get(e.length>1?"allAddonsInstalled":"addonInstalled"),"",(function(){location.href=i.settings.get("addonsPageUrl")})),t()})).catch((function(){return n()})),[2]}))}))}))}})},e.prototype.maybeInstallAddon=function(e,t){var n=this;return new Promise((function(r,i){ga.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:"atum_validate_license",security:n.settings.get("nonce"),addon:e,key:t},beforeSend:function(){return n.setAddonStatus(e,"installing")},success:function(u){!1!==u.success?n.addonsPage.installAddon(e,t).then((function(t){n.setAddonStatus(e,"success"),r(t)})).catch((function(t){n.setAddonStatus(e,"error"),o().showValidationMessage("<span><strong>".concat(e,":</strong> ").concat(t,"</span>")),i(t)})):(n.setAddonStatus(e,"error"),o().showValidationMessage("<span><strong>".concat(e,":</strong> ").concat(u.data,"</span>")),i(u.data))}})}))},e.prototype.setAddonStatus=function(e,t){var n=ga('.auto-install-list [data-addon="'.concat(e,'"] i')).removeClass("atmi-cloud-download atmi-cloud-sync atmi-cloud-check atmi-cloud"),r=n.closest("ul");switch(this.tooltip.destroyTooltips(r),t){case"success":n.addClass("color-success atmi-cloud-check").attr("title",this.settings.get("addonInstalled"));break;case"error":n.addClass("color-danger atmi-cloud").attr("title",this.settings.get("addonNotInstalled"));break;case"installing":n.addClass("color-primary atmi-cloud-sync").attr("title",this.settings.get("installing"))}this.tooltip.addTooltips(r)},e}();const wa=ya;const ba=function(){function e(e,t){void 0===t&&(t={}),this.varName=e,this.defaults=t,this.settings={};var n=void 0!==window[e]?window[e]:{};Object.assign(this.settings,t,n)}return e.prototype.get=function(e){if(void 0!==this.settings[e])return this.settings[e]},e.prototype.getAll=function(){return this.settings},e.prototype.delete=function(e){this.settings.hasOwnProperty(e)&&delete this.settings[e]},e}();var Ea=n(3029),xa=n.n(Ea),Aa=n(1669),Ca=function(){function e(e){void 0===e&&(e=!0),e&&this.addTooltips()}return e.prototype.addTooltips=function(e){var t=this;e||(e=Aa("body")),e.find(".tips, .atum-tooltip").each((function(e,n){var r=Aa(n),i=r.data("tip")||r.attr("title")||r.attr("data-bs-original-title");if(i){if(t.getInstance(r))return;new(xa())(r.get(0),{html:!0,title:i,container:"body",delay:{show:100,hide:200}}),r.on("inserted.bs.tooltip",(function(e){var t=Aa(e.currentTarget).attr("aria-describedby");Aa('.tooltip[class*="bs-tooltip-"]').not("#".concat(t)).remove()}))}}))},e.prototype.destroyTooltips=function(e){var t=this;e||(e=Aa("body")),e.find(".tips, .atum-tooltip").each((function(e,n){var r=t.getInstance(Aa(n));r&&r.dispose()}))},e.prototype.getInstance=function(e){return xa().getInstance(e.get(0))},e}();const Fa=Ca;var Na=n(1669);const _a=function(){function e(e,t){this.settings=e,this.successCallback=t,this.bindEvents()}return e.prototype.bindEvents=function(){var e=this;Na("body").on("click",".extend-atum-trial",(function(t){t.preventDefault(),t.stopImmediatePropagation();var n=Na(t.currentTarget);e.extendTrialConfirmation(n.closest(".atum-addon").data("addon"),n.data("key"))}))},e.prototype.extendTrialConfirmation=function(e,t){var n=this;o().fire({title:this.settings.get("trialExtension"),text:this.settings.get("trialWillExtend"),icon:"info",showCancelButton:!0,confirmButtonText:this.settings.get("extend"),cancelButtonText:this.settings.get("cancel"),showCloseButton:!0,allowEnterKey:!1,reverseButtons:!0,showLoaderOnConfirm:!0,preConfirm:function(){return n.extendTrial(e,t,!0,(function(e){e.success?o().fire({title:n.settings.get("success"),html:e.data,icon:"success",confirmButtonText:n.settings.get("ok")}).then((function(e){n.successCallback&&e.isConfirmed&&n.successCallback()})):o().showValidationMessage(e.data)}))}})},e.prototype.extendTrial=function(e,t,n,r){var i=this;return void 0===n&&(n=!1),void 0===r&&(r=null),new Promise((function(n){Na.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:"atum_extend_trial",security:i.settings.get("nonce"),addon:e,key:t},success:function(e){r&&r(e),n()}})}))},e}();n(1669)((function(e){var t=new ba("atumAddons"),n=new Fa,r=new _a(t),i=new a(t,r);new wa(t,i,n)}))})()})();