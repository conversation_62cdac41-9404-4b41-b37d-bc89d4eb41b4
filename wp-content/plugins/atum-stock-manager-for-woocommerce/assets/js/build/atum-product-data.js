(()=>{"use strict";var t={1669:t=>{t.exports=jQuery}},e={};function n(i){var a=e[i];if(void 0!==a)return a.exports;var o=e[i]={exports:{}};return t[i](o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var i=n(1669),a=function(){return a=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},a.apply(this,arguments)};const o=function(){function t(t,e,n){void 0===n&&(n=!1),this.$buttons=t,this.options=e,this.preview=n,this.defaultOptions={frame:"select",multiple:!1},this.wpHooks=window.wp.hooks,this.doFileUploaders()}return t.prototype.doFileUploaders=function(){var t=this;window.wp.hasOwnProperty("media")&&this.$buttons.on("click",(function(e){var n=i(e.currentTarget),o=a(a({},t.defaultOptions),t.options);n.data("modal-title")&&(o.title=n.data("modal-title")),n.data("modal-button")&&(o.button={text:n.data("modal-button")});var s=window.wp.media(o).on("select",(function(){var e=s.state().get("selection"),i=o.multiple?e.toJSON():e.first().toJSON(),a=n.siblings("input:hidden");if(o.multiple){var c=[];i.forEach((function(t){c.push(t.id)})),a.val(JSON.stringify(t.wpHooks.applyFilters("atum_fileUploader_inputVal",c,a)))}else a.val(t.wpHooks.applyFilters("atum_fileUploader_inputVal",i.id,a));t.preview&&(!o.library.type||o.library.type.indexOf("image")>-1)&&(n.siblings("img").remove(),o.multiple?i.forEach((function(t){n.after('<img class="atum-file-uploader__preview" src="'.concat(t.url,'">'))})):n.after('<img class="atum-file-uploader__preview" src="'.concat(i.url,'">'))),t.wpHooks.doAction("atum_fileUploader_selected",s,n)})).open()}))},t}();var s=n(1669);const c=function(){function t(t){var e=this;this.settings=t,this.$emailSelector=s("<select>",{class:"attach-to-email"}),this.wpHooks=window.wp.hooks,this.$attachmentsList=s(".atum-attachments-list"),this.$input=s("#atum-attachments"),s.each(this.settings.get("emailNotifications"),(function(t,n){e.$emailSelector.append('\n\t\t\t\t<option value="'.concat(t,'">').concat(n,"</option>\n\t\t\t"))})),this.addHooks(),this.bindEvents();new o(s("#atum_files").find(".atum-file-uploader"),{multiple:!0})}return t.prototype.addHooks=function(){var t=this;this.wpHooks.addAction("atum_fileUploader_selected","atum",(function(e){e.state().get("selection").toJSON().forEach((function(e){var n=s("<li>").data("id",e.id),i=e.hasOwnProperty("url")?e.url:e.sizes.full.url,a=e.hasOwnProperty("sizes")?e.sizes.medium.url:i;n.append("<label>".concat(t.settings.get("attachToEmail"),"</label>")).append(t.$emailSelector.clone());var o="";o=["jpg","jpeg","jpe","gif","png","webp","svg"].includes(e.subtype)&&a?'<img src="'.concat(a,'" alt="').concat(e.title,'">'):'<div class="atum-attachment-icon"><i class="atum-icon atmi-file-empty" title="'.concat(e.title,'"></i></div>'),n.append('\n\t\t\t\t\t<a href="'.concat(i,'" target="_blank" title="').concat(e.title,'">\n\t\t\t\t\t\t').concat(o,'\n\t\t\t\t\t</a>\n\t\t\t\t\t<i class="delete-attachment dashicons dashicons-dismiss atum-tooltip" title="').concat(t.settings.get("deleteAttachment"),'"></i>\n\t\t\t\t')),t.$attachmentsList.append(n)})),t.updateInput()}))},t.prototype.bindEvents=function(){var t=this;this.$attachmentsList.on("change",".attach-to-email",(function(){return t.updateInput()})).on("click",".delete-attachment",(function(e){var n=s(e.currentTarget),i=n.attr("aria-describedby");n.closest("li").remove(),s("#".concat(i)).remove(),t.updateInput()}))},t.prototype.updateInput=function(){var t=[];this.$attachmentsList.find("li").each((function(e,n){var i=s(n);t.push({id:i.data("id"),email:i.find(".attach-to-email").val()})})),this.$input.val(JSON.stringify(t))},t}();var r=n(1669);const l={doButtonGroups:function(t){var e=this;t.on("click",".btn-group .btn",(function(t){var n=r(t.currentTarget);return n.find(":checkbox").length?n.toggleClass("active"):(n.siblings(".active").removeClass("active"),n.addClass("active")),e.updateChecked(n.closest(".btn-group")),n.find("input").trigger("change"),!1}))},updateChecked:function(t){t.find(".btn").each((function(t,e){var n=r(e);n.find("input").prop("checked",n.hasClass("active"))}))}};var u=n(1669),d=function(){return d=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},d.apply(this,arguments)};const p=function(){function t(t){void 0===t&&(t=null);var e=this;this.addAtumClasses(t),u("body").on("wc-enhanced-select-init",(function(){return e.addAtumClasses(t)}))}return t.prototype.maybeRestoreEnhancedSelect=function(){u(".select2-container--open").remove(),u("body").trigger("wc-enhanced-select-init")},t.prototype.doSelect2=function(t,e,n){var i=this;void 0===e&&(e={}),void 0===n&&(n=!1),"function"==typeof u.fn.select2&&(e=Object.assign({minimumResultsForSearch:10},e),t.each((function(t,a){var o=u(a),s=d({},e);o.hasClass("atum-select-multiple")&&!1===o.prop("multiple")&&o.prop("multiple",!0),o.hasClass("atum-select2")||(o.addClass("atum-select2"),i.addAtumClasses(o)),n&&o.on("select2:selecting",(function(t){var e=u(t.currentTarget),n=e.val();Array.isArray(n)&&(u.inArray("",n)>-1||u.inArray("-1",n)>-1)&&(u.each(n,(function(t,e){""!==e&&"-1"!==e||n.splice(t,1)})),e.val(n))})),o.select2(s),o.siblings(".select2-container").addClass("atum-select2"),i.maybeAddTooltip(o)})))},t.prototype.addAtumClasses=function(t){var e=this;void 0===t&&(t=null),(t=t||u("select").filter(".atum-select2, .atum-enhanced-select")).length&&t.each((function(t,n){var i=u(n),a=i.siblings(".select2-container").not(".atum-select2, .atum-enhanced-select");a.length&&(a.addClass(i.hasClass("atum-select2")?"atum-select2":"atum-enhanced-select"),e.maybeAddTooltip(i))})).on("select2:opening",(function(t){var e=u(t.currentTarget).data();if(e.hasOwnProperty("select2")){var n=e.select2.dropdown.$dropdown;n.length&&n.addClass("atum-select2-dropdown")}}))},t.prototype.maybeAddTooltip=function(t){t.hasClass("atum-tooltip")&&t.siblings(".select2-container").find(".select2-selection__rendered").addClass("atum-tooltip")},t}(),f=Swal;var h=n.n(f),m=n(1669);const g=function(){function t(t){var e=this;this.settings=t,this.$productDataMetaBox=m("#woocommerce-product-data"),new p,l.doButtonGroups(this.$productDataMetaBox),this.$productDataMetaBox.on("woocommerce_variations_loaded woocommerce_variations_added",(function(){l.doButtonGroups(e.$productDataMetaBox.find(".woocommerce_variations")),e.maybeBlockFields()})),m("#_manage_stock").on("change",(function(t){return m("#_out_stock_threshold").closest(".options_group").css("display",m(t.currentTarget).is(":checked")?"block":"none")})).trigger("change"),m(".product-tab-runner").find(".run-script").on("click",(function(t){var n=m(t.currentTarget),i=n.siblings("select").val();h().fire({title:e.settings.get("areYouSure"),text:n.data("confirm").replace("%s",'"'.concat(i,'"')),icon:"warning",showCancelButton:!0,confirmButtonText:e.settings.get("continue"),cancelButtonText:e.settings.get("cancel"),reverseButtons:!0,showLoaderOnConfirm:!0,preConfirm:function(){return new Promise((function(t,a){m.ajax({url:window.ajaxurl,data:{action:n.data("action"),security:e.settings.get("nonce"),parent_id:m("#post_ID").val(),value:i},method:"POST",dataType:"json",success:function(e){"object"==typeof e&&!0===e.success||h().showValidationMessage(e.data),t(e.data)}})}))},allowOutsideClick:function(){return!h().isLoading()}}).then((function(t){t.isConfirmed&&h().fire({icon:"success",title:e.settings.get("success"),text:t.value}).then((function(){return location.reload()}))}))})),this.$productDataMetaBox.on("focus select2:opening",".atum-field :input",(function(t){return m(t.target).siblings(".input-group-prepend").addClass("focus")})).on("blur select2:close",".atum-field :input",(function(t){return m(t.target).siblings(".input-group-prepend").removeClass("focus")})),this.maybeBlockFields()}return t.prototype.maybeBlockFields=function(){void 0!==this.settings.get("lockFields")&&"yes"===this.settings.get("lockFields")&&(m(".atum-field input").each((function(t,e){m(e).prop("readonly",!0).next().after(m(".wcml_lock_img").clone().removeClass("wcml_lock_img").show())})),m(".atum-field select").each((function(t,e){m(e).prop("disabled",!0).next().next().after(m(".wcml_lock_img").clone().removeClass("wcml_lock_img").show())})))},t}();const v=function(){function t(t,e){void 0===e&&(e={}),this.varName=t,this.defaults=e,this.settings={};var n=void 0!==window[t]?window[t]:{};Object.assign(this.settings,e,n)}return t.prototype.get=function(t){if(void 0!==this.settings[t])return this.settings[t]},t.prototype.getAll=function(){return this.settings},t.prototype.delete=function(t){this.settings.hasOwnProperty(t)&&delete this.settings[t]},t}();n(1669)((function(t){var e=new v("atumProductData");new g(e),new c(e)}))})();