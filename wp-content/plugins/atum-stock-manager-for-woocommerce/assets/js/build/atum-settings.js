(()=>{var e={1170:(e,t,n)=>{"use strict";n.r(t),n.d(t,{afterMain:()=>v,afterRead:()=>g,afterWrite:()=>k,applyStyles:()=>O,arrow:()=>Q,auto:()=>o,basePlacements:()=>d,beforeMain:()=>L,beforeRead:()=>y,beforeWrite:()=>w,bottom:()=>s,clippingParents:()=>c,computeStyles:()=>ne,createPopper:()=>Oe,createPopperBase:()=>je,createPopperLite:()=>Pe,detectOverflow:()=>Me,end:()=>l,eventListeners:()=>se,flip:()=>ge,hide:()=>ve,left:()=>i,main:()=>Y,modifierPhases:()=>D,offset:()=>we,placements:()=>f,popper:()=>m,popperGenerator:()=>He,popperOffsets:()=>be,preventOverflow:()=>ke,read:()=>M,reference:()=>h,right:()=>r,start:()=>u,top:()=>a,variationPlacements:()=>p,viewport:()=>_,write:()=>b});var a="top",s="bottom",r="right",i="left",o="auto",d=[a,s,r,i],u="start",l="end",c="clippingParents",_="viewport",m="popper",h="reference",p=d.reduce((function(e,t){return e.concat([t+"-"+u,t+"-"+l])}),[]),f=[].concat(d,[o]).reduce((function(e,t){return e.concat([t,t+"-"+u,t+"-"+l])}),[]),y="beforeRead",M="read",g="afterRead",L="beforeMain",Y="main",v="afterMain",w="beforeWrite",b="write",k="afterWrite",D=[y,M,g,L,Y,v,w,b,k];function T(e){return e?(e.nodeName||"").toLowerCase():null}function S(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function x(e){return e instanceof S(e).Element||e instanceof Element}function H(e){return e instanceof S(e).HTMLElement||e instanceof HTMLElement}function j(e){return"undefined"!=typeof ShadowRoot&&(e instanceof S(e).ShadowRoot||e instanceof ShadowRoot)}const O={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},a=t.attributes[e]||{},s=t.elements[e];H(s)&&T(s)&&(Object.assign(s.style,n),Object.keys(a).forEach((function(e){var t=a[e];!1===t?s.removeAttribute(e):s.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var a=t.elements[e],s=t.attributes[e]||{},r=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});H(a)&&T(a)&&(Object.assign(a.style,r),Object.keys(s).forEach((function(e){a.removeAttribute(e)})))}))}},requires:["computeStyles"]};function P(e){return e.split("-")[0]}var E=Math.max,A=Math.min,C=Math.round;function W(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function F(){return!/^((?!chrome|android).)*safari/i.test(W())}function $(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var a=e.getBoundingClientRect(),s=1,r=1;t&&H(e)&&(s=e.offsetWidth>0&&C(a.width)/e.offsetWidth||1,r=e.offsetHeight>0&&C(a.height)/e.offsetHeight||1);var i=(x(e)?S(e):window).visualViewport,o=!F()&&n,d=(a.left+(o&&i?i.offsetLeft:0))/s,u=(a.top+(o&&i?i.offsetTop:0))/r,l=a.width/s,c=a.height/r;return{width:l,height:c,top:u,right:d+l,bottom:u+c,left:d,x:d,y:u}}function N(e){var t=$(e),n=e.offsetWidth,a=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-a)<=1&&(a=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:a}}function z(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&j(n)){var a=t;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function I(e){return S(e).getComputedStyle(e)}function R(e){return["table","td","th"].indexOf(T(e))>=0}function U(e){return((x(e)?e.ownerDocument:e.document)||window.document).documentElement}function J(e){return"html"===T(e)?e:e.assignedSlot||e.parentNode||(j(e)?e.host:null)||U(e)}function q(e){return H(e)&&"fixed"!==I(e).position?e.offsetParent:null}function B(e){for(var t=S(e),n=q(e);n&&R(n)&&"static"===I(n).position;)n=q(n);return n&&("html"===T(n)||"body"===T(n)&&"static"===I(n).position)?t:n||function(e){var t=/firefox/i.test(W());if(/Trident/i.test(W())&&H(e)&&"fixed"===I(e).position)return null;var n=J(e);for(j(n)&&(n=n.host);H(n)&&["html","body"].indexOf(T(n))<0;){var a=I(n);if("none"!==a.transform||"none"!==a.perspective||"paint"===a.contain||-1!==["transform","perspective"].indexOf(a.willChange)||t&&"filter"===a.willChange||t&&a.filter&&"none"!==a.filter)return n;n=n.parentNode}return null}(e)||t}function G(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function V(e,t,n){return E(e,A(t,n))}function K(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Z(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}const Q={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,u=e.options,l=n.elements.arrow,c=n.modifiersData.popperOffsets,_=P(n.placement),m=G(_),h=[i,r].indexOf(_)>=0?"height":"width";if(l&&c){var p=function(e,t){return K("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Z(e,d))}(u.padding,n),f=N(l),y="y"===m?a:i,M="y"===m?s:r,g=n.rects.reference[h]+n.rects.reference[m]-c[m]-n.rects.popper[h],L=c[m]-n.rects.reference[m],Y=B(l),v=Y?"y"===m?Y.clientHeight||0:Y.clientWidth||0:0,w=g/2-L/2,b=p[y],k=v-f[h]-p[M],D=v/2-f[h]/2+w,T=V(b,D,k),S=m;n.modifiersData[o]=((t={})[S]=T,t.centerOffset=T-D,t)}},effect:function(e){var t=e.state,n=e.options.element,a=void 0===n?"[data-popper-arrow]":n;null!=a&&("string"!=typeof a||(a=t.elements.popper.querySelector(a)))&&z(t.elements.popper,a)&&(t.elements.arrow=a)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function X(e){return e.split("-")[1]}var ee={top:"auto",right:"auto",bottom:"auto",left:"auto"};function te(e){var t,n=e.popper,o=e.popperRect,d=e.placement,u=e.variation,c=e.offsets,_=e.position,m=e.gpuAcceleration,h=e.adaptive,p=e.roundOffsets,f=e.isFixed,y=c.x,M=void 0===y?0:y,g=c.y,L=void 0===g?0:g,Y="function"==typeof p?p({x:M,y:L}):{x:M,y:L};M=Y.x,L=Y.y;var v=c.hasOwnProperty("x"),w=c.hasOwnProperty("y"),b=i,k=a,D=window;if(h){var T=B(n),x="clientHeight",H="clientWidth";if(T===S(n)&&"static"!==I(T=U(n)).position&&"absolute"===_&&(x="scrollHeight",H="scrollWidth"),d===a||(d===i||d===r)&&u===l)k=s,L-=(f&&T===D&&D.visualViewport?D.visualViewport.height:T[x])-o.height,L*=m?1:-1;if(d===i||(d===a||d===s)&&u===l)b=r,M-=(f&&T===D&&D.visualViewport?D.visualViewport.width:T[H])-o.width,M*=m?1:-1}var j,O=Object.assign({position:_},h&&ee),P=!0===p?function(e,t){var n=e.x,a=e.y,s=t.devicePixelRatio||1;return{x:C(n*s)/s||0,y:C(a*s)/s||0}}({x:M,y:L},S(n)):{x:M,y:L};return M=P.x,L=P.y,m?Object.assign({},O,((j={})[k]=w?"0":"",j[b]=v?"0":"",j.transform=(D.devicePixelRatio||1)<=1?"translate("+M+"px, "+L+"px)":"translate3d("+M+"px, "+L+"px, 0)",j)):Object.assign({},O,((t={})[k]=w?L+"px":"",t[b]=v?M+"px":"",t.transform="",t))}const ne={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,a=n.gpuAcceleration,s=void 0===a||a,r=n.adaptive,i=void 0===r||r,o=n.roundOffsets,d=void 0===o||o,u={placement:P(t.placement),variation:X(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,te(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:d})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,te(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:d})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var ae={passive:!0};const se={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,a=e.options,s=a.scroll,r=void 0===s||s,i=a.resize,o=void 0===i||i,d=S(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return r&&u.forEach((function(e){e.addEventListener("scroll",n.update,ae)})),o&&d.addEventListener("resize",n.update,ae),function(){r&&u.forEach((function(e){e.removeEventListener("scroll",n.update,ae)})),o&&d.removeEventListener("resize",n.update,ae)}},data:{}};var re={left:"right",right:"left",bottom:"top",top:"bottom"};function ie(e){return e.replace(/left|right|bottom|top/g,(function(e){return re[e]}))}var oe={start:"end",end:"start"};function de(e){return e.replace(/start|end/g,(function(e){return oe[e]}))}function ue(e){var t=S(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function le(e){return $(U(e)).left+ue(e).scrollLeft}function ce(e){var t=I(e),n=t.overflow,a=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+s+a)}function _e(e){return["html","body","#document"].indexOf(T(e))>=0?e.ownerDocument.body:H(e)&&ce(e)?e:_e(J(e))}function me(e,t){var n;void 0===t&&(t=[]);var a=_e(e),s=a===(null==(n=e.ownerDocument)?void 0:n.body),r=S(a),i=s?[r].concat(r.visualViewport||[],ce(a)?a:[]):a,o=t.concat(i);return s?o:o.concat(me(J(i)))}function he(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function pe(e,t,n){return t===_?he(function(e,t){var n=S(e),a=U(e),s=n.visualViewport,r=a.clientWidth,i=a.clientHeight,o=0,d=0;if(s){r=s.width,i=s.height;var u=F();(u||!u&&"fixed"===t)&&(o=s.offsetLeft,d=s.offsetTop)}return{width:r,height:i,x:o+le(e),y:d}}(e,n)):x(t)?function(e,t){var n=$(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):he(function(e){var t,n=U(e),a=ue(e),s=null==(t=e.ownerDocument)?void 0:t.body,r=E(n.scrollWidth,n.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),i=E(n.scrollHeight,n.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),o=-a.scrollLeft+le(e),d=-a.scrollTop;return"rtl"===I(s||n).direction&&(o+=E(n.clientWidth,s?s.clientWidth:0)-r),{width:r,height:i,x:o,y:d}}(U(e)))}function fe(e,t,n,a){var s="clippingParents"===t?function(e){var t=me(J(e)),n=["absolute","fixed"].indexOf(I(e).position)>=0&&H(e)?B(e):e;return x(n)?t.filter((function(e){return x(e)&&z(e,n)&&"body"!==T(e)})):[]}(e):[].concat(t),r=[].concat(s,[n]),i=r[0],o=r.reduce((function(t,n){var s=pe(e,n,a);return t.top=E(s.top,t.top),t.right=A(s.right,t.right),t.bottom=A(s.bottom,t.bottom),t.left=E(s.left,t.left),t}),pe(e,i,a));return o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}function ye(e){var t,n=e.reference,o=e.element,d=e.placement,c=d?P(d):null,_=d?X(d):null,m=n.x+n.width/2-o.width/2,h=n.y+n.height/2-o.height/2;switch(c){case a:t={x:m,y:n.y-o.height};break;case s:t={x:m,y:n.y+n.height};break;case r:t={x:n.x+n.width,y:h};break;case i:t={x:n.x-o.width,y:h};break;default:t={x:n.x,y:n.y}}var p=c?G(c):null;if(null!=p){var f="y"===p?"height":"width";switch(_){case u:t[p]=t[p]-(n[f]/2-o[f]/2);break;case l:t[p]=t[p]+(n[f]/2-o[f]/2)}}return t}function Me(e,t){void 0===t&&(t={});var n=t,i=n.placement,o=void 0===i?e.placement:i,u=n.strategy,l=void 0===u?e.strategy:u,p=n.boundary,f=void 0===p?c:p,y=n.rootBoundary,M=void 0===y?_:y,g=n.elementContext,L=void 0===g?m:g,Y=n.altBoundary,v=void 0!==Y&&Y,w=n.padding,b=void 0===w?0:w,k=K("number"!=typeof b?b:Z(b,d)),D=L===m?h:m,T=e.rects.popper,S=e.elements[v?D:L],H=fe(x(S)?S:S.contextElement||U(e.elements.popper),f,M,l),j=$(e.elements.reference),O=ye({reference:j,element:T,strategy:"absolute",placement:o}),P=he(Object.assign({},T,O)),E=L===m?P:j,A={top:H.top-E.top+k.top,bottom:E.bottom-H.bottom+k.bottom,left:H.left-E.left+k.left,right:E.right-H.right+k.right},C=e.modifiersData.offset;if(L===m&&C){var W=C[o];Object.keys(A).forEach((function(e){var t=[r,s].indexOf(e)>=0?1:-1,n=[a,s].indexOf(e)>=0?"y":"x";A[e]+=W[n]*t}))}return A}const ge={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,l=e.name;if(!t.modifiersData[l]._skip){for(var c=n.mainAxis,_=void 0===c||c,m=n.altAxis,h=void 0===m||m,y=n.fallbackPlacements,M=n.padding,g=n.boundary,L=n.rootBoundary,Y=n.altBoundary,v=n.flipVariations,w=void 0===v||v,b=n.allowedAutoPlacements,k=t.options.placement,D=P(k),T=y||(D===k||!w?[ie(k)]:function(e){if(P(e)===o)return[];var t=ie(e);return[de(e),t,de(t)]}(k)),S=[k].concat(T).reduce((function(e,n){return e.concat(P(n)===o?function(e,t){void 0===t&&(t={});var n=t,a=n.placement,s=n.boundary,r=n.rootBoundary,i=n.padding,o=n.flipVariations,u=n.allowedAutoPlacements,l=void 0===u?f:u,c=X(a),_=c?o?p:p.filter((function(e){return X(e)===c})):d,m=_.filter((function(e){return l.indexOf(e)>=0}));0===m.length&&(m=_);var h=m.reduce((function(t,n){return t[n]=Me(e,{placement:n,boundary:s,rootBoundary:r,padding:i})[P(n)],t}),{});return Object.keys(h).sort((function(e,t){return h[e]-h[t]}))}(t,{placement:n,boundary:g,rootBoundary:L,padding:M,flipVariations:w,allowedAutoPlacements:b}):n)}),[]),x=t.rects.reference,H=t.rects.popper,j=new Map,O=!0,E=S[0],A=0;A<S.length;A++){var C=S[A],W=P(C),F=X(C)===u,$=[a,s].indexOf(W)>=0,N=$?"width":"height",z=Me(t,{placement:C,boundary:g,rootBoundary:L,altBoundary:Y,padding:M}),I=$?F?r:i:F?s:a;x[N]>H[N]&&(I=ie(I));var R=ie(I),U=[];if(_&&U.push(z[W]<=0),h&&U.push(z[I]<=0,z[R]<=0),U.every((function(e){return e}))){E=C,O=!1;break}j.set(C,U)}if(O)for(var J=function(e){var t=S.find((function(t){var n=j.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return E=t,"break"},q=w?3:1;q>0;q--){if("break"===J(q))break}t.placement!==E&&(t.modifiersData[l]._skip=!0,t.placement=E,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Le(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Ye(e){return[a,r,s,i].some((function(t){return e[t]>=0}))}const ve={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,a=t.rects.reference,s=t.rects.popper,r=t.modifiersData.preventOverflow,i=Me(t,{elementContext:"reference"}),o=Me(t,{altBoundary:!0}),d=Le(i,a),u=Le(o,s,r),l=Ye(d),c=Ye(u);t.modifiersData[n]={referenceClippingOffsets:d,popperEscapeOffsets:u,isReferenceHidden:l,hasPopperEscaped:c},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":l,"data-popper-escaped":c})}};const we={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,s=e.name,o=n.offset,d=void 0===o?[0,0]:o,u=f.reduce((function(e,n){return e[n]=function(e,t,n){var s=P(e),o=[i,a].indexOf(s)>=0?-1:1,d="function"==typeof n?n(Object.assign({},t,{placement:e})):n,u=d[0],l=d[1];return u=u||0,l=(l||0)*o,[i,r].indexOf(s)>=0?{x:l,y:u}:{x:u,y:l}}(n,t.rects,d),e}),{}),l=u[t.placement],c=l.x,_=l.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=_),t.modifiersData[s]=u}};const be={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=ye({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}};const ke={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,d=n.mainAxis,l=void 0===d||d,c=n.altAxis,_=void 0!==c&&c,m=n.boundary,h=n.rootBoundary,p=n.altBoundary,f=n.padding,y=n.tether,M=void 0===y||y,g=n.tetherOffset,L=void 0===g?0:g,Y=Me(t,{boundary:m,rootBoundary:h,padding:f,altBoundary:p}),v=P(t.placement),w=X(t.placement),b=!w,k=G(v),D="x"===k?"y":"x",T=t.modifiersData.popperOffsets,S=t.rects.reference,x=t.rects.popper,H="function"==typeof L?L(Object.assign({},t.rects,{placement:t.placement})):L,j="number"==typeof H?{mainAxis:H,altAxis:H}:Object.assign({mainAxis:0,altAxis:0},H),O=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,C={x:0,y:0};if(T){if(l){var W,F="y"===k?a:i,$="y"===k?s:r,z="y"===k?"height":"width",I=T[k],R=I+Y[F],U=I-Y[$],J=M?-x[z]/2:0,q=w===u?S[z]:x[z],K=w===u?-x[z]:-S[z],Z=t.elements.arrow,Q=M&&Z?N(Z):{width:0,height:0},ee=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},te=ee[F],ne=ee[$],ae=V(0,S[z],Q[z]),se=b?S[z]/2-J-ae-te-j.mainAxis:q-ae-te-j.mainAxis,re=b?-S[z]/2+J+ae+ne+j.mainAxis:K+ae+ne+j.mainAxis,ie=t.elements.arrow&&B(t.elements.arrow),oe=ie?"y"===k?ie.clientTop||0:ie.clientLeft||0:0,de=null!=(W=null==O?void 0:O[k])?W:0,ue=I+re-de,le=V(M?A(R,I+se-de-oe):R,I,M?E(U,ue):U);T[k]=le,C[k]=le-I}if(_){var ce,_e="x"===k?a:i,me="x"===k?s:r,he=T[D],pe="y"===D?"height":"width",fe=he+Y[_e],ye=he-Y[me],ge=-1!==[a,i].indexOf(v),Le=null!=(ce=null==O?void 0:O[D])?ce:0,Ye=ge?fe:he-S[pe]-x[pe]-Le+j.altAxis,ve=ge?he+S[pe]+x[pe]-Le-j.altAxis:ye,we=M&&ge?function(e,t,n){var a=V(e,t,n);return a>n?n:a}(Ye,he,ve):V(M?Ye:fe,he,M?ve:ye);T[D]=we,C[D]=we-he}t.modifiersData[o]=C}},requiresIfExists:["offset"]};function De(e,t,n){void 0===n&&(n=!1);var a,s,r=H(t),i=H(t)&&function(e){var t=e.getBoundingClientRect(),n=C(t.width)/e.offsetWidth||1,a=C(t.height)/e.offsetHeight||1;return 1!==n||1!==a}(t),o=U(t),d=$(e,i,n),u={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(r||!r&&!n)&&(("body"!==T(t)||ce(o))&&(u=(a=t)!==S(a)&&H(a)?{scrollLeft:(s=a).scrollLeft,scrollTop:s.scrollTop}:ue(a)),H(t)?((l=$(t,!0)).x+=t.clientLeft,l.y+=t.clientTop):o&&(l.x=le(o))),{x:d.left+u.scrollLeft-l.x,y:d.top+u.scrollTop-l.y,width:d.width,height:d.height}}function Te(e){var t=new Map,n=new Set,a=[];function s(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var a=t.get(e);a&&s(a)}})),a.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||s(e)})),a}var Se={placement:"bottom",modifiers:[],strategy:"absolute"};function xe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function He(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,a=void 0===n?[]:n,s=t.defaultOptions,r=void 0===s?Se:s;return function(e,t,n){void 0===n&&(n=r);var s,i,o={placement:"bottom",orderedModifiers:[],options:Object.assign({},Se,r),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},d=[],u=!1,l={state:o,setOptions:function(n){var s="function"==typeof n?n(o.options):n;c(),o.options=Object.assign({},r,o.options,s),o.scrollParents={reference:x(e)?me(e):e.contextElement?me(e.contextElement):[],popper:me(t)};var i,u,_=function(e){var t=Te(e);return D.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((i=[].concat(a,o.options.modifiers),u=i.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(u).map((function(e){return u[e]}))));return o.orderedModifiers=_.filter((function(e){return e.enabled})),o.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,a=void 0===n?{}:n,s=e.effect;if("function"==typeof s){var r=s({state:o,name:t,instance:l,options:a}),i=function(){};d.push(r||i)}})),l.update()},forceUpdate:function(){if(!u){var e=o.elements,t=e.reference,n=e.popper;if(xe(t,n)){o.rects={reference:De(t,B(n),"fixed"===o.options.strategy),popper:N(n)},o.reset=!1,o.placement=o.options.placement,o.orderedModifiers.forEach((function(e){return o.modifiersData[e.name]=Object.assign({},e.data)}));for(var a=0;a<o.orderedModifiers.length;a++)if(!0!==o.reset){var s=o.orderedModifiers[a],r=s.fn,i=s.options,d=void 0===i?{}:i,c=s.name;"function"==typeof r&&(o=r({state:o,options:d,name:c,instance:l})||o)}else o.reset=!1,a=-1}}},update:(s=function(){return new Promise((function(e){l.forceUpdate(),e(o)}))},function(){return i||(i=new Promise((function(e){Promise.resolve().then((function(){i=void 0,e(s())}))}))),i}),destroy:function(){c(),u=!0}};if(!xe(e,t))return l;function c(){d.forEach((function(e){return e()})),d=[]}return l.setOptions(n).then((function(e){!u&&n.onFirstUpdate&&n.onFirstUpdate(e)})),l}}var je=He(),Oe=He({defaultModifiers:[se,be,ne,O,we,ge,ke,Q,ve]}),Pe=He({defaultModifiers:[se,be,ne,O]})},1669:e=>{"use strict";e.exports=jQuery},2105:function(e,t,n){e.exports=function(e,t){"use strict";class n{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(n,a){const s=t.isElement(a)?e.getDataAttribute(a,"config"):{};return{...this.constructor.Default,..."object"==typeof s?s:{},...t.isElement(a)?e.getDataAttributes(a):{},..."object"==typeof n?n:{}}}_typeCheckConfig(e,n=this.constructor.DefaultType){for(const[a,s]of Object.entries(n)){const n=e[a],r=t.isElement(n)?"element":t.toType(n);if(!new RegExp(s).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${a}" provided type "${r}" but expected type "${s}".`)}}}return n}(n(2333),n(4035))},2333:function(e){e.exports=function(){"use strict";function e(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function t(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}return{setDataAttribute(e,n,a){e.setAttribute(`data-bs-${t(n)}`,a)},removeDataAttribute(e,n){e.removeAttribute(`data-bs-${t(n)}`)},getDataAttributes(t){if(!t)return{};const n={},a=Object.keys(t.dataset).filter((e=>e.startsWith("bs")&&!e.startsWith("bsConfig")));for(const s of a){let a=s.replace(/^bs/,"");a=a.charAt(0).toLowerCase()+a.slice(1),n[a]=e(t.dataset[s])}return n},getDataAttribute:(n,a)=>e(n.getAttribute(`data-bs-${t(a)}`))}}()},2812:function(e,t){!function(e){"use strict";const t={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},n=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),a=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,s=(e,t)=>{const s=e.nodeName.toLowerCase();return t.includes(s)?!n.has(s)||Boolean(a.test(e.nodeValue)):t.filter((e=>e instanceof RegExp)).some((e=>e.test(s)))};function r(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);const a=(new window.DOMParser).parseFromString(e,"text/html"),r=[].concat(...a.body.querySelectorAll("*"));for(const e of r){const n=e.nodeName.toLowerCase();if(!Object.keys(t).includes(n)){e.remove();continue}const a=[].concat(...e.attributes),r=[].concat(t["*"]||[],t[n]||[]);for(const t of a)s(t,r)||e.removeAttribute(t.nodeName)}return a.body.innerHTML}e.DefaultAllowlist=t,e.sanitizeHtml=r,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(t)},3029:function(e,t,n){e.exports=function(e,t,n,a,s,r,i){"use strict";function o(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e)for(const n in e)if("default"!==n){const a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:()=>e[n]})}return t.default=e,Object.freeze(t)}const d=o(e),u="tooltip",l=new Set(["sanitize","allowList","sanitizeFn"]),c="fade",_="show",m=".tooltip-inner",h=".modal",p="hide.bs.modal",f="hover",y="focus",M="click",g="manual",L="hide",Y="hidden",v="show",w="shown",b="inserted",k="click",D="focusin",T="focusout",S="mouseenter",x="mouseleave",H={AUTO:"auto",TOP:"top",RIGHT:s.isRTL()?"left":"right",BOTTOM:"bottom",LEFT:s.isRTL()?"right":"left"},j={allowList:r.DefaultAllowlist,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},O={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class P extends t{constructor(e,t){if(void 0===d)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return j}static get DefaultType(){return O}static get NAME(){return u}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),n.off(this._element.closest(h),p,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=n.trigger(this._element,this.constructor.eventName(v)),t=(s.findShadowRoot(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const a=this._getTipElement();this._element.setAttribute("aria-describedby",a.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(a),n.trigger(this._element,this.constructor.eventName(b))),this._popper=this._createPopper(a),a.classList.add(_),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))n.on(e,"mouseover",s.noop);const i=()=>{n.trigger(this._element,this.constructor.eventName(w)),!1===this._isHovered&&this._leave(),this._isHovered=!1};this._queueCallback(i,this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(n.trigger(this._element,this.constructor.eventName(L)).defaultPrevented)return;if(this._getTipElement().classList.remove(_),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))n.off(e,"mouseover",s.noop);this._activeTrigger[M]=!1,this._activeTrigger[y]=!1,this._activeTrigger[f]=!1,this._isHovered=null;const e=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),n.trigger(this._element,this.constructor.eventName(Y)))};this._queueCallback(e,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(c,_),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=s.getUID(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(c),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new i({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[m]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(c)}_isShown(){return this.tip&&this.tip.classList.contains(_)}_createPopper(e){const t=s.execute(this._config.placement,[this,e,this._element]),n=H[t.toUpperCase()];return d.createPopper(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return s.execute(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...s.execute(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if("click"===t)n.on(this._element,this.constructor.eventName(k),this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger[M]=!(t._isShown()&&t._activeTrigger[M]),t.toggle()}));else if(t!==g){const e=t===f?this.constructor.eventName(S):this.constructor.eventName(D),a=t===f?this.constructor.eventName(x):this.constructor.eventName(T);n.on(this._element,e,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?y:f]=!0,t._enter()})),n.on(this._element,a,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?y:f]=t._element.contains(e.relatedTarget),t._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},n.on(this._element.closest(h),p,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=a.getDataAttributes(this._element);for(const e of Object.keys(t))l.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:s.getElement(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each((function(){const t=P.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}return s.defineJQueryPlugin(P),P}(n(1170),n(9011),n(7956),n(2333),n(4035),n(2812),n(3982))},3941:function(e,t,n){(e=n.nmd(e)).exports=function(){"use strict";var t;function n(){return t.apply(null,arguments)}function a(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function s(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function r(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function i(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;for(var t in e)if(r(e,t))return;return 1}function o(e){return void 0===e}function d(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function u(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function l(e,t){for(var n=[],a=e.length,s=0;s<a;++s)n.push(t(e[s],s));return n}function c(e,t){for(var n in t)r(t,n)&&(e[n]=t[n]);return r(t,"toString")&&(e.toString=t.toString),r(t,"valueOf")&&(e.valueOf=t.valueOf),e}function _(e,t,n,a){return xt(e,t,n,a,!0).utc()}function m(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function h(e){var t,n,a=e._d&&!isNaN(e._d.getTime());return a&&(t=m(e),n=f.call(t.parsedDateParts,(function(e){return null!=e})),a=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n),e._strict)&&(a=a&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e)?a:(e._isValid=a,e._isValid)}function p(e){var t=_(NaN);return null!=e?c(m(t),e):m(t).userInvalidated=!0,t}var f=Array.prototype.some||function(e){for(var t=Object(this),n=t.length>>>0,a=0;a<n;a++)if(a in t&&e.call(this,t[a],a,t))return!0;return!1},y=n.momentProperties=[],M=!1;function g(e,t){var n,a,s,r=y.length;if(o(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),o(t._i)||(e._i=t._i),o(t._f)||(e._f=t._f),o(t._l)||(e._l=t._l),o(t._strict)||(e._strict=t._strict),o(t._tzm)||(e._tzm=t._tzm),o(t._isUTC)||(e._isUTC=t._isUTC),o(t._offset)||(e._offset=t._offset),o(t._pf)||(e._pf=m(t)),o(t._locale)||(e._locale=t._locale),0<r)for(n=0;n<r;n++)o(s=t[a=y[n]])||(e[a]=s);return e}function L(e){g(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===M&&(M=!0,n.updateOffset(this),M=!1)}function Y(e){return e instanceof L||null!=e&&null!=e._isAMomentObject}function v(e){!1===n.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function w(e,t){var a=!0;return c((function(){if(null!=n.deprecationHandler&&n.deprecationHandler(null,e),a){for(var s,i,o=[],d=arguments.length,u=0;u<d;u++){if(s="","object"==typeof arguments[u]){for(i in s+="\n["+u+"] ",arguments[0])r(arguments[0],i)&&(s+=i+": "+arguments[0][i]+", ");s=s.slice(0,-2)}else s=arguments[u];o.push(s)}v(e+"\nArguments: "+Array.prototype.slice.call(o).join("")+"\n"+(new Error).stack),a=!1}return t.apply(this,arguments)}),t)}var b={};function k(e,t){null!=n.deprecationHandler&&n.deprecationHandler(e,t),b[e]||(v(t),b[e]=!0)}function D(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function T(e,t){var n,a=c({},e);for(n in t)r(t,n)&&(s(e[n])&&s(t[n])?(a[n]={},c(a[n],e[n]),c(a[n],t[n])):null!=t[n]?a[n]=t[n]:delete a[n]);for(n in e)r(e,n)&&!r(t,n)&&s(e[n])&&(a[n]=c({},a[n]));return a}function S(e){null!=e&&this.set(e)}n.suppressDeprecationWarnings=!1,n.deprecationHandler=null;var x=Object.keys||function(e){var t,n=[];for(t in e)r(e,t)&&n.push(t);return n};function H(e,t,n){var a=""+Math.abs(e);return(0<=e?n?"+":"":"-")+Math.pow(10,Math.max(0,t-a.length)).toString().substr(1)+a}var j=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,O=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,P={},E={};function A(e,t,n,a){var s="string"==typeof a?function(){return this[a]()}:a;e&&(E[e]=s),t&&(E[t[0]]=function(){return H(s.apply(this,arguments),t[1],t[2])}),n&&(E[n]=function(){return this.localeData().ordinal(s.apply(this,arguments),e)})}function C(e,t){return e.isValid()?(t=W(t,e.localeData()),P[t]=P[t]||function(e){for(var t,n=e.match(j),a=0,s=n.length;a<s;a++)E[n[a]]?n[a]=E[n[a]]:n[a]=(t=n[a]).match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"");return function(t){for(var a="",r=0;r<s;r++)a+=D(n[r])?n[r].call(t,e):n[r];return a}}(t),P[t](e)):e.localeData().invalidDate()}function W(e,t){var n=5;function a(e){return t.longDateFormat(e)||e}for(O.lastIndex=0;0<=n&&O.test(e);)e=e.replace(O,a),O.lastIndex=0,--n;return e}var F={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function $(e){return"string"==typeof e?F[e]||F[e.toLowerCase()]:void 0}function N(e){var t,n,a={};for(n in e)r(e,n)&&(t=$(n))&&(a[t]=e[n]);return a}var z={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1},I=/\d/,R=/\d\d/,U=/\d{3}/,J=/\d{4}/,q=/[+-]?\d{6}/,B=/\d\d?/,G=/\d\d\d\d?/,V=/\d\d\d\d\d\d?/,K=/\d{1,3}/,Z=/\d{1,4}/,Q=/[+-]?\d{1,6}/,X=/\d+/,ee=/[+-]?\d+/,te=/Z|[+-]\d\d:?\d\d/gi,ne=/Z|[+-]\d\d(?::?\d\d)?/gi,ae=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,se=/^[1-9]\d?/,re=/^([1-9]\d|\d)/;function ie(e,t,n){ce[e]=D(t)?t:function(e,a){return e&&n?n:t}}function oe(e,t){return r(ce,e)?ce[e](t._strict,t._locale):new RegExp(de(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(e,t,n,a,s){return t||n||a||s}))))}function de(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function ue(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function le(e){var t=0;return 0!=(e=+e)&&isFinite(e)?ue(e):t}var ce={},_e={};function me(e,t){var n,a,s=t;for("string"==typeof e&&(e=[e]),d(t)&&(s=function(e,n){n[t]=le(e)}),a=e.length,n=0;n<a;n++)_e[e[n]]=s}function he(e,t){me(e,(function(e,n,a,s){a._w=a._w||{},t(e,a._w,a,s)}))}function pe(e){return e%4==0&&e%100!=0||e%400==0}var fe=0,ye=1,Me=2,ge=3,Le=4,Ye=5,ve=6,we=7,be=8;function ke(e){return pe(e)?366:365}A("Y",0,0,(function(){var e=this.year();return e<=9999?H(e,4):"+"+e})),A(0,["YY",2],0,(function(){return this.year()%100})),A(0,["YYYY",4],0,"year"),A(0,["YYYYY",5],0,"year"),A(0,["YYYYYY",6,!0],0,"year"),ie("Y",ee),ie("YY",B,R),ie("YYYY",Z,J),ie("YYYYY",Q,q),ie("YYYYYY",Q,q),me(["YYYYY","YYYYYY"],fe),me("YYYY",(function(e,t){t[fe]=2===e.length?n.parseTwoDigitYear(e):le(e)})),me("YY",(function(e,t){t[fe]=n.parseTwoDigitYear(e)})),me("Y",(function(e,t){t[fe]=parseInt(e,10)})),n.parseTwoDigitYear=function(e){return le(e)+(68<le(e)?1900:2e3)};var De,Te=Se("FullYear",!0);function Se(e,t){return function(a){return null!=a?(He(this,e,a),n.updateOffset(this,t),this):xe(this,e)}}function xe(e,t){if(!e.isValid())return NaN;var n=e._d,a=e._isUTC;switch(t){case"Milliseconds":return a?n.getUTCMilliseconds():n.getMilliseconds();case"Seconds":return a?n.getUTCSeconds():n.getSeconds();case"Minutes":return a?n.getUTCMinutes():n.getMinutes();case"Hours":return a?n.getUTCHours():n.getHours();case"Date":return a?n.getUTCDate():n.getDate();case"Day":return a?n.getUTCDay():n.getDay();case"Month":return a?n.getUTCMonth():n.getMonth();case"FullYear":return a?n.getUTCFullYear():n.getFullYear();default:return NaN}}function He(e,t,n){var a,s,r;if(e.isValid()&&!isNaN(n)){switch(a=e._d,s=e._isUTC,t){case"Milliseconds":return s?a.setUTCMilliseconds(n):a.setMilliseconds(n);case"Seconds":return s?a.setUTCSeconds(n):a.setSeconds(n);case"Minutes":return s?a.setUTCMinutes(n):a.setMinutes(n);case"Hours":return s?a.setUTCHours(n):a.setHours(n);case"Date":return s?a.setUTCDate(n):a.setDate(n);case"FullYear":break;default:return}t=n,r=e.month(),e=29!==(e=e.date())||1!==r||pe(t)?e:28,s?a.setUTCFullYear(t,r,e):a.setFullYear(t,r,e)}}function je(e,t){var n;return isNaN(e)||isNaN(t)?NaN:(e+=(t-(n=(t%(n=12)+n)%n))/12,1==n?pe(e)?29:28:31-n%7%2)}De=Array.prototype.indexOf||function(e){for(var t=0;t<this.length;++t)if(this[t]===e)return t;return-1},A("M",["MM",2],"Mo",(function(){return this.month()+1})),A("MMM",0,0,(function(e){return this.localeData().monthsShort(this,e)})),A("MMMM",0,0,(function(e){return this.localeData().months(this,e)})),ie("M",B,se),ie("MM",B,R),ie("MMM",(function(e,t){return t.monthsShortRegex(e)})),ie("MMMM",(function(e,t){return t.monthsRegex(e)})),me(["M","MM"],(function(e,t){t[ye]=le(e)-1})),me(["MMM","MMMM"],(function(e,t,n,a){null!=(a=n._locale.monthsParse(e,a,n._strict))?t[ye]=a:m(n).invalidMonth=e}));var Oe="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Pe="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Ee=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ae=ae,Ce=ae;function We(e,t){if(e.isValid()){if("string"==typeof t)if(/^\d+$/.test(t))t=le(t);else if(!d(t=e.localeData().monthsParse(t)))return;var n=(n=e.date())<29?n:Math.min(n,je(e.year(),t));e._isUTC?e._d.setUTCMonth(t,n):e._d.setMonth(t,n)}}function Fe(e){return null!=e?(We(this,e),n.updateOffset(this,!0),this):xe(this,"Month")}function $e(){function e(e,t){return t.length-e.length}for(var t,n,a=[],s=[],r=[],i=0;i<12;i++)n=_([2e3,i]),t=de(this.monthsShort(n,"")),n=de(this.months(n,"")),a.push(t),s.push(n),r.push(n),r.push(t);a.sort(e),s.sort(e),r.sort(e),this._monthsRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+a.join("|")+")","i")}function Ne(e,t,n,a,s,r,i){var o;return e<100&&0<=e?(o=new Date(e+400,t,n,a,s,r,i),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,n,a,s,r,i),o}function ze(e){var t;return e<100&&0<=e?((t=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,t)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function Ie(e,t,n){return(n=7+t-n)-(7+ze(e,0,n).getUTCDay()-t)%7-1}function Re(e,t,n,a,s){var r;return n=(t=1+7*(t-1)+(7+n-a)%7+Ie(e,a,s))<=0?ke(r=e-1)+t:t>ke(e)?(r=e+1,t-ke(e)):(r=e,t),{year:r,dayOfYear:n}}function Ue(e,t,n){var a,s,r=Ie(e.year(),t,n);return(r=Math.floor((e.dayOfYear()-r-1)/7)+1)<1?a=r+Je(s=e.year()-1,t,n):r>Je(e.year(),t,n)?(a=r-Je(e.year(),t,n),s=e.year()+1):(s=e.year(),a=r),{week:a,year:s}}function Je(e,t,n){var a=Ie(e,t,n);return t=Ie(e+1,t,n),(ke(e)-a+t)/7}function qe(e,t){return e.slice(t,7).concat(e.slice(0,t))}A("w",["ww",2],"wo","week"),A("W",["WW",2],"Wo","isoWeek"),ie("w",B,se),ie("ww",B,R),ie("W",B,se),ie("WW",B,R),he(["w","ww","W","WW"],(function(e,t,n,a){t[a.substr(0,1)]=le(e)})),A("d",0,"do","day"),A("dd",0,0,(function(e){return this.localeData().weekdaysMin(this,e)})),A("ddd",0,0,(function(e){return this.localeData().weekdaysShort(this,e)})),A("dddd",0,0,(function(e){return this.localeData().weekdays(this,e)})),A("e",0,0,"weekday"),A("E",0,0,"isoWeekday"),ie("d",B),ie("e",B),ie("E",B),ie("dd",(function(e,t){return t.weekdaysMinRegex(e)})),ie("ddd",(function(e,t){return t.weekdaysShortRegex(e)})),ie("dddd",(function(e,t){return t.weekdaysRegex(e)})),he(["dd","ddd","dddd"],(function(e,t,n,a){null!=(a=n._locale.weekdaysParse(e,a,n._strict))?t.d=a:m(n).invalidWeekday=e})),he(["d","e","E"],(function(e,t,n,a){t[a]=le(e)}));var Be="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Ge="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ve="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Ke=ae,Ze=ae,Qe=ae;function Xe(){function e(e,t){return t.length-e.length}for(var t,n,a,s=[],r=[],i=[],o=[],d=0;d<7;d++)a=_([2e3,1]).day(d),t=de(this.weekdaysMin(a,"")),n=de(this.weekdaysShort(a,"")),a=de(this.weekdays(a,"")),s.push(t),r.push(n),i.push(a),o.push(t),o.push(n),o.push(a);s.sort(e),r.sort(e),i.sort(e),o.sort(e),this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+s.join("|")+")","i")}function et(){return this.hours()%12||12}function tt(e,t){A(e,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}function nt(e,t){return t._meridiemParse}A("H",["HH",2],0,"hour"),A("h",["hh",2],0,et),A("k",["kk",2],0,(function(){return this.hours()||24})),A("hmm",0,0,(function(){return""+et.apply(this)+H(this.minutes(),2)})),A("hmmss",0,0,(function(){return""+et.apply(this)+H(this.minutes(),2)+H(this.seconds(),2)})),A("Hmm",0,0,(function(){return""+this.hours()+H(this.minutes(),2)})),A("Hmmss",0,0,(function(){return""+this.hours()+H(this.minutes(),2)+H(this.seconds(),2)})),tt("a",!0),tt("A",!1),ie("a",nt),ie("A",nt),ie("H",B,re),ie("h",B,se),ie("k",B,se),ie("HH",B,R),ie("hh",B,R),ie("kk",B,R),ie("hmm",G),ie("hmmss",V),ie("Hmm",G),ie("Hmmss",V),me(["H","HH"],ge),me(["k","kk"],(function(e,t,n){e=le(e),t[ge]=24===e?0:e})),me(["a","A"],(function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e})),me(["h","hh"],(function(e,t,n){t[ge]=le(e),m(n).bigHour=!0})),me("hmm",(function(e,t,n){var a=e.length-2;t[ge]=le(e.substr(0,a)),t[Le]=le(e.substr(a)),m(n).bigHour=!0})),me("hmmss",(function(e,t,n){var a=e.length-4,s=e.length-2;t[ge]=le(e.substr(0,a)),t[Le]=le(e.substr(a,2)),t[Ye]=le(e.substr(s)),m(n).bigHour=!0})),me("Hmm",(function(e,t,n){var a=e.length-2;t[ge]=le(e.substr(0,a)),t[Le]=le(e.substr(a))})),me("Hmmss",(function(e,t,n){var a=e.length-4,s=e.length-2;t[ge]=le(e.substr(0,a)),t[Le]=le(e.substr(a,2)),t[Ye]=le(e.substr(s))})),ae=Se("Hours",!0);var at,st={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Oe,monthsShort:Pe,week:{dow:0,doy:6},weekdays:Be,weekdaysMin:Ve,weekdaysShort:Ge,meridiemParse:/[ap]\.?m?\.?/i},rt={},it={};function ot(e){return e&&e.toLowerCase().replace("_","-")}function dt(e){for(var t,n,a,s,r=0;r<e.length;){for(t=(s=ot(e[r]).split("-")).length,n=(n=ot(e[r+1]))?n.split("-"):null;0<t;){if(a=ut(s.slice(0,t).join("-")))return a;if(n&&n.length>=t&&function(e,t){for(var n=Math.min(e.length,t.length),a=0;a<n;a+=1)if(e[a]!==t[a])return a;return n}(s,n)>=t-1)break;t--}r++}return at}function ut(t){var n,a;if(void 0===rt[t]&&e&&e.exports&&(a=t)&&a.match("^[^/\\\\]*$"))try{n=at._abbr,Object(function(){var e=new Error("Cannot find module 'undefined'");throw e.code="MODULE_NOT_FOUND",e}()),lt(n)}catch(n){rt[t]=null}return rt[t]}function lt(e,t){return e&&((t=o(t)?_t(e):ct(e,t))?at=t:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),at._abbr}function ct(e,t){if(null===t)return delete rt[e],null;var n,a=st;if(t.abbr=e,null!=rt[e])k("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),a=rt[e]._config;else if(null!=t.parentLocale)if(null!=rt[t.parentLocale])a=rt[t.parentLocale]._config;else{if(null==(n=ut(t.parentLocale)))return it[t.parentLocale]||(it[t.parentLocale]=[]),it[t.parentLocale].push({name:e,config:t}),null;a=n._config}return rt[e]=new S(T(a,t)),it[e]&&it[e].forEach((function(e){ct(e.name,e.config)})),lt(e),rt[e]}function _t(e){var t;if(!(e=e&&e._locale&&e._locale._abbr?e._locale._abbr:e))return at;if(!a(e)){if(t=ut(e))return t;e=[e]}return dt(e)}function mt(e){var t=e._a;return t&&-2===m(e).overflow&&(t=t[ye]<0||11<t[ye]?ye:t[Me]<1||t[Me]>je(t[fe],t[ye])?Me:t[ge]<0||24<t[ge]||24===t[ge]&&(0!==t[Le]||0!==t[Ye]||0!==t[ve])?ge:t[Le]<0||59<t[Le]?Le:t[Ye]<0||59<t[Ye]?Ye:t[ve]<0||999<t[ve]?ve:-1,m(e)._overflowDayOfYear&&(t<fe||Me<t)&&(t=Me),m(e)._overflowWeeks&&-1===t&&(t=we),m(e)._overflowWeekday&&-1===t&&(t=be),m(e).overflow=t),e}var ht=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,pt=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ft=/Z|[+-]\d\d(?::?\d\d)?/,yt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],Mt=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],gt=/^\/?Date\((-?\d+)/i,Lt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,Yt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function vt(e){var t,n,a,s,r,i,o=e._i,d=ht.exec(o)||pt.exec(o),u=(o=yt.length,Mt.length);if(d){for(m(e).iso=!0,t=0,n=o;t<n;t++)if(yt[t][1].exec(d[1])){s=yt[t][0],a=!1!==yt[t][2];break}if(null==s)e._isValid=!1;else{if(d[3]){for(t=0,n=u;t<n;t++)if(Mt[t][1].exec(d[3])){r=(d[2]||" ")+Mt[t][0];break}if(null==r)return void(e._isValid=!1)}if(a||null==r){if(d[4]){if(!ft.exec(d[4]))return void(e._isValid=!1);i="Z"}e._f=s+(r||"")+(i||""),Tt(e)}else e._isValid=!1}}else e._isValid=!1}function wt(e,t,n,a,s,r){return e=[function(e){return(e=parseInt(e,10))<=49?2e3+e:e<=999?1900+e:e}(e),Pe.indexOf(t),parseInt(n,10),parseInt(a,10),parseInt(s,10)],r&&e.push(parseInt(r,10)),e}function bt(e){var t,n,a=Lt.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));a?(t=wt(a[4],a[3],a[2],a[5],a[6],a[7]),function(e,t,n){if(!e||Ge.indexOf(e)===new Date(t[0],t[1],t[2]).getDay())return 1;m(n).weekdayMismatch=!0,n._isValid=!1}(a[1],t,e)&&(e._a=t,e._tzm=(t=a[8],n=a[9],a=a[10],t?Yt[t]:n?0:((t=parseInt(a,10))-(n=t%100))/100*60+n),e._d=ze.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),m(e).rfc2822=!0)):e._isValid=!1}function kt(e,t,n){return null!=e?e:null!=t?t:n}function Dt(e){var t,a,s,r,i,o,d,u,l,c,_,h=[];if(!e._d){for(s=e,r=new Date(n.now()),a=s._useUTC?[r.getUTCFullYear(),r.getUTCMonth(),r.getUTCDate()]:[r.getFullYear(),r.getMonth(),r.getDate()],e._w&&null==e._a[Me]&&null==e._a[ye]&&(null!=(r=(s=e)._w).GG||null!=r.W||null!=r.E?(u=1,l=4,i=kt(r.GG,s._a[fe],Ue(Ht(),1,4).year),o=kt(r.W,1),((d=kt(r.E,1))<1||7<d)&&(c=!0)):(u=s._locale._week.dow,l=s._locale._week.doy,_=Ue(Ht(),u,l),i=kt(r.gg,s._a[fe],_.year),o=kt(r.w,_.week),null!=r.d?((d=r.d)<0||6<d)&&(c=!0):null!=r.e?(d=r.e+u,(r.e<0||6<r.e)&&(c=!0)):d=u),o<1||o>Je(i,u,l)?m(s)._overflowWeeks=!0:null!=c?m(s)._overflowWeekday=!0:(_=Re(i,o,d,u,l),s._a[fe]=_.year,s._dayOfYear=_.dayOfYear)),null!=e._dayOfYear&&(r=kt(e._a[fe],a[fe]),(e._dayOfYear>ke(r)||0===e._dayOfYear)&&(m(e)._overflowDayOfYear=!0),c=ze(r,0,e._dayOfYear),e._a[ye]=c.getUTCMonth(),e._a[Me]=c.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=h[t]=a[t];for(;t<7;t++)e._a[t]=h[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[ge]&&0===e._a[Le]&&0===e._a[Ye]&&0===e._a[ve]&&(e._nextDay=!0,e._a[ge]=0),e._d=(e._useUTC?ze:Ne).apply(null,h),i=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[ge]=24),e._w&&void 0!==e._w.d&&e._w.d!==i&&(m(e).weekdayMismatch=!0)}}function Tt(e){if(e._f===n.ISO_8601)vt(e);else if(e._f===n.RFC_2822)bt(e);else{e._a=[],m(e).empty=!0;for(var t,a,s,i,o,d=""+e._i,u=d.length,l=0,c=W(e._f,e._locale).match(j)||[],_=c.length,h=0;h<_;h++)a=c[h],(t=(d.match(oe(a,e))||[])[0])&&(0<(s=d.substr(0,d.indexOf(t))).length&&m(e).unusedInput.push(s),d=d.slice(d.indexOf(t)+t.length),l+=t.length),E[a]?(t?m(e).empty=!1:m(e).unusedTokens.push(a),s=a,o=e,null!=(i=t)&&r(_e,s)&&_e[s](i,o._a,o,s)):e._strict&&!t&&m(e).unusedTokens.push(a);m(e).charsLeftOver=u-l,0<d.length&&m(e).unusedInput.push(d),e._a[ge]<=12&&!0===m(e).bigHour&&0<e._a[ge]&&(m(e).bigHour=void 0),m(e).parsedDateParts=e._a.slice(0),m(e).meridiem=e._meridiem,e._a[ge]=function(e,t,n){return null==n?t:null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?((e=e.isPM(n))&&t<12&&(t+=12),t=e||12!==t?t:0):t}(e._locale,e._a[ge],e._meridiem),null!==(u=m(e).era)&&(e._a[fe]=e._locale.erasConvertYear(u,e._a[fe])),Dt(e),mt(e)}}function St(e){var t,r,i,_=e._i,f=e._f;if(e._locale=e._locale||_t(e._l),null===_||void 0===f&&""===_)return p({nullInput:!0});if("string"==typeof _&&(e._i=_=e._locale.preparse(_)),Y(_))return new L(mt(_));if(u(_))e._d=_;else if(a(f)){var y,M,v,w,b,k,D=e,T=!1,S=D._f.length;if(0===S)m(D).invalidFormat=!0,D._d=new Date(NaN);else{for(w=0;w<S;w++)b=0,k=!1,y=g({},D),null!=D._useUTC&&(y._useUTC=D._useUTC),y._f=D._f[w],Tt(y),h(y)&&(k=!0),b=(b+=m(y).charsLeftOver)+10*m(y).unusedTokens.length,m(y).score=b,T?b<v&&(v=b,M=y):(null==v||b<v||k)&&(v=b,M=y,k)&&(T=!0);c(D,M||y)}}else f?Tt(e):o(f=(_=e)._i)?_._d=new Date(n.now()):u(f)?_._d=new Date(f.valueOf()):"string"==typeof f?(r=_,null!==(t=gt.exec(r._i))?r._d=new Date(+t[1]):(vt(r),!1===r._isValid&&(delete r._isValid,bt(r),!1===r._isValid)&&(delete r._isValid,r._strict?r._isValid=!1:n.createFromInputFallback(r)))):a(f)?(_._a=l(f.slice(0),(function(e){return parseInt(e,10)})),Dt(_)):s(f)?(t=_)._d||(i=void 0===(r=N(t._i)).day?r.date:r.day,t._a=l([r.year,r.month,i,r.hour,r.minute,r.second,r.millisecond],(function(e){return e&&parseInt(e,10)})),Dt(t)):d(f)?_._d=new Date(f):n.createFromInputFallback(_);return h(e)||(e._d=null),e}function xt(e,t,n,r,o){var d={};return!0!==t&&!1!==t||(r=t,t=void 0),!0!==n&&!1!==n||(r=n,n=void 0),(s(e)&&i(e)||a(e)&&0===e.length)&&(e=void 0),d._isAMomentObject=!0,d._useUTC=d._isUTC=o,d._l=n,d._i=e,d._f=t,d._strict=r,(o=new L(mt(St(o=d))))._nextDay&&(o.add(1,"d"),o._nextDay=void 0),o}function Ht(e,t,n,a){return xt(e,t,n,a,!1)}function jt(e,t){var n,s;if(!(t=1===t.length&&a(t[0])?t[0]:t).length)return Ht();for(n=t[0],s=1;s<t.length;++s)t[s].isValid()&&!t[s][e](n)||(n=t[s]);return n}n.createFromInputFallback=w("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))})),n.ISO_8601=function(){},n.RFC_2822=function(){},G=w("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Ht.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:p()})),V=w("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Ht.apply(null,arguments);return this.isValid()&&e.isValid()?this<e?this:e:p()}));var Ot=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Pt(e){var t=(e=N(e)).year||0,n=e.quarter||0,a=e.month||0,s=e.week||e.isoWeek||0,i=e.day||0,o=e.hour||0,d=e.minute||0,u=e.second||0,l=e.millisecond||0;this._isValid=function(e){var t,n,a=!1,s=Ot.length;for(t in e)if(r(e,t)&&(-1===De.call(Ot,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<s;++n)if(e[Ot[n]]){if(a)return!1;parseFloat(e[Ot[n]])!==le(e[Ot[n]])&&(a=!0)}return!0}(e),this._milliseconds=+l+1e3*u+6e4*d+1e3*o*60*60,this._days=+i+7*s,this._months=+a+3*n+12*t,this._data={},this._locale=_t(),this._bubble()}function Et(e){return e instanceof Pt}function At(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Ct(e,t){A(e,0,0,(function(){var e=this.utcOffset(),n="+";return e<0&&(e=-e,n="-"),n+H(~~(e/60),2)+t+H(~~e%60,2)}))}Ct("Z",":"),Ct("ZZ",""),ie("Z",ne),ie("ZZ",ne),me(["Z","ZZ"],(function(e,t,n){n._useUTC=!0,n._tzm=Ft(ne,e)}));var Wt=/([\+\-]|\d\d)/gi;function Ft(e,t){return null===(t=(t||"").match(e))?null:0===(t=60*(e=((t[t.length-1]||[])+"").match(Wt)||["-",0,0])[1]+le(e[2]))?0:"+"===e[0]?t:-t}function $t(e,t){var a;return t._isUTC?(t=t.clone(),a=(Y(e)||u(e)?e:Ht(e)).valueOf()-t.valueOf(),t._d.setTime(t._d.valueOf()+a),n.updateOffset(t,!1),t):Ht(e).local()}function Nt(e){return-Math.round(e._d.getTimezoneOffset())}function zt(){return!!this.isValid()&&this._isUTC&&0===this._offset}n.updateOffset=function(){};var It=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Rt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Ut(e,t){var n,a=e;return Et(e)?a={ms:e._milliseconds,d:e._days,M:e._months}:d(e)||!isNaN(+e)?(a={},t?a[t]=+e:a.milliseconds=+e):(t=It.exec(e))?(n="-"===t[1]?-1:1,a={y:0,d:le(t[Me])*n,h:le(t[ge])*n,m:le(t[Le])*n,s:le(t[Ye])*n,ms:le(At(1e3*t[ve]))*n}):(t=Rt.exec(e))?(n="-"===t[1]?-1:1,a={y:Jt(t[2],n),M:Jt(t[3],n),w:Jt(t[4],n),d:Jt(t[5],n),h:Jt(t[6],n),m:Jt(t[7],n),s:Jt(t[8],n)}):null==a?a={}:"object"==typeof a&&("from"in a||"to"in a)&&(t=function(e,t){var n;return e.isValid()&&t.isValid()?(t=$t(t,e),e.isBefore(t)?n=qt(e,t):((n=qt(t,e)).milliseconds=-n.milliseconds,n.months=-n.months),n):{milliseconds:0,months:0}}(Ht(a.from),Ht(a.to)),(a={}).ms=t.milliseconds,a.M=t.months),n=new Pt(a),Et(e)&&r(e,"_locale")&&(n._locale=e._locale),Et(e)&&r(e,"_isValid")&&(n._isValid=e._isValid),n}function Jt(e,t){return e=e&&parseFloat(e.replace(",",".")),(isNaN(e)?0:e)*t}function qt(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function Bt(e,t){return function(n,a){var s;return null===a||isNaN(+a)||(k(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),s=n,n=a,a=s),Gt(this,Ut(n,a),e),this}}function Gt(e,t,a,s){var r=t._milliseconds,i=At(t._days);t=At(t._months),e.isValid()&&(s=null==s||s,t&&We(e,xe(e,"Month")+t*a),i&&He(e,"Date",xe(e,"Date")+i*a),r&&e._d.setTime(e._d.valueOf()+r*a),s)&&n.updateOffset(e,i||t)}function Vt(e){return"string"==typeof e||e instanceof String}function Kt(e){return Y(e)||u(e)||Vt(e)||d(e)||function(e){var t=a(e),n=!1;return t&&(n=0===e.filter((function(t){return!d(t)&&Vt(e)})).length),t&&n}(e)||function(e){var t,n,a=s(e)&&!i(e),o=!1,d=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],u=d.length;for(t=0;t<u;t+=1)n=d[t],o=o||r(e,n);return a&&o}(e)||null==e}function Zt(e,t){var n,a;return e.date()<t.date()?-Zt(t,e):-((n=12*(t.year()-e.year())+(t.month()-e.month()))+(t-(a=e.clone().add(n,"months"))<0?(t-a)/(a-e.clone().add(n-1,"months")):(t-a)/(e.clone().add(1+n,"months")-a)))||0}function Qt(e){return void 0===e?this._locale._abbr:(null!=(e=_t(e))&&(this._locale=e),this)}function Xt(){return this._locale}Ut.fn=Pt.prototype,Ut.invalid=function(){return Ut(NaN)},Oe=Bt(1,"add"),Be=Bt(-1,"subtract"),n.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",n.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]",Ve=w("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(e){return void 0===e?this.localeData():this.locale(e)}));var en,tn=126227808e5;function nn(e,t){return(e%t+t)%t}function an(e,t,n){return e<100&&0<=e?new Date(e+400,t,n)-tn:new Date(e,t,n).valueOf()}function sn(e,t,n){return e<100&&0<=e?Date.UTC(e+400,t,n)-tn:Date.UTC(e,t,n)}function rn(e,t){return t.erasAbbrRegex(e)}function on(){for(var e,t,n,a=[],s=[],r=[],i=[],o=this.eras(),d=0,u=o.length;d<u;++d)e=de(o[d].name),t=de(o[d].abbr),n=de(o[d].narrow),s.push(e),a.push(t),r.push(n),i.push(e),i.push(t),i.push(n);this._erasRegex=new RegExp("^("+i.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+s.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+r.join("|")+")","i")}function dn(e,t){A(0,[e,e.length],0,t)}function un(e,t,n,a,s){var r;return null==e?Ue(this,a,s).year:(r=Je(e,a,s),function(e,t,n,a,s){return t=ze((e=Re(e,t,n,a,s)).year,0,e.dayOfYear),this.year(t.getUTCFullYear()),this.month(t.getUTCMonth()),this.date(t.getUTCDate()),this}.call(this,e,t=r<t?r:t,n,a,s))}for(A("N",0,0,"eraAbbr"),A("NN",0,0,"eraAbbr"),A("NNN",0,0,"eraAbbr"),A("NNNN",0,0,"eraName"),A("NNNNN",0,0,"eraNarrow"),A("y",["y",1],"yo","eraYear"),A("y",["yy",2],0,"eraYear"),A("y",["yyy",3],0,"eraYear"),A("y",["yyyy",4],0,"eraYear"),ie("N",rn),ie("NN",rn),ie("NNN",rn),ie("NNNN",(function(e,t){return t.erasNameRegex(e)})),ie("NNNNN",(function(e,t){return t.erasNarrowRegex(e)})),me(["N","NN","NNN","NNNN","NNNNN"],(function(e,t,n,a){(a=n._locale.erasParse(e,a,n._strict))?m(n).era=a:m(n).invalidEra=e})),ie("y",X),ie("yy",X),ie("yyy",X),ie("yyyy",X),ie("yo",(function(e,t){return t._eraYearOrdinalRegex||X})),me(["y","yy","yyy","yyyy"],fe),me(["yo"],(function(e,t,n,a){var s;n._locale._eraYearOrdinalRegex&&(s=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[fe]=n._locale.eraYearOrdinalParse(e,s):t[fe]=parseInt(e,10)})),A(0,["gg",2],0,(function(){return this.weekYear()%100})),A(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),dn("gggg","weekYear"),dn("ggggg","weekYear"),dn("GGGG","isoWeekYear"),dn("GGGGG","isoWeekYear"),ie("G",ee),ie("g",ee),ie("GG",B,R),ie("gg",B,R),ie("GGGG",Z,J),ie("gggg",Z,J),ie("GGGGG",Q,q),ie("ggggg",Q,q),he(["gggg","ggggg","GGGG","GGGGG"],(function(e,t,n,a){t[a.substr(0,2)]=le(e)})),he(["gg","GG"],(function(e,t,a,s){t[s]=n.parseTwoDigitYear(e)})),A("Q",0,"Qo","quarter"),ie("Q",I),me("Q",(function(e,t){t[ye]=3*(le(e)-1)})),A("D",["DD",2],"Do","date"),ie("D",B,se),ie("DD",B,R),ie("Do",(function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient})),me(["D","DD"],Me),me("Do",(function(e,t){t[Me]=le(e.match(B)[0])})),Z=Se("Date",!0),A("DDD",["DDDD",3],"DDDo","dayOfYear"),ie("DDD",K),ie("DDDD",U),me(["DDD","DDDD"],(function(e,t,n){n._dayOfYear=le(e)})),A("m",["mm",2],0,"minute"),ie("m",B,re),ie("mm",B,R),me(["m","mm"],Le),J=Se("Minutes",!1),A("s",["ss",2],0,"second"),ie("s",B,re),ie("ss",B,R),me(["s","ss"],Ye),Q=Se("Seconds",!1),A("S",0,0,(function(){return~~(this.millisecond()/100)})),A(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),A(0,["SSS",3],0,"millisecond"),A(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),A(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),A(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),A(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),A(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),A(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),ie("S",K,I),ie("SS",K,R),ie("SSS",K,U),en="SSSS";en.length<=9;en+="S")ie(en,X);function ln(e,t){t[ve]=le(1e3*("0."+e))}for(en="S";en.length<=9;en+="S")me(en,ln);function cn(e){return e}function _n(e,t,n,a){var s=_t();return a=_().set(a,t),s[n](a,e)}function mn(e,t,n){if(d(e)&&(t=e,e=void 0),e=e||"",null!=t)return _n(e,t,n,"month");for(var a=[],s=0;s<12;s++)a[s]=_n(e,s,n,"month");return a}function hn(e,t,n,a){"boolean"==typeof e?d(t)&&(n=t,t=void 0):(t=e,e=!1,d(n=t)&&(n=t,t=void 0)),t=t||"";var s,r=_t(),i=e?r._week.dow:0,o=[];if(null!=n)return _n(t,(n+i)%7,a,"day");for(s=0;s<7;s++)o[s]=_n(t,(s+i)%7,a,"day");return o}q=Se("Milliseconds",!1),A("z",0,0,"zoneAbbr"),A("zz",0,0,"zoneName"),(se=L.prototype).add=Oe,se.calendar=function(e,t){1===arguments.length&&(arguments[0]?Kt(arguments[0])?(e=arguments[0],t=void 0):function(e){for(var t=s(e)&&!i(e),n=!1,a=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],o=0;o<a.length;o+=1)n=n||r(e,a[o]);return t&&n}(arguments[0])&&(t=arguments[0],e=void 0):t=e=void 0);var a=$t(e=e||Ht(),this).startOf("day");return a=n.calendarFormat(this,a)||"sameElse",t=t&&(D(t[a])?t[a].call(this,e):t[a]),this.format(t||this.localeData().calendar(a,this,Ht(e)))},se.clone=function(){return new L(this)},se.diff=function(e,t,n){var a,s,r;if(!this.isValid())return NaN;if(!(a=$t(e,this)).isValid())return NaN;switch(s=6e4*(a.utcOffset()-this.utcOffset()),t=$(t)){case"year":r=Zt(this,a)/12;break;case"month":r=Zt(this,a);break;case"quarter":r=Zt(this,a)/3;break;case"second":r=(this-a)/1e3;break;case"minute":r=(this-a)/6e4;break;case"hour":r=(this-a)/36e5;break;case"day":r=(this-a-s)/864e5;break;case"week":r=(this-a-s)/6048e5;break;default:r=this-a}return n?r:ue(r)},se.endOf=function(e){var t,a;if(void 0!==(e=$(e))&&"millisecond"!==e&&this.isValid()){switch(a=this._isUTC?sn:an,e){case"year":t=a(this.year()+1,0,1)-1;break;case"quarter":t=a(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=a(this.year(),this.month()+1,1)-1;break;case"week":t=a(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=a(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-nn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-nn(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-nn(t,1e3)-1}this._d.setTime(t),n.updateOffset(this,!0)}return this},se.format=function(e){return e=C(this,e=e||(this.isUtc()?n.defaultFormatUtc:n.defaultFormat)),this.localeData().postformat(e)},se.from=function(e,t){return this.isValid()&&(Y(e)&&e.isValid()||Ht(e).isValid())?Ut({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},se.fromNow=function(e){return this.from(Ht(),e)},se.to=function(e,t){return this.isValid()&&(Y(e)&&e.isValid()||Ht(e).isValid())?Ut({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},se.toNow=function(e){return this.to(Ht(),e)},se.get=function(e){return D(this[e=$(e)])?this[e]():this},se.invalidAt=function(){return m(this).overflow},se.isAfter=function(e,t){return e=Y(e)?e:Ht(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=$(t)||"millisecond")?this.valueOf()>e.valueOf():e.valueOf()<this.clone().startOf(t).valueOf())},se.isBefore=function(e,t){return e=Y(e)?e:Ht(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=$(t)||"millisecond")?this.valueOf()<e.valueOf():this.clone().endOf(t).valueOf()<e.valueOf())},se.isBetween=function(e,t,n,a){return e=Y(e)?e:Ht(e),t=Y(t)?t:Ht(t),!!(this.isValid()&&e.isValid()&&t.isValid())&&("("===(a=a||"()")[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===a[1]?this.isBefore(t,n):!this.isAfter(t,n))},se.isSame=function(e,t){return e=Y(e)?e:Ht(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=$(t)||"millisecond")?this.valueOf()===e.valueOf():(e=e.valueOf(),this.clone().startOf(t).valueOf()<=e&&e<=this.clone().endOf(t).valueOf()))},se.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},se.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},se.isValid=function(){return h(this)},se.lang=Ve,se.locale=Qt,se.localeData=Xt,se.max=V,se.min=G,se.parsingFlags=function(){return c({},m(this))},se.set=function(e,t){if("object"==typeof e)for(var n=function(e){var t,n=[];for(t in e)r(e,t)&&n.push({unit:t,priority:z[t]});return n.sort((function(e,t){return e.priority-t.priority})),n}(e=N(e)),a=n.length,s=0;s<a;s++)this[n[s].unit](e[n[s].unit]);else if(D(this[e=$(e)]))return this[e](t);return this},se.startOf=function(e){var t,a;if(void 0!==(e=$(e))&&"millisecond"!==e&&this.isValid()){switch(a=this._isUTC?sn:an,e){case"year":t=a(this.year(),0,1);break;case"quarter":t=a(this.year(),this.month()-this.month()%3,1);break;case"month":t=a(this.year(),this.month(),1);break;case"week":t=a(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=a(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=nn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=nn(t,6e4);break;case"second":t=this._d.valueOf(),t-=nn(t,1e3)}this._d.setTime(t),n.updateOffset(this,!0)}return this},se.subtract=Be,se.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},se.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},se.toDate=function(){return new Date(this.valueOf())},se.toISOString=function(e){var t;return this.isValid()?(t=(e=!0!==e)?this.clone().utc():this).year()<0||9999<t.year()?C(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):D(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",C(t,"Z")):C(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ"):null},se.inspect=function(){var e,t,n;return this.isValid()?(t="moment",e="",this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z"),t="["+t+'("]',n=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(t+n+"-MM-DD[T]HH:mm:ss.SSS"+e+'[")]')):"moment.invalid(/* "+this._i+" */)"},"undefined"!=typeof Symbol&&null!=Symbol.for&&(se[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),se.toJSON=function(){return this.isValid()?this.toISOString():null},se.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},se.unix=function(){return Math.floor(this.valueOf()/1e3)},se.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},se.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},se.eraName=function(){for(var e,t=this.localeData().eras(),n=0,a=t.length;n<a;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].name;if(t[n].until<=e&&e<=t[n].since)return t[n].name}return""},se.eraNarrow=function(){for(var e,t=this.localeData().eras(),n=0,a=t.length;n<a;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].narrow;if(t[n].until<=e&&e<=t[n].since)return t[n].narrow}return""},se.eraAbbr=function(){for(var e,t=this.localeData().eras(),n=0,a=t.length;n<a;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].abbr;if(t[n].until<=e&&e<=t[n].since)return t[n].abbr}return""},se.eraYear=function(){for(var e,t,a=this.localeData().eras(),s=0,r=a.length;s<r;++s)if(e=a[s].since<=a[s].until?1:-1,t=this.clone().startOf("day").valueOf(),a[s].since<=t&&t<=a[s].until||a[s].until<=t&&t<=a[s].since)return(this.year()-n(a[s].since).year())*e+a[s].offset;return this.year()},se.year=Te,se.isLeapYear=function(){return pe(this.year())},se.weekYear=function(e){return un.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},se.isoWeekYear=function(e){return un.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},se.quarter=se.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},se.month=Fe,se.daysInMonth=function(){return je(this.year(),this.month())},se.week=se.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},se.isoWeek=se.isoWeeks=function(e){var t=Ue(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},se.weeksInYear=function(){var e=this.localeData()._week;return Je(this.year(),e.dow,e.doy)},se.weeksInWeekYear=function(){var e=this.localeData()._week;return Je(this.weekYear(),e.dow,e.doy)},se.isoWeeksInYear=function(){return Je(this.year(),1,4)},se.isoWeeksInISOWeekYear=function(){return Je(this.isoWeekYear(),1,4)},se.date=Z,se.day=se.days=function(e){var t,n,a;return this.isValid()?(t=xe(this,"Day"),null!=e?(n=e,a=this.localeData(),e="string"!=typeof n?n:isNaN(n)?"number"==typeof(n=a.weekdaysParse(n))?n:null:parseInt(n,10),this.add(e-t,"d")):t):null!=e?this:NaN},se.weekday=function(e){var t;return this.isValid()?(t=(this.day()+7-this.localeData()._week.dow)%7,null==e?t:this.add(e-t,"d")):null!=e?this:NaN},se.isoWeekday=function(e){var t,n;return this.isValid()?null!=e?(t=e,n=this.localeData(),n="string"==typeof t?n.weekdaysParse(t)%7||7:isNaN(t)?null:t,this.day(this.day()%7?n:n-7)):this.day()||7:null!=e?this:NaN},se.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},se.hour=se.hours=ae,se.minute=se.minutes=J,se.second=se.seconds=Q,se.millisecond=se.milliseconds=q,se.utcOffset=function(e,t,a){var s,r=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null==e)return this._isUTC?r:Nt(this);if("string"==typeof e){if(null===(e=Ft(ne,e)))return this}else Math.abs(e)<16&&!a&&(e*=60);return!this._isUTC&&t&&(s=Nt(this)),this._offset=e,this._isUTC=!0,null!=s&&this.add(s,"m"),r!==e&&(!t||this._changeInProgress?Gt(this,Ut(e-r,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,n.updateOffset(this,!0),this._changeInProgress=null)),this},se.utc=function(e){return this.utcOffset(0,e)},se.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e)&&this.subtract(Nt(this),"m"),this},se.parseZone=function(){var e;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(e=Ft(te,this._i))?this.utcOffset(e):this.utcOffset(0,!0)),this},se.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?Ht(e).utcOffset():0,(this.utcOffset()-e)%60==0)},se.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},se.isLocal=function(){return!!this.isValid()&&!this._isUTC},se.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},se.isUtc=zt,se.isUTC=zt,se.zoneAbbr=function(){return this._isUTC?"UTC":""},se.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},se.dates=w("dates accessor is deprecated. Use date instead.",Z),se.months=w("months accessor is deprecated. Use month instead",Fe),se.years=w("years accessor is deprecated. Use year instead",Te),se.zone=w("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",(function(e,t){return null!=e?(this.utcOffset(e="string"!=typeof e?-e:e,t),this):-this.utcOffset()})),se.isDSTShifted=w("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",(function(){var e,t;return o(this._isDSTShifted)&&(g(e={},this),(e=St(e))._a?(t=(e._isUTC?_:Ht)(e._a),this._isDSTShifted=this.isValid()&&0<function(e,t,n){for(var a=Math.min(e.length,t.length),s=Math.abs(e.length-t.length),r=0,i=0;i<a;i++)(n&&e[i]!==t[i]||!n&&le(e[i])!==le(t[i]))&&r++;return r+s}(e._a,t.toArray())):this._isDSTShifted=!1),this._isDSTShifted})),(re=S.prototype).calendar=function(e,t,n){return D(e=this._calendar[e]||this._calendar.sameElse)?e.call(t,n):e},re.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(j).map((function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e})).join(""),this._longDateFormat[e])},re.invalidDate=function(){return this._invalidDate},re.ordinal=function(e){return this._ordinal.replace("%d",e)},re.preparse=cn,re.postformat=cn,re.relativeTime=function(e,t,n,a){var s=this._relativeTime[n];return D(s)?s(e,t,n,a):s.replace(/%d/i,e)},re.pastFuture=function(e,t){return D(e=this._relativeTime[0<e?"future":"past"])?e(t):e.replace(/%s/i,t)},re.set=function(e){var t,n;for(n in e)r(e,n)&&(D(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},re.eras=function(e,t){for(var a,s=this._eras||_t("en")._eras,r=0,i=s.length;r<i;++r)switch("string"==typeof s[r].since&&(a=n(s[r].since).startOf("day"),s[r].since=a.valueOf()),typeof s[r].until){case"undefined":s[r].until=1/0;break;case"string":a=n(s[r].until).startOf("day").valueOf(),s[r].until=a.valueOf()}return s},re.erasParse=function(e,t,n){var a,s,r,i,o,d=this.eras();for(e=e.toUpperCase(),a=0,s=d.length;a<s;++a)if(r=d[a].name.toUpperCase(),i=d[a].abbr.toUpperCase(),o=d[a].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(i===e)return d[a];break;case"NNNN":if(r===e)return d[a];break;case"NNNNN":if(o===e)return d[a]}else if(0<=[r,i,o].indexOf(e))return d[a]},re.erasConvertYear=function(e,t){var a=e.since<=e.until?1:-1;return void 0===t?n(e.since).year():n(e.since).year()+(t-e.offset)*a},re.erasAbbrRegex=function(e){return r(this,"_erasAbbrRegex")||on.call(this),e?this._erasAbbrRegex:this._erasRegex},re.erasNameRegex=function(e){return r(this,"_erasNameRegex")||on.call(this),e?this._erasNameRegex:this._erasRegex},re.erasNarrowRegex=function(e){return r(this,"_erasNarrowRegex")||on.call(this),e?this._erasNarrowRegex:this._erasRegex},re.months=function(e,t){return e?(a(this._months)?this._months:this._months[(this._months.isFormat||Ee).test(t)?"format":"standalone"])[e.month()]:a(this._months)?this._months:this._months.standalone},re.monthsShort=function(e,t){return e?(a(this._monthsShort)?this._monthsShort:this._monthsShort[Ee.test(t)?"format":"standalone"])[e.month()]:a(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},re.monthsParse=function(e,t,n){var a,s;if(this._monthsParseExact)return function(e,t,n){var a,s,r;if(e=e.toLocaleLowerCase(),!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],a=0;a<12;++a)r=_([2e3,a]),this._shortMonthsParse[a]=this.monthsShort(r,"").toLocaleLowerCase(),this._longMonthsParse[a]=this.months(r,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(s=De.call(this._shortMonthsParse,e))?s:null:-1!==(s=De.call(this._longMonthsParse,e))?s:null:"MMM"===t?-1!==(s=De.call(this._shortMonthsParse,e))||-1!==(s=De.call(this._longMonthsParse,e))?s:null:-1!==(s=De.call(this._longMonthsParse,e))||-1!==(s=De.call(this._shortMonthsParse,e))?s:null}.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),a=0;a<12;a++){if(s=_([2e3,a]),n&&!this._longMonthsParse[a]&&(this._longMonthsParse[a]=new RegExp("^"+this.months(s,"").replace(".","")+"$","i"),this._shortMonthsParse[a]=new RegExp("^"+this.monthsShort(s,"").replace(".","")+"$","i")),n||this._monthsParse[a]||(s="^"+this.months(s,"")+"|^"+this.monthsShort(s,""),this._monthsParse[a]=new RegExp(s.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[a].test(e))return a;if(n&&"MMM"===t&&this._shortMonthsParse[a].test(e))return a;if(!n&&this._monthsParse[a].test(e))return a}},re.monthsRegex=function(e){return this._monthsParseExact?(r(this,"_monthsRegex")||$e.call(this),e?this._monthsStrictRegex:this._monthsRegex):(r(this,"_monthsRegex")||(this._monthsRegex=Ce),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},re.monthsShortRegex=function(e){return this._monthsParseExact?(r(this,"_monthsRegex")||$e.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(r(this,"_monthsShortRegex")||(this._monthsShortRegex=Ae),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},re.week=function(e){return Ue(e,this._week.dow,this._week.doy).week},re.firstDayOfYear=function(){return this._week.doy},re.firstDayOfWeek=function(){return this._week.dow},re.weekdays=function(e,t){return t=a(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"],!0===e?qe(t,this._week.dow):e?t[e.day()]:t},re.weekdaysMin=function(e){return!0===e?qe(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},re.weekdaysShort=function(e){return!0===e?qe(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},re.weekdaysParse=function(e,t,n){var a,s;if(this._weekdaysParseExact)return function(e,t,n){var a,s,r;if(e=e.toLocaleLowerCase(),!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],a=0;a<7;++a)r=_([2e3,1]).day(a),this._minWeekdaysParse[a]=this.weekdaysMin(r,"").toLocaleLowerCase(),this._shortWeekdaysParse[a]=this.weekdaysShort(r,"").toLocaleLowerCase(),this._weekdaysParse[a]=this.weekdays(r,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(s=De.call(this._weekdaysParse,e))?s:null:"ddd"===t?-1!==(s=De.call(this._shortWeekdaysParse,e))?s:null:-1!==(s=De.call(this._minWeekdaysParse,e))?s:null:"dddd"===t?-1!==(s=De.call(this._weekdaysParse,e))||-1!==(s=De.call(this._shortWeekdaysParse,e))||-1!==(s=De.call(this._minWeekdaysParse,e))?s:null:"ddd"===t?-1!==(s=De.call(this._shortWeekdaysParse,e))||-1!==(s=De.call(this._weekdaysParse,e))||-1!==(s=De.call(this._minWeekdaysParse,e))?s:null:-1!==(s=De.call(this._minWeekdaysParse,e))||-1!==(s=De.call(this._weekdaysParse,e))||-1!==(s=De.call(this._shortWeekdaysParse,e))?s:null}.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),a=0;a<7;a++){if(s=_([2e3,1]).day(a),n&&!this._fullWeekdaysParse[a]&&(this._fullWeekdaysParse[a]=new RegExp("^"+this.weekdays(s,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[a]=new RegExp("^"+this.weekdaysShort(s,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[a]=new RegExp("^"+this.weekdaysMin(s,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[a]||(s="^"+this.weekdays(s,"")+"|^"+this.weekdaysShort(s,"")+"|^"+this.weekdaysMin(s,""),this._weekdaysParse[a]=new RegExp(s.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[a].test(e))return a;if(n&&"ddd"===t&&this._shortWeekdaysParse[a].test(e))return a;if(n&&"dd"===t&&this._minWeekdaysParse[a].test(e))return a;if(!n&&this._weekdaysParse[a].test(e))return a}},re.weekdaysRegex=function(e){return this._weekdaysParseExact?(r(this,"_weekdaysRegex")||Xe.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(r(this,"_weekdaysRegex")||(this._weekdaysRegex=Ke),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},re.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(r(this,"_weekdaysRegex")||Xe.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(r(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Ze),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},re.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(r(this,"_weekdaysRegex")||Xe.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(r(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Qe),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},re.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},re.meridiem=function(e,t,n){return 11<e?n?"pm":"PM":n?"am":"AM"},lt("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===le(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),n.lang=w("moment.lang is deprecated. Use moment.locale instead.",lt),n.langData=w("moment.langData is deprecated. Use moment.localeData instead.",_t);var pn=Math.abs;function fn(e,t,n,a){return t=Ut(t,n),e._milliseconds+=a*t._milliseconds,e._days+=a*t._days,e._months+=a*t._months,e._bubble()}function yn(e){return e<0?Math.floor(e):Math.ceil(e)}function Mn(e){return 4800*e/146097}function gn(e){return 146097*e/4800}function Ln(e){return function(){return this.as(e)}}function Yn(e){return function(){return this.isValid()?this._data[e]:NaN}}I=Ln("ms"),R=Ln("s"),K=Ln("m"),U=Ln("h"),Oe=Ln("d"),V=Ln("w"),G=Ln("M"),Be=Ln("Q"),ae=Ln("y"),J=I,Q=Yn("milliseconds"),q=Yn("seconds"),Z=Yn("minutes"),Te=Yn("hours"),re=Yn("days");var vn=Yn("months"),wn=Yn("years"),bn=Math.round,kn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function Dn(e,t,n,a){var s=Ut(e).abs(),r=bn(s.as("s")),i=bn(s.as("m")),o=bn(s.as("h")),d=bn(s.as("d")),u=bn(s.as("M")),l=bn(s.as("w"));return s=bn(s.as("y")),r=(r<=n.ss?["s",r]:r<n.s&&["ss",r])||(i<=1?["m"]:i<n.m&&["mm",i])||(o<=1?["h"]:o<n.h&&["hh",o])||(d<=1?["d"]:d<n.d&&["dd",d]),(r=(r=null!=n.w?r||(l<=1?["w"]:l<n.w&&["ww",l]):r)||(u<=1?["M"]:u<n.M&&["MM",u])||(s<=1?["y"]:["yy",s]))[2]=t,r[3]=0<+e,r[4]=a,function(e,t,n,a,s){return s.relativeTime(t||1,!!n,e,a)}.apply(null,r)}var Tn=Math.abs;function Sn(e){return(0<e)-(e<0)||+e}function xn(){var e,t,n,a,s,r,i,o,d,u,l;return this.isValid()?(e=Tn(this._milliseconds)/1e3,t=Tn(this._days),n=Tn(this._months),(o=this.asSeconds())?(a=ue(e/60),s=ue(a/60),e%=60,a%=60,r=ue(n/12),n%=12,i=e?e.toFixed(3).replace(/\.?0+$/,""):"",d=Sn(this._months)!==Sn(o)?"-":"",u=Sn(this._days)!==Sn(o)?"-":"",l=Sn(this._milliseconds)!==Sn(o)?"-":"",(o<0?"-":"")+"P"+(r?d+r+"Y":"")+(n?d+n+"M":"")+(t?u+t+"D":"")+(s||a||e?"T":"")+(s?l+s+"H":"")+(a?l+a+"M":"")+(e?l+i+"S":"")):"P0D"):this.localeData().invalidDate()}function Hn(e){return 0===e?0:1===e?1:2===e?2:3<=e%100&&e%100<=10?3:11<=e%100?4:5}function jn(e){return function(t,n,a,s){var r=Hn(t),i=Cn[e][Hn(t)];return(i=2===r?i[n?0:1]:i).replace(/%d/i,t)}}function On(e){return 0===e?0:1===e?1:2===e?2:3<=e%100&&e%100<=10?3:11<=e%100?4:5}function Pn(e){return function(t,n,a,s){var r=On(t),i=Fn[e][On(t)];return(i=2===r?i[n?0:1]:i).replace(/%d/i,t)}}function En(e){return 0===e?0:1===e?1:2===e?2:3<=e%100&&e%100<=10?3:11<=e%100?4:5}function An(e){return function(t,n,a,s){var r=En(t),i=Jn[e][En(t)];return(i=2===r?i[n?0:1]:i).replace(/%d/i,t)}}var Cn=((ma=Pt.prototype).isValid=function(){return this._isValid},ma.abs=function(){var e=this._data;return this._milliseconds=pn(this._milliseconds),this._days=pn(this._days),this._months=pn(this._months),e.milliseconds=pn(e.milliseconds),e.seconds=pn(e.seconds),e.minutes=pn(e.minutes),e.hours=pn(e.hours),e.months=pn(e.months),e.years=pn(e.years),this},ma.add=function(e,t){return fn(this,e,t,1)},ma.subtract=function(e,t){return fn(this,e,t,-1)},ma.as=function(e){if(!this.isValid())return NaN;var t,n,a=this._milliseconds;if("month"===(e=$(e))||"quarter"===e||"year"===e)switch(t=this._days+a/864e5,n=this._months+Mn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(gn(this._months)),e){case"week":return t/7+a/6048e5;case"day":return t+a/864e5;case"hour":return 24*t+a/36e5;case"minute":return 1440*t+a/6e4;case"second":return 86400*t+a/1e3;case"millisecond":return Math.floor(864e5*t)+a;default:throw new Error("Unknown unit "+e)}},ma.asMilliseconds=I,ma.asSeconds=R,ma.asMinutes=K,ma.asHours=U,ma.asDays=Oe,ma.asWeeks=V,ma.asMonths=G,ma.asQuarters=Be,ma.asYears=ae,ma.valueOf=J,ma._bubble=function(){var e=this._milliseconds,t=this._days,n=this._months,a=this._data;return 0<=e&&0<=t&&0<=n||e<=0&&t<=0&&n<=0||(e+=864e5*yn(gn(n)+t),n=t=0),a.milliseconds=e%1e3,e=ue(e/1e3),a.seconds=e%60,e=ue(e/60),a.minutes=e%60,e=ue(e/60),a.hours=e%24,t+=ue(e/24),n+=e=ue(Mn(t)),t-=yn(gn(e)),e=ue(n/12),n%=12,a.days=t,a.months=n,a.years=e,this},ma.clone=function(){return Ut(this)},ma.get=function(e){return e=$(e),this.isValid()?this[e+"s"]():NaN},ma.milliseconds=Q,ma.seconds=q,ma.minutes=Z,ma.hours=Te,ma.days=re,ma.weeks=function(){return ue(this.days()/7)},ma.months=vn,ma.years=wn,ma.humanize=function(e,t){var n,a;return this.isValid()?(n=!1,a=kn,"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(n=e),"object"==typeof t&&(a=Object.assign({},kn,t),null!=t.s)&&null==t.ss&&(a.ss=t.s-1),t=Dn(this,!n,a,e=this.localeData()),n&&(t=e.pastFuture(+this,t)),e.postformat(t)):this.localeData().invalidDate()},ma.toISOString=xn,ma.toString=xn,ma.toJSON=xn,ma.locale=Qt,ma.localeData=Xt,ma.toIsoString=w("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",xn),ma.lang=Ve,A("X",0,0,"unix"),A("x",0,0,"valueOf"),ie("x",ee),ie("X",/[+-]?\d+(\.\d{1,3})?/),me("X",(function(e,t,n){n._d=new Date(1e3*parseFloat(e))})),me("x",(function(e,t,n){n._d=new Date(le(e))})),n.version="2.30.1",t=Ht,n.fn=se,n.min=function(){return jt("isBefore",[].slice.call(arguments,0))},n.max=function(){return jt("isAfter",[].slice.call(arguments,0))},n.now=function(){return Date.now?Date.now():+new Date},n.utc=_,n.unix=function(e){return Ht(1e3*e)},n.months=function(e,t){return mn(e,t,"months")},n.isDate=u,n.locale=lt,n.invalid=p,n.duration=Ut,n.isMoment=Y,n.weekdays=function(e,t,n){return hn(e,t,n,"weekdays")},n.parseZone=function(){return Ht.apply(null,arguments).parseZone()},n.localeData=_t,n.isDuration=Et,n.monthsShort=function(e,t){return mn(e,t,"monthsShort")},n.weekdaysMin=function(e,t,n){return hn(e,t,n,"weekdaysMin")},n.defineLocale=ct,n.updateLocale=function(e,t){var n,a;return null!=t?(a=st,null!=rt[e]&&null!=rt[e].parentLocale?rt[e].set(T(rt[e]._config,t)):(t=T(a=null!=(n=ut(e))?n._config:a,t),null==n&&(t.abbr=e),(a=new S(t)).parentLocale=rt[e],rt[e]=a),lt(e)):null!=rt[e]&&(null!=rt[e].parentLocale?(rt[e]=rt[e].parentLocale,e===lt()&&lt(e)):null!=rt[e]&&delete rt[e]),rt[e]},n.locales=function(){return x(rt)},n.weekdaysShort=function(e,t,n){return hn(e,t,n,"weekdaysShort")},n.normalizeUnits=$,n.relativeTimeRounding=function(e){return void 0===e?bn:"function"==typeof e&&(bn=e,!0)},n.relativeTimeThreshold=function(e,t){return void 0!==kn[e]&&(void 0===t?kn[e]:(kn[e]=t,"s"===e&&(kn.ss=t-1),!0))},n.calendarFormat=function(e,t){return(e=e.diff(t,"days",!0))<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},n.prototype=se,n.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},n.defineLocale("af",{months:"Januarie_Februarie_Maart_April_Mei_Junie_Julie_Augustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des".split("_"),weekdays:"Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag".split("_"),weekdaysShort:"Son_Maa_Din_Woe_Don_Vry_Sat".split("_"),weekdaysMin:"So_Ma_Di_Wo_Do_Vr_Sa".split("_"),meridiemParse:/vm|nm/i,isPM:function(e){return/^nm$/i.test(e)},meridiem:function(e,t,n){return e<12?n?"vm":"VM":n?"nm":"NM"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Vandag om] LT",nextDay:"[Môre om] LT",nextWeek:"dddd [om] LT",lastDay:"[Gister om] LT",lastWeek:"[Laas] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oor %s",past:"%s gelede",s:"'n paar sekondes",ss:"%d sekondes",m:"'n minuut",mm:"%d minute",h:"'n uur",hh:"%d ure",d:"'n dag",dd:"%d dae",M:"'n maand",MM:"%d maande",y:"'n jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||20<=e?"ste":"de")},week:{dow:1,doy:4}}),{s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]}),Wn=(I=["جانفي","فيفري","مارس","أفريل","ماي","جوان","جويلية","أوت","سبتمبر","أكتوبر","نوفمبر","ديسمبر"],n.defineLocale("ar-dz",{months:I,monthsShort:I,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/\u0635|\u0645/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:jn("s"),ss:jn("s"),m:jn("m"),mm:jn("m"),h:jn("h"),hh:jn("h"),d:jn("d"),dd:jn("d"),M:jn("M"),MM:jn("M"),y:jn("y"),yy:jn("y")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:0,doy:4}}),n.defineLocale("ar-kw",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:0,doy:12}}),{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",0:"0"}),Fn={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},$n=(R=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"],n.defineLocale("ar-ly",{months:R,monthsShort:R,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/\u0635|\u0645/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:Pn("s"),ss:Pn("s"),m:Pn("m"),mm:Pn("m"),h:Pn("h"),hh:Pn("h"),d:Pn("d"),dd:Pn("d"),M:Pn("M"),MM:Pn("M"),y:Pn("y"),yy:Pn("y")},preparse:function(e){return e.replace(/\u060c/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return Wn[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}}),n.defineLocale("ar-ma",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اثنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:1,doy:4}}),{1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"}),Nn={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},zn=(n.defineLocale("ar-ps",{months:"كانون الثاني_شباط_آذار_نيسان_أيّار_حزيران_تمّوز_آب_أيلول_تشري الأوّل_تشرين الثاني_كانون الأوّل".split("_"),monthsShort:"ك٢_شباط_آذار_نيسان_أيّار_حزيران_تمّوز_آب_أيلول_ت١_ت٢_ك١".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/\u0635|\u0645/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},preparse:function(e){return e.replace(/[\u0663\u0664\u0665\u0666\u0667\u0668\u0669\u0660]/g,(function(e){return Nn[e]})).split("").reverse().join("").replace(/[\u0661\u0662](?![\u062a\u0643])/g,(function(e){return Nn[e]})).split("").reverse().join("").replace(/\u060c/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return $n[e]})).replace(/,/g,"،")},week:{dow:0,doy:6}}),{1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"}),In={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},Rn=(n.defineLocale("ar-sa",{months:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/\u0635|\u0645/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},preparse:function(e){return e.replace(/[\u0661\u0662\u0663\u0664\u0665\u0666\u0667\u0668\u0669\u0660]/g,(function(e){return In[e]})).replace(/\u060c/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return zn[e]})).replace(/,/g,"،")},week:{dow:0,doy:6}}),n.defineLocale("ar-tn",{months:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:1,doy:4}}),{1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"}),Un={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},Jn={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},qn=(K=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"],n.defineLocale("ar",{months:K,monthsShort:K,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/\u0635|\u0645/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:An("s"),ss:An("s"),m:An("m"),mm:An("m"),h:An("h"),hh:An("h"),d:An("d"),dd:An("d"),M:An("M"),MM:An("M"),y:An("y"),yy:An("y")},preparse:function(e){return e.replace(/[\u0661\u0662\u0663\u0664\u0665\u0666\u0667\u0668\u0669\u0660]/g,(function(e){return Un[e]})).replace(/\u060c/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return Rn[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}}),{1:"-inci",5:"-inci",8:"-inci",70:"-inci",80:"-inci",2:"-nci",7:"-nci",20:"-nci",50:"-nci",3:"-üncü",4:"-üncü",100:"-üncü",6:"-ncı",9:"-uncu",10:"-uncu",30:"-uncu",60:"-ıncı",90:"-ıncı"});function Bn(e,t,n){return"m"===n?t?"хвіліна":"хвіліну":"h"===n?t?"гадзіна":"гадзіну":e+" "+(e=+e,t=(t={ss:t?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:t?"хвіліна_хвіліны_хвілін":"хвіліну_хвіліны_хвілін",hh:t?"гадзіна_гадзіны_гадзін":"гадзіну_гадзіны_гадзін",dd:"дзень_дні_дзён",MM:"месяц_месяцы_месяцаў",yy:"год_гады_гадоў"}[n]).split("_"),e%10==1&&e%100!=11?t[0]:2<=e%10&&e%10<=4&&(e%100<10||20<=e%100)?t[1]:t[2])}n.defineLocale("az",{months:"yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr".split("_"),monthsShort:"yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek".split("_"),weekdays:"Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə".split("_"),weekdaysShort:"Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən".split("_"),weekdaysMin:"Bz_BE_ÇA_Çə_CA_Cü_Şə".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[sabah saat] LT",nextWeek:"[gələn həftə] dddd [saat] LT",lastDay:"[dünən] LT",lastWeek:"[keçən həftə] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s əvvəl",s:"bir neçə saniyə",ss:"%d saniyə",m:"bir dəqiqə",mm:"%d dəqiqə",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",M:"bir ay",MM:"%d ay",y:"bir il",yy:"%d il"},meridiemParse:/gec\u0259|s\u0259h\u0259r|g\xfcnd\xfcz|ax\u015fam/,isPM:function(e){return/^(g\xfcnd\xfcz|ax\u015fam)$/.test(e)},meridiem:function(e,t,n){return e<4?"gecə":e<12?"səhər":e<17?"gündüz":"axşam"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0131nc\u0131|inci|nci|\xfcnc\xfc|nc\u0131|uncu)/,ordinal:function(e){var t;return 0===e?e+"-ıncı":e+(qn[t=e%10]||qn[e%100-t]||qn[100<=e?100:null])},week:{dow:1,doy:7}}),n.defineLocale("be",{months:{format:"студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня".split("_"),standalone:"студзень_люты_сакавік_красавік_травень_чэрвень_ліпень_жнівень_верасень_кастрычнік_лістапад_снежань".split("_")},monthsShort:"студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж".split("_"),weekdays:{format:"нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу".split("_"),standalone:"нядзеля_панядзелак_аўторак_серада_чацвер_пятніца_субота".split("_"),isFormat:/\[ ?[\u0423\u0443\u045e] ?(?:\u043c\u0456\u043d\u0443\u043b\u0443\u044e|\u043d\u0430\u0441\u0442\u0443\u043f\u043d\u0443\u044e)? ?\] ?dddd/},weekdaysShort:"нд_пн_ат_ср_чц_пт_сб".split("_"),weekdaysMin:"нд_пн_ат_ср_чц_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., HH:mm",LLLL:"dddd, D MMMM YYYY г., HH:mm"},calendar:{sameDay:"[Сёння ў] LT",nextDay:"[Заўтра ў] LT",lastDay:"[Учора ў] LT",nextWeek:function(){return"[У] dddd [ў] LT"},lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return"[У мінулую] dddd [ў] LT";case 1:case 2:case 4:return"[У мінулы] dddd [ў] LT"}},sameElse:"L"},relativeTime:{future:"праз %s",past:"%s таму",s:"некалькі секунд",m:Bn,mm:Bn,h:Bn,hh:Bn,d:"дзень",dd:Bn,M:"месяц",MM:Bn,y:"год",yy:Bn},meridiemParse:/\u043d\u043e\u0447\u044b|\u0440\u0430\u043d\u0456\u0446\u044b|\u0434\u043d\u044f|\u0432\u0435\u0447\u0430\u0440\u0430/,isPM:function(e){return/^(\u0434\u043d\u044f|\u0432\u0435\u0447\u0430\u0440\u0430)$/.test(e)},meridiem:function(e,t,n){return e<4?"ночы":e<12?"раніцы":e<17?"дня":"вечара"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0456|\u044b|\u0433\u0430)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return e%10!=2&&e%10!=3||e%100==12||e%100==13?e+"-ы":e+"-і";case"D":return e+"-га";default:return e}},week:{dow:1,doy:7}}),n.defineLocale("bg",{months:"януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември".split("_"),monthsShort:"яну_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек".split("_"),weekdays:"неделя_понеделник_вторник_сряда_четвъртък_петък_събота".split("_"),weekdaysShort:"нед_пон_вто_сря_чет_пет_съб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Днес в] LT",nextDay:"[Утре в] LT",nextWeek:"dddd [в] LT",lastDay:"[Вчера в] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Миналата] dddd [в] LT";case 1:case 2:case 4:case 5:return"[Миналия] dddd [в] LT"}},sameElse:"L"},relativeTime:{future:"след %s",past:"преди %s",s:"няколко секунди",ss:"%d секунди",m:"минута",mm:"%d минути",h:"час",hh:"%d часа",d:"ден",dd:"%d дена",w:"седмица",ww:"%d седмици",M:"месец",MM:"%d месеца",y:"година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0435\u0432|\u0435\u043d|\u0442\u0438|\u0432\u0438|\u0440\u0438|\u043c\u0438)/,ordinal:function(e){var t=e%10,n=e%100;return 0===e?e+"-ев":0==n?e+"-ен":10<n&&n<20?e+"-ти":1==t?e+"-ви":2==t?e+"-ри":7==t||8==t?e+"-ми":e+"-ти"},week:{dow:1,doy:7}}),n.defineLocale("bm",{months:"Zanwuyekalo_Fewuruyekalo_Marisikalo_Awirilikalo_Mɛkalo_Zuwɛnkalo_Zuluyekalo_Utikalo_Sɛtanburukalo_ɔkutɔburukalo_Nowanburukalo_Desanburukalo".split("_"),monthsShort:"Zan_Few_Mar_Awi_Mɛ_Zuw_Zul_Uti_Sɛt_ɔku_Now_Des".split("_"),weekdays:"Kari_Ntɛnɛn_Tarata_Araba_Alamisa_Juma_Sibiri".split("_"),weekdaysShort:"Kar_Ntɛ_Tar_Ara_Ala_Jum_Sib".split("_"),weekdaysMin:"Ka_Nt_Ta_Ar_Al_Ju_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"MMMM [tile] D [san] YYYY",LLL:"MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm",LLLL:"dddd MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm"},calendar:{sameDay:"[Bi lɛrɛ] LT",nextDay:"[Sini lɛrɛ] LT",nextWeek:"dddd [don lɛrɛ] LT",lastDay:"[Kunu lɛrɛ] LT",lastWeek:"dddd [tɛmɛnen lɛrɛ] LT",sameElse:"L"},relativeTime:{future:"%s kɔnɔ",past:"a bɛ %s bɔ",s:"sanga dama dama",ss:"sekondi %d",m:"miniti kelen",mm:"miniti %d",h:"lɛrɛ kelen",hh:"lɛrɛ %d",d:"tile kelen",dd:"tile %d",M:"kalo kelen",MM:"kalo %d",y:"san kelen",yy:"san %d"},week:{dow:1,doy:4}});var Gn={1:"১",2:"২",3:"৩",4:"৪",5:"৫",6:"৬",7:"৭",8:"৮",9:"৯",0:"০"},Vn={"১":"1","২":"2","৩":"3","৪":"4","৫":"5","৬":"6","৭":"7","৮":"8","৯":"9","০":"0"},Kn=(n.defineLocale("bn-bd",{months:"জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর".split("_"),monthsShort:"জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে".split("_"),weekdays:"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার".split("_"),weekdaysShort:"রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি".split("_"),weekdaysMin:"রবি_সোম_মঙ্গল_বুধ_বৃহ_শুক্র_শনি".split("_"),longDateFormat:{LT:"A h:mm সময়",LTS:"A h:mm:ss সময়",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm সময়",LLLL:"dddd, D MMMM YYYY, A h:mm সময়"},calendar:{sameDay:"[আজ] LT",nextDay:"[আগামীকাল] LT",nextWeek:"dddd, LT",lastDay:"[গতকাল] LT",lastWeek:"[গত] dddd, LT",sameElse:"L"},relativeTime:{future:"%s পরে",past:"%s আগে",s:"কয়েক সেকেন্ড",ss:"%d সেকেন্ড",m:"এক মিনিট",mm:"%d মিনিট",h:"এক ঘন্টা",hh:"%d ঘন্টা",d:"এক দিন",dd:"%d দিন",M:"এক মাস",MM:"%d মাস",y:"এক বছর",yy:"%d বছর"},preparse:function(e){return e.replace(/[\u09e7\u09e8\u09e9\u09ea\u09eb\u09ec\u09ed\u09ee\u09ef\u09e6]/g,(function(e){return Vn[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return Gn[e]}))},meridiemParse:/\u09b0\u09be\u09a4|\u09ad\u09cb\u09b0|\u09b8\u0995\u09be\u09b2|\u09a6\u09c1\u09aa\u09c1\u09b0|\u09ac\u09bf\u0995\u09be\u09b2|\u09b8\u09a8\u09cd\u09a7\u09cd\u09af\u09be|\u09b0\u09be\u09a4/,meridiemHour:function(e,t){return 12===e&&(e=0),"রাত"===t?e<4?e:e+12:"ভোর"===t||"সকাল"===t?e:"দুপুর"===t?3<=e?e:e+12:"বিকাল"===t||"সন্ধ্যা"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"রাত":e<6?"ভোর":e<12?"সকাল":e<15?"দুপুর":e<18?"বিকাল":e<20?"সন্ধ্যা":"রাত"},week:{dow:0,doy:6}}),{1:"১",2:"২",3:"৩",4:"৪",5:"৫",6:"৬",7:"৭",8:"৮",9:"৯",0:"০"}),Zn={"১":"1","২":"2","৩":"3","৪":"4","৫":"5","৬":"6","৭":"7","৮":"8","৯":"9","০":"0"},Qn=(n.defineLocale("bn",{months:"জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর".split("_"),monthsShort:"জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে".split("_"),weekdays:"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার".split("_"),weekdaysShort:"রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি".split("_"),weekdaysMin:"রবি_সোম_মঙ্গল_বুধ_বৃহ_শুক্র_শনি".split("_"),longDateFormat:{LT:"A h:mm সময়",LTS:"A h:mm:ss সময়",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm সময়",LLLL:"dddd, D MMMM YYYY, A h:mm সময়"},calendar:{sameDay:"[আজ] LT",nextDay:"[আগামীকাল] LT",nextWeek:"dddd, LT",lastDay:"[গতকাল] LT",lastWeek:"[গত] dddd, LT",sameElse:"L"},relativeTime:{future:"%s পরে",past:"%s আগে",s:"কয়েক সেকেন্ড",ss:"%d সেকেন্ড",m:"এক মিনিট",mm:"%d মিনিট",h:"এক ঘন্টা",hh:"%d ঘন্টা",d:"এক দিন",dd:"%d দিন",M:"এক মাস",MM:"%d মাস",y:"এক বছর",yy:"%d বছর"},preparse:function(e){return e.replace(/[\u09e7\u09e8\u09e9\u09ea\u09eb\u09ec\u09ed\u09ee\u09ef\u09e6]/g,(function(e){return Zn[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return Kn[e]}))},meridiemParse:/\u09b0\u09be\u09a4|\u09b8\u0995\u09be\u09b2|\u09a6\u09c1\u09aa\u09c1\u09b0|\u09ac\u09bf\u0995\u09be\u09b2|\u09b0\u09be\u09a4/,meridiemHour:function(e,t){return 12===e&&(e=0),"রাত"===t&&4<=e||"দুপুর"===t&&e<5||"বিকাল"===t?e+12:e},meridiem:function(e,t,n){return e<4?"রাত":e<10?"সকাল":e<17?"দুপুর":e<20?"বিকাল":"রাত"},week:{dow:0,doy:6}}),{1:"༡",2:"༢",3:"༣",4:"༤",5:"༥",6:"༦",7:"༧",8:"༨",9:"༩",0:"༠"}),Xn={"༡":"1","༢":"2","༣":"3","༤":"4","༥":"5","༦":"6","༧":"7","༨":"8","༩":"9","༠":"0"};function ea(e,t,n){return e+" "+(n={mm:"munutenn",MM:"miz",dd:"devezh"}[n],2!==e?n:void 0!==(e={m:"v",b:"v",d:"z"})[n.charAt(0)]?e[n.charAt(0)]+n.substring(1):n)}function ta(e,t,n){var a=e+" ";switch(n){case"ss":return a+(1===e?"sekunda":2===e||3===e||4===e?"sekunde":"sekundi");case"mm":return a+(1===e||2!==e&&3!==e&&4!==e?"minuta":"minute");case"h":return"jedan sat";case"hh":return a+(1===e?"sat":2===e||3===e||4===e?"sata":"sati");case"dd":return a+(1===e?"dan":"dana");case"MM":return a+(1===e?"mjesec":2===e||3===e||4===e?"mjeseca":"mjeseci");case"yy":return a+(1===e||2!==e&&3!==e&&4!==e?"godina":"godine")}}function na(e){return 1<e&&e<5&&1!=~~(e/10)}function aa(e,t,n,a){var s=e+" ";switch(n){case"s":return t||a?"pár sekund":"pár sekundami";case"ss":return t||a?s+(na(e)?"sekundy":"sekund"):s+"sekundami";case"m":return t?"minuta":a?"minutu":"minutou";case"mm":return t||a?s+(na(e)?"minuty":"minut"):s+"minutami";case"h":return t?"hodina":a?"hodinu":"hodinou";case"hh":return t||a?s+(na(e)?"hodiny":"hodin"):s+"hodinami";case"d":return t||a?"den":"dnem";case"dd":return t||a?s+(na(e)?"dny":"dní"):s+"dny";case"M":return t||a?"měsíc":"měsícem";case"MM":return t||a?s+(na(e)?"měsíce":"měsíců"):s+"měsíci";case"y":return t||a?"rok":"rokem";case"yy":return t||a?s+(na(e)?"roky":"let"):s+"lety"}}function sa(e,t,n,a){return e={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]},t?e[n][0]:e[n][1]}function ra(e,t,n,a){return e={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]},t?e[n][0]:e[n][1]}function ia(e,t,n,a){return e={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]},t?e[n][0]:e[n][1]}n.defineLocale("bo",{months:"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ".split("_"),monthsShort:"ཟླ་1_ཟླ་2_ཟླ་3_ཟླ་4_ཟླ་5_ཟླ་6_ཟླ་7_ཟླ་8_ཟླ་9_ཟླ་10_ཟླ་11_ཟླ་12".split("_"),monthsShortRegex:/^(\u0f5f\u0fb3\u0f0b\d{1,2})/,monthsParseExact:!0,weekdays:"གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་".split("_"),weekdaysShort:"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་".split("_"),weekdaysMin:"ཉི_ཟླ_མིག_ལྷག_ཕུར_སངས_སྤེན".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[དི་རིང] LT",nextDay:"[སང་ཉིན] LT",nextWeek:"[བདུན་ཕྲག་རྗེས་མ], LT",lastDay:"[ཁ་སང] LT",lastWeek:"[བདུན་ཕྲག་མཐའ་མ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ལ་",past:"%s སྔན་ལ",s:"ལམ་སང",ss:"%d སྐར་ཆ།",m:"སྐར་མ་གཅིག",mm:"%d སྐར་མ",h:"ཆུ་ཚོད་གཅིག",hh:"%d ཆུ་ཚོད",d:"ཉིན་གཅིག",dd:"%d ཉིན་",M:"ཟླ་བ་གཅིག",MM:"%d ཟླ་བ",y:"ལོ་གཅིག",yy:"%d ལོ"},preparse:function(e){return e.replace(/[\u0f21\u0f22\u0f23\u0f24\u0f25\u0f26\u0f27\u0f28\u0f29\u0f20]/g,(function(e){return Xn[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return Qn[e]}))},meridiemParse:/\u0f58\u0f5a\u0f53\u0f0b\u0f58\u0f7c|\u0f5e\u0f7c\u0f42\u0f66\u0f0b\u0f40\u0f66|\u0f49\u0f72\u0f53\u0f0b\u0f42\u0f74\u0f44|\u0f51\u0f42\u0f7c\u0f44\u0f0b\u0f51\u0f42|\u0f58\u0f5a\u0f53\u0f0b\u0f58\u0f7c/,meridiemHour:function(e,t){return 12===e&&(e=0),"མཚན་མོ"===t&&4<=e||"ཉིན་གུང"===t&&e<5||"དགོང་དག"===t?e+12:e},meridiem:function(e,t,n){return e<4?"མཚན་མོ":e<10?"ཞོགས་ཀས":e<17?"ཉིན་གུང":e<20?"དགོང་དག":"མཚན་མོ"},week:{dow:0,doy:6}}),U=[/^gen/i,/^c[\u02bc\']hwe/i,/^meu/i,/^ebr/i,/^mae/i,/^(mez|eve)/i,/^gou/i,/^eos/i,/^gwe/i,/^her/i,/^du/i,/^ker/i],Oe=/^(genver|c[\u02bc\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu|gen|c[\u02bc\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,V=[/^Su/i,/^Lu/i,/^Me([^r]|$)/i,/^Mer/i,/^Ya/i,/^Gw/i,/^Sa/i],n.defineLocale("br",{months:"Genver_Cʼhwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu".split("_"),monthsShort:"Gen_Cʼhwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker".split("_"),weekdays:"Sul_Lun_Meurzh_Mercʼher_Yaou_Gwener_Sadorn".split("_"),weekdaysShort:"Sul_Lun_Meu_Mer_Yao_Gwe_Sad".split("_"),weekdaysMin:"Su_Lu_Me_Mer_Ya_Gw_Sa".split("_"),weekdaysParse:V,fullWeekdaysParse:[/^sul/i,/^lun/i,/^meurzh/i,/^merc[\u02bc\']her/i,/^yaou/i,/^gwener/i,/^sadorn/i],shortWeekdaysParse:[/^Sul/i,/^Lun/i,/^Meu/i,/^Mer/i,/^Yao/i,/^Gwe/i,/^Sad/i],minWeekdaysParse:V,monthsRegex:Oe,monthsShortRegex:Oe,monthsStrictRegex:/^(genver|c[\u02bc\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu)/i,monthsShortStrictRegex:/^(gen|c[\u02bc\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,monthsParse:U,longMonthsParse:U,shortMonthsParse:U,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [a viz] MMMM YYYY",LLL:"D [a viz] MMMM YYYY HH:mm",LLLL:"dddd, D [a viz] MMMM YYYY HH:mm"},calendar:{sameDay:"[Hiziv da] LT",nextDay:"[Warcʼhoazh da] LT",nextWeek:"dddd [da] LT",lastDay:"[Decʼh da] LT",lastWeek:"dddd [paset da] LT",sameElse:"L"},relativeTime:{future:"a-benn %s",past:"%s ʼzo",s:"un nebeud segondennoù",ss:"%d eilenn",m:"ur vunutenn",mm:ea,h:"un eur",hh:"%d eur",d:"un devezh",dd:ea,M:"ur miz",MM:ea,y:"ur bloaz",yy:function(e){switch(function e(t){return 9<t?e(t%10):t}(e)){case 1:case 3:case 4:case 5:case 9:return e+" bloaz";default:return e+" vloaz"}}},dayOfMonthOrdinalParse:/\d{1,2}(a\xf1|vet)/,ordinal:function(e){return e+(1===e?"añ":"vet")},week:{dow:1,doy:4},meridiemParse:/a.m.|g.m./,isPM:function(e){return"g.m."===e},meridiem:function(e,t,n){return e<12?"a.m.":"g.m."}}),n.defineLocale("bs",{months:"januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[prošlu] dddd [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:ta,m:function(e,t,n,a){if("m"===n)return t?"jedna minuta":a?"jednu minutu":"jedne minute"},mm:ta,h:ta,hh:ta,d:"dan",dd:ta,M:"mjesec",MM:ta,y:"godinu",yy:ta},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}}),n.defineLocale("ca",{months:{standalone:"gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre".split("_"),format:"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre".split("_"),isFormat:/D[oD]?(\s)+MMMM/},monthsShort:"gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.".split("_"),monthsParseExact:!0,weekdays:"diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dt._dc._dj._dv._ds.".split("_"),weekdaysMin:"dg_dl_dt_dc_dj_dv_ds".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [de] YYYY",ll:"D MMM YYYY",LLL:"D MMMM [de] YYYY [a les] H:mm",lll:"D MMM YYYY, H:mm",LLLL:"dddd D MMMM [de] YYYY [a les] H:mm",llll:"ddd D MMM YYYY, H:mm"},calendar:{sameDay:function(){return"[avui a "+(1!==this.hours()?"les":"la")+"] LT"},nextDay:function(){return"[demà a "+(1!==this.hours()?"les":"la")+"] LT"},nextWeek:function(){return"dddd [a "+(1!==this.hours()?"les":"la")+"] LT"},lastDay:function(){return"[ahir a "+(1!==this.hours()?"les":"la")+"] LT"},lastWeek:function(){return"[el] dddd [passat a "+(1!==this.hours()?"les":"la")+"] LT"},sameElse:"L"},relativeTime:{future:"d'aquí %s",past:"fa %s",s:"uns segons",ss:"%d segons",m:"un minut",mm:"%d minuts",h:"una hora",hh:"%d hores",d:"un dia",dd:"%d dies",M:"un mes",MM:"%d mesos",y:"un any",yy:"%d anys"},dayOfMonthOrdinalParse:/\d{1,2}(r|n|t|\xe8|a)/,ordinal:function(e,t){return e+("w"!==t&&"W"!==t?1===e?"r":2===e?"n":3===e?"r":4===e?"t":"è":"a")},week:{dow:1,doy:4}}),G={standalone:"leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec".split("_"),format:"ledna_února_března_dubna_května_června_července_srpna_září_října_listopadu_prosince".split("_"),isFormat:/DD?[o.]?(\[[^\[\]]*\]|\s)+MMMM/},Be="led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro".split("_"),ae=[/^led/i,/^\xfano/i,/^b\u0159e/i,/^dub/i,/^kv\u011b/i,/^(\u010dvn|\u010derven$|\u010dervna)/i,/^(\u010dvc|\u010dervenec|\u010dervence)/i,/^srp/i,/^z\xe1\u0159/i,/^\u0159\xedj/i,/^lis/i,/^pro/i],J=/^(leden|\xfanor|b\u0159ezen|duben|kv\u011bten|\u010dervenec|\u010dervence|\u010derven|\u010dervna|srpen|z\xe1\u0159\xed|\u0159\xedjen|listopad|prosinec|led|\xfano|b\u0159e|dub|kv\u011b|\u010dvn|\u010dvc|srp|z\xe1\u0159|\u0159\xedj|lis|pro)/i,n.defineLocale("cs",{months:G,monthsShort:Be,monthsRegex:J,monthsShortRegex:J,monthsStrictRegex:/^(leden|ledna|\xfanora|\xfanor|b\u0159ezen|b\u0159ezna|duben|dubna|kv\u011bten|kv\u011btna|\u010dervenec|\u010dervence|\u010derven|\u010dervna|srpen|srpna|z\xe1\u0159\xed|\u0159\xedjen|\u0159\xedjna|listopadu|listopad|prosinec|prosince)/i,monthsShortStrictRegex:/^(led|\xfano|b\u0159e|dub|kv\u011b|\u010dvn|\u010dvc|srp|z\xe1\u0159|\u0159\xedj|lis|pro)/i,monthsParse:ae,longMonthsParse:ae,shortMonthsParse:ae,weekdays:"neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota".split("_"),weekdaysShort:"ne_po_út_st_čt_pá_so".split("_"),weekdaysMin:"ne_po_út_st_čt_pá_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm",l:"D. M. YYYY"},calendar:{sameDay:"[dnes v] LT",nextDay:"[zítra v] LT",nextWeek:function(){switch(this.day()){case 0:return"[v neděli v] LT";case 1:case 2:return"[v] dddd [v] LT";case 3:return"[ve středu v] LT";case 4:return"[ve čtvrtek v] LT";case 5:return"[v pátek v] LT";case 6:return"[v sobotu v] LT"}},lastDay:"[včera v] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulou neděli v] LT";case 1:case 2:return"[minulé] dddd [v] LT";case 3:return"[minulou středu v] LT";case 4:case 5:return"[minulý] dddd [v] LT";case 6:return"[minulou sobotu v] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"před %s",s:aa,ss:aa,m:aa,mm:aa,h:aa,hh:aa,d:aa,dd:aa,M:aa,MM:aa,y:aa,yy:aa},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("cv",{months:"кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав".split("_"),monthsShort:"кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш".split("_"),weekdays:"вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун".split("_"),weekdaysShort:"выр_тун_ытл_юн_кӗҫ_эрн_шӑм".split("_"),weekdaysMin:"вр_тн_ыт_юн_кҫ_эр_шм".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]",LLL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm",LLLL:"dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm"},calendar:{sameDay:"[Паян] LT [сехетре]",nextDay:"[Ыран] LT [сехетре]",lastDay:"[Ӗнер] LT [сехетре]",nextWeek:"[Ҫитес] dddd LT [сехетре]",lastWeek:"[Иртнӗ] dddd LT [сехетре]",sameElse:"L"},relativeTime:{future:function(e){return e+(/\u0441\u0435\u0445\u0435\u0442$/i.exec(e)?"рен":/\u04ab\u0443\u043b$/i.exec(e)?"тан":"ран")},past:"%s каялла",s:"пӗр-ик ҫеккунт",ss:"%d ҫеккунт",m:"пӗр минут",mm:"%d минут",h:"пӗр сехет",hh:"%d сехет",d:"пӗр кун",dd:"%d кун",M:"пӗр уйӑх",MM:"%d уйӑх",y:"пӗр ҫул",yy:"%d ҫул"},dayOfMonthOrdinalParse:/\d{1,2}-\u043c\u04d7\u0448/,ordinal:"%d-мӗш",week:{dow:1,doy:7}}),n.defineLocale("cy",{months:"Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr".split("_"),monthsShort:"Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag".split("_"),weekdays:"Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn".split("_"),weekdaysShort:"Sul_Llun_Maw_Mer_Iau_Gwe_Sad".split("_"),weekdaysMin:"Su_Ll_Ma_Me_Ia_Gw_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Heddiw am] LT",nextDay:"[Yfory am] LT",nextWeek:"dddd [am] LT",lastDay:"[Ddoe am] LT",lastWeek:"dddd [diwethaf am] LT",sameElse:"L"},relativeTime:{future:"mewn %s",past:"%s yn ôl",s:"ychydig eiliadau",ss:"%d eiliad",m:"munud",mm:"%d munud",h:"awr",hh:"%d awr",d:"diwrnod",dd:"%d diwrnod",M:"mis",MM:"%d mis",y:"blwyddyn",yy:"%d flynedd"},dayOfMonthOrdinalParse:/\d{1,2}(fed|ain|af|il|ydd|ed|eg)/,ordinal:function(e){var t="";return 20<e?t=40===e||50===e||60===e||80===e||100===e?"fed":"ain":0<e&&(t=["","af","il","ydd","ydd","ed","ed","ed","fed","fed","fed","eg","fed","eg","eg","fed","eg","eg","fed","eg","fed"][e]),e+t},week:{dow:1,doy:4}}),n.defineLocale("da",{months:"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"søn_man_tir_ons_tor_fre_lør".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd [d.] D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"på dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[i] dddd[s kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"få sekunder",ss:"%d sekunder",m:"et minut",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dage",M:"en måned",MM:"%d måneder",y:"et år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("de-at",{months:"Jänner_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jän._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:sa,mm:"%d Minuten",h:sa,hh:"%d Stunden",d:sa,dd:sa,w:sa,ww:"%d Wochen",M:sa,MM:sa,y:sa,yy:sa},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("de-ch",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:ra,mm:"%d Minuten",h:ra,hh:"%d Stunden",d:ra,dd:ra,w:ra,ww:"%d Wochen",M:ra,MM:ra,y:ra,yy:ra},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("de",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:ia,mm:"%d Minuten",h:ia,hh:"%d Stunden",d:ia,dd:ia,w:ia,ww:"%d Wochen",M:ia,MM:ia,y:ia,yy:ia},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),Q=["ޖެނުއަރީ","ފެބްރުއަރީ","މާރިޗު","އޭޕްރީލު","މޭ","ޖޫން","ޖުލައި","އޯގަސްޓު","ސެޕްޓެމްބަރު","އޮކްޓޯބަރު","ނޮވެމްބަރު","ޑިސެމްބަރު"],q=["އާދިއްތަ","ހޯމަ","އަންގާރަ","ބުދަ","ބުރާސްފަތި","ހުކުރު","ހޮނިހިރު"],n.defineLocale("dv",{months:Q,monthsShort:Q,weekdays:q,weekdaysShort:q,weekdaysMin:"އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/M/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/\u0789\u0786|\u0789\u078a/,isPM:function(e){return"މފ"===e},meridiem:function(e,t,n){return e<12?"މކ":"މފ"},calendar:{sameDay:"[މިއަދު] LT",nextDay:"[މާދަމާ] LT",nextWeek:"dddd LT",lastDay:"[އިއްޔެ] LT",lastWeek:"[ފާއިތުވި] dddd LT",sameElse:"L"},relativeTime:{future:"ތެރޭގައި %s",past:"ކުރިން %s",s:"ސިކުންތުކޮޅެއް",ss:"d% ސިކުންތު",m:"މިނިޓެއް",mm:"މިނިޓު %d",h:"ގަޑިއިރެއް",hh:"ގަޑިއިރު %d",d:"ދުވަހެއް",dd:"ދުވަސް %d",M:"މަހެއް",MM:"މަސް %d",y:"އަހަރެއް",yy:"އަހަރު %d"},preparse:function(e){return e.replace(/\u060c/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:7,doy:12}}),n.defineLocale("el",{monthsNominativeEl:"Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος".split("_"),monthsGenitiveEl:"Ιανουαρίου_Φεβρουαρίου_Μαρτίου_Απριλίου_Μαΐου_Ιουνίου_Ιουλίου_Αυγούστου_Σεπτεμβρίου_Οκτωβρίου_Νοεμβρίου_Δεκεμβρίου".split("_"),months:function(e,t){return e?("string"==typeof t&&/D/.test(t.substring(0,t.indexOf("MMMM")))?this._monthsGenitiveEl:this._monthsNominativeEl)[e.month()]:this._monthsNominativeEl},monthsShort:"Ιαν_Φεβ_Μαρ_Απρ_Μαϊ_Ιουν_Ιουλ_Αυγ_Σεπ_Οκτ_Νοε_Δεκ".split("_"),weekdays:"Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο".split("_"),weekdaysShort:"Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ".split("_"),weekdaysMin:"Κυ_Δε_Τρ_Τε_Πε_Πα_Σα".split("_"),meridiem:function(e,t,n){return 11<e?n?"μμ":"ΜΜ":n?"πμ":"ΠΜ"},isPM:function(e){return"μ"===(e+"").toLowerCase()[0]},meridiemParse:/[\u03a0\u039c]\.?\u039c?\.?/i,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendarEl:{sameDay:"[Σήμερα {}] LT",nextDay:"[Αύριο {}] LT",nextWeek:"dddd [{}] LT",lastDay:"[Χθες {}] LT",lastWeek:function(){return 6===this.day()?"[το προηγούμενο] dddd [{}] LT":"[την προηγούμενη] dddd [{}] LT"},sameElse:"L"},calendar:function(e,t){e=this._calendarEl[e];var n,a=t&&t.hours();return n=e,(e="undefined"!=typeof Function&&n instanceof Function||"[object Function]"===Object.prototype.toString.call(n)?e.apply(t):e).replace("{}",a%12==1?"στη":"στις")},relativeTime:{future:"σε %s",past:"%s πριν",s:"λίγα δευτερόλεπτα",ss:"%d δευτερόλεπτα",m:"ένα λεπτό",mm:"%d λεπτά",h:"μία ώρα",hh:"%d ώρες",d:"μία μέρα",dd:"%d μέρες",M:"ένας μήνας",MM:"%d μήνες",y:"ένας χρόνος",yy:"%d χρόνια"},dayOfMonthOrdinalParse:/\d{1,2}\u03b7/,ordinal:"%dη",week:{dow:1,doy:4}}),n.defineLocale("en-au",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")},week:{dow:0,doy:4}}),n.defineLocale("en-ca",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"YYYY-MM-DD",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),n.defineLocale("en-gb",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")},week:{dow:1,doy:4}}),n.defineLocale("en-ie",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")},week:{dow:1,doy:4}}),n.defineLocale("en-il",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),n.defineLocale("en-in",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")},week:{dow:0,doy:6}}),n.defineLocale("en-nz",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")},week:{dow:1,doy:4}}),n.defineLocale("en-sg",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")},week:{dow:1,doy:4}}),n.defineLocale("eo",{months:"januaro_februaro_marto_aprilo_majo_junio_julio_aŭgusto_septembro_oktobro_novembro_decembro".split("_"),monthsShort:"jan_feb_mart_apr_maj_jun_jul_aŭg_sept_okt_nov_dec".split("_"),weekdays:"dimanĉo_lundo_mardo_merkredo_ĵaŭdo_vendredo_sabato".split("_"),weekdaysShort:"dim_lun_mard_merk_ĵaŭ_ven_sab".split("_"),weekdaysMin:"di_lu_ma_me_ĵa_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"[la] D[-an de] MMMM, YYYY",LLL:"[la] D[-an de] MMMM, YYYY HH:mm",LLLL:"dddd[n], [la] D[-an de] MMMM, YYYY HH:mm",llll:"ddd, [la] D[-an de] MMM, YYYY HH:mm"},meridiemParse:/[ap]\.t\.m/i,isPM:function(e){return"p"===e.charAt(0).toLowerCase()},meridiem:function(e,t,n){return 11<e?n?"p.t.m.":"P.T.M.":n?"a.t.m.":"A.T.M."},calendar:{sameDay:"[Hodiaŭ je] LT",nextDay:"[Morgaŭ je] LT",nextWeek:"dddd[n je] LT",lastDay:"[Hieraŭ je] LT",lastWeek:"[pasintan] dddd[n je] LT",sameElse:"L"},relativeTime:{future:"post %s",past:"antaŭ %s",s:"kelkaj sekundoj",ss:"%d sekundoj",m:"unu minuto",mm:"%d minutoj",h:"unu horo",hh:"%d horoj",d:"unu tago",dd:"%d tagoj",M:"unu monato",MM:"%d monatoj",y:"unu jaro",yy:"%d jaroj"},dayOfMonthOrdinalParse:/\d{1,2}a/,ordinal:"%da",week:{dow:1,doy:7}});var oa="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),da="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),ua=(Z=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],Te=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,n.defineLocale("es-do",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,t){return e?(/-MMM-/.test(t)?da:oa)[e.month()]:oa},monthsRegex:Te,monthsShortRegex:Te,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:Z,longMonthsParse:Z,shortMonthsParse:Z,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:1,doy:4}}),"ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_")),la="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),ca=(re=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],vn=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,n.defineLocale("es-mx",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,t){return e?(/-MMM-/.test(t)?la:ua)[e.month()]:ua},monthsRegex:vn,monthsShortRegex:vn,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:re,longMonthsParse:re,shortMonthsParse:re,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:0,doy:4},invalidDate:"Fecha inválida"}),"ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_")),_a="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),ma=(wn=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i),ha=(n.defineLocale("es-us",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,t){return e?(/-MMM-/.test(t)?_a:ca)[e.month()]:ca},monthsRegex:ma,monthsShortRegex:ma,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:wn,longMonthsParse:wn,shortMonthsParse:wn,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"MM/DD/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:0,doy:6}}),"ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_")),pa="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_");function fa(e,t,n,a){return e={s:["mõne sekundi","mõni sekund","paar sekundit"],ss:[e+"sekundi",e+"sekundit"],m:["ühe minuti","üks minut"],mm:[e+" minuti",e+" minutit"],h:["ühe tunni","tund aega","üks tund"],hh:[e+" tunni",e+" tundi"],d:["ühe päeva","üks päev"],M:["kuu aja","kuu aega","üks kuu"],MM:[e+" kuu",e+" kuud"],y:["ühe aasta","aasta","üks aasta"],yy:[e+" aasta",e+" aastat"]},t?e[n][2]||e[n][1]:a?e[n][0]:e[n][1]}Ve=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],ee=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,n.defineLocale("es",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,t){return e?(/-MMM-/.test(t)?pa:ha)[e.month()]:ha},monthsRegex:ee,monthsShortRegex:ee,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:Ve,longMonthsParse:Ve,shortMonthsParse:Ve,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:1,doy:4},invalidDate:"Fecha inválida"}),n.defineLocale("et",{months:"jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember".split("_"),monthsShort:"jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets".split("_"),weekdays:"pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev".split("_"),weekdaysShort:"P_E_T_K_N_R_L".split("_"),weekdaysMin:"P_E_T_K_N_R_L".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[Täna,] LT",nextDay:"[Homme,] LT",nextWeek:"[Järgmine] dddd LT",lastDay:"[Eile,] LT",lastWeek:"[Eelmine] dddd LT",sameElse:"L"},relativeTime:{future:"%s pärast",past:"%s tagasi",s:fa,ss:fa,m:fa,mm:fa,h:fa,hh:fa,d:fa,dd:"%d päeva",M:fa,MM:fa,y:fa,yy:fa},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("eu",{months:"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua".split("_"),monthsShort:"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.".split("_"),monthsParseExact:!0,weekdays:"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata".split("_"),weekdaysShort:"ig._al._ar._az._og._ol._lr.".split("_"),weekdaysMin:"ig_al_ar_az_og_ol_lr".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY[ko] MMMM[ren] D[a]",LLL:"YYYY[ko] MMMM[ren] D[a] HH:mm",LLLL:"dddd, YYYY[ko] MMMM[ren] D[a] HH:mm",l:"YYYY-M-D",ll:"YYYY[ko] MMM D[a]",lll:"YYYY[ko] MMM D[a] HH:mm",llll:"ddd, YYYY[ko] MMM D[a] HH:mm"},calendar:{sameDay:"[gaur] LT[etan]",nextDay:"[bihar] LT[etan]",nextWeek:"dddd LT[etan]",lastDay:"[atzo] LT[etan]",lastWeek:"[aurreko] dddd LT[etan]",sameElse:"L"},relativeTime:{future:"%s barru",past:"duela %s",s:"segundo batzuk",ss:"%d segundo",m:"minutu bat",mm:"%d minutu",h:"ordu bat",hh:"%d ordu",d:"egun bat",dd:"%d egun",M:"hilabete bat",MM:"%d hilabete",y:"urte bat",yy:"%d urte"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});var ya={1:"۱",2:"۲",3:"۳",4:"۴",5:"۵",6:"۶",7:"۷",8:"۸",9:"۹",0:"۰"},Ma={"۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","۰":"0"},ga=(n.defineLocale("fa",{months:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),monthsShort:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),weekdays:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysShort:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysMin:"ی_د_س_چ_پ_ج_ش".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/\u0642\u0628\u0644 \u0627\u0632 \u0638\u0647\u0631|\u0628\u0639\u062f \u0627\u0632 \u0638\u0647\u0631/,isPM:function(e){return/\u0628\u0639\u062f \u0627\u0632 \u0638\u0647\u0631/.test(e)},meridiem:function(e,t,n){return e<12?"قبل از ظهر":"بعد از ظهر"},calendar:{sameDay:"[امروز ساعت] LT",nextDay:"[فردا ساعت] LT",nextWeek:"dddd [ساعت] LT",lastDay:"[دیروز ساعت] LT",lastWeek:"dddd [پیش] [ساعت] LT",sameElse:"L"},relativeTime:{future:"در %s",past:"%s پیش",s:"چند ثانیه",ss:"%d ثانیه",m:"یک دقیقه",mm:"%d دقیقه",h:"یک ساعت",hh:"%d ساعت",d:"یک روز",dd:"%d روز",M:"یک ماه",MM:"%d ماه",y:"یک سال",yy:"%d سال"},preparse:function(e){return e.replace(/[\u06f0-\u06f9]/g,(function(e){return Ma[e]})).replace(/\u060c/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return ya[e]})).replace(/,/g,"،")},dayOfMonthOrdinalParse:/\d{1,2}\u0645/,ordinal:"%dم",week:{dow:6,doy:12}}),"nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän".split(" ")),La=["nolla","yhden","kahden","kolmen","neljän","viiden","kuuden",ga[7],ga[8],ga[9]];function Ya(e,t,n,a){var s="";switch(n){case"s":return a?"muutaman sekunnin":"muutama sekunti";case"ss":s=a?"sekunnin":"sekuntia";break;case"m":return a?"minuutin":"minuutti";case"mm":s=a?"minuutin":"minuuttia";break;case"h":return a?"tunnin":"tunti";case"hh":s=a?"tunnin":"tuntia";break;case"d":return a?"päivän":"päivä";case"dd":s=a?"päivän":"päivää";break;case"M":return a?"kuukauden":"kuukausi";case"MM":s=a?"kuukauden":"kuukautta";break;case"y":return a?"vuoden":"vuosi";case"yy":s=a?"vuoden":"vuotta"}return n=a,(e<10?(n?La:ga)[e]:e)+" "+s}n.defineLocale("fi",{months:"tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu".split("_"),monthsShort:"tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu".split("_"),weekdays:"sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai".split("_"),weekdaysShort:"su_ma_ti_ke_to_pe_la".split("_"),weekdaysMin:"su_ma_ti_ke_to_pe_la".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"Do MMMM[ta] YYYY",LLL:"Do MMMM[ta] YYYY, [klo] HH.mm",LLLL:"dddd, Do MMMM[ta] YYYY, [klo] HH.mm",l:"D.M.YYYY",ll:"Do MMM YYYY",lll:"Do MMM YYYY, [klo] HH.mm",llll:"ddd, Do MMM YYYY, [klo] HH.mm"},calendar:{sameDay:"[tänään] [klo] LT",nextDay:"[huomenna] [klo] LT",nextWeek:"dddd [klo] LT",lastDay:"[eilen] [klo] LT",lastWeek:"[viime] dddd[na] [klo] LT",sameElse:"L"},relativeTime:{future:"%s päästä",past:"%s sitten",s:Ya,ss:Ya,m:Ya,mm:Ya,h:Ya,hh:Ya,d:Ya,dd:Ya,M:Ya,MM:Ya,y:Ya,yy:Ya},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("fil",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY HH:mm",LLLL:"dddd, MMMM DD, YYYY HH:mm"},calendar:{sameDay:"LT [ngayong araw]",nextDay:"[Bukas ng] LT",nextWeek:"LT [sa susunod na] dddd",lastDay:"LT [kahapon]",lastWeek:"LT [noong nakaraang] dddd",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",ss:"%d segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}}),n.defineLocale("fo",{months:"januar_februar_mars_apríl_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sunnudagur_mánadagur_týsdagur_mikudagur_hósdagur_fríggjadagur_leygardagur".split("_"),weekdaysShort:"sun_mán_týs_mik_hós_frí_ley".split("_"),weekdaysMin:"su_má_tý_mi_hó_fr_le".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D. MMMM, YYYY HH:mm"},calendar:{sameDay:"[Í dag kl.] LT",nextDay:"[Í morgin kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[Í gjár kl.] LT",lastWeek:"[síðstu] dddd [kl] LT",sameElse:"L"},relativeTime:{future:"um %s",past:"%s síðani",s:"fá sekund",ss:"%d sekundir",m:"ein minuttur",mm:"%d minuttir",h:"ein tími",hh:"%d tímar",d:"ein dagur",dd:"%d dagar",M:"ein mánaður",MM:"%d mánaðir",y:"eitt ár",yy:"%d ár"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("fr-ca",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(e,t){switch(t){default:case"M":case"Q":case"D":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}}}),n.defineLocale("fr-ch",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(e,t){switch(t){default:case"M":case"Q":case"D":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}}),se=/(janv\.?|f\xe9vr\.?|mars|avr\.?|mai|juin|juil\.?|ao\xfbt|sept\.?|oct\.?|nov\.?|d\xe9c\.?|janvier|f\xe9vrier|mars|avril|mai|juin|juillet|ao\xfbt|septembre|octobre|novembre|d\xe9cembre)/i,I=[/^janv/i,/^f\xe9vr/i,/^mars/i,/^avr/i,/^mai/i,/^juin/i,/^juil/i,/^ao\xfbt/i,/^sept/i,/^oct/i,/^nov/i,/^d\xe9c/i];var va=(n.defineLocale("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsRegex:se,monthsShortRegex:se,monthsStrictRegex:/^(janvier|f\xe9vrier|mars|avril|mai|juin|juillet|ao\xfbt|septembre|octobre|novembre|d\xe9cembre)/i,monthsShortStrictRegex:/(janv\.?|f\xe9vr\.?|mars|avr\.?|mai|juin|juil\.?|ao\xfbt|sept\.?|oct\.?|nov\.?|d\xe9c\.?)/i,monthsParse:I,longMonthsParse:I,shortMonthsParse:I,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",w:"une semaine",ww:"%d semaines",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,t){switch(t){case"D":return e+(1===e?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}}),"jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.".split("_")),wa="jan_feb_mrt_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_");function ba(e,t,n,a){return e={s:["थोडया सॅकंडांनी","थोडे सॅकंड"],ss:[e+" सॅकंडांनी",e+" सॅकंड"],m:["एका मिणटान","एक मिनूट"],mm:[e+" मिणटांनी",e+" मिणटां"],h:["एका वरान","एक वर"],hh:[e+" वरांनी",e+" वरां"],d:["एका दिसान","एक दीस"],dd:[e+" दिसांनी",e+" दीस"],M:["एका म्हयन्यान","एक म्हयनो"],MM:[e+" म्हयन्यानी",e+" म्हयने"],y:["एका वर्सान","एक वर्स"],yy:[e+" वर्सांनी",e+" वर्सां"]},a?e[n][0]:e[n][1]}function ka(e,t,n,a){return e={s:["thoddea sekondamni","thodde sekond"],ss:[e+" sekondamni",e+" sekond"],m:["eka mintan","ek minut"],mm:[e+" mintamni",e+" mintam"],h:["eka voran","ek vor"],hh:[e+" voramni",e+" voram"],d:["eka disan","ek dis"],dd:[e+" disamni",e+" dis"],M:["eka mhoinean","ek mhoino"],MM:[e+" mhoineamni",e+" mhoine"],y:["eka vorsan","ek voros"],yy:[e+" vorsamni",e+" vorsam"]},a?e[n][0]:e[n][1]}n.defineLocale("fy",{months:"jannewaris_febrewaris_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber".split("_"),monthsShort:function(e,t){return e?(/-MMM-/.test(t)?wa:va)[e.month()]:va},monthsParseExact:!0,weekdays:"snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon".split("_"),weekdaysShort:"si._mo._ti._wo._to._fr._so.".split("_"),weekdaysMin:"Si_Mo_Ti_Wo_To_Fr_So".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[hjoed om] LT",nextDay:"[moarn om] LT",nextWeek:"dddd [om] LT",lastDay:"[juster om] LT",lastWeek:"[ôfrûne] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oer %s",past:"%s lyn",s:"in pear sekonden",ss:"%d sekonden",m:"ien minút",mm:"%d minuten",h:"ien oere",hh:"%d oeren",d:"ien dei",dd:"%d dagen",M:"ien moanne",MM:"%d moannen",y:"ien jier",yy:"%d jierren"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||20<=e?"ste":"de")},week:{dow:1,doy:4}}),n.defineLocale("ga",{months:["Eanáir","Feabhra","Márta","Aibreán","Bealtaine","Meitheamh","Iúil","Lúnasa","Meán Fómhair","Deireadh Fómhair","Samhain","Nollaig"],monthsShort:["Ean","Feabh","Márt","Aib","Beal","Meith","Iúil","Lún","M.F.","D.F.","Samh","Noll"],monthsParseExact:!0,weekdays:["Dé Domhnaigh","Dé Luain","Dé Máirt","Dé Céadaoin","Déardaoin","Dé hAoine","Dé Sathairn"],weekdaysShort:["Domh","Luan","Máirt","Céad","Déar","Aoine","Sath"],weekdaysMin:["Do","Lu","Má","Cé","Dé","A","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Inniu ag] LT",nextDay:"[Amárach ag] LT",nextWeek:"dddd [ag] LT",lastDay:"[Inné ag] LT",lastWeek:"dddd [seo caite] [ag] LT",sameElse:"L"},relativeTime:{future:"i %s",past:"%s ó shin",s:"cúpla soicind",ss:"%d soicind",m:"nóiméad",mm:"%d nóiméad",h:"uair an chloig",hh:"%d uair an chloig",d:"lá",dd:"%d lá",M:"mí",MM:"%d míonna",y:"bliain",yy:"%d bliain"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(e){return e+(1===e?"d":e%10==2?"na":"mh")},week:{dow:1,doy:4}}),n.defineLocale("gd",{months:["Am Faoilleach","An Gearran","Am Màrt","An Giblean","An Cèitean","An t-Ògmhios","An t-Iuchar","An Lùnastal","An t-Sultain","An Dàmhair","An t-Samhain","An Dùbhlachd"],monthsShort:["Faoi","Gear","Màrt","Gibl","Cèit","Ògmh","Iuch","Lùn","Sult","Dàmh","Samh","Dùbh"],monthsParseExact:!0,weekdays:["Didòmhnaich","Diluain","Dimàirt","Diciadain","Diardaoin","Dihaoine","Disathairne"],weekdaysShort:["Did","Dil","Dim","Dic","Dia","Dih","Dis"],weekdaysMin:["Dò","Lu","Mà","Ci","Ar","Ha","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[An-diugh aig] LT",nextDay:"[A-màireach aig] LT",nextWeek:"dddd [aig] LT",lastDay:"[An-dè aig] LT",lastWeek:"dddd [seo chaidh] [aig] LT",sameElse:"L"},relativeTime:{future:"ann an %s",past:"bho chionn %s",s:"beagan diogan",ss:"%d diogan",m:"mionaid",mm:"%d mionaidean",h:"uair",hh:"%d uairean",d:"latha",dd:"%d latha",M:"mìos",MM:"%d mìosan",y:"bliadhna",yy:"%d bliadhna"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(e){return e+(1===e?"d":e%10==2?"na":"mh")},week:{dow:1,doy:4}}),n.defineLocale("gl",{months:"xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro".split("_"),monthsShort:"xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"domingo_luns_martes_mércores_xoves_venres_sábado".split("_"),weekdaysShort:"dom._lun._mar._mér._xov._ven._sáb.".split("_"),weekdaysMin:"do_lu_ma_mé_xo_ve_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoxe "+(1!==this.hours()?"ás":"á")+"] LT"},nextDay:function(){return"[mañá "+(1!==this.hours()?"ás":"á")+"] LT"},nextWeek:function(){return"dddd ["+(1!==this.hours()?"ás":"a")+"] LT"},lastDay:function(){return"[onte "+(1!==this.hours()?"á":"a")+"] LT"},lastWeek:function(){return"[o] dddd [pasado "+(1!==this.hours()?"ás":"a")+"] LT"},sameElse:"L"},relativeTime:{future:function(e){return 0===e.indexOf("un")?"n"+e:"en "+e},past:"hai %s",s:"uns segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"unha hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:1,doy:4}}),n.defineLocale("gom-deva",{months:{standalone:"जानेवारी_फेब्रुवारी_मार्च_एप्रील_मे_जून_जुलय_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर".split("_"),format:"जानेवारीच्या_फेब्रुवारीच्या_मार्चाच्या_एप्रीलाच्या_मेयाच्या_जूनाच्या_जुलयाच्या_ऑगस्टाच्या_सप्टेंबराच्या_ऑक्टोबराच्या_नोव्हेंबराच्या_डिसेंबराच्या".split("_"),isFormat:/MMMM(\s)+D[oD]?/},monthsShort:"जाने._फेब्रु._मार्च_एप्री._मे_जून_जुल._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.".split("_"),monthsParseExact:!0,weekdays:"आयतार_सोमार_मंगळार_बुधवार_बिरेस्तार_सुक्रार_शेनवार".split("_"),weekdaysShort:"आयत._सोम._मंगळ._बुध._ब्रेस्त._सुक्र._शेन.".split("_"),weekdaysMin:"आ_सो_मं_बु_ब्रे_सु_शे".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"A h:mm [वाजतां]",LTS:"A h:mm:ss [वाजतां]",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY A h:mm [वाजतां]",LLLL:"dddd, MMMM Do, YYYY, A h:mm [वाजतां]",llll:"ddd, D MMM YYYY, A h:mm [वाजतां]"},calendar:{sameDay:"[आयज] LT",nextDay:"[फाल्यां] LT",nextWeek:"[फुडलो] dddd[,] LT",lastDay:"[काल] LT",lastWeek:"[फाटलो] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%s",past:"%s आदीं",s:ba,ss:ba,m:ba,mm:ba,h:ba,hh:ba,d:ba,dd:ba,M:ba,MM:ba,y:ba,yy:ba},dayOfMonthOrdinalParse:/\d{1,2}(\u0935\u0947\u0930)/,ordinal:function(e,t){return"D"===t?e+"वेर":e},week:{dow:0,doy:3},meridiemParse:/\u0930\u093e\u0924\u0940|\u0938\u0915\u093e\u0933\u0940\u0902|\u0926\u0928\u092a\u093e\u0930\u093e\u0902|\u0938\u093e\u0902\u091c\u0947/,meridiemHour:function(e,t){return 12===e&&(e=0),"राती"===t?e<4?e:e+12:"सकाळीं"===t?e:"दनपारां"===t?12<e?e:e+12:"सांजे"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"राती":e<12?"सकाळीं":e<16?"दनपारां":e<20?"सांजे":"राती"}}),n.defineLocale("gom-latn",{months:{standalone:"Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr".split("_"),format:"Janerachea_Febrerachea_Marsachea_Abrilachea_Maiachea_Junachea_Julaiachea_Agostachea_Setembrachea_Otubrachea_Novembrachea_Dezembrachea".split("_"),isFormat:/MMMM(\s)+D[oD]?/},monthsShort:"Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Aitar_Somar_Mongllar_Budhvar_Birestar_Sukrar_Son'var".split("_"),weekdaysShort:"Ait._Som._Mon._Bud._Bre._Suk._Son.".split("_"),weekdaysMin:"Ai_Sm_Mo_Bu_Br_Su_Sn".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"A h:mm [vazta]",LTS:"A h:mm:ss [vazta]",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY A h:mm [vazta]",LLLL:"dddd, MMMM Do, YYYY, A h:mm [vazta]",llll:"ddd, D MMM YYYY, A h:mm [vazta]"},calendar:{sameDay:"[Aiz] LT",nextDay:"[Faleam] LT",nextWeek:"[Fuddlo] dddd[,] LT",lastDay:"[Kal] LT",lastWeek:"[Fattlo] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%s",past:"%s adim",s:ka,ss:ka,m:ka,mm:ka,h:ka,hh:ka,d:ka,dd:ka,M:ka,MM:ka,y:ka,yy:ka},dayOfMonthOrdinalParse:/\d{1,2}(er)/,ordinal:function(e,t){return"D"===t?e+"er":e},week:{dow:0,doy:3},meridiemParse:/rati|sokallim|donparam|sanje/,meridiemHour:function(e,t){return 12===e&&(e=0),"rati"===t?e<4?e:e+12:"sokallim"===t?e:"donparam"===t?12<e?e:e+12:"sanje"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"rati":e<12?"sokallim":e<16?"donparam":e<20?"sanje":"rati"}});var Da={1:"૧",2:"૨",3:"૩",4:"૪",5:"૫",6:"૬",7:"૭",8:"૮",9:"૯",0:"૦"},Ta={"૧":"1","૨":"2","૩":"3","૪":"4","૫":"5","૬":"6","૭":"7","૮":"8","૯":"9","૦":"0"},Sa=(n.defineLocale("gu",{months:"જાન્યુઆરી_ફેબ્રુઆરી_માર્ચ_એપ્રિલ_મે_જૂન_જુલાઈ_ઑગસ્ટ_સપ્ટેમ્બર_ઑક્ટ્બર_નવેમ્બર_ડિસેમ્બર".split("_"),monthsShort:"જાન્યુ._ફેબ્રુ._માર્ચ_એપ્રિ._મે_જૂન_જુલા._ઑગ._સપ્ટે._ઑક્ટ્._નવે._ડિસે.".split("_"),monthsParseExact:!0,weekdays:"રવિવાર_સોમવાર_મંગળવાર_બુધ્વાર_ગુરુવાર_શુક્રવાર_શનિવાર".split("_"),weekdaysShort:"રવિ_સોમ_મંગળ_બુધ્_ગુરુ_શુક્ર_શનિ".split("_"),weekdaysMin:"ર_સો_મં_બુ_ગુ_શુ_શ".split("_"),longDateFormat:{LT:"A h:mm વાગ્યે",LTS:"A h:mm:ss વાગ્યે",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm વાગ્યે",LLLL:"dddd, D MMMM YYYY, A h:mm વાગ્યે"},calendar:{sameDay:"[આજ] LT",nextDay:"[કાલે] LT",nextWeek:"dddd, LT",lastDay:"[ગઇકાલે] LT",lastWeek:"[પાછલા] dddd, LT",sameElse:"L"},relativeTime:{future:"%s મા",past:"%s પહેલા",s:"અમુક પળો",ss:"%d સેકંડ",m:"એક મિનિટ",mm:"%d મિનિટ",h:"એક કલાક",hh:"%d કલાક",d:"એક દિવસ",dd:"%d દિવસ",M:"એક મહિનો",MM:"%d મહિનો",y:"એક વર્ષ",yy:"%d વર્ષ"},preparse:function(e){return e.replace(/[\u0ae7\u0ae8\u0ae9\u0aea\u0aeb\u0aec\u0aed\u0aee\u0aef\u0ae6]/g,(function(e){return Ta[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return Da[e]}))},meridiemParse:/\u0ab0\u0abe\u0aa4|\u0aac\u0aaa\u0acb\u0ab0|\u0ab8\u0ab5\u0abe\u0ab0|\u0ab8\u0abe\u0a82\u0a9c/,meridiemHour:function(e,t){return 12===e&&(e=0),"રાત"===t?e<4?e:e+12:"સવાર"===t?e:"બપોર"===t?10<=e?e:e+12:"સાંજ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"રાત":e<10?"સવાર":e<17?"બપોર":e<20?"સાંજ":"રાત"},week:{dow:0,doy:6}}),n.defineLocale("he",{months:"ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר".split("_"),monthsShort:"ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳".split("_"),weekdays:"ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת".split("_"),weekdaysShort:"א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳".split("_"),weekdaysMin:"א_ב_ג_ד_ה_ו_ש".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [ב]MMMM YYYY",LLL:"D [ב]MMMM YYYY HH:mm",LLLL:"dddd, D [ב]MMMM YYYY HH:mm",l:"D/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[היום ב־]LT",nextDay:"[מחר ב־]LT",nextWeek:"dddd [בשעה] LT",lastDay:"[אתמול ב־]LT",lastWeek:"[ביום] dddd [האחרון בשעה] LT",sameElse:"L"},relativeTime:{future:"בעוד %s",past:"לפני %s",s:"מספר שניות",ss:"%d שניות",m:"דקה",mm:"%d דקות",h:"שעה",hh:function(e){return 2===e?"שעתיים":e+" שעות"},d:"יום",dd:function(e){return 2===e?"יומיים":e+" ימים"},M:"חודש",MM:function(e){return 2===e?"חודשיים":e+" חודשים"},y:"שנה",yy:function(e){return 2===e?"שנתיים":e%10==0&&10!==e?e+" שנה":e+" שנים"}},meridiemParse:/\u05d0\u05d7\u05d4"\u05e6|\u05dc\u05e4\u05e0\u05d4"\u05e6|\u05d0\u05d7\u05e8\u05d9 \u05d4\u05e6\u05d4\u05e8\u05d9\u05d9\u05dd|\u05dc\u05e4\u05e0\u05d9 \u05d4\u05e6\u05d4\u05e8\u05d9\u05d9\u05dd|\u05dc\u05e4\u05e0\u05d5\u05ea \u05d1\u05d5\u05e7\u05e8|\u05d1\u05d1\u05d5\u05e7\u05e8|\u05d1\u05e2\u05e8\u05d1/i,isPM:function(e){return/^(\u05d0\u05d7\u05d4"\u05e6|\u05d0\u05d7\u05e8\u05d9 \u05d4\u05e6\u05d4\u05e8\u05d9\u05d9\u05dd|\u05d1\u05e2\u05e8\u05d1)$/.test(e)},meridiem:function(e,t,n){return e<5?"לפנות בוקר":e<10?"בבוקר":e<12?n?'לפנה"צ':"לפני הצהריים":e<18?n?'אחה"צ':"אחרי הצהריים":"בערב"}}),{1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"}),xa={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};function Ha(e,t,n){var a=e+" ";switch(n){case"ss":return a+(1===e?"sekunda":2===e||3===e||4===e?"sekunde":"sekundi");case"m":return t?"jedna minuta":"jedne minute";case"mm":return a+(1===e||2!==e&&3!==e&&4!==e?"minuta":"minute");case"h":return t?"jedan sat":"jednog sata";case"hh":return a+(1===e?"sat":2===e||3===e||4===e?"sata":"sati");case"dd":return a+(1===e?"dan":"dana");case"MM":return a+(1===e?"mjesec":2===e||3===e||4===e?"mjeseca":"mjeseci");case"yy":return a+(1===e||2!==e&&3!==e&&4!==e?"godina":"godine")}}R=[/^\u091c\u0928/i,/^\u092b\u093c\u0930|\u092b\u0930/i,/^\u092e\u093e\u0930\u094d\u091a/i,/^\u0905\u092a\u094d\u0930\u0948/i,/^\u092e\u0908/i,/^\u091c\u0942\u0928/i,/^\u091c\u0941\u0932/i,/^\u0905\u0917/i,/^\u0938\u093f\u0924\u0902|\u0938\u093f\u0924/i,/^\u0905\u0915\u094d\u091f\u0942/i,/^\u0928\u0935|\u0928\u0935\u0902/i,/^\u0926\u093f\u0938\u0902|\u0926\u093f\u0938/i],n.defineLocale("hi",{months:{format:"जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर".split("_"),standalone:"जनवरी_फरवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितंबर_अक्टूबर_नवंबर_दिसंबर".split("_")},monthsShort:"जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.".split("_"),weekdays:"रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm बजे",LTS:"A h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm बजे",LLLL:"dddd, D MMMM YYYY, A h:mm बजे"},monthsParse:R,longMonthsParse:R,shortMonthsParse:[/^\u091c\u0928/i,/^\u092b\u093c\u0930/i,/^\u092e\u093e\u0930\u094d\u091a/i,/^\u0905\u092a\u094d\u0930\u0948/i,/^\u092e\u0908/i,/^\u091c\u0942\u0928/i,/^\u091c\u0941\u0932/i,/^\u0905\u0917/i,/^\u0938\u093f\u0924/i,/^\u0905\u0915\u094d\u091f\u0942/i,/^\u0928\u0935/i,/^\u0926\u093f\u0938/i],monthsRegex:/^(\u091c\u0928\u0935\u0930\u0940|\u091c\u0928\.?|\u092b\u093c\u0930\u0935\u0930\u0940|\u092b\u0930\u0935\u0930\u0940|\u092b\u093c\u0930\.?|\u092e\u093e\u0930\u094d\u091a?|\u0905\u092a\u094d\u0930\u0948\u0932|\u0905\u092a\u094d\u0930\u0948\.?|\u092e\u0908?|\u091c\u0942\u0928?|\u091c\u0941\u0932\u093e\u0908|\u091c\u0941\u0932\.?|\u0905\u0917\u0938\u094d\u0924|\u0905\u0917\.?|\u0938\u093f\u0924\u092e\u094d\u092c\u0930|\u0938\u093f\u0924\u0902\u092c\u0930|\u0938\u093f\u0924\.?|\u0905\u0915\u094d\u091f\u0942\u092c\u0930|\u0905\u0915\u094d\u091f\u0942\.?|\u0928\u0935\u092e\u094d\u092c\u0930|\u0928\u0935\u0902\u092c\u0930|\u0928\u0935\.?|\u0926\u093f\u0938\u092e\u094d\u092c\u0930|\u0926\u093f\u0938\u0902\u092c\u0930|\u0926\u093f\u0938\.?)/i,monthsShortRegex:/^(\u091c\u0928\u0935\u0930\u0940|\u091c\u0928\.?|\u092b\u093c\u0930\u0935\u0930\u0940|\u092b\u0930\u0935\u0930\u0940|\u092b\u093c\u0930\.?|\u092e\u093e\u0930\u094d\u091a?|\u0905\u092a\u094d\u0930\u0948\u0932|\u0905\u092a\u094d\u0930\u0948\.?|\u092e\u0908?|\u091c\u0942\u0928?|\u091c\u0941\u0932\u093e\u0908|\u091c\u0941\u0932\.?|\u0905\u0917\u0938\u094d\u0924|\u0905\u0917\.?|\u0938\u093f\u0924\u092e\u094d\u092c\u0930|\u0938\u093f\u0924\u0902\u092c\u0930|\u0938\u093f\u0924\.?|\u0905\u0915\u094d\u091f\u0942\u092c\u0930|\u0905\u0915\u094d\u091f\u0942\.?|\u0928\u0935\u092e\u094d\u092c\u0930|\u0928\u0935\u0902\u092c\u0930|\u0928\u0935\.?|\u0926\u093f\u0938\u092e\u094d\u092c\u0930|\u0926\u093f\u0938\u0902\u092c\u0930|\u0926\u093f\u0938\.?)/i,monthsStrictRegex:/^(\u091c\u0928\u0935\u0930\u0940?|\u092b\u093c\u0930\u0935\u0930\u0940|\u092b\u0930\u0935\u0930\u0940?|\u092e\u093e\u0930\u094d\u091a?|\u0905\u092a\u094d\u0930\u0948\u0932?|\u092e\u0908?|\u091c\u0942\u0928?|\u091c\u0941\u0932\u093e\u0908?|\u0905\u0917\u0938\u094d\u0924?|\u0938\u093f\u0924\u092e\u094d\u092c\u0930|\u0938\u093f\u0924\u0902\u092c\u0930|\u0938\u093f\u0924?\.?|\u0905\u0915\u094d\u091f\u0942\u092c\u0930|\u0905\u0915\u094d\u091f\u0942\.?|\u0928\u0935\u092e\u094d\u092c\u0930|\u0928\u0935\u0902\u092c\u0930?|\u0926\u093f\u0938\u092e\u094d\u092c\u0930|\u0926\u093f\u0938\u0902\u092c\u0930?)/i,monthsShortStrictRegex:/^(\u091c\u0928\.?|\u092b\u093c\u0930\.?|\u092e\u093e\u0930\u094d\u091a?|\u0905\u092a\u094d\u0930\u0948\.?|\u092e\u0908?|\u091c\u0942\u0928?|\u091c\u0941\u0932\.?|\u0905\u0917\.?|\u0938\u093f\u0924\.?|\u0905\u0915\u094d\u091f\u0942\.?|\u0928\u0935\.?|\u0926\u093f\u0938\.?)/i,calendar:{sameDay:"[आज] LT",nextDay:"[कल] LT",nextWeek:"dddd, LT",lastDay:"[कल] LT",lastWeek:"[पिछले] dddd, LT",sameElse:"L"},relativeTime:{future:"%s में",past:"%s पहले",s:"कुछ ही क्षण",ss:"%d सेकंड",m:"एक मिनट",mm:"%d मिनट",h:"एक घंटा",hh:"%d घंटे",d:"एक दिन",dd:"%d दिन",M:"एक महीने",MM:"%d महीने",y:"एक वर्ष",yy:"%d वर्ष"},preparse:function(e){return e.replace(/[\u0967\u0968\u0969\u096a\u096b\u096c\u096d\u096e\u096f\u0966]/g,(function(e){return xa[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return Sa[e]}))},meridiemParse:/\u0930\u093e\u0924|\u0938\u0941\u092c\u0939|\u0926\u094b\u092a\u0939\u0930|\u0936\u093e\u092e/,meridiemHour:function(e,t){return 12===e&&(e=0),"रात"===t?e<4?e:e+12:"सुबह"===t?e:"दोपहर"===t?10<=e?e:e+12:"शाम"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"रात":e<10?"सुबह":e<17?"दोपहर":e<20?"शाम":"रात"},week:{dow:0,doy:6}}),n.defineLocale("hr",{months:{format:"siječnja_veljače_ožujka_travnja_svibnja_lipnja_srpnja_kolovoza_rujna_listopada_studenoga_prosinca".split("_"),standalone:"siječanj_veljača_ožujak_travanj_svibanj_lipanj_srpanj_kolovoz_rujan_listopad_studeni_prosinac".split("_")},monthsShort:"sij._velj._ožu._tra._svi._lip._srp._kol._ruj._lis._stu._pro.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"Do MMMM YYYY",LLL:"Do MMMM YYYY H:mm",LLLL:"dddd, Do MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:return"[prošlu] [nedjelju] [u] LT";case 3:return"[prošlu] [srijedu] [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:Ha,m:Ha,mm:Ha,h:Ha,hh:Ha,d:"dan",dd:Ha,M:"mjesec",MM:Ha,y:"godinu",yy:Ha},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});var ja="vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton".split(" ");function Oa(e,t,n,a){var s=e;switch(n){case"s":return a||t?"néhány másodperc":"néhány másodperce";case"ss":return s+(a||t)?" másodperc":" másodperce";case"m":return"egy"+(a||t?" perc":" perce");case"mm":return s+(a||t?" perc":" perce");case"h":return"egy"+(a||t?" óra":" órája");case"hh":return s+(a||t?" óra":" órája");case"d":return"egy"+(a||t?" nap":" napja");case"dd":return s+(a||t?" nap":" napja");case"M":return"egy"+(a||t?" hónap":" hónapja");case"MM":return s+(a||t?" hónap":" hónapja");case"y":return"egy"+(a||t?" év":" éve");case"yy":return s+(a||t?" év":" éve")}return""}function Pa(e){return(e?"":"[múlt] ")+"["+ja[this.day()]+"] LT[-kor]"}function Ea(e){return e%100==11||e%10!=1}function Aa(e,t,n,a){var s=e+" ";switch(n){case"s":return t||a?"nokkrar sekúndur":"nokkrum sekúndum";case"ss":return Ea(e)?s+(t||a?"sekúndur":"sekúndum"):s+"sekúnda";case"m":return t?"mínúta":"mínútu";case"mm":return Ea(e)?s+(t||a?"mínútur":"mínútum"):t?s+"mínúta":s+"mínútu";case"hh":return Ea(e)?s+(t||a?"klukkustundir":"klukkustundum"):s+"klukkustund";case"d":return t?"dagur":a?"dag":"degi";case"dd":return Ea(e)?t?s+"dagar":s+(a?"daga":"dögum"):t?s+"dagur":s+(a?"dag":"degi");case"M":return t?"mánuður":a?"mánuð":"mánuði";case"MM":return Ea(e)?t?s+"mánuðir":s+(a?"mánuði":"mánuðum"):t?s+"mánuður":s+(a?"mánuð":"mánuði");case"y":return t||a?"ár":"ári";case"yy":return Ea(e)?s+(t||a?"ár":"árum"):s+(t||a?"ár":"ári")}}n.defineLocale("hu",{months:"január_február_március_április_május_június_július_augusztus_szeptember_október_november_december".split("_"),monthsShort:"jan._feb._márc._ápr._máj._jún._júl._aug._szept._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat".split("_"),weekdaysShort:"vas_hét_kedd_sze_csüt_pén_szo".split("_"),weekdaysMin:"v_h_k_sze_cs_p_szo".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY. MMMM D.",LLL:"YYYY. MMMM D. H:mm",LLLL:"YYYY. MMMM D., dddd H:mm"},meridiemParse:/de|du/i,isPM:function(e){return"u"===e.charAt(1).toLowerCase()},meridiem:function(e,t,n){return e<12?!0===n?"de":"DE":!0===n?"du":"DU"},calendar:{sameDay:"[ma] LT[-kor]",nextDay:"[holnap] LT[-kor]",nextWeek:function(){return Pa.call(this,!0)},lastDay:"[tegnap] LT[-kor]",lastWeek:function(){return Pa.call(this,!1)},sameElse:"L"},relativeTime:{future:"%s múlva",past:"%s",s:Oa,ss:Oa,m:Oa,mm:Oa,h:Oa,hh:Oa,d:Oa,dd:Oa,M:Oa,MM:Oa,y:Oa,yy:Oa},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("hy-am",{months:{format:"հունվարի_փետրվարի_մարտի_ապրիլի_մայիսի_հունիսի_հուլիսի_օգոստոսի_սեպտեմբերի_հոկտեմբերի_նոյեմբերի_դեկտեմբերի".split("_"),standalone:"հունվար_փետրվար_մարտ_ապրիլ_մայիս_հունիս_հուլիս_օգոստոս_սեպտեմբեր_հոկտեմբեր_նոյեմբեր_դեկտեմբեր".split("_")},monthsShort:"հնվ_փտր_մրտ_ապր_մյս_հնս_հլս_օգս_սպտ_հկտ_նմբ_դկտ".split("_"),weekdays:"կիրակի_երկուշաբթի_երեքշաբթի_չորեքշաբթի_հինգշաբթի_ուրբաթ_շաբաթ".split("_"),weekdaysShort:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),weekdaysMin:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY թ.",LLL:"D MMMM YYYY թ., HH:mm",LLLL:"dddd, D MMMM YYYY թ., HH:mm"},calendar:{sameDay:"[այսօր] LT",nextDay:"[վաղը] LT",lastDay:"[երեկ] LT",nextWeek:function(){return"dddd [օրը ժամը] LT"},lastWeek:function(){return"[անցած] dddd [օրը ժամը] LT"},sameElse:"L"},relativeTime:{future:"%s հետո",past:"%s առաջ",s:"մի քանի վայրկյան",ss:"%d վայրկյան",m:"րոպե",mm:"%d րոպե",h:"ժամ",hh:"%d ժամ",d:"օր",dd:"%d օր",M:"ամիս",MM:"%d ամիս",y:"տարի",yy:"%d տարի"},meridiemParse:/\u0563\u056b\u0577\u0565\u0580\u057e\u0561|\u0561\u057c\u0561\u057e\u0578\u057f\u057e\u0561|\u0581\u0565\u0580\u0565\u056f\u057e\u0561|\u0565\u0580\u0565\u056f\u0578\u0575\u0561\u0576/,isPM:function(e){return/^(\u0581\u0565\u0580\u0565\u056f\u057e\u0561|\u0565\u0580\u0565\u056f\u0578\u0575\u0561\u0576)$/.test(e)},meridiem:function(e){return e<4?"գիշերվա":e<12?"առավոտվա":e<17?"ցերեկվա":"երեկոյան"},dayOfMonthOrdinalParse:/\d{1,2}|\d{1,2}-(\u056b\u0576|\u0580\u0564)/,ordinal:function(e,t){switch(t){case"DDD":case"w":case"W":case"DDDo":return 1===e?e+"-ին":e+"-րդ";default:return e}},week:{dow:1,doy:7}}),n.defineLocale("id",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Agt_Sep_Okt_Nov_Des".split("_"),weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|siang|sore|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"siang"===t?11<=e?e:e+12:"sore"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"pagi":e<15?"siang":e<19?"sore":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Besok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kemarin pukul] LT",lastWeek:"dddd [lalu pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",ss:"%d detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:0,doy:6}}),n.defineLocale("is",{months:"janúar_febrúar_mars_apríl_maí_júní_júlí_ágúst_september_október_nóvember_desember".split("_"),monthsShort:"jan_feb_mar_apr_maí_jún_júl_ágú_sep_okt_nóv_des".split("_"),weekdays:"sunnudagur_mánudagur_þriðjudagur_miðvikudagur_fimmtudagur_föstudagur_laugardagur".split("_"),weekdaysShort:"sun_mán_þri_mið_fim_fös_lau".split("_"),weekdaysMin:"Su_Má_Þr_Mi_Fi_Fö_La".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd, D. MMMM YYYY [kl.] H:mm"},calendar:{sameDay:"[í dag kl.] LT",nextDay:"[á morgun kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[í gær kl.] LT",lastWeek:"[síðasta] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"eftir %s",past:"fyrir %s síðan",s:Aa,ss:Aa,m:Aa,mm:Aa,h:"klukkustund",hh:Aa,d:Aa,dd:Aa,M:Aa,MM:Aa,y:Aa,yy:Aa},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("it-ch",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Oggi alle] LT",nextDay:"[Domani alle] LT",nextWeek:"dddd [alle] LT",lastDay:"[Ieri alle] LT",lastWeek:function(){return 0===this.day()?"[la scorsa] dddd [alle] LT":"[lo scorso] dddd [alle] LT"},sameElse:"L"},relativeTime:{future:function(e){return(/^[0-9].+$/.test(e)?"tra":"in")+" "+e},past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:1,doy:4}}),n.defineLocale("it",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:function(){return"[Oggi a"+(1<this.hours()?"lle ":0===this.hours()?" ":"ll'")+"]LT"},nextDay:function(){return"[Domani a"+(1<this.hours()?"lle ":0===this.hours()?" ":"ll'")+"]LT"},nextWeek:function(){return"dddd [a"+(1<this.hours()?"lle ":0===this.hours()?" ":"ll'")+"]LT"},lastDay:function(){return"[Ieri a"+(1<this.hours()?"lle ":0===this.hours()?" ":"ll'")+"]LT"},lastWeek:function(){return 0===this.day()?"[La scorsa] dddd [a"+(1<this.hours()?"lle ":0===this.hours()?" ":"ll'")+"]LT":"[Lo scorso] dddd [a"+(1<this.hours()?"lle ":0===this.hours()?" ":"ll'")+"]LT"},sameElse:"L"},relativeTime:{future:"tra %s",past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",w:"una settimana",ww:"%d settimane",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:1,doy:4}}),n.defineLocale("ja",{eras:[{since:"2019-05-01",offset:1,name:"令和",narrow:"㋿",abbr:"R"},{since:"1989-01-08",until:"2019-04-30",offset:1,name:"平成",narrow:"㍻",abbr:"H"},{since:"1926-12-25",until:"1989-01-07",offset:1,name:"昭和",narrow:"㍼",abbr:"S"},{since:"1912-07-30",until:"1926-12-24",offset:1,name:"大正",narrow:"㍽",abbr:"T"},{since:"1873-01-01",until:"1912-07-29",offset:6,name:"明治",narrow:"㍾",abbr:"M"},{since:"0001-01-01",until:"1873-12-31",offset:1,name:"西暦",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"紀元前",narrow:"BC",abbr:"BC"}],eraYearOrdinalRegex:/(\u5143|\d+)\u5e74/,eraYearOrdinalParse:function(e,t){return"元"===t[1]?1:parseInt(t[1]||e,10)},months:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日".split("_"),weekdaysShort:"日_月_火_水_木_金_土".split("_"),weekdaysMin:"日_月_火_水_木_金_土".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日 dddd HH:mm",l:"YYYY/MM/DD",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日(ddd) HH:mm"},meridiemParse:/\u5348\u524d|\u5348\u5f8c/i,isPM:function(e){return"午後"===e},meridiem:function(e,t,n){return e<12?"午前":"午後"},calendar:{sameDay:"[今日] LT",nextDay:"[明日] LT",nextWeek:function(e){return e.week()!==this.week()?"[来週]dddd LT":"dddd LT"},lastDay:"[昨日] LT",lastWeek:function(e){return this.week()!==e.week()?"[先週]dddd LT":"dddd LT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}\u65e5/,ordinal:function(e,t){switch(t){case"y":return 1===e?"元年":e+"年";case"d":case"D":case"DDD":return e+"日";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"数秒",ss:"%d秒",m:"1分",mm:"%d分",h:"1時間",hh:"%d時間",d:"1日",dd:"%d日",M:"1ヶ月",MM:"%dヶ月",y:"1年",yy:"%d年"}}),n.defineLocale("jv",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des".split("_"),weekdays:"Minggu_Senen_Seloso_Rebu_Kemis_Jemuwah_Septu".split("_"),weekdaysShort:"Min_Sen_Sel_Reb_Kem_Jem_Sep".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sp".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/enjing|siyang|sonten|ndalu/,meridiemHour:function(e,t){return 12===e&&(e=0),"enjing"===t?e:"siyang"===t?11<=e?e:e+12:"sonten"===t||"ndalu"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"enjing":e<15?"siyang":e<19?"sonten":"ndalu"},calendar:{sameDay:"[Dinten puniko pukul] LT",nextDay:"[Mbenjang pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kala wingi pukul] LT",lastWeek:"dddd [kepengker pukul] LT",sameElse:"L"},relativeTime:{future:"wonten ing %s",past:"%s ingkang kepengker",s:"sawetawis detik",ss:"%d detik",m:"setunggal menit",mm:"%d menit",h:"setunggal jam",hh:"%d jam",d:"sedinten",dd:"%d dinten",M:"sewulan",MM:"%d wulan",y:"setaun",yy:"%d taun"},week:{dow:1,doy:7}}),n.defineLocale("ka",{months:"იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი".split("_"),monthsShort:"იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ".split("_"),weekdays:{standalone:"კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი".split("_"),format:"კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს".split("_"),isFormat:/(\u10ec\u10d8\u10dc\u10d0|\u10e8\u10d4\u10db\u10d3\u10d4\u10d2)/},weekdaysShort:"კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ".split("_"),weekdaysMin:"კვ_ორ_სა_ოთ_ხუ_პა_შა".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[დღეს] LT[-ზე]",nextDay:"[ხვალ] LT[-ზე]",lastDay:"[გუშინ] LT[-ზე]",nextWeek:"[შემდეგ] dddd LT[-ზე]",lastWeek:"[წინა] dddd LT-ზე",sameElse:"L"},relativeTime:{future:function(e){return e.replace(/(\u10ec\u10d0\u10db|\u10ec\u10e3\u10d7|\u10e1\u10d0\u10d0\u10d7|\u10ec\u10d4\u10da|\u10d3\u10e6|\u10d7\u10d5)(\u10d8|\u10d4)/,(function(e,t,n){return"ი"===n?t+"ში":t+n+"ში"}))},past:function(e){return/(\u10ec\u10d0\u10db\u10d8|\u10ec\u10e3\u10d7\u10d8|\u10e1\u10d0\u10d0\u10d7\u10d8|\u10d3\u10e6\u10d4|\u10d7\u10d5\u10d4)/.test(e)?e.replace(/(\u10d8|\u10d4)$/,"ის წინ"):/\u10ec\u10d4\u10da\u10d8/.test(e)?e.replace(/\u10ec\u10d4\u10da\u10d8$/,"წლის წინ"):e},s:"რამდენიმე წამი",ss:"%d წამი",m:"წუთი",mm:"%d წუთი",h:"საათი",hh:"%d საათი",d:"დღე",dd:"%d დღე",M:"თვე",MM:"%d თვე",y:"წელი",yy:"%d წელი"},dayOfMonthOrdinalParse:/0|1-\u10da\u10d8|\u10db\u10d4-\d{1,2}|\d{1,2}-\u10d4/,ordinal:function(e){return 0===e?e:1===e?e+"-ლი":e<20||e<=100&&e%20==0||e%100==0?"მე-"+e:e+"-ე"},week:{dow:1,doy:7}});var Ca={0:"-ші",1:"-ші",2:"-ші",3:"-ші",4:"-ші",5:"-ші",6:"-шы",7:"-ші",8:"-ші",9:"-шы",10:"-шы",20:"-шы",30:"-шы",40:"-шы",50:"-ші",60:"-шы",70:"-ші",80:"-ші",90:"-шы",100:"-ші"},Wa=(n.defineLocale("kk",{months:"қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан".split("_"),monthsShort:"қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел".split("_"),weekdays:"жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі".split("_"),weekdaysShort:"жек_дүй_сей_сәр_бей_жұм_сен".split("_"),weekdaysMin:"жк_дй_сй_ср_бй_жм_сн".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгін сағат] LT",nextDay:"[Ертең сағат] LT",nextWeek:"dddd [сағат] LT",lastDay:"[Кеше сағат] LT",lastWeek:"[Өткен аптаның] dddd [сағат] LT",sameElse:"L"},relativeTime:{future:"%s ішінде",past:"%s бұрын",s:"бірнеше секунд",ss:"%d секунд",m:"бір минут",mm:"%d минут",h:"бір сағат",hh:"%d сағат",d:"бір күн",dd:"%d күн",M:"бір ай",MM:"%d ай",y:"бір жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0448\u0456|\u0448\u044b)/,ordinal:function(e){return e+(Ca[e]||Ca[e%10]||Ca[100<=e?100:null])},week:{dow:1,doy:7}}),{1:"១",2:"២",3:"៣",4:"៤",5:"៥",6:"៦",7:"៧",8:"៨",9:"៩",0:"០"}),Fa={"១":"1","២":"2","៣":"3","៤":"4","៥":"5","៦":"6","៧":"7","៨":"8","៩":"9","០":"0"},$a=(n.defineLocale("km",{months:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),monthsShort:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),weekdays:"អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍".split("_"),weekdaysShort:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysMin:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/\u1796\u17d2\u179a\u17b9\u1780|\u179b\u17d2\u1784\u17b6\u1785/,isPM:function(e){return"ល្ងាច"===e},meridiem:function(e,t,n){return e<12?"ព្រឹក":"ល្ងាច"},calendar:{sameDay:"[ថ្ងៃនេះ ម៉ោង] LT",nextDay:"[ស្អែក ម៉ោង] LT",nextWeek:"dddd [ម៉ោង] LT",lastDay:"[ម្សិលមិញ ម៉ោង] LT",lastWeek:"dddd [សប្តាហ៍មុន] [ម៉ោង] LT",sameElse:"L"},relativeTime:{future:"%sទៀត",past:"%sមុន",s:"ប៉ុន្មានវិនាទី",ss:"%d វិនាទី",m:"មួយនាទី",mm:"%d នាទី",h:"មួយម៉ោង",hh:"%d ម៉ោង",d:"មួយថ្ងៃ",dd:"%d ថ្ងៃ",M:"មួយខែ",MM:"%d ខែ",y:"មួយឆ្នាំ",yy:"%d ឆ្នាំ"},dayOfMonthOrdinalParse:/\u1791\u17b8\d{1,2}/,ordinal:"ទី%d",preparse:function(e){return e.replace(/[\u17e1\u17e2\u17e3\u17e4\u17e5\u17e6\u17e7\u17e8\u17e9\u17e0]/g,(function(e){return Fa[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return Wa[e]}))},week:{dow:1,doy:4}}),{1:"೧",2:"೨",3:"೩",4:"೪",5:"೫",6:"೬",7:"೭",8:"೮",9:"೯",0:"೦"}),Na={"೧":"1","೨":"2","೩":"3","೪":"4","೫":"5","೬":"6","೭":"7","೮":"8","೯":"9","೦":"0"};function za(e,t,n,a){return e={s:["çend sanîye","çend sanîyeyan"],ss:[e+" sanîye",e+" sanîyeyan"],m:["deqîqeyek","deqîqeyekê"],mm:[e+" deqîqe",e+" deqîqeyan"],h:["saetek","saetekê"],hh:[e+" saet",e+" saetan"],d:["rojek","rojekê"],dd:[e+" roj",e+" rojan"],w:["hefteyek","hefteyekê"],ww:[e+" hefte",e+" hefteyan"],M:["mehek","mehekê"],MM:[e+" meh",e+" mehan"],y:["salek","salekê"],yy:[e+" sal",e+" salan"]},t?e[n][0]:e[n][1]}n.defineLocale("kn",{months:"ಜನವರಿ_ಫೆಬ್ರವರಿ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂಬರ್_ಅಕ್ಟೋಬರ್_ನವೆಂಬರ್_ಡಿಸೆಂಬರ್".split("_"),monthsShort:"ಜನ_ಫೆಬ್ರ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂ_ಅಕ್ಟೋ_ನವೆಂ_ಡಿಸೆಂ".split("_"),monthsParseExact:!0,weekdays:"ಭಾನುವಾರ_ಸೋಮವಾರ_ಮಂಗಳವಾರ_ಬುಧವಾರ_ಗುರುವಾರ_ಶುಕ್ರವಾರ_ಶನಿವಾರ".split("_"),weekdaysShort:"ಭಾನು_ಸೋಮ_ಮಂಗಳ_ಬುಧ_ಗುರು_ಶುಕ್ರ_ಶನಿ".split("_"),weekdaysMin:"ಭಾ_ಸೋ_ಮಂ_ಬು_ಗು_ಶು_ಶ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[ಇಂದು] LT",nextDay:"[ನಾಳೆ] LT",nextWeek:"dddd, LT",lastDay:"[ನಿನ್ನೆ] LT",lastWeek:"[ಕೊನೆಯ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ನಂತರ",past:"%s ಹಿಂದೆ",s:"ಕೆಲವು ಕ್ಷಣಗಳು",ss:"%d ಸೆಕೆಂಡುಗಳು",m:"ಒಂದು ನಿಮಿಷ",mm:"%d ನಿಮಿಷ",h:"ಒಂದು ಗಂಟೆ",hh:"%d ಗಂಟೆ",d:"ಒಂದು ದಿನ",dd:"%d ದಿನ",M:"ಒಂದು ತಿಂಗಳು",MM:"%d ತಿಂಗಳು",y:"ಒಂದು ವರ್ಷ",yy:"%d ವರ್ಷ"},preparse:function(e){return e.replace(/[\u0ce7\u0ce8\u0ce9\u0cea\u0ceb\u0cec\u0ced\u0cee\u0cef\u0ce6]/g,(function(e){return Na[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return $a[e]}))},meridiemParse:/\u0cb0\u0cbe\u0ca4\u0ccd\u0cb0\u0cbf|\u0cac\u0cc6\u0cb3\u0cbf\u0c97\u0ccd\u0c97\u0cc6|\u0cae\u0ca7\u0ccd\u0caf\u0cbe\u0cb9\u0ccd\u0ca8|\u0cb8\u0c82\u0c9c\u0cc6/,meridiemHour:function(e,t){return 12===e&&(e=0),"ರಾತ್ರಿ"===t?e<4?e:e+12:"ಬೆಳಿಗ್ಗೆ"===t?e:"ಮಧ್ಯಾಹ್ನ"===t?10<=e?e:e+12:"ಸಂಜೆ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"ರಾತ್ರಿ":e<10?"ಬೆಳಿಗ್ಗೆ":e<17?"ಮಧ್ಯಾಹ್ನ":e<20?"ಸಂಜೆ":"ರಾತ್ರಿ"},dayOfMonthOrdinalParse:/\d{1,2}(\u0ca8\u0cc6\u0cd5)/,ordinal:function(e){return e+"ನೇ"},week:{dow:0,doy:6}}),n.defineLocale("ko",{months:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),monthsShort:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),weekdays:"일요일_월요일_화요일_수요일_목요일_금요일_토요일".split("_"),weekdaysShort:"일_월_화_수_목_금_토".split("_"),weekdaysMin:"일_월_화_수_목_금_토".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY년 MMMM D일",LLL:"YYYY년 MMMM D일 A h:mm",LLLL:"YYYY년 MMMM D일 dddd A h:mm",l:"YYYY.MM.DD.",ll:"YYYY년 MMMM D일",lll:"YYYY년 MMMM D일 A h:mm",llll:"YYYY년 MMMM D일 dddd A h:mm"},calendar:{sameDay:"오늘 LT",nextDay:"내일 LT",nextWeek:"dddd LT",lastDay:"어제 LT",lastWeek:"지난주 dddd LT",sameElse:"L"},relativeTime:{future:"%s 후",past:"%s 전",s:"몇 초",ss:"%d초",m:"1분",mm:"%d분",h:"한 시간",hh:"%d시간",d:"하루",dd:"%d일",M:"한 달",MM:"%d달",y:"일 년",yy:"%d년"},dayOfMonthOrdinalParse:/\d{1,2}(\uc77c|\uc6d4|\uc8fc)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"일";case"M":return e+"월";case"w":case"W":return e+"주";default:return e}},meridiemParse:/\uc624\uc804|\uc624\ud6c4/,isPM:function(e){return"오후"===e},meridiem:function(e,t,n){return e<12?"오전":"오후"}}),n.defineLocale("ku-kmr",{months:"Rêbendan_Sibat_Adar_Nîsan_Gulan_Hezîran_Tîrmeh_Tebax_Îlon_Cotmeh_Mijdar_Berfanbar".split("_"),monthsShort:"Rêb_Sib_Ada_Nîs_Gul_Hez_Tîr_Teb_Îlo_Cot_Mij_Ber".split("_"),monthsParseExact:!0,weekdays:"Yekşem_Duşem_Sêşem_Çarşem_Pêncşem_În_Şemî".split("_"),weekdaysShort:"Yek_Du_Sê_Çar_Pên_În_Şem".split("_"),weekdaysMin:"Ye_Du_Sê_Ça_Pê_În_Şe".split("_"),meridiem:function(e,t,n){return e<12?n?"bn":"BN":n?"pn":"PN"},meridiemParse:/bn|BN|pn|PN/,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"Do MMMM[a] YYYY[an]",LLL:"Do MMMM[a] YYYY[an] HH:mm",LLLL:"dddd, Do MMMM[a] YYYY[an] HH:mm",ll:"Do MMM[.] YYYY[an]",lll:"Do MMM[.] YYYY[an] HH:mm",llll:"ddd[.], Do MMM[.] YYYY[an] HH:mm"},calendar:{sameDay:"[Îro di saet] LT [de]",nextDay:"[Sibê di saet] LT [de]",nextWeek:"dddd [di saet] LT [de]",lastDay:"[Duh di saet] LT [de]",lastWeek:"dddd[a borî di saet] LT [de]",sameElse:"L"},relativeTime:{future:"di %s de",past:"berî %s",s:za,ss:za,m:za,mm:za,h:za,hh:za,d:za,dd:za,w:za,ww:za,M:za,MM:za,y:za,yy:za},dayOfMonthOrdinalParse:/\d{1,2}(?:y\xea|\xea|\.)/,ordinal:function(e,t){return(t=t.toLowerCase()).includes("w")||t.includes("m")?e+".":e+(e=(t=""+(t=e)).substring(t.length-1),12==(t=1<t.length?t.substring(t.length-2):"")||13==t||"2"!=e&&"3"!=e&&"50"!=t&&"70"!=e&&"80"!=e?"ê":"yê")},week:{dow:1,doy:4}});var Ia={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},Ra={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},Ua=(K=["کانونی دووەم","شوبات","ئازار","نیسان","ئایار","حوزەیران","تەمموز","ئاب","ئەیلوول","تشرینی یەكەم","تشرینی دووەم","كانونی یەکەم"],n.defineLocale("ku",{months:K,monthsShort:K,weekdays:"یه‌كشه‌ممه‌_دووشه‌ممه‌_سێشه‌ممه‌_چوارشه‌ممه‌_پێنجشه‌ممه‌_هه‌ینی_شه‌ممه‌".split("_"),weekdaysShort:"یه‌كشه‌م_دووشه‌م_سێشه‌م_چوارشه‌م_پێنجشه‌م_هه‌ینی_شه‌ممه‌".split("_"),weekdaysMin:"ی_د_س_چ_پ_ه_ش".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/\u0626\u06ce\u0648\u0627\u0631\u0647\u200c|\u0628\u0647\u200c\u06cc\u0627\u0646\u06cc/,isPM:function(e){return/\u0626\u06ce\u0648\u0627\u0631\u0647\u200c/.test(e)},meridiem:function(e,t,n){return e<12?"به‌یانی":"ئێواره‌"},calendar:{sameDay:"[ئه‌مرۆ كاتژمێر] LT",nextDay:"[به‌یانی كاتژمێر] LT",nextWeek:"dddd [كاتژمێر] LT",lastDay:"[دوێنێ كاتژمێر] LT",lastWeek:"dddd [كاتژمێر] LT",sameElse:"L"},relativeTime:{future:"له‌ %s",past:"%s",s:"چه‌ند چركه‌یه‌ك",ss:"چركه‌ %d",m:"یه‌ك خوله‌ك",mm:"%d خوله‌ك",h:"یه‌ك كاتژمێر",hh:"%d كاتژمێر",d:"یه‌ك ڕۆژ",dd:"%d ڕۆژ",M:"یه‌ك مانگ",MM:"%d مانگ",y:"یه‌ك ساڵ",yy:"%d ساڵ"},preparse:function(e){return e.replace(/[\u0661\u0662\u0663\u0664\u0665\u0666\u0667\u0668\u0669\u0660]/g,(function(e){return Ra[e]})).replace(/\u060c/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return Ia[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}}),{0:"-чү",1:"-чи",2:"-чи",3:"-чү",4:"-чү",5:"-чи",6:"-чы",7:"-чи",8:"-чи",9:"-чу",10:"-чу",20:"-чы",30:"-чу",40:"-чы",50:"-чү",60:"-чы",70:"-чи",80:"-чи",90:"-чу",100:"-чү"});function Ja(e,t,n,a){var s={m:["eng Minutt","enger Minutt"],h:["eng Stonn","enger Stonn"],d:["een Dag","engem Dag"],M:["ee Mount","engem Mount"],y:["ee Joer","engem Joer"]};return t?s[n][0]:s[n][1]}function qa(e){if(e=parseInt(e,10),isNaN(e))return!1;if(e<0)return!0;if(e<10)return 4<=e&&e<=7;var t;if(e<100)return qa(0==(t=e%10)?e/10:t);if(e<1e4){for(;10<=e;)e/=10;return qa(e)}return qa(e/=1e3)}n.defineLocale("ky",{months:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_"),monthsShort:"янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек".split("_"),weekdays:"Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби".split("_"),weekdaysShort:"Жек_Дүй_Шей_Шар_Бей_Жум_Ише".split("_"),weekdaysMin:"Жк_Дй_Шй_Шр_Бй_Жм_Иш".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгүн саат] LT",nextDay:"[Эртең саат] LT",nextWeek:"dddd [саат] LT",lastDay:"[Кечээ саат] LT",lastWeek:"[Өткөн аптанын] dddd [күнү] [саат] LT",sameElse:"L"},relativeTime:{future:"%s ичинде",past:"%s мурун",s:"бирнече секунд",ss:"%d секунд",m:"бир мүнөт",mm:"%d мүнөт",h:"бир саат",hh:"%d саат",d:"бир күн",dd:"%d күн",M:"бир ай",MM:"%d ай",y:"бир жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0447\u0438|\u0447\u044b|\u0447\u04af|\u0447\u0443)/,ordinal:function(e){return e+(Ua[e]||Ua[e%10]||Ua[100<=e?100:null])},week:{dow:1,doy:7}}),n.defineLocale("lb",{months:"Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg".split("_"),weekdaysShort:"So._Mé._Dë._Më._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mé_Dë_Më_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm [Auer]",LTS:"H:mm:ss [Auer]",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm [Auer]",LLLL:"dddd, D. MMMM YYYY H:mm [Auer]"},calendar:{sameDay:"[Haut um] LT",sameElse:"L",nextDay:"[Muer um] LT",nextWeek:"dddd [um] LT",lastDay:"[Gëschter um] LT",lastWeek:function(){switch(this.day()){case 2:case 4:return"[Leschten] dddd [um] LT";default:return"[Leschte] dddd [um] LT"}}},relativeTime:{future:function(e){return qa(e.substr(0,e.indexOf(" ")))?"a "+e:"an "+e},past:function(e){return qa(e.substr(0,e.indexOf(" ")))?"viru "+e:"virun "+e},s:"e puer Sekonnen",ss:"%d Sekonnen",m:Ja,mm:"%d Minutten",h:Ja,hh:"%d Stonnen",d:Ja,dd:"%d Deeg",M:Ja,MM:"%d Méint",y:Ja,yy:"%d Joer"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("lo",{months:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),monthsShort:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),weekdays:"ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysShort:"ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysMin:"ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"ວັນdddd D MMMM YYYY HH:mm"},meridiemParse:/\u0e95\u0ead\u0e99\u0ec0\u0e8a\u0ebb\u0ec9\u0eb2|\u0e95\u0ead\u0e99\u0ec1\u0ea5\u0e87/,isPM:function(e){return"ຕອນແລງ"===e},meridiem:function(e,t,n){return e<12?"ຕອນເຊົ້າ":"ຕອນແລງ"},calendar:{sameDay:"[ມື້ນີ້ເວລາ] LT",nextDay:"[ມື້ອື່ນເວລາ] LT",nextWeek:"[ວັນ]dddd[ໜ້າເວລາ] LT",lastDay:"[ມື້ວານນີ້ເວລາ] LT",lastWeek:"[ວັນ]dddd[ແລ້ວນີ້ເວລາ] LT",sameElse:"L"},relativeTime:{future:"ອີກ %s",past:"%sຜ່ານມາ",s:"ບໍ່ເທົ່າໃດວິນາທີ",ss:"%d ວິນາທີ",m:"1 ນາທີ",mm:"%d ນາທີ",h:"1 ຊົ່ວໂມງ",hh:"%d ຊົ່ວໂມງ",d:"1 ມື້",dd:"%d ມື້",M:"1 ເດືອນ",MM:"%d ເດືອນ",y:"1 ປີ",yy:"%d ປີ"},dayOfMonthOrdinalParse:/(\u0e97\u0eb5\u0ec8)\d{1,2}/,ordinal:function(e){return"ທີ່"+e}});var Ba={ss:"sekundė_sekundžių_sekundes",m:"minutė_minutės_minutę",mm:"minutės_minučių_minutes",h:"valanda_valandos_valandą",hh:"valandos_valandų_valandas",d:"diena_dienos_dieną",dd:"dienos_dienų_dienas",M:"mėnuo_mėnesio_mėnesį",MM:"mėnesiai_mėnesių_mėnesius",y:"metai_metų_metus",yy:"metai_metų_metus"};function Ga(e,t,n,a){return t?Ka(n)[0]:a?Ka(n)[1]:Ka(n)[2]}function Va(e){return e%10==0||10<e&&e<20}function Ka(e){return Ba[e].split("_")}function Za(e,t,n,a){var s=e+" ";return 1===e?s+Ga(0,t,n[0],a):t?s+(Va(e)?Ka(n)[1]:Ka(n)[0]):a?s+Ka(n)[1]:s+(Va(e)?Ka(n)[1]:Ka(n)[2])}n.defineLocale("lt",{months:{format:"sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio".split("_"),standalone:"sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis".split("_"),isFormat:/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?|MMMM?(\[[^\[\]]*\]|\s)+D[oD]?/},monthsShort:"sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd".split("_"),weekdays:{format:"sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį".split("_"),standalone:"sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis".split("_"),isFormat:/dddd HH:mm/},weekdaysShort:"Sek_Pir_Ant_Tre_Ket_Pen_Šeš".split("_"),weekdaysMin:"S_P_A_T_K_Pn_Š".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY [m.] MMMM D [d.]",LLL:"YYYY [m.] MMMM D [d.], HH:mm [val.]",LLLL:"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]",l:"YYYY-MM-DD",ll:"YYYY [m.] MMMM D [d.]",lll:"YYYY [m.] MMMM D [d.], HH:mm [val.]",llll:"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]"},calendar:{sameDay:"[Šiandien] LT",nextDay:"[Rytoj] LT",nextWeek:"dddd LT",lastDay:"[Vakar] LT",lastWeek:"[Praėjusį] dddd LT",sameElse:"L"},relativeTime:{future:"po %s",past:"prieš %s",s:function(e,t,n,a){return t?"kelios sekundės":a?"kelių sekundžių":"kelias sekundes"},ss:Za,m:Ga,mm:Za,h:Ga,hh:Za,d:Ga,dd:Za,M:Ga,MM:Za,y:Ga,yy:Za},dayOfMonthOrdinalParse:/\d{1,2}-oji/,ordinal:function(e){return e+"-oji"},week:{dow:1,doy:4}});var Qa={ss:"sekundes_sekundēm_sekunde_sekundes".split("_"),m:"minūtes_minūtēm_minūte_minūtes".split("_"),mm:"minūtes_minūtēm_minūte_minūtes".split("_"),h:"stundas_stundām_stunda_stundas".split("_"),hh:"stundas_stundām_stunda_stundas".split("_"),d:"dienas_dienām_diena_dienas".split("_"),dd:"dienas_dienām_diena_dienas".split("_"),M:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),MM:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),y:"gada_gadiem_gads_gadi".split("_"),yy:"gada_gadiem_gads_gadi".split("_")};function Xa(e,t,n){return n?t%10==1&&t%100!=11?e[2]:e[3]:t%10==1&&t%100!=11?e[0]:e[1]}function es(e,t,n){return e+" "+Xa(Qa[n],e,t)}function ts(e,t,n){return Xa(Qa[n],e,t)}n.defineLocale("lv",{months:"janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris".split("_"),monthsShort:"jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec".split("_"),weekdays:"svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena".split("_"),weekdaysShort:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysMin:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY.",LL:"YYYY. [gada] D. MMMM",LLL:"YYYY. [gada] D. MMMM, HH:mm",LLLL:"YYYY. [gada] D. MMMM, dddd, HH:mm"},calendar:{sameDay:"[Šodien pulksten] LT",nextDay:"[Rīt pulksten] LT",nextWeek:"dddd [pulksten] LT",lastDay:"[Vakar pulksten] LT",lastWeek:"[Pagājušā] dddd [pulksten] LT",sameElse:"L"},relativeTime:{future:"pēc %s",past:"pirms %s",s:function(e,t){return t?"dažas sekundes":"dažām sekundēm"},ss:es,m:ts,mm:es,h:ts,hh:es,d:ts,dd:es,M:ts,MM:es,y:ts,yy:es},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});var ns={words:{ss:["sekund","sekunda","sekundi"],m:["jedan minut","jednog minuta"],mm:["minut","minuta","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mjesec","mjeseca","mjeseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(e,t){return 1===e?t[0]:2<=e&&e<=4?t[1]:t[2]},translate:function(e,t,n){var a=ns.words[n];return 1===n.length?t?a[0]:a[1]:e+" "+ns.correctGrammaticalCase(e,a)}};function as(e,t,n,a){switch(n){case"s":return t?"хэдхэн секунд":"хэдхэн секундын";case"ss":return e+(t?" секунд":" секундын");case"m":case"mm":return e+(t?" минут":" минутын");case"h":case"hh":return e+(t?" цаг":" цагийн");case"d":case"dd":return e+(t?" өдөр":" өдрийн");case"M":case"MM":return e+(t?" сар":" сарын");case"y":case"yy":return e+(t?" жил":" жилийн");default:return e}}n.defineLocale("me",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sjutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){return["[prošle] [nedjelje] [u] LT","[prošlog] [ponedjeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srijede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"nekoliko sekundi",ss:ns.translate,m:ns.translate,mm:ns.translate,h:ns.translate,hh:ns.translate,d:"dan",dd:ns.translate,M:"mjesec",MM:ns.translate,y:"godinu",yy:ns.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}}),n.defineLocale("mi",{months:"Kohi-tāte_Hui-tanguru_Poutū-te-rangi_Paenga-whāwhā_Haratua_Pipiri_Hōngoingoi_Here-turi-kōkā_Mahuru_Whiringa-ā-nuku_Whiringa-ā-rangi_Hakihea".split("_"),monthsShort:"Kohi_Hui_Pou_Pae_Hara_Pipi_Hōngoi_Here_Mahu_Whi-nu_Whi-ra_Haki".split("_"),monthsRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,2}/i,weekdays:"Rātapu_Mane_Tūrei_Wenerei_Tāite_Paraire_Hātarei".split("_"),weekdaysShort:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),weekdaysMin:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [i] HH:mm",LLLL:"dddd, D MMMM YYYY [i] HH:mm"},calendar:{sameDay:"[i teie mahana, i] LT",nextDay:"[apopo i] LT",nextWeek:"dddd [i] LT",lastDay:"[inanahi i] LT",lastWeek:"dddd [whakamutunga i] LT",sameElse:"L"},relativeTime:{future:"i roto i %s",past:"%s i mua",s:"te hēkona ruarua",ss:"%d hēkona",m:"he meneti",mm:"%d meneti",h:"te haora",hh:"%d haora",d:"he ra",dd:"%d ra",M:"he marama",MM:"%d marama",y:"he tau",yy:"%d tau"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:1,doy:4}}),n.defineLocale("mk",{months:"јануари_февруари_март_април_мај_јуни_јули_август_септември_октомври_ноември_декември".split("_"),monthsShort:"јан_фев_мар_апр_мај_јун_јул_авг_сеп_окт_ное_дек".split("_"),weekdays:"недела_понеделник_вторник_среда_четврток_петок_сабота".split("_"),weekdaysShort:"нед_пон_вто_сре_чет_пет_саб".split("_"),weekdaysMin:"нe_пo_вт_ср_че_пе_сa".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Денес во] LT",nextDay:"[Утре во] LT",nextWeek:"[Во] dddd [во] LT",lastDay:"[Вчера во] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Изминатата] dddd [во] LT";case 1:case 2:case 4:case 5:return"[Изминатиот] dddd [во] LT"}},sameElse:"L"},relativeTime:{future:"за %s",past:"пред %s",s:"неколку секунди",ss:"%d секунди",m:"една минута",mm:"%d минути",h:"еден час",hh:"%d часа",d:"еден ден",dd:"%d дена",M:"еден месец",MM:"%d месеци",y:"една година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0435\u0432|\u0435\u043d|\u0442\u0438|\u0432\u0438|\u0440\u0438|\u043c\u0438)/,ordinal:function(e){var t=e%10,n=e%100;return 0===e?e+"-ев":0==n?e+"-ен":10<n&&n<20?e+"-ти":1==t?e+"-ви":2==t?e+"-ри":7==t||8==t?e+"-ми":e+"-ти"},week:{dow:1,doy:7}}),n.defineLocale("ml",{months:"ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ".split("_"),monthsShort:"ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.".split("_"),monthsParseExact:!0,weekdays:"ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച".split("_"),weekdaysShort:"ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി".split("_"),weekdaysMin:"ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ".split("_"),longDateFormat:{LT:"A h:mm -നു",LTS:"A h:mm:ss -നു",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm -നു",LLLL:"dddd, D MMMM YYYY, A h:mm -നു"},calendar:{sameDay:"[ഇന്ന്] LT",nextDay:"[നാളെ] LT",nextWeek:"dddd, LT",lastDay:"[ഇന്നലെ] LT",lastWeek:"[കഴിഞ്ഞ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s കഴിഞ്ഞ്",past:"%s മുൻപ്",s:"അൽപ നിമിഷങ്ങൾ",ss:"%d സെക്കൻഡ്",m:"ഒരു മിനിറ്റ്",mm:"%d മിനിറ്റ്",h:"ഒരു മണിക്കൂർ",hh:"%d മണിക്കൂർ",d:"ഒരു ദിവസം",dd:"%d ദിവസം",M:"ഒരു മാസം",MM:"%d മാസം",y:"ഒരു വർഷം",yy:"%d വർഷം"},meridiemParse:/\u0d30\u0d3e\u0d24\u0d4d\u0d30\u0d3f|\u0d30\u0d3e\u0d35\u0d3f\u0d32\u0d46|\u0d09\u0d1a\u0d4d\u0d1a \u0d15\u0d34\u0d3f\u0d1e\u0d4d\u0d1e\u0d4d|\u0d35\u0d48\u0d15\u0d41\u0d28\u0d4d\u0d28\u0d47\u0d30\u0d02|\u0d30\u0d3e\u0d24\u0d4d\u0d30\u0d3f/i,meridiemHour:function(e,t){return 12===e&&(e=0),"രാത്രി"===t&&4<=e||"ഉച്ച കഴിഞ്ഞ്"===t||"വൈകുന്നേരം"===t?e+12:e},meridiem:function(e,t,n){return e<4?"രാത്രി":e<12?"രാവിലെ":e<17?"ഉച്ച കഴിഞ്ഞ്":e<20?"വൈകുന്നേരം":"രാത്രി"}}),n.defineLocale("mn",{months:"Нэгдүгээр сар_Хоёрдугаар сар_Гуравдугаар сар_Дөрөвдүгээр сар_Тавдугаар сар_Зургадугаар сар_Долдугаар сар_Наймдугаар сар_Есдүгээр сар_Аравдугаар сар_Арван нэгдүгээр сар_Арван хоёрдугаар сар".split("_"),monthsShort:"1 сар_2 сар_3 сар_4 сар_5 сар_6 сар_7 сар_8 сар_9 сар_10 сар_11 сар_12 сар".split("_"),monthsParseExact:!0,weekdays:"Ням_Даваа_Мягмар_Лхагва_Пүрэв_Баасан_Бямба".split("_"),weekdaysShort:"Ням_Дав_Мяг_Лха_Пүр_Баа_Бям".split("_"),weekdaysMin:"Ня_Да_Мя_Лх_Пү_Ба_Бя".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY оны MMMMын D",LLL:"YYYY оны MMMMын D HH:mm",LLLL:"dddd, YYYY оны MMMMын D HH:mm"},meridiemParse:/\u04ae\u04e8|\u04ae\u0425/i,isPM:function(e){return"ҮХ"===e},meridiem:function(e,t,n){return e<12?"ҮӨ":"ҮХ"},calendar:{sameDay:"[Өнөөдөр] LT",nextDay:"[Маргааш] LT",nextWeek:"[Ирэх] dddd LT",lastDay:"[Өчигдөр] LT",lastWeek:"[Өнгөрсөн] dddd LT",sameElse:"L"},relativeTime:{future:"%s дараа",past:"%s өмнө",s:as,ss:as,m:as,mm:as,h:as,hh:as,d:as,dd:as,M:as,MM:as,y:as,yy:as},dayOfMonthOrdinalParse:/\d{1,2} \u04e9\u0434\u04e9\u0440/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+" өдөр";default:return e}}});var ss={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},rs={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};function is(e,t,n,a){var s="";if(t)switch(n){case"s":s="काही सेकंद";break;case"ss":s="%d सेकंद";break;case"m":s="एक मिनिट";break;case"mm":s="%d मिनिटे";break;case"h":s="एक तास";break;case"hh":s="%d तास";break;case"d":s="एक दिवस";break;case"dd":s="%d दिवस";break;case"M":s="एक महिना";break;case"MM":s="%d महिने";break;case"y":s="एक वर्ष";break;case"yy":s="%d वर्षे"}else switch(n){case"s":s="काही सेकंदां";break;case"ss":s="%d सेकंदां";break;case"m":s="एका मिनिटा";break;case"mm":s="%d मिनिटां";break;case"h":s="एका तासा";break;case"hh":s="%d तासां";break;case"d":s="एका दिवसा";break;case"dd":s="%d दिवसां";break;case"M":s="एका महिन्या";break;case"MM":s="%d महिन्यां";break;case"y":s="एका वर्षा";break;case"yy":s="%d वर्षां"}return s.replace(/%d/i,e)}n.defineLocale("mr",{months:"जानेवारी_फेब्रुवारी_मार्च_एप्रिल_मे_जून_जुलै_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर".split("_"),monthsShort:"जाने._फेब्रु._मार्च._एप्रि._मे._जून._जुलै._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.".split("_"),monthsParseExact:!0,weekdays:"रविवार_सोमवार_मंगळवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगळ_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm वाजता",LTS:"A h:mm:ss वाजता",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm वाजता",LLLL:"dddd, D MMMM YYYY, A h:mm वाजता"},calendar:{sameDay:"[आज] LT",nextDay:"[उद्या] LT",nextWeek:"dddd, LT",lastDay:"[काल] LT",lastWeek:"[मागील] dddd, LT",sameElse:"L"},relativeTime:{future:"%sमध्ये",past:"%sपूर्वी",s:is,ss:is,m:is,mm:is,h:is,hh:is,d:is,dd:is,M:is,MM:is,y:is,yy:is},preparse:function(e){return e.replace(/[\u0967\u0968\u0969\u096a\u096b\u096c\u096d\u096e\u096f\u0966]/g,(function(e){return rs[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return ss[e]}))},meridiemParse:/\u092a\u0939\u093e\u091f\u0947|\u0938\u0915\u093e\u0933\u0940|\u0926\u0941\u092a\u093e\u0930\u0940|\u0938\u093e\u092f\u0902\u0915\u093e\u0933\u0940|\u0930\u093e\u0924\u094d\u0930\u0940/,meridiemHour:function(e,t){return 12===e&&(e=0),"पहाटे"===t||"सकाळी"===t?e:"दुपारी"===t||"सायंकाळी"===t||"रात्री"===t?12<=e?e:e+12:void 0},meridiem:function(e,t,n){return 0<=e&&e<6?"पहाटे":e<12?"सकाळी":e<17?"दुपारी":e<20?"सायंकाळी":"रात्री"},week:{dow:0,doy:6}}),n.defineLocale("ms-my",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"tengahari"===t?11<=e?e:e+12:"petang"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"pagi":e<15?"tengahari":e<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}}),n.defineLocale("ms",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"tengahari"===t?11<=e?e:e+12:"petang"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"pagi":e<15?"tengahari":e<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}}),n.defineLocale("mt",{months:"Jannar_Frar_Marzu_April_Mejju_Ġunju_Lulju_Awwissu_Settembru_Ottubru_Novembru_Diċembru".split("_"),monthsShort:"Jan_Fra_Mar_Apr_Mej_Ġun_Lul_Aww_Set_Ott_Nov_Diċ".split("_"),weekdays:"Il-Ħadd_It-Tnejn_It-Tlieta_L-Erbgħa_Il-Ħamis_Il-Ġimgħa_Is-Sibt".split("_"),weekdaysShort:"Ħad_Tne_Tli_Erb_Ħam_Ġim_Sib".split("_"),weekdaysMin:"Ħa_Tn_Tl_Er_Ħa_Ġi_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Illum fil-]LT",nextDay:"[Għada fil-]LT",nextWeek:"dddd [fil-]LT",lastDay:"[Il-bieraħ fil-]LT",lastWeek:"dddd [li għadda] [fil-]LT",sameElse:"L"},relativeTime:{future:"f’ %s",past:"%s ilu",s:"ftit sekondi",ss:"%d sekondi",m:"minuta",mm:"%d minuti",h:"siegħa",hh:"%d siegħat",d:"ġurnata",dd:"%d ġranet",M:"xahar",MM:"%d xhur",y:"sena",yy:"%d sni"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:1,doy:4}});var os={1:"၁",2:"၂",3:"၃",4:"၄",5:"၅",6:"၆",7:"၇",8:"၈",9:"၉",0:"၀"},ds={"၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","၀":"0"},us=(n.defineLocale("my",{months:"ဇန်နဝါရီ_ဖေဖော်ဝါရီ_မတ်_ဧပြီ_မေ_ဇွန်_ဇူလိုင်_သြဂုတ်_စက်တင်ဘာ_အောက်တိုဘာ_နိုဝင်ဘာ_ဒီဇင်ဘာ".split("_"),monthsShort:"ဇန်_ဖေ_မတ်_ပြီ_မေ_ဇွန်_လိုင်_သြ_စက်_အောက်_နို_ဒီ".split("_"),weekdays:"တနင်္ဂနွေ_တနင်္လာ_အင်္ဂါ_ဗုဒ္ဓဟူး_ကြာသပတေး_သောကြာ_စနေ".split("_"),weekdaysShort:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),weekdaysMin:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ယနေ.] LT [မှာ]",nextDay:"[မနက်ဖြန်] LT [မှာ]",nextWeek:"dddd LT [မှာ]",lastDay:"[မနေ.က] LT [မှာ]",lastWeek:"[ပြီးခဲ့သော] dddd LT [မှာ]",sameElse:"L"},relativeTime:{future:"လာမည့် %s မှာ",past:"လွန်ခဲ့သော %s က",s:"စက္ကန်.အနည်းငယ်",ss:"%d စက္ကန့်",m:"တစ်မိနစ်",mm:"%d မိနစ်",h:"တစ်နာရီ",hh:"%d နာရီ",d:"တစ်ရက်",dd:"%d ရက်",M:"တစ်လ",MM:"%d လ",y:"တစ်နှစ်",yy:"%d နှစ်"},preparse:function(e){return e.replace(/[\u1041\u1042\u1043\u1044\u1045\u1046\u1047\u1048\u1049\u1040]/g,(function(e){return ds[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return os[e]}))},week:{dow:1,doy:4}}),n.defineLocale("nb",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.".split("_"),monthsParseExact:!0,weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"sø._ma._ti._on._to._fr._lø.".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] HH:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[forrige] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"noen sekunder",ss:"%d sekunder",m:"ett minutt",mm:"%d minutter",h:"én time",hh:"%d timer",d:"én dag",dd:"%d dager",w:"én uke",ww:"%d uker",M:"én måned",MM:"%d måneder",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),{1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"}),ls={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"},cs=(n.defineLocale("ne",{months:"जनवरी_फेब्रुवरी_मार्च_अप्रिल_मई_जुन_जुलाई_अगष्ट_सेप्टेम्बर_अक्टोबर_नोभेम्बर_डिसेम्बर".split("_"),monthsShort:"जन._फेब्रु._मार्च_अप्रि._मई_जुन_जुलाई._अग._सेप्ट._अक्टो._नोभे._डिसे.".split("_"),monthsParseExact:!0,weekdays:"आइतबार_सोमबार_मङ्गलबार_बुधबार_बिहिबार_शुक्रबार_शनिबार".split("_"),weekdaysShort:"आइत._सोम._मङ्गल._बुध._बिहि._शुक्र._शनि.".split("_"),weekdaysMin:"आ._सो._मं._बु._बि._शु._श.".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"Aको h:mm बजे",LTS:"Aको h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, Aको h:mm बजे",LLLL:"dddd, D MMMM YYYY, Aको h:mm बजे"},preparse:function(e){return e.replace(/[\u0967\u0968\u0969\u096a\u096b\u096c\u096d\u096e\u096f\u0966]/g,(function(e){return ls[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return us[e]}))},meridiemParse:/\u0930\u093e\u0924\u093f|\u092c\u093f\u0939\u093e\u0928|\u0926\u093f\u0909\u0901\u0938\u094b|\u0938\u093e\u0901\u091d/,meridiemHour:function(e,t){return 12===e&&(e=0),"राति"===t?e<4?e:e+12:"बिहान"===t?e:"दिउँसो"===t?10<=e?e:e+12:"साँझ"===t?e+12:void 0},meridiem:function(e,t,n){return e<3?"राति":e<12?"बिहान":e<16?"दिउँसो":e<20?"साँझ":"राति"},calendar:{sameDay:"[आज] LT",nextDay:"[भोलि] LT",nextWeek:"[आउँदो] dddd[,] LT",lastDay:"[हिजो] LT",lastWeek:"[गएको] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%sमा",past:"%s अगाडि",s:"केही क्षण",ss:"%d सेकेण्ड",m:"एक मिनेट",mm:"%d मिनेट",h:"एक घण्टा",hh:"%d घण्टा",d:"एक दिन",dd:"%d दिन",M:"एक महिना",MM:"%d महिना",y:"एक बर्ष",yy:"%d बर्ष"},week:{dow:0,doy:6}}),"jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_")),_s="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),ms=(V=[/^jan/i,/^feb/i,/^(maart|mrt\.?)$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i],Oe=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,n.defineLocale("nl-be",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(e,t){return e?(/-MMM-/.test(t)?_s:cs)[e.month()]:cs},monthsRegex:Oe,monthsShortRegex:Oe,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:V,longMonthsParse:V,shortMonthsParse:V,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||20<=e?"ste":"de")},week:{dow:1,doy:4}}),"jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_")),hs="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),ps=(U=[/^jan/i,/^feb/i,/^(maart|mrt\.?)$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i],G=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,n.defineLocale("nl",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(e,t){return e?(/-MMM-/.test(t)?hs:ms)[e.month()]:ms},monthsRegex:G,monthsShortRegex:G,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:U,longMonthsParse:U,shortMonthsParse:U,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",w:"één week",ww:"%d weken",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||20<=e?"ste":"de")},week:{dow:1,doy:4}}),n.defineLocale("nn",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.".split("_"),monthsParseExact:!0,weekdays:"sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag".split("_"),weekdaysShort:"su._må._ty._on._to._fr._lau.".split("_"),weekdaysMin:"su_må_ty_on_to_fr_la".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[I dag klokka] LT",nextDay:"[I morgon klokka] LT",nextWeek:"dddd [klokka] LT",lastDay:"[I går klokka] LT",lastWeek:"[Føregåande] dddd [klokka] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s sidan",s:"nokre sekund",ss:"%d sekund",m:"eit minutt",mm:"%d minutt",h:"ein time",hh:"%d timar",d:"ein dag",dd:"%d dagar",w:"ei veke",ww:"%d veker",M:"ein månad",MM:"%d månader",y:"eit år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("oc-lnc",{months:{standalone:"genièr_febrièr_març_abril_mai_junh_julhet_agost_setembre_octòbre_novembre_decembre".split("_"),format:"de genièr_de febrièr_de març_d'abril_de mai_de junh_de julhet_d'agost_de setembre_d'octòbre_de novembre_de decembre".split("_"),isFormat:/D[oD]?(\s)+MMMM/},monthsShort:"gen._febr._març_abr._mai_junh_julh._ago._set._oct._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"dimenge_diluns_dimars_dimècres_dijòus_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dm._dc._dj._dv._ds.".split("_"),weekdaysMin:"dg_dl_dm_dc_dj_dv_ds".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [de] YYYY",ll:"D MMM YYYY",LLL:"D MMMM [de] YYYY [a] H:mm",lll:"D MMM YYYY, H:mm",LLLL:"dddd D MMMM [de] YYYY [a] H:mm",llll:"ddd D MMM YYYY, H:mm"},calendar:{sameDay:"[uèi a] LT",nextDay:"[deman a] LT",nextWeek:"dddd [a] LT",lastDay:"[ièr a] LT",lastWeek:"dddd [passat a] LT",sameElse:"L"},relativeTime:{future:"d'aquí %s",past:"fa %s",s:"unas segondas",ss:"%d segondas",m:"una minuta",mm:"%d minutas",h:"una ora",hh:"%d oras",d:"un jorn",dd:"%d jorns",M:"un mes",MM:"%d meses",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(r|n|t|\xe8|a)/,ordinal:function(e,t){return e+("w"!==t&&"W"!==t?1===e?"r":2===e?"n":3===e?"r":4===e?"t":"è":"a")},week:{dow:1,doy:4}}),{1:"੧",2:"੨",3:"੩",4:"੪",5:"੫",6:"੬",7:"੭",8:"੮",9:"੯",0:"੦"}),fs={"੧":"1","੨":"2","੩":"3","੪":"4","੫":"5","੬":"6","੭":"7","੮":"8","੯":"9","੦":"0"},ys=(n.defineLocale("pa-in",{months:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),monthsShort:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),weekdays:"ਐਤਵਾਰ_ਸੋਮਵਾਰ_ਮੰਗਲਵਾਰ_ਬੁਧਵਾਰ_ਵੀਰਵਾਰ_ਸ਼ੁੱਕਰਵਾਰ_ਸ਼ਨੀਚਰਵਾਰ".split("_"),weekdaysShort:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),weekdaysMin:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),longDateFormat:{LT:"A h:mm ਵਜੇ",LTS:"A h:mm:ss ਵਜੇ",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm ਵਜੇ",LLLL:"dddd, D MMMM YYYY, A h:mm ਵਜੇ"},calendar:{sameDay:"[ਅਜ] LT",nextDay:"[ਕਲ] LT",nextWeek:"[ਅਗਲਾ] dddd, LT",lastDay:"[ਕਲ] LT",lastWeek:"[ਪਿਛਲੇ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ਵਿੱਚ",past:"%s ਪਿਛਲੇ",s:"ਕੁਝ ਸਕਿੰਟ",ss:"%d ਸਕਿੰਟ",m:"ਇਕ ਮਿੰਟ",mm:"%d ਮਿੰਟ",h:"ਇੱਕ ਘੰਟਾ",hh:"%d ਘੰਟੇ",d:"ਇੱਕ ਦਿਨ",dd:"%d ਦਿਨ",M:"ਇੱਕ ਮਹੀਨਾ",MM:"%d ਮਹੀਨੇ",y:"ਇੱਕ ਸਾਲ",yy:"%d ਸਾਲ"},preparse:function(e){return e.replace(/[\u0a67\u0a68\u0a69\u0a6a\u0a6b\u0a6c\u0a6d\u0a6e\u0a6f\u0a66]/g,(function(e){return fs[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return ps[e]}))},meridiemParse:/\u0a30\u0a3e\u0a24|\u0a38\u0a35\u0a47\u0a30|\u0a26\u0a41\u0a2a\u0a39\u0a3f\u0a30|\u0a38\u0a3c\u0a3e\u0a2e/,meridiemHour:function(e,t){return 12===e&&(e=0),"ਰਾਤ"===t?e<4?e:e+12:"ਸਵੇਰ"===t?e:"ਦੁਪਹਿਰ"===t?10<=e?e:e+12:"ਸ਼ਾਮ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"ਰਾਤ":e<10?"ਸਵੇਰ":e<17?"ਦੁਪਹਿਰ":e<20?"ਸ਼ਾਮ":"ਰਾਤ"},week:{dow:0,doy:6}}),"styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień".split("_")),Ms="stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia".split("_");function gs(e){return e%10<5&&1<e%10&&~~(e/10)%10!=1}function Ls(e,t,n){var a=e+" ";switch(n){case"ss":return a+(gs(e)?"sekundy":"sekund");case"m":return t?"minuta":"minutę";case"mm":return a+(gs(e)?"minuty":"minut");case"h":return t?"godzina":"godzinę";case"hh":return a+(gs(e)?"godziny":"godzin");case"ww":return a+(gs(e)?"tygodnie":"tygodni");case"MM":return a+(gs(e)?"miesiące":"miesięcy");case"yy":return a+(gs(e)?"lata":"lat")}}function Ys(e,t,n){return e+(20<=e%100||100<=e&&e%100==0?" de ":" ")+{ss:"secunde",mm:"minute",hh:"ore",dd:"zile",ww:"săptămâni",MM:"luni",yy:"ani"}[n]}function vs(e,t,n){return"m"===n?t?"минута":"минуту":e+" "+(e=+e,t=(t={ss:t?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:t?"минута_минуты_минут":"минуту_минуты_минут",hh:"час_часа_часов",dd:"день_дня_дней",ww:"неделя_недели_недель",MM:"месяц_месяца_месяцев",yy:"год_года_лет"}[n]).split("_"),e%10==1&&e%100!=11?t[0]:2<=e%10&&e%10<=4&&(e%100<10||20<=e%100)?t[1]:t[2])}function ws(e){return 1<e&&e<5}function bs(e,t,n,a){var s=e+" ";switch(n){case"s":return t||a?"pár sekúnd":"pár sekundami";case"ss":return t||a?s+(ws(e)?"sekundy":"sekúnd"):s+"sekundami";case"m":return t?"minúta":a?"minútu":"minútou";case"mm":return t||a?s+(ws(e)?"minúty":"minút"):s+"minútami";case"h":return t?"hodina":a?"hodinu":"hodinou";case"hh":return t||a?s+(ws(e)?"hodiny":"hodín"):s+"hodinami";case"d":return t||a?"deň":"dňom";case"dd":return t||a?s+(ws(e)?"dni":"dní"):s+"dňami";case"M":return t||a?"mesiac":"mesiacom";case"MM":return t||a?s+(ws(e)?"mesiace":"mesiacov"):s+"mesiacmi";case"y":return t||a?"rok":"rokom";case"yy":return t||a?s+(ws(e)?"roky":"rokov"):s+"rokmi"}}function ks(e,t,n,a){var s=e+" ";switch(n){case"s":return t||a?"nekaj sekund":"nekaj sekundami";case"ss":return s+(1===e?t?"sekundo":"sekundi":2===e?t||a?"sekundi":"sekundah":e<5?t||a?"sekunde":"sekundah":"sekund");case"m":return t?"ena minuta":"eno minuto";case"mm":return s+(1===e?t?"minuta":"minuto":2===e?t||a?"minuti":"minutama":e<5?t||a?"minute":"minutami":t||a?"minut":"minutami");case"h":return t?"ena ura":"eno uro";case"hh":return s+(1===e?t?"ura":"uro":2===e?t||a?"uri":"urama":e<5?t||a?"ure":"urami":t||a?"ur":"urami");case"d":return t||a?"en dan":"enim dnem";case"dd":return s+(1===e?t||a?"dan":"dnem":2===e?t||a?"dni":"dnevoma":t||a?"dni":"dnevi");case"M":return t||a?"en mesec":"enim mesecem";case"MM":return s+(1===e?t||a?"mesec":"mesecem":2===e?t||a?"meseca":"mesecema":e<5?t||a?"mesece":"meseci":t||a?"mesecev":"meseci");case"y":return t||a?"eno leto":"enim letom";case"yy":return s+(1===e?t||a?"leto":"letom":2===e?t||a?"leti":"letoma":e<5?t||a?"leta":"leti":t||a?"let":"leti")}}Be=[/^sty/i,/^lut/i,/^mar/i,/^kwi/i,/^maj/i,/^cze/i,/^lip/i,/^sie/i,/^wrz/i,/^pa\u017a/i,/^lis/i,/^gru/i],n.defineLocale("pl",{months:function(e,t){return e?(/D MMMM/.test(t)?Ms:ys)[e.month()]:ys},monthsShort:"sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru".split("_"),monthsParse:Be,longMonthsParse:Be,shortMonthsParse:Be,weekdays:"niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota".split("_"),weekdaysShort:"ndz_pon_wt_śr_czw_pt_sob".split("_"),weekdaysMin:"Nd_Pn_Wt_Śr_Cz_Pt_So".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Dziś o] LT",nextDay:"[Jutro o] LT",nextWeek:function(){switch(this.day()){case 0:return"[W niedzielę o] LT";case 2:return"[We wtorek o] LT";case 3:return"[W środę o] LT";case 6:return"[W sobotę o] LT";default:return"[W] dddd [o] LT"}},lastDay:"[Wczoraj o] LT",lastWeek:function(){switch(this.day()){case 0:return"[W zeszłą niedzielę o] LT";case 3:return"[W zeszłą środę o] LT";case 6:return"[W zeszłą sobotę o] LT";default:return"[W zeszły] dddd [o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"%s temu",s:"kilka sekund",ss:Ls,m:Ls,mm:Ls,h:Ls,hh:Ls,d:"1 dzień",dd:"%d dni",w:"tydzień",ww:Ls,M:"miesiąc",MM:Ls,y:"rok",yy:Ls},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("pt-br",{months:"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"domingo_segunda-feira_terça-feira_quarta-feira_quinta-feira_sexta-feira_sábado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_sáb".split("_"),weekdaysMin:"do_2ª_3ª_4ª_5ª_6ª_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [às] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [às] HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"poucos segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",invalidDate:"Data inválida"}),n.defineLocale("pt",{months:"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado".split("_"),weekdaysShort:"Dom_Seg_Ter_Qua_Qui_Sex_Sáb".split("_"),weekdaysMin:"Do_2ª_3ª_4ª_5ª_6ª_Sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",w:"uma semana",ww:"%d semanas",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}\xba/,ordinal:"%dº",week:{dow:1,doy:4}}),n.defineLocale("ro",{months:"ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie".split("_"),monthsShort:"ian._feb._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"duminică_luni_marți_miercuri_joi_vineri_sâmbătă".split("_"),weekdaysShort:"Dum_Lun_Mar_Mie_Joi_Vin_Sâm".split("_"),weekdaysMin:"Du_Lu_Ma_Mi_Jo_Vi_Sâ".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[azi la] LT",nextDay:"[mâine la] LT",nextWeek:"dddd [la] LT",lastDay:"[ieri la] LT",lastWeek:"[fosta] dddd [la] LT",sameElse:"L"},relativeTime:{future:"peste %s",past:"%s în urmă",s:"câteva secunde",ss:Ys,m:"un minut",mm:Ys,h:"o oră",hh:Ys,d:"o zi",dd:Ys,w:"o săptămână",ww:Ys,M:"o lună",MM:Ys,y:"un an",yy:Ys},week:{dow:1,doy:7}}),J=[/^\u044f\u043d\u0432/i,/^\u0444\u0435\u0432/i,/^\u043c\u0430\u0440/i,/^\u0430\u043f\u0440/i,/^\u043c\u0430[\u0439\u044f]/i,/^\u0438\u044e\u043d/i,/^\u0438\u044e\u043b/i,/^\u0430\u0432\u0433/i,/^\u0441\u0435\u043d/i,/^\u043e\u043a\u0442/i,/^\u043d\u043e\u044f/i,/^\u0434\u0435\u043a/i],n.defineLocale("ru",{months:{format:"января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря".split("_"),standalone:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_")},monthsShort:{format:"янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.".split("_"),standalone:"янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.".split("_")},weekdays:{standalone:"воскресенье_понедельник_вторник_среда_четверг_пятница_суббота".split("_"),format:"воскресенье_понедельник_вторник_среду_четверг_пятницу_субботу".split("_"),isFormat:/\[ ?[\u0412\u0432] ?(?:\u043f\u0440\u043e\u0448\u043b\u0443\u044e|\u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0443\u044e|\u044d\u0442\u0443)? ?] ?dddd/},weekdaysShort:"вс_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"вс_пн_вт_ср_чт_пт_сб".split("_"),monthsParse:J,longMonthsParse:J,shortMonthsParse:J,monthsRegex:/^(\u044f\u043d\u0432\u0430\u0440[\u044c\u044f]|\u044f\u043d\u0432\.?|\u0444\u0435\u0432\u0440\u0430\u043b[\u044c\u044f]|\u0444\u0435\u0432\u0440?\.?|\u043c\u0430\u0440\u0442\u0430?|\u043c\u0430\u0440\.?|\u0430\u043f\u0440\u0435\u043b[\u044c\u044f]|\u0430\u043f\u0440\.?|\u043c\u0430[\u0439\u044f]|\u0438\u044e\u043d[\u044c\u044f]|\u0438\u044e\u043d\.?|\u0438\u044e\u043b[\u044c\u044f]|\u0438\u044e\u043b\.?|\u0430\u0432\u0433\u0443\u0441\u0442\u0430?|\u0430\u0432\u0433\.?|\u0441\u0435\u043d\u0442\u044f\u0431\u0440[\u044c\u044f]|\u0441\u0435\u043d\u0442?\.?|\u043e\u043a\u0442\u044f\u0431\u0440[\u044c\u044f]|\u043e\u043a\u0442\.?|\u043d\u043e\u044f\u0431\u0440[\u044c\u044f]|\u043d\u043e\u044f\u0431?\.?|\u0434\u0435\u043a\u0430\u0431\u0440[\u044c\u044f]|\u0434\u0435\u043a\.?)/i,monthsShortRegex:/^(\u044f\u043d\u0432\u0430\u0440[\u044c\u044f]|\u044f\u043d\u0432\.?|\u0444\u0435\u0432\u0440\u0430\u043b[\u044c\u044f]|\u0444\u0435\u0432\u0440?\.?|\u043c\u0430\u0440\u0442\u0430?|\u043c\u0430\u0440\.?|\u0430\u043f\u0440\u0435\u043b[\u044c\u044f]|\u0430\u043f\u0440\.?|\u043c\u0430[\u0439\u044f]|\u0438\u044e\u043d[\u044c\u044f]|\u0438\u044e\u043d\.?|\u0438\u044e\u043b[\u044c\u044f]|\u0438\u044e\u043b\.?|\u0430\u0432\u0433\u0443\u0441\u0442\u0430?|\u0430\u0432\u0433\.?|\u0441\u0435\u043d\u0442\u044f\u0431\u0440[\u044c\u044f]|\u0441\u0435\u043d\u0442?\.?|\u043e\u043a\u0442\u044f\u0431\u0440[\u044c\u044f]|\u043e\u043a\u0442\.?|\u043d\u043e\u044f\u0431\u0440[\u044c\u044f]|\u043d\u043e\u044f\u0431?\.?|\u0434\u0435\u043a\u0430\u0431\u0440[\u044c\u044f]|\u0434\u0435\u043a\.?)/i,monthsStrictRegex:/^(\u044f\u043d\u0432\u0430\u0440[\u044f\u044c]|\u0444\u0435\u0432\u0440\u0430\u043b[\u044f\u044c]|\u043c\u0430\u0440\u0442\u0430?|\u0430\u043f\u0440\u0435\u043b[\u044f\u044c]|\u043c\u0430[\u044f\u0439]|\u0438\u044e\u043d[\u044f\u044c]|\u0438\u044e\u043b[\u044f\u044c]|\u0430\u0432\u0433\u0443\u0441\u0442\u0430?|\u0441\u0435\u043d\u0442\u044f\u0431\u0440[\u044f\u044c]|\u043e\u043a\u0442\u044f\u0431\u0440[\u044f\u044c]|\u043d\u043e\u044f\u0431\u0440[\u044f\u044c]|\u0434\u0435\u043a\u0430\u0431\u0440[\u044f\u044c])/i,monthsShortStrictRegex:/^(\u044f\u043d\u0432\.|\u0444\u0435\u0432\u0440?\.|\u043c\u0430\u0440[\u0442.]|\u0430\u043f\u0440\.|\u043c\u0430[\u044f\u0439]|\u0438\u044e\u043d[\u044c\u044f.]|\u0438\u044e\u043b[\u044c\u044f.]|\u0430\u0432\u0433\.|\u0441\u0435\u043d\u0442?\.|\u043e\u043a\u0442\.|\u043d\u043e\u044f\u0431?\.|\u0434\u0435\u043a\.)/i,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., H:mm",LLLL:"dddd, D MMMM YYYY г., H:mm"},calendar:{sameDay:"[Сегодня, в] LT",nextDay:"[Завтра, в] LT",lastDay:"[Вчера, в] LT",nextWeek:function(e){if(e.week()===this.week())return 2===this.day()?"[Во] dddd, [в] LT":"[В] dddd, [в] LT";switch(this.day()){case 0:return"[В следующее] dddd, [в] LT";case 1:case 2:case 4:return"[В следующий] dddd, [в] LT";case 3:case 5:case 6:return"[В следующую] dddd, [в] LT"}},lastWeek:function(e){if(e.week()===this.week())return 2===this.day()?"[Во] dddd, [в] LT":"[В] dddd, [в] LT";switch(this.day()){case 0:return"[В прошлое] dddd, [в] LT";case 1:case 2:case 4:return"[В прошлый] dddd, [в] LT";case 3:case 5:case 6:return"[В прошлую] dddd, [в] LT"}},sameElse:"L"},relativeTime:{future:"через %s",past:"%s назад",s:"несколько секунд",ss:vs,m:vs,mm:vs,h:"час",hh:vs,d:"день",dd:vs,w:"неделя",ww:vs,M:"месяц",MM:vs,y:"год",yy:vs},meridiemParse:/\u043d\u043e\u0447\u0438|\u0443\u0442\u0440\u0430|\u0434\u043d\u044f|\u0432\u0435\u0447\u0435\u0440\u0430/i,isPM:function(e){return/^(\u0434\u043d\u044f|\u0432\u0435\u0447\u0435\u0440\u0430)$/.test(e)},meridiem:function(e,t,n){return e<4?"ночи":e<12?"утра":e<17?"дня":"вечера"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0439|\u0433\u043e|\u044f)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":return e+"-й";case"D":return e+"-го";case"w":case"W":return e+"-я";default:return e}},week:{dow:1,doy:4}}),ae=["جنوري","فيبروري","مارچ","اپريل","مئي","جون","جولاءِ","آگسٽ","سيپٽمبر","آڪٽوبر","نومبر","ڊسمبر"],Q=["آچر","سومر","اڱارو","اربع","خميس","جمع","ڇنڇر"],n.defineLocale("sd",{months:ae,monthsShort:ae,weekdays:Q,weekdaysShort:Q,weekdaysMin:Q,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/\u0635\u0628\u062d|\u0634\u0627\u0645/,isPM:function(e){return"شام"===e},meridiem:function(e,t,n){return e<12?"صبح":"شام"},calendar:{sameDay:"[اڄ] LT",nextDay:"[سڀاڻي] LT",nextWeek:"dddd [اڳين هفتي تي] LT",lastDay:"[ڪالهه] LT",lastWeek:"[گزريل هفتي] dddd [تي] LT",sameElse:"L"},relativeTime:{future:"%s پوء",past:"%s اڳ",s:"چند سيڪنڊ",ss:"%d سيڪنڊ",m:"هڪ منٽ",mm:"%d منٽ",h:"هڪ ڪلاڪ",hh:"%d ڪلاڪ",d:"هڪ ڏينهن",dd:"%d ڏينهن",M:"هڪ مهينو",MM:"%d مهينا",y:"هڪ سال",yy:"%d سال"},preparse:function(e){return e.replace(/\u060c/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:4}}),n.defineLocale("se",{months:"ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu".split("_"),monthsShort:"ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov".split("_"),weekdays:"sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat".split("_"),weekdaysShort:"sotn_vuos_maŋ_gask_duor_bear_láv".split("_"),weekdaysMin:"s_v_m_g_d_b_L".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"MMMM D. [b.] YYYY",LLL:"MMMM D. [b.] YYYY [ti.] HH:mm",LLLL:"dddd, MMMM D. [b.] YYYY [ti.] HH:mm"},calendar:{sameDay:"[otne ti] LT",nextDay:"[ihttin ti] LT",nextWeek:"dddd [ti] LT",lastDay:"[ikte ti] LT",lastWeek:"[ovddit] dddd [ti] LT",sameElse:"L"},relativeTime:{future:"%s geažes",past:"maŋit %s",s:"moadde sekunddat",ss:"%d sekunddat",m:"okta minuhta",mm:"%d minuhtat",h:"okta diimmu",hh:"%d diimmut",d:"okta beaivi",dd:"%d beaivvit",M:"okta mánnu",MM:"%d mánut",y:"okta jahki",yy:"%d jagit"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("si",{months:"ජනවාරි_පෙබරවාරි_මාර්තු_අප්‍රේල්_මැයි_ජූනි_ජූලි_අගෝස්තු_සැප්තැම්බර්_ඔක්තෝබර්_නොවැම්බර්_දෙසැම්බර්".split("_"),monthsShort:"ජන_පෙබ_මාර්_අප්_මැයි_ජූනි_ජූලි_අගෝ_සැප්_ඔක්_නොවැ_දෙසැ".split("_"),weekdays:"ඉරිදා_සඳුදා_අඟහරුවාදා_බදාදා_බ්‍රහස්පතින්දා_සිකුරාදා_සෙනසුරාදා".split("_"),weekdaysShort:"ඉරි_සඳු_අඟ_බදා_බ්‍රහ_සිකු_සෙන".split("_"),weekdaysMin:"ඉ_ස_අ_බ_බ්‍ර_සි_සෙ".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"a h:mm",LTS:"a h:mm:ss",L:"YYYY/MM/DD",LL:"YYYY MMMM D",LLL:"YYYY MMMM D, a h:mm",LLLL:"YYYY MMMM D [වැනි] dddd, a h:mm:ss"},calendar:{sameDay:"[අද] LT[ට]",nextDay:"[හෙට] LT[ට]",nextWeek:"dddd LT[ට]",lastDay:"[ඊයේ] LT[ට]",lastWeek:"[පසුගිය] dddd LT[ට]",sameElse:"L"},relativeTime:{future:"%sකින්",past:"%sකට පෙර",s:"තත්පර කිහිපය",ss:"තත්පර %d",m:"මිනිත්තුව",mm:"මිනිත්තු %d",h:"පැය",hh:"පැය %d",d:"දිනය",dd:"දින %d",M:"මාසය",MM:"මාස %d",y:"වසර",yy:"වසර %d"},dayOfMonthOrdinalParse:/\d{1,2} \u0dc0\u0dd0\u0db1\u0dd2/,ordinal:function(e){return e+" වැනි"},meridiemParse:/\u0db4\u0dd9\u0dbb \u0dc0\u0dbb\u0dd4|\u0db4\u0dc3\u0dca \u0dc0\u0dbb\u0dd4|\u0db4\u0dd9.\u0dc0|\u0db4.\u0dc0./,isPM:function(e){return"ප.ව."===e||"පස් වරු"===e},meridiem:function(e,t,n){return 11<e?n?"ප.ව.":"පස් වරු":n?"පෙ.ව.":"පෙර වරු"}}),q="január_február_marec_apríl_máj_jún_júl_august_september_október_november_december".split("_"),Te="jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec".split("_"),n.defineLocale("sk",{months:q,monthsShort:Te,weekdays:"nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota".split("_"),weekdaysShort:"ne_po_ut_st_št_pi_so".split("_"),weekdaysMin:"ne_po_ut_st_št_pi_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm"},calendar:{sameDay:"[dnes o] LT",nextDay:"[zajtra o] LT",nextWeek:function(){switch(this.day()){case 0:return"[v nedeľu o] LT";case 1:case 2:return"[v] dddd [o] LT";case 3:return"[v stredu o] LT";case 4:return"[vo štvrtok o] LT";case 5:return"[v piatok o] LT";case 6:return"[v sobotu o] LT"}},lastDay:"[včera o] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulú nedeľu o] LT";case 1:case 2:case 4:case 5:return"[minulý] dddd [o] LT";case 3:return"[minulú stredu o] LT";case 6:return"[minulú sobotu o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"pred %s",s:bs,ss:bs,m:bs,mm:bs,h:bs,hh:bs,d:bs,dd:bs,M:bs,MM:bs,y:bs,yy:bs},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("sl",{months:"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota".split("_"),weekdaysShort:"ned._pon._tor._sre._čet._pet._sob.".split("_"),weekdaysMin:"ne_po_to_sr_če_pe_so".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danes ob] LT",nextDay:"[jutri ob] LT",nextWeek:function(){switch(this.day()){case 0:return"[v] [nedeljo] [ob] LT";case 3:return"[v] [sredo] [ob] LT";case 6:return"[v] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[v] dddd [ob] LT"}},lastDay:"[včeraj ob] LT",lastWeek:function(){switch(this.day()){case 0:return"[prejšnjo] [nedeljo] [ob] LT";case 3:return"[prejšnjo] [sredo] [ob] LT";case 6:return"[prejšnjo] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[prejšnji] dddd [ob] LT"}},sameElse:"L"},relativeTime:{future:"čez %s",past:"pred %s",s:ks,ss:ks,m:ks,mm:ks,h:ks,hh:ks,d:ks,dd:ks,M:ks,MM:ks,y:ks,yy:ks},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}}),n.defineLocale("sq",{months:"Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor".split("_"),monthsShort:"Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj".split("_"),weekdays:"E Diel_E Hënë_E Martë_E Mërkurë_E Enjte_E Premte_E Shtunë".split("_"),weekdaysShort:"Die_Hën_Mar_Mër_Enj_Pre_Sht".split("_"),weekdaysMin:"D_H_Ma_Më_E_P_Sh".split("_"),weekdaysParseExact:!0,meridiemParse:/PD|MD/,isPM:function(e){return"M"===e.charAt(0)},meridiem:function(e,t,n){return e<12?"PD":"MD"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Sot në] LT",nextDay:"[Nesër në] LT",nextWeek:"dddd [në] LT",lastDay:"[Dje në] LT",lastWeek:"dddd [e kaluar në] LT",sameElse:"L"},relativeTime:{future:"në %s",past:"%s më parë",s:"disa sekonda",ss:"%d sekonda",m:"një minutë",mm:"%d minuta",h:"një orë",hh:"%d orë",d:"një ditë",dd:"%d ditë",M:"një muaj",MM:"%d muaj",y:"një vit",yy:"%d vite"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});var Ds={words:{ss:["секунда","секунде","секунди"],m:["један минут","једног минута"],mm:["минут","минута","минута"],h:["један сат","једног сата"],hh:["сат","сата","сати"],d:["један дан","једног дана"],dd:["дан","дана","дана"],M:["један месец","једног месеца"],MM:["месец","месеца","месеци"],y:["једну годину","једне године"],yy:["годину","године","година"]},correctGrammaticalCase:function(e,t){return 1<=e%10&&e%10<=4&&(e%100<10||20<=e%100)?e%10==1?t[0]:t[1]:t[2]},translate:function(e,t,n,a){var s=Ds.words[n];return 1===n.length?"y"===n&&t?"једна година":a||t?s[0]:s[1]:(a=Ds.correctGrammaticalCase(e,s),"yy"===n&&t&&"годину"===a?e+" година":e+" "+a)}},Ts=(n.defineLocale("sr-cyrl",{months:"јануар_фебруар_март_април_мај_јун_јул_август_септембар_октобар_новембар_децембар".split("_"),monthsShort:"јан._феб._мар._апр._мај_јун_јул_авг._сеп._окт._нов._дец.".split("_"),monthsParseExact:!0,weekdays:"недеља_понедељак_уторак_среда_четвртак_петак_субота".split("_"),weekdaysShort:"нед._пон._уто._сре._чет._пет._суб.".split("_"),weekdaysMin:"не_по_ут_ср_че_пе_су".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D. M. YYYY.",LL:"D. MMMM YYYY.",LLL:"D. MMMM YYYY. H:mm",LLLL:"dddd, D. MMMM YYYY. H:mm"},calendar:{sameDay:"[данас у] LT",nextDay:"[сутра у] LT",nextWeek:function(){switch(this.day()){case 0:return"[у] [недељу] [у] LT";case 3:return"[у] [среду] [у] LT";case 6:return"[у] [суботу] [у] LT";case 1:case 2:case 4:case 5:return"[у] dddd [у] LT"}},lastDay:"[јуче у] LT",lastWeek:function(){return["[прошле] [недеље] [у] LT","[прошлог] [понедељка] [у] LT","[прошлог] [уторка] [у] LT","[прошле] [среде] [у] LT","[прошлог] [четвртка] [у] LT","[прошлог] [петка] [у] LT","[прошле] [суботе] [у] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"за %s",past:"пре %s",s:"неколико секунди",ss:Ds.translate,m:Ds.translate,mm:Ds.translate,h:Ds.translate,hh:Ds.translate,d:Ds.translate,dd:Ds.translate,M:Ds.translate,MM:Ds.translate,y:Ds.translate,yy:Ds.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}}),{words:{ss:["sekunda","sekunde","sekundi"],m:["jedan minut","jednog minuta"],mm:["minut","minuta","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],d:["jedan dan","jednog dana"],dd:["dan","dana","dana"],M:["jedan mesec","jednog meseca"],MM:["mesec","meseca","meseci"],y:["jednu godinu","jedne godine"],yy:["godinu","godine","godina"]},correctGrammaticalCase:function(e,t){return 1<=e%10&&e%10<=4&&(e%100<10||20<=e%100)?e%10==1?t[0]:t[1]:t[2]},translate:function(e,t,n,a){var s=Ts.words[n];return 1===n.length?"y"===n&&t?"jedna godina":a||t?s[0]:s[1]:(a=Ts.correctGrammaticalCase(e,s),"yy"===n&&t&&"godinu"===a?e+" godina":e+" "+a)}}),Ss=(n.defineLocale("sr",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedelja_ponedeljak_utorak_sreda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sre._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D. M. YYYY.",LL:"D. MMMM YYYY.",LLL:"D. MMMM YYYY. H:mm",LLLL:"dddd, D. MMMM YYYY. H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedelju] [u] LT";case 3:return"[u] [sredu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){return["[prošle] [nedelje] [u] LT","[prošlog] [ponedeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"pre %s",s:"nekoliko sekundi",ss:Ts.translate,m:Ts.translate,mm:Ts.translate,h:Ts.translate,hh:Ts.translate,d:Ts.translate,dd:Ts.translate,M:Ts.translate,MM:Ts.translate,y:Ts.translate,yy:Ts.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}}),n.defineLocale("ss",{months:"Bhimbidvwane_Indlovana_Indlov'lenkhulu_Mabasa_Inkhwekhweti_Inhlaba_Kholwane_Ingci_Inyoni_Imphala_Lweti_Ingongoni".split("_"),monthsShort:"Bhi_Ina_Inu_Mab_Ink_Inh_Kho_Igc_Iny_Imp_Lwe_Igo".split("_"),weekdays:"Lisontfo_Umsombuluko_Lesibili_Lesitsatfu_Lesine_Lesihlanu_Umgcibelo".split("_"),weekdaysShort:"Lis_Umb_Lsb_Les_Lsi_Lsh_Umg".split("_"),weekdaysMin:"Li_Us_Lb_Lt_Ls_Lh_Ug".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Namuhla nga] LT",nextDay:"[Kusasa nga] LT",nextWeek:"dddd [nga] LT",lastDay:"[Itolo nga] LT",lastWeek:"dddd [leliphelile] [nga] LT",sameElse:"L"},relativeTime:{future:"nga %s",past:"wenteka nga %s",s:"emizuzwana lomcane",ss:"%d mzuzwana",m:"umzuzu",mm:"%d emizuzu",h:"lihora",hh:"%d emahora",d:"lilanga",dd:"%d emalanga",M:"inyanga",MM:"%d tinyanga",y:"umnyaka",yy:"%d iminyaka"},meridiemParse:/ekuseni|emini|entsambama|ebusuku/,meridiem:function(e,t,n){return e<11?"ekuseni":e<15?"emini":e<19?"entsambama":"ebusuku"},meridiemHour:function(e,t){return 12===e&&(e=0),"ekuseni"===t?e:"emini"===t?11<=e?e:e+12:"entsambama"===t||"ebusuku"===t?0===e?0:e+12:void 0},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:"%d",week:{dow:1,doy:4}}),n.defineLocale("sv",{months:"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag".split("_"),weekdaysShort:"sön_mån_tis_ons_tor_fre_lör".split("_"),weekdaysMin:"sö_må_ti_on_to_fr_lö".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [kl.] HH:mm",LLLL:"dddd D MMMM YYYY [kl.] HH:mm",lll:"D MMM YYYY HH:mm",llll:"ddd D MMM YYYY HH:mm"},calendar:{sameDay:"[Idag] LT",nextDay:"[Imorgon] LT",lastDay:"[Igår] LT",nextWeek:"[På] dddd LT",lastWeek:"[I] dddd[s] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"för %s sedan",s:"några sekunder",ss:"%d sekunder",m:"en minut",mm:"%d minuter",h:"en timme",hh:"%d timmar",d:"en dag",dd:"%d dagar",M:"en månad",MM:"%d månader",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}(\:e|\:a)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)||1!=t&&2!=t?":e":":a")},week:{dow:1,doy:4}}),n.defineLocale("sw",{months:"Januari_Februari_Machi_Aprili_Mei_Juni_Julai_Agosti_Septemba_Oktoba_Novemba_Desemba".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ago_Sep_Okt_Nov_Des".split("_"),weekdays:"Jumapili_Jumatatu_Jumanne_Jumatano_Alhamisi_Ijumaa_Jumamosi".split("_"),weekdaysShort:"Jpl_Jtat_Jnne_Jtan_Alh_Ijm_Jmos".split("_"),weekdaysMin:"J2_J3_J4_J5_Al_Ij_J1".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"hh:mm A",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[leo saa] LT",nextDay:"[kesho saa] LT",nextWeek:"[wiki ijayo] dddd [saat] LT",lastDay:"[jana] LT",lastWeek:"[wiki iliyopita] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s baadaye",past:"tokea %s",s:"hivi punde",ss:"sekunde %d",m:"dakika moja",mm:"dakika %d",h:"saa limoja",hh:"masaa %d",d:"siku moja",dd:"siku %d",M:"mwezi mmoja",MM:"miezi %d",y:"mwaka mmoja",yy:"miaka %d"},week:{dow:1,doy:7}}),{1:"௧",2:"௨",3:"௩",4:"௪",5:"௫",6:"௬",7:"௭",8:"௮",9:"௯",0:"௦"}),xs={"௧":"1","௨":"2","௩":"3","௪":"4","௫":"5","௬":"6","௭":"7","௮":"8","௯":"9","௦":"0"},Hs=(n.defineLocale("ta",{months:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),monthsShort:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),weekdays:"ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை".split("_"),weekdaysShort:"ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி".split("_"),weekdaysMin:"ஞா_தி_செ_பு_வி_வெ_ச".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, HH:mm",LLLL:"dddd, D MMMM YYYY, HH:mm"},calendar:{sameDay:"[இன்று] LT",nextDay:"[நாளை] LT",nextWeek:"dddd, LT",lastDay:"[நேற்று] LT",lastWeek:"[கடந்த வாரம்] dddd, LT",sameElse:"L"},relativeTime:{future:"%s இல்",past:"%s முன்",s:"ஒரு சில விநாடிகள்",ss:"%d விநாடிகள்",m:"ஒரு நிமிடம்",mm:"%d நிமிடங்கள்",h:"ஒரு மணி நேரம்",hh:"%d மணி நேரம்",d:"ஒரு நாள்",dd:"%d நாட்கள்",M:"ஒரு மாதம்",MM:"%d மாதங்கள்",y:"ஒரு வருடம்",yy:"%d ஆண்டுகள்"},dayOfMonthOrdinalParse:/\d{1,2}\u0bb5\u0ba4\u0bc1/,ordinal:function(e){return e+"வது"},preparse:function(e){return e.replace(/[\u0be7\u0be8\u0be9\u0bea\u0beb\u0bec\u0bed\u0bee\u0bef\u0be6]/g,(function(e){return xs[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return Ss[e]}))},meridiemParse:/\u0baf\u0bbe\u0bae\u0bae\u0bcd|\u0bb5\u0bc8\u0b95\u0bb1\u0bc8|\u0b95\u0bbe\u0bb2\u0bc8|\u0ba8\u0ba3\u0bcd\u0baa\u0b95\u0bb2\u0bcd|\u0b8e\u0bb1\u0bcd\u0baa\u0bbe\u0b9f\u0bc1|\u0bae\u0bbe\u0bb2\u0bc8/,meridiem:function(e,t,n){return e<2?" யாமம்":e<6?" வைகறை":e<10?" காலை":e<14?" நண்பகல்":e<18?" எற்பாடு":e<22?" மாலை":" யாமம்"},meridiemHour:function(e,t){return 12===e&&(e=0),"யாமம்"===t?e<2?e:e+12:"வைகறை"===t||"காலை"===t||"நண்பகல்"===t&&10<=e?e:e+12},week:{dow:0,doy:6}}),n.defineLocale("te",{months:"జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జులై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్".split("_"),monthsShort:"జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జులై_ఆగ._సెప్._అక్టో._నవ._డిసె.".split("_"),monthsParseExact:!0,weekdays:"ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం".split("_"),weekdaysShort:"ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని".split("_"),weekdaysMin:"ఆ_సో_మం_బు_గు_శు_శ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[నేడు] LT",nextDay:"[రేపు] LT",nextWeek:"dddd, LT",lastDay:"[నిన్న] LT",lastWeek:"[గత] dddd, LT",sameElse:"L"},relativeTime:{future:"%s లో",past:"%s క్రితం",s:"కొన్ని క్షణాలు",ss:"%d సెకన్లు",m:"ఒక నిమిషం",mm:"%d నిమిషాలు",h:"ఒక గంట",hh:"%d గంటలు",d:"ఒక రోజు",dd:"%d రోజులు",M:"ఒక నెల",MM:"%d నెలలు",y:"ఒక సంవత్సరం",yy:"%d సంవత్సరాలు"},dayOfMonthOrdinalParse:/\d{1,2}\u0c35/,ordinal:"%dవ",meridiemParse:/\u0c30\u0c3e\u0c24\u0c4d\u0c30\u0c3f|\u0c09\u0c26\u0c2f\u0c02|\u0c2e\u0c27\u0c4d\u0c2f\u0c3e\u0c39\u0c4d\u0c28\u0c02|\u0c38\u0c3e\u0c2f\u0c02\u0c24\u0c4d\u0c30\u0c02/,meridiemHour:function(e,t){return 12===e&&(e=0),"రాత్రి"===t?e<4?e:e+12:"ఉదయం"===t?e:"మధ్యాహ్నం"===t?10<=e?e:e+12:"సాయంత్రం"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"రాత్రి":e<10?"ఉదయం":e<17?"మధ్యాహ్నం":e<20?"సాయంత్రం":"రాత్రి"},week:{dow:0,doy:6}}),n.defineLocale("tet",{months:"Janeiru_Fevereiru_Marsu_Abril_Maiu_Juñu_Jullu_Agustu_Setembru_Outubru_Novembru_Dezembru".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingu_Segunda_Tersa_Kuarta_Kinta_Sesta_Sabadu".split("_"),weekdaysShort:"Dom_Seg_Ters_Kua_Kint_Sest_Sab".split("_"),weekdaysMin:"Do_Seg_Te_Ku_Ki_Ses_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Ohin iha] LT",nextDay:"[Aban iha] LT",nextWeek:"dddd [iha] LT",lastDay:"[Horiseik iha] LT",lastWeek:"dddd [semana kotuk] [iha] LT",sameElse:"L"},relativeTime:{future:"iha %s",past:"%s liuba",s:"segundu balun",ss:"segundu %d",m:"minutu ida",mm:"minutu %d",h:"oras ida",hh:"oras %d",d:"loron ida",dd:"loron %d",M:"fulan ida",MM:"fulan %d",y:"tinan ida",yy:"tinan %d"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")},week:{dow:1,doy:4}}),{0:"-ум",1:"-ум",2:"-юм",3:"-юм",4:"-ум",5:"-ум",6:"-ум",7:"-ум",8:"-ум",9:"-ум",10:"-ум",12:"-ум",13:"-ум",20:"-ум",30:"-юм",40:"-ум",50:"-ум",60:"-ум",70:"-ум",80:"-ум",90:"-ум",100:"-ум"}),js=(n.defineLocale("tg",{months:{format:"январи_феврали_марти_апрели_майи_июни_июли_августи_сентябри_октябри_ноябри_декабри".split("_"),standalone:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_")},monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"якшанбе_душанбе_сешанбе_чоршанбе_панҷшанбе_ҷумъа_шанбе".split("_"),weekdaysShort:"яшб_дшб_сшб_чшб_пшб_ҷум_шнб".split("_"),weekdaysMin:"яш_дш_сш_чш_пш_ҷм_шб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Имрӯз соати] LT",nextDay:"[Фардо соати] LT",lastDay:"[Дирӯз соати] LT",nextWeek:"dddd[и] [ҳафтаи оянда соати] LT",lastWeek:"dddd[и] [ҳафтаи гузашта соати] LT",sameElse:"L"},relativeTime:{future:"баъди %s",past:"%s пеш",s:"якчанд сония",m:"як дақиқа",mm:"%d дақиқа",h:"як соат",hh:"%d соат",d:"як рӯз",dd:"%d рӯз",M:"як моҳ",MM:"%d моҳ",y:"як сол",yy:"%d сол"},meridiemParse:/\u0448\u0430\u0431|\u0441\u0443\u0431\u04b3|\u0440\u04ef\u0437|\u0431\u0435\u0433\u043e\u04b3/,meridiemHour:function(e,t){return 12===e&&(e=0),"шаб"===t?e<4?e:e+12:"субҳ"===t?e:"рӯз"===t?11<=e?e:e+12:"бегоҳ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"шаб":e<11?"субҳ":e<16?"рӯз":e<19?"бегоҳ":"шаб"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0443\u043c|\u044e\u043c)/,ordinal:function(e){return e+(Hs[e]||Hs[e%10]||Hs[100<=e?100:null])},week:{dow:1,doy:7}}),n.defineLocale("th",{months:"มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม".split("_"),monthsShort:"ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.".split("_"),monthsParseExact:!0,weekdays:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์".split("_"),weekdaysShort:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์".split("_"),weekdaysMin:"อา._จ._อ._พ._พฤ._ศ._ส.".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY เวลา H:mm",LLLL:"วันddddที่ D MMMM YYYY เวลา H:mm"},meridiemParse:/\u0e01\u0e48\u0e2d\u0e19\u0e40\u0e17\u0e35\u0e48\u0e22\u0e07|\u0e2b\u0e25\u0e31\u0e07\u0e40\u0e17\u0e35\u0e48\u0e22\u0e07/,isPM:function(e){return"หลังเที่ยง"===e},meridiem:function(e,t,n){return e<12?"ก่อนเที่ยง":"หลังเที่ยง"},calendar:{sameDay:"[วันนี้ เวลา] LT",nextDay:"[พรุ่งนี้ เวลา] LT",nextWeek:"dddd[หน้า เวลา] LT",lastDay:"[เมื่อวานนี้ เวลา] LT",lastWeek:"[วัน]dddd[ที่แล้ว เวลา] LT",sameElse:"L"},relativeTime:{future:"อีก %s",past:"%sที่แล้ว",s:"ไม่กี่วินาที",ss:"%d วินาที",m:"1 นาที",mm:"%d นาที",h:"1 ชั่วโมง",hh:"%d ชั่วโมง",d:"1 วัน",dd:"%d วัน",w:"1 สัปดาห์",ww:"%d สัปดาห์",M:"1 เดือน",MM:"%d เดือน",y:"1 ปี",yy:"%d ปี"}}),{1:"'inji",5:"'inji",8:"'inji",70:"'inji",80:"'inji",2:"'nji",7:"'nji",20:"'nji",50:"'nji",3:"'ünji",4:"'ünji",100:"'ünji",6:"'njy",9:"'unjy",10:"'unjy",30:"'unjy",60:"'ynjy",90:"'ynjy"}),Os=(n.defineLocale("tk",{months:"Ýanwar_Fewral_Mart_Aprel_Maý_Iýun_Iýul_Awgust_Sentýabr_Oktýabr_Noýabr_Dekabr".split("_"),monthsShort:"Ýan_Few_Mar_Apr_Maý_Iýn_Iýl_Awg_Sen_Okt_Noý_Dek".split("_"),weekdays:"Ýekşenbe_Duşenbe_Sişenbe_Çarşenbe_Penşenbe_Anna_Şenbe".split("_"),weekdaysShort:"Ýek_Duş_Siş_Çar_Pen_Ann_Şen".split("_"),weekdaysMin:"Ýk_Dş_Sş_Çr_Pn_An_Şn".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün sagat] LT",nextDay:"[ertir sagat] LT",nextWeek:"[indiki] dddd [sagat] LT",lastDay:"[düýn] LT",lastWeek:"[geçen] dddd [sagat] LT",sameElse:"L"},relativeTime:{future:"%s soň",past:"%s öň",s:"birnäçe sekunt",m:"bir minut",mm:"%d minut",h:"bir sagat",hh:"%d sagat",d:"bir gün",dd:"%d gün",M:"bir aý",MM:"%d aý",y:"bir ýyl",yy:"%d ýyl"},ordinal:function(e,t){switch(t){case"d":case"D":case"Do":case"DD":return e;default:var n;return 0===e?e+"'unjy":e+(js[n=e%10]||js[e%100-n]||js[100<=e?100:null])}},week:{dow:1,doy:7}}),n.defineLocale("tl-ph",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY HH:mm",LLLL:"dddd, MMMM DD, YYYY HH:mm"},calendar:{sameDay:"LT [ngayong araw]",nextDay:"[Bukas ng] LT",nextWeek:"LT [sa susunod na] dddd",lastDay:"LT [kahapon]",lastWeek:"LT [noong nakaraang] dddd",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",ss:"%d segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}}),"pagh_wa’_cha’_wej_loS_vagh_jav_Soch_chorgh_Hut".split("_"));function Ps(e,t,n,a){var s=function(e){var t=Math.floor(e%1e3/100),n=Math.floor(e%100/10),a=(e%=10,"");return 0<t&&(a+=Os[t]+"vatlh"),0<n&&(a+=(""!==a?" ":"")+Os[n]+"maH"),0<e&&(a+=(""!==a?" ":"")+Os[e]),""===a?"pagh":a}(e);switch(n){case"ss":return s+" lup";case"mm":return s+" tup";case"hh":return s+" rep";case"dd":return s+" jaj";case"MM":return s+" jar";case"yy":return s+" DIS"}}n.defineLocale("tlh",{months:"tera’ jar wa’_tera’ jar cha’_tera’ jar wej_tera’ jar loS_tera’ jar vagh_tera’ jar jav_tera’ jar Soch_tera’ jar chorgh_tera’ jar Hut_tera’ jar wa’maH_tera’ jar wa’maH wa’_tera’ jar wa’maH cha’".split("_"),monthsShort:"jar wa’_jar cha’_jar wej_jar loS_jar vagh_jar jav_jar Soch_jar chorgh_jar Hut_jar wa’maH_jar wa’maH wa’_jar wa’maH cha’".split("_"),monthsParseExact:!0,weekdays:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysShort:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysMin:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[DaHjaj] LT",nextDay:"[wa’leS] LT",nextWeek:"LLL",lastDay:"[wa’Hu’] LT",lastWeek:"LLL",sameElse:"L"},relativeTime:{future:function(e){var t=e;return-1!==e.indexOf("jaj")?t.slice(0,-3)+"leS":-1!==e.indexOf("jar")?t.slice(0,-3)+"waQ":-1!==e.indexOf("DIS")?t.slice(0,-3)+"nem":t+" pIq"},past:function(e){var t=e;return-1!==e.indexOf("jaj")?t.slice(0,-3)+"Hu’":-1!==e.indexOf("jar")?t.slice(0,-3)+"wen":-1!==e.indexOf("DIS")?t.slice(0,-3)+"ben":t+" ret"},s:"puS lup",ss:Ps,m:"wa’ tup",mm:Ps,h:"wa’ rep",hh:Ps,d:"wa’ jaj",dd:Ps,M:"wa’ jar",MM:Ps,y:"wa’ DIS",yy:Ps},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});var Es={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"};function As(e,t,n,a){return e={s:["viensas secunds","'iensas secunds"],ss:[e+" secunds",e+" secunds"],m:["'n míut","'iens míut"],mm:[e+" míuts",e+" míuts"],h:["'n þora","'iensa þora"],hh:[e+" þoras",e+" þoras"],d:["'n ziua","'iensa ziua"],dd:[e+" ziuas",e+" ziuas"],M:["'n mes","'iens mes"],MM:[e+" mesen",e+" mesen"],y:["'n ar","'iens ar"],yy:[e+" ars",e+" ars"]},a||t?e[n][0]:e[n][1]}function Cs(e,t,n){return"m"===n?t?"хвилина":"хвилину":"h"===n?t?"година":"годину":e+" "+(e=+e,t=(t={ss:t?"секунда_секунди_секунд":"секунду_секунди_секунд",mm:t?"хвилина_хвилини_хвилин":"хвилину_хвилини_хвилин",hh:t?"година_години_годин":"годину_години_годин",dd:"день_дні_днів",MM:"місяць_місяці_місяців",yy:"рік_роки_років"}[n]).split("_"),e%10==1&&e%100!=11?t[0]:2<=e%10&&e%10<=4&&(e%100<10||20<=e%100)?t[1]:t[2])}function Ws(e){return function(){return e+"о"+(11===this.hours()?"б":"")+"] LT"}}return n.defineLocale("tr",{months:"Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık".split("_"),monthsShort:"Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara".split("_"),weekdays:"Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi".split("_"),weekdaysShort:"Paz_Pzt_Sal_Çar_Per_Cum_Cmt".split("_"),weekdaysMin:"Pz_Pt_Sa_Ça_Pe_Cu_Ct".split("_"),meridiem:function(e,t,n){return e<12?n?"öö":"ÖÖ":n?"ös":"ÖS"},meridiemParse:/\xf6\xf6|\xd6\xd6|\xf6s|\xd6S/,isPM:function(e){return"ös"===e||"ÖS"===e},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[yarın saat] LT",nextWeek:"[gelecek] dddd [saat] LT",lastDay:"[dün] LT",lastWeek:"[geçen] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s önce",s:"birkaç saniye",ss:"%d saniye",m:"bir dakika",mm:"%d dakika",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",w:"bir hafta",ww:"%d hafta",M:"bir ay",MM:"%d ay",y:"bir yıl",yy:"%d yıl"},ordinal:function(e,t){switch(t){case"d":case"D":case"Do":case"DD":return e;default:var n;return 0===e?e+"'ıncı":e+(Es[n=e%10]||Es[e%100-n]||Es[100<=e?100:null])}},week:{dow:1,doy:7}}),n.defineLocale("tzl",{months:"Januar_Fevraglh_Març_Avrïu_Mai_Gün_Julia_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar".split("_"),monthsShort:"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec".split("_"),weekdays:"Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi".split("_"),weekdaysShort:"Súl_Lún_Mai_Már_Xhú_Vié_Sát".split("_"),weekdaysMin:"Sú_Lú_Ma_Má_Xh_Vi_Sá".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"D. MMMM [dallas] YYYY",LLL:"D. MMMM [dallas] YYYY HH.mm",LLLL:"dddd, [li] D. MMMM [dallas] YYYY HH.mm"},meridiemParse:/d\'o|d\'a/i,isPM:function(e){return"d'o"===e.toLowerCase()},meridiem:function(e,t,n){return 11<e?n?"d'o":"D'O":n?"d'a":"D'A"},calendar:{sameDay:"[oxhi à] LT",nextDay:"[demà à] LT",nextWeek:"dddd [à] LT",lastDay:"[ieiri à] LT",lastWeek:"[sür el] dddd [lasteu à] LT",sameElse:"L"},relativeTime:{future:"osprei %s",past:"ja%s",s:As,ss:As,m:As,mm:As,h:As,hh:As,d:As,dd:As,M:As,MM:As,y:As,yy:As},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}}),n.defineLocale("tzm-latn",{months:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),monthsShort:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),weekdays:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysShort:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysMin:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[asdkh g] LT",nextDay:"[aska g] LT",nextWeek:"dddd [g] LT",lastDay:"[assant g] LT",lastWeek:"dddd [g] LT",sameElse:"L"},relativeTime:{future:"dadkh s yan %s",past:"yan %s",s:"imik",ss:"%d imik",m:"minuḍ",mm:"%d minuḍ",h:"saɛa",hh:"%d tassaɛin",d:"ass",dd:"%d ossan",M:"ayowr",MM:"%d iyyirn",y:"asgas",yy:"%d isgasn"},week:{dow:6,doy:12}}),n.defineLocale("tzm",{months:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),monthsShort:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),weekdays:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysShort:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysMin:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ⴰⵙⴷⵅ ⴴ] LT",nextDay:"[ⴰⵙⴽⴰ ⴴ] LT",nextWeek:"dddd [ⴴ] LT",lastDay:"[ⴰⵚⴰⵏⵜ ⴴ] LT",lastWeek:"dddd [ⴴ] LT",sameElse:"L"},relativeTime:{future:"ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s",past:"ⵢⴰⵏ %s",s:"ⵉⵎⵉⴽ",ss:"%d ⵉⵎⵉⴽ",m:"ⵎⵉⵏⵓⴺ",mm:"%d ⵎⵉⵏⵓⴺ",h:"ⵙⴰⵄⴰ",hh:"%d ⵜⴰⵙⵙⴰⵄⵉⵏ",d:"ⴰⵙⵙ",dd:"%d oⵙⵙⴰⵏ",M:"ⴰⵢoⵓⵔ",MM:"%d ⵉⵢⵢⵉⵔⵏ",y:"ⴰⵙⴳⴰⵙ",yy:"%d ⵉⵙⴳⴰⵙⵏ"},week:{dow:6,doy:12}}),n.defineLocale("ug-cn",{months:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),monthsShort:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),weekdays:"يەكشەنبە_دۈشەنبە_سەيشەنبە_چارشەنبە_پەيشەنبە_جۈمە_شەنبە".split("_"),weekdaysShort:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),weekdaysMin:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY-يىلىM-ئاينىڭD-كۈنى",LLL:"YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm",LLLL:"dddd، YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm"},meridiemParse:/\u064a\u06d0\u0631\u0649\u0645 \u0643\u06d0\u0686\u06d5|\u0633\u06d5\u06be\u06d5\u0631|\u0686\u06c8\u0634\u062a\u0649\u0646 \u0628\u06c7\u0631\u06c7\u0646|\u0686\u06c8\u0634|\u0686\u06c8\u0634\u062a\u0649\u0646 \u0643\u06d0\u064a\u0649\u0646|\u0643\u06d5\u0686/,meridiemHour:function(e,t){return 12===e&&(e=0),"يېرىم كېچە"===t||"سەھەر"===t||"چۈشتىن بۇرۇن"===t||"چۈشتىن كېيىن"!==t&&"كەچ"!==t&&11<=e?e:e+12},meridiem:function(e,t,n){return(e=100*e+t)<600?"يېرىم كېچە":e<900?"سەھەر":e<1130?"چۈشتىن بۇرۇن":e<1230?"چۈش":e<1800?"چۈشتىن كېيىن":"كەچ"},calendar:{sameDay:"[بۈگۈن سائەت] LT",nextDay:"[ئەتە سائەت] LT",nextWeek:"[كېلەركى] dddd [سائەت] LT",lastDay:"[تۆنۈگۈن] LT",lastWeek:"[ئالدىنقى] dddd [سائەت] LT",sameElse:"L"},relativeTime:{future:"%s كېيىن",past:"%s بۇرۇن",s:"نەچچە سېكونت",ss:"%d سېكونت",m:"بىر مىنۇت",mm:"%d مىنۇت",h:"بىر سائەت",hh:"%d سائەت",d:"بىر كۈن",dd:"%d كۈن",M:"بىر ئاي",MM:"%d ئاي",y:"بىر يىل",yy:"%d يىل"},dayOfMonthOrdinalParse:/\d{1,2}(-\u0643\u06c8\u0646\u0649|-\u0626\u0627\u064a|-\u06be\u06d5\u067e\u062a\u06d5)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"-كۈنى";case"w":case"W":return e+"-ھەپتە";default:return e}},preparse:function(e){return e.replace(/\u060c/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:7}}),n.defineLocale("uk",{months:{format:"січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня".split("_"),standalone:"січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень".split("_")},monthsShort:"січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд".split("_"),weekdays:function(e,t){var n={nominative:"неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота".split("_"),accusative:"неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу".split("_"),genitive:"неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи".split("_")};return!0===e?n.nominative.slice(1,7).concat(n.nominative.slice(0,1)):e?n[/(\[[\u0412\u0432\u0423\u0443]\]) ?dddd/.test(t)?"accusative":/\[?(?:\u043c\u0438\u043d\u0443\u043b\u043e\u0457|\u043d\u0430\u0441\u0442\u0443\u043f\u043d\u043e\u0457)? ?\] ?dddd/.test(t)?"genitive":"nominative"][e.day()]:n.nominative},weekdaysShort:"нд_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY р.",LLL:"D MMMM YYYY р., HH:mm",LLLL:"dddd, D MMMM YYYY р., HH:mm"},calendar:{sameDay:Ws("[Сьогодні "),nextDay:Ws("[Завтра "),lastDay:Ws("[Вчора "),nextWeek:Ws("[У] dddd ["),lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return Ws("[Минулої] dddd [").call(this);case 1:case 2:case 4:return Ws("[Минулого] dddd [").call(this)}},sameElse:"L"},relativeTime:{future:"за %s",past:"%s тому",s:"декілька секунд",ss:Cs,m:Cs,mm:Cs,h:"годину",hh:Cs,d:"день",dd:Cs,M:"місяць",MM:Cs,y:"рік",yy:Cs},meridiemParse:/\u043d\u043e\u0447\u0456|\u0440\u0430\u043d\u043a\u0443|\u0434\u043d\u044f|\u0432\u0435\u0447\u043e\u0440\u0430/,isPM:function(e){return/^(\u0434\u043d\u044f|\u0432\u0435\u0447\u043e\u0440\u0430)$/.test(e)},meridiem:function(e,t,n){return e<4?"ночі":e<12?"ранку":e<17?"дня":"вечора"},dayOfMonthOrdinalParse:/\d{1,2}-(\u0439|\u0433\u043e)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return e+"-й";case"D":return e+"-го";default:return e}},week:{dow:1,doy:7}}),Z=["جنوری","فروری","مارچ","اپریل","مئی","جون","جولائی","اگست","ستمبر","اکتوبر","نومبر","دسمبر"],vn=["اتوار","پیر","منگل","بدھ","جمعرات","جمعہ","ہفتہ"],n.defineLocale("ur",{months:Z,monthsShort:Z,weekdays:vn,weekdaysShort:vn,weekdaysMin:vn,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/\u0635\u0628\u062d|\u0634\u0627\u0645/,isPM:function(e){return"شام"===e},meridiem:function(e,t,n){return e<12?"صبح":"شام"},calendar:{sameDay:"[آج بوقت] LT",nextDay:"[کل بوقت] LT",nextWeek:"dddd [بوقت] LT",lastDay:"[گذشتہ روز بوقت] LT",lastWeek:"[گذشتہ] dddd [بوقت] LT",sameElse:"L"},relativeTime:{future:"%s بعد",past:"%s قبل",s:"چند سیکنڈ",ss:"%d سیکنڈ",m:"ایک منٹ",mm:"%d منٹ",h:"ایک گھنٹہ",hh:"%d گھنٹے",d:"ایک دن",dd:"%d دن",M:"ایک ماہ",MM:"%d ماہ",y:"ایک سال",yy:"%d سال"},preparse:function(e){return e.replace(/\u060c/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:4}}),n.defineLocale("uz-latn",{months:"Yanvar_Fevral_Mart_Aprel_May_Iyun_Iyul_Avgust_Sentabr_Oktabr_Noyabr_Dekabr".split("_"),monthsShort:"Yan_Fev_Mar_Apr_May_Iyun_Iyul_Avg_Sen_Okt_Noy_Dek".split("_"),weekdays:"Yakshanba_Dushanba_Seshanba_Chorshanba_Payshanba_Juma_Shanba".split("_"),weekdaysShort:"Yak_Dush_Sesh_Chor_Pay_Jum_Shan".split("_"),weekdaysMin:"Ya_Du_Se_Cho_Pa_Ju_Sha".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Bugun soat] LT [da]",nextDay:"[Ertaga] LT [da]",nextWeek:"dddd [kuni soat] LT [da]",lastDay:"[Kecha soat] LT [da]",lastWeek:"[O'tgan] dddd [kuni soat] LT [da]",sameElse:"L"},relativeTime:{future:"Yaqin %s ichida",past:"Bir necha %s oldin",s:"soniya",ss:"%d soniya",m:"bir daqiqa",mm:"%d daqiqa",h:"bir soat",hh:"%d soat",d:"bir kun",dd:"%d kun",M:"bir oy",MM:"%d oy",y:"bir yil",yy:"%d yil"},week:{dow:1,doy:7}}),n.defineLocale("uz",{months:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_"),monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"Якшанба_Душанба_Сешанба_Чоршанба_Пайшанба_Жума_Шанба".split("_"),weekdaysShort:"Якш_Душ_Сеш_Чор_Пай_Жум_Шан".split("_"),weekdaysMin:"Як_Ду_Се_Чо_Па_Жу_Ша".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Бугун соат] LT [да]",nextDay:"[Эртага] LT [да]",nextWeek:"dddd [куни соат] LT [да]",lastDay:"[Кеча соат] LT [да]",lastWeek:"[Утган] dddd [куни соат] LT [да]",sameElse:"L"},relativeTime:{future:"Якин %s ичида",past:"Бир неча %s олдин",s:"фурсат",ss:"%d фурсат",m:"бир дакика",mm:"%d дакика",h:"бир соат",hh:"%d соат",d:"бир кун",dd:"%d кун",M:"бир ой",MM:"%d ой",y:"бир йил",yy:"%d йил"},week:{dow:1,doy:7}}),n.defineLocale("vi",{months:"tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12".split("_"),monthsShort:"Thg 01_Thg 02_Thg 03_Thg 04_Thg 05_Thg 06_Thg 07_Thg 08_Thg 09_Thg 10_Thg 11_Thg 12".split("_"),monthsParseExact:!0,weekdays:"chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy".split("_"),weekdaysShort:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysMin:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysParseExact:!0,meridiemParse:/sa|ch/i,isPM:function(e){return/^ch$/i.test(e)},meridiem:function(e,t,n){return e<12?n?"sa":"SA":n?"ch":"CH"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [năm] YYYY",LLL:"D MMMM [năm] YYYY HH:mm",LLLL:"dddd, D MMMM [năm] YYYY HH:mm",l:"DD/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[Hôm nay lúc] LT",nextDay:"[Ngày mai lúc] LT",nextWeek:"dddd [tuần tới lúc] LT",lastDay:"[Hôm qua lúc] LT",lastWeek:"dddd [tuần trước lúc] LT",sameElse:"L"},relativeTime:{future:"%s tới",past:"%s trước",s:"vài giây",ss:"%d giây",m:"một phút",mm:"%d phút",h:"một giờ",hh:"%d giờ",d:"một ngày",dd:"%d ngày",w:"một tuần",ww:"%d tuần",M:"một tháng",MM:"%d tháng",y:"một năm",yy:"%d năm"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}}),n.defineLocale("x-pseudo",{months:"J~áñúá~rý_F~ébrú~árý_~Márc~h_Áp~ríl_~Máý_~Júñé~_Júl~ý_Áú~gúst~_Sép~témb~ér_Ó~ctób~ér_Ñ~óvém~bér_~Décé~mbér".split("_"),monthsShort:"J~áñ_~Féb_~Már_~Ápr_~Máý_~Júñ_~Júl_~Áúg_~Sép_~Óct_~Ñóv_~Déc".split("_"),monthsParseExact:!0,weekdays:"S~úñdá~ý_Mó~ñdáý~_Túé~sdáý~_Wéd~ñésd~áý_T~húrs~dáý_~Fríd~áý_S~átúr~dáý".split("_"),weekdaysShort:"S~úñ_~Móñ_~Túé_~Wéd_~Thú_~Frí_~Sát".split("_"),weekdaysMin:"S~ú_Mó~_Tú_~Wé_T~h_Fr~_Sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[T~ódá~ý át] LT",nextDay:"[T~ómó~rró~w át] LT",nextWeek:"dddd [át] LT",lastDay:"[Ý~ést~érdá~ý át] LT",lastWeek:"[L~ást] dddd [át] LT",sameElse:"L"},relativeTime:{future:"í~ñ %s",past:"%s á~gó",s:"á ~féw ~sécó~ñds",ss:"%d s~écóñ~ds",m:"á ~míñ~úté",mm:"%d m~íñú~tés",h:"á~ñ hó~úr",hh:"%d h~óúrs",d:"á ~dáý",dd:"%d d~áýs",M:"á ~móñ~th",MM:"%d m~óñt~hs",y:"á ~ýéár",yy:"%d ý~éárs"},dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")},week:{dow:1,doy:4}}),n.defineLocale("yo",{months:"Sẹ́rẹ́_Èrèlè_Ẹrẹ̀nà_Ìgbé_Èbibi_Òkùdu_Agẹmo_Ògún_Owewe_Ọ̀wàrà_Bélú_Ọ̀pẹ̀̀".split("_"),monthsShort:"Sẹ́r_Èrl_Ẹrn_Ìgb_Èbi_Òkù_Agẹ_Ògú_Owe_Ọ̀wà_Bél_Ọ̀pẹ̀̀".split("_"),weekdays:"Àìkú_Ajé_Ìsẹ́gun_Ọjọ́rú_Ọjọ́bọ_Ẹtì_Àbámẹ́ta".split("_"),weekdaysShort:"Àìk_Ajé_Ìsẹ́_Ọjr_Ọjb_Ẹtì_Àbá".split("_"),weekdaysMin:"Àì_Aj_Ìs_Ọr_Ọb_Ẹt_Àb".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Ònì ni] LT",nextDay:"[Ọ̀la ni] LT",nextWeek:"dddd [Ọsẹ̀ tón'bọ] [ni] LT",lastDay:"[Àna ni] LT",lastWeek:"dddd [Ọsẹ̀ tólọ́] [ni] LT",sameElse:"L"},relativeTime:{future:"ní %s",past:"%s kọjá",s:"ìsẹjú aayá die",ss:"aayá %d",m:"ìsẹjú kan",mm:"ìsẹjú %d",h:"wákati kan",hh:"wákati %d",d:"ọjọ́ kan",dd:"ọjọ́ %d",M:"osù kan",MM:"osù %d",y:"ọdún kan",yy:"ọdún %d"},dayOfMonthOrdinalParse:/\u1ecdj\u1ecd\u0301\s\d{1,2}/,ordinal:"ọjọ́ %d",week:{dow:1,doy:4}}),n.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/\u51cc\u6668|\u65e9\u4e0a|\u4e0a\u5348|\u4e2d\u5348|\u4e0b\u5348|\u665a\u4e0a/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t||"下午"!==t&&"晚上"!==t&&11<=e?e:e+12},meridiem:function(e,t,n){return(e=100*e+t)<600?"凌晨":e<900?"早上":e<1130?"上午":e<1230?"中午":e<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:function(e){return e.week()!==this.week()?"[下]dddLT":"[本]dddLT"},lastDay:"[昨天]LT",lastWeek:function(e){return this.week()!==e.week()?"[上]dddLT":"[本]dddLT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(\u65e5|\u6708|\u5468)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"周";default:return e}},relativeTime:{future:"%s后",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",w:"1 周",ww:"%d 周",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}}),n.defineLocale("zh-hk",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/\u51cc\u6668|\u65e9\u4e0a|\u4e0a\u5348|\u4e2d\u5348|\u4e0b\u5348|\u665a\u4e0a/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?11<=e?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,n){return(e=100*e+t)<600?"凌晨":e<900?"早上":e<1200?"上午":1200===e?"中午":e<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:"[下]ddddLT",lastDay:"[昨天]LT",lastWeek:"[上]ddddLT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(\u65e5|\u6708|\u9031)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}}),n.defineLocale("zh-mo",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"D/M/YYYY",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/\u51cc\u6668|\u65e9\u4e0a|\u4e0a\u5348|\u4e2d\u5348|\u4e0b\u5348|\u665a\u4e0a/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?11<=e?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,n){return(e=100*e+t)<600?"凌晨":e<900?"早上":e<1130?"上午":e<1230?"中午":e<1800?"下午":"晚上"},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(\u65e5|\u6708|\u9031)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s內",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}}),n.defineLocale("zh-tw",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/\u51cc\u6668|\u65e9\u4e0a|\u4e0a\u5348|\u4e2d\u5348|\u4e0b\u5348|\u665a\u4e0a/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?11<=e?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,n){return(e=100*e+t)<600?"凌晨":e<900?"早上":e<1130?"上午":e<1230?"中午":e<1800?"下午":"晚上"},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(\u65e5|\u6708|\u9031)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}}),n.locale("en"),n}()},3982:function(e,t,n){e.exports=function(e,t,n,a){"use strict";const s="TemplateFactory",r={allowList:n.DefaultAllowlist,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},i={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},o={entry:"(string|element|function|null)",selector:"(string|element)"};class d extends t{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return r}static get DefaultType(){return i}static get NAME(){return s}getContent(){return Object.values(this._config.content).map((e=>this._resolvePossibleFunction(e))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[t,n]of Object.entries(this._config.content))this._setContent(e,n,t);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},o)}_setContent(t,n,s){const r=e.findOne(s,t);r&&((n=this._resolvePossibleFunction(n))?a.isElement(n)?this._putElementInTemplate(a.getElement(n),r):this._config.html?r.innerHTML=this._maybeSanitize(n):r.textContent=n:r.remove())}_maybeSanitize(e){return this._config.sanitize?n.sanitizeHtml(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return a.execute(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}return d}(n(5411),n(2105),n(2812),n(4035))},4035:function(e,t){!function(e){"use strict";const t=1e6,n=1e3,a="transitionend",s=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,((e,t)=>`#${CSS.escape(t)}`))),e),r=e=>null==e?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),i=e=>{do{e+=Math.floor(Math.random()*t)}while(document.getElementById(e));return e},o=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:a}=window.getComputedStyle(e);const s=Number.parseFloat(t),r=Number.parseFloat(a);return s||r?(t=t.split(",")[0],a=a.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(a))*n):0},d=e=>{e.dispatchEvent(new Event(a))},u=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),l=e=>u(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(s(e)):null,c=e=>{if(!u(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const t=e.closest("summary");if(t&&t.parentNode!==n)return!1;if(null===t)return!1}return t},_=e=>!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),m=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?m(e.parentNode):null},h=()=>{},p=e=>{e.offsetHeight},f=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,y=[],M=e=>{"loading"===document.readyState?(y.length||document.addEventListener("DOMContentLoaded",(()=>{for(const e of y)e()})),y.push(e)):e()},g=()=>"rtl"===document.documentElement.dir,L=e=>{M((()=>{const t=f();if(t){const n=e.NAME,a=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=a,e.jQueryInterface)}}))},Y=(e,t=[],n=e)=>"function"==typeof e?e.call(...t):n,v=(e,t,n=!0)=>{if(!n)return void Y(e);const s=5,r=o(t)+s;let i=!1;const u=({target:n})=>{n===t&&(i=!0,t.removeEventListener(a,u),Y(e))};t.addEventListener(a,u),setTimeout((()=>{i||d(t)}),r)},w=(e,t,n,a)=>{const s=e.length;let r=e.indexOf(t);return-1===r?!n&&a?e[s-1]:e[0]:(r+=n?1:-1,a&&(r=(r+s)%s),e[Math.max(0,Math.min(r,s-1))])};e.defineJQueryPlugin=L,e.execute=Y,e.executeAfterTransition=v,e.findShadowRoot=m,e.getElement=l,e.getNextActiveElement=w,e.getTransitionDurationFromElement=o,e.getUID=i,e.getjQuery=f,e.isDisabled=_,e.isElement=u,e.isRTL=g,e.isVisible=c,e.noop=h,e.onDOMContentLoaded=M,e.parseSelector=s,e.reflow=p,e.toType=r,e.triggerTransitionEnd=d,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(t)},4174:(e,t,n)=>{var a,s=n(1669);(a=s).address=function(){var e,t,n,s,r=function(e){var t=a.extend(a.Event(e),function(){for(var e={},t=a.address.parameterNames(),n=0,s=t.length;n<s;n++)e[t[n]]=a.address.parameter(t[n]);return{value:a.address.value(),path:a.address.path(),pathNames:a.address.pathNames(),parameterNames:t,parameters:e,queryString:a.address.queryString()}}.call(a.address));return a(a.address).trigger(t),t},i=function(e){return Array.prototype.slice.call(e)},o=function(e,t,n){return a().bind.apply(a(a.address),Array.prototype.slice.call(arguments)),a.address},d=function(){return W.pushState&&H.state!==e},u=function(){return("/"+F.pathname.replace(new RegExp(H.state),"")+F.search+(l()?"#"+l():"")).replace(z,"/")},l=function(){var e=F.href.indexOf("#");return-1!=e?F.href.substr(e+1):""},c=function(){return d()?u():l()},_=function(){try{return top.document!==e&&top.document.title!==e&&top.jQuery!==e&&top.jQuery.address!==e&&!1!==top.jQuery.address.frames()?top:window}catch(e){return window}},m=function(e){return e=e.toString(),(H.strict&&"/"!=e.substr(0,1)?"/":"")+e},h=function(e,t){return parseInt(e.css(t),10)},p=function(){if(!B){var e=c();decodeURI(Z)!=decodeURI(e)&&(E&&O<7?F.reload():(E&&!R&&H.history&&N(M,50),Z=e,f(x)))}},f=function(e){return N(y,10),r(k).isDefaultPrevented()||r(e?D:T).isDefaultPrevented()},y=function(){if("null"!==H.tracker&&H.tracker!==Y){var t=a.isFunction(H.tracker)?H.tracker:A[H.tracker],n=(F.pathname+F.search+(a.address&&!d()?a.address.value():"")).replace(/\/\//,"/").replace(/^\/$/,"");a.isFunction(t)?t(n):(a.isFunction(A.urchinTracker)&&A.urchinTracker(n),A.pageTracker!==e&&a.isFunction(A.pageTracker._trackPageview)&&A.pageTracker._trackPageview(n),A._gaq!==e&&a.isFunction(A._gaq.push)&&A._gaq.push(["_trackPageview",decodeURI(n)]),a.isFunction(A.ga)&&A.ga("send","pageview",n))}},M=function(){var e="javascript:"+x+";document.open();document.writeln('<html><head><title>"+C.title.replace(/\'/g,"\\'")+"</title><script>var "+v+' = "'+encodeURIComponent(c()).replace(/\'/g,"\\'")+(C.domain!=F.hostname?'";document.domain="'+C.domain:"")+"\";<\/script></head></html>');document.close();";O<7?t.src=e:t.contentWindow.location.replace(e)},g=function(){if(U&&-1!=J){var e,t,n=U.substr(J+1).split("&");for(e=0;e<n.length;e++)t=n[e].split("="),/^(autoUpdate|history|strict|wrap)$/.test(t[0])&&(H[t[0]]=isNaN(t[1])?/^(true|yes)$/i.test(t[1]):0!==parseInt(t[1],10)),/^(state|tracker)$/.test(t[0])&&(H[t[0]]=t[1]);U=Y}Z=c()},L=function(){if(!G){if(G=S,g(),a('a[rel*="address:"]').address(),H.wrap){var n=a("body");a("body > *").wrapAll('<div style="padding:'+(h(n,"marginTop")+h(n,"paddingTop"))+"px "+(h(n,"marginRight")+h(n,"paddingRight"))+"px "+(h(n,"marginBottom")+h(n,"paddingBottom"))+"px "+(h(n,"marginLeft")+h(n,"paddingLeft"))+'px;" />').parent().wrap('<div id="'+v+'" style="height:100%;overflow:auto;position:relative;'+(P&&!window.statusbar.visible?"resize:both;":"")+'" />'),a("html, body").css({height:"100%",margin:0,padding:0,overflow:"hidden"}),P&&a('<style type="text/css" />').appendTo("head").text("#"+v+"::-webkit-resizer { background-color: #fff; }")}if(E&&!R){var s=C.getElementsByTagName("frameset")[0];(t=C.createElement((s?"":"i")+"frame")).src="javascript:"+x,s?(s.insertAdjacentElement("beforeEnd",t),s[s.cols?"cols":"rows"]+=",0",t.noResize=S,t.frameBorder=t.frameSpacing=0):(t.style.display="none",t.style.width=t.style.height=0,t.tabIndex=-1,C.body.insertAdjacentElement("afterBegin",t)),N((function(){a(t).bind("load",(function(){var n=t.contentWindow;(Z=n[v]!==e?n[v]:"")!=c()&&(f(x),F.hash=Z)})),t.contentWindow[v]===e&&M()}),50)}N((function(){r("init"),f(x)}),1),d()||(E&&O>7||!E&&R?A.addEventListener?A.addEventListener(w,p,x):A.attachEvent&&A.attachEvent("on"+w,p):$(p,50)),"state"in window.history&&a(window).trigger("popstate")}},Y=null,v="jQueryAddress",w="hashchange",b="init",k="change",D="internalChange",T="externalChange",S=!0,x=!1,H={autoUpdate:S,history:S,strict:S,frames:S,wrap:x},j=(n={},(s=function(e){e=e.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}}(navigator.userAgent)).browser&&(n[s.browser]=!0,n.version=s.version),n.chrome?n.webkit=!0:n.webkit&&(n.safari=!0),n),O=parseFloat(j.version),P=j.webkit||j.safari,E=j.msie,A=_(),C=A.document,W=A.history,F=A.location,$=setInterval,N=setTimeout,z=/\/{2,9}/g,I=navigator.userAgent,R="on"+w in A,U=a("script:last").attr("src"),J=U?U.indexOf("?"):-1,q=C.title,B=x,G=x,V=S,K=x,Z=c();if(E){O=parseFloat(I.substr(I.indexOf("MSIE")+4)),C.documentMode&&C.documentMode!=O&&(O=8!=C.documentMode?7:8);var Q=C.onpropertychange;C.onpropertychange=function(){Q&&Q.call(C),C.title!=q&&-1!=C.title.indexOf("#"+c())&&(C.title=q)}}if(W.navigationMode&&(W.navigationMode="compatible"),"complete"==document.readyState)var X=setInterval((function(){a.address&&(L(),clearInterval(X))}),50);else g(),a(L);return a(window).bind("popstate",(function(){decodeURI(Z)!=decodeURI(c())&&(Z=c(),f(x))})).bind("unload",(function(){A.removeEventListener?A.removeEventListener(w,p,x):A.detachEvent&&A.detachEvent("on"+w,p)})),{bind:function(e,t,n){return o.apply(this,i(arguments))},unbind:function(e,t){return function(e,t){return a().unbind.apply(a(a.address),Array.prototype.slice.call(arguments)),a.address}.apply(this,i(arguments))},init:function(e,t){return o.apply(this,[b].concat(i(arguments)))},change:function(e,t){return o.apply(this,[k].concat(i(arguments)))},internalChange:function(e,t){return o.apply(this,[D].concat(i(arguments)))},externalChange:function(e,t){return o.apply(this,[T].concat(i(arguments)))},baseURL:function(){var e=F.href;return-1!=e.indexOf("#")&&(e=e.substr(0,e.indexOf("#"))),/\/$/.test(e)&&(e=e.substr(0,e.length-1)),e},autoUpdate:function(t){return t!==e?(H.autoUpdate=t,this):H.autoUpdate},history:function(t){return t!==e?(H.history=t,this):H.history},state:function(t){if(t!==e){H.state=t;var n=u();return H.state!==e&&(W.pushState?"/#/"==n.substr(0,3)&&F.replace(H.state.replace(/^\/$/,"")+n.substr(2)):"/"!=n&&n.replace(/^\/#/,"")!=l()&&N((function(){F.replace(H.state.replace(/^\/$/,"")+"/#"+n)}),1)),this}return H.state},frames:function(t){return t!==e?(H.frames=t,A=_(),this):H.frames},strict:function(t){return t!==e?(H.strict=t,this):H.strict},tracker:function(t){return t!==e?(H.tracker=t,this):H.tracker},wrap:function(t){return t!==e?(H.wrap=t,this):H.wrap},update:function(){return K=S,this.value(Z),K=x,this},title:function(n){return n!==e?(N((function(){q=C.title=n,V&&t&&t.contentWindow&&t.contentWindow.document&&(t.contentWindow.document.title=n,V=x)}),50),this):C.title},value:function(t){if(t!==e){if("/"==(t=m(t))&&(t=""),Z==t&&!K)return;if(Z=t,H.autoUpdate||K){if(f(S))return this;d()?W[H.history?"pushState":"replaceState"]({},"",H.state.replace(/\/$/,"")+(""===Z?"/":Z)):(B=S,(P||Z!=c())&&(H.history?F.hash="#"+Z:F.replace("#"+Z)),E&&!R&&H.history&&N(M,50),P?N((function(){B=x}),1):B=x)}return this}return m(Z)},path:function(t){if(t!==e){var n=this.queryString(),a=this.hash();return this.value(t+(n?"?"+n:"")+(a?"#"+a:"")),this}return m(Z).split("#")[0].split("?")[0]},pathNames:function(){var e=this.path(),t=e.replace(z,"/").split("/");return"/"!=e.substr(0,1)&&0!==e.length||t.splice(0,1),"/"==e.substr(e.length-1,1)&&t.splice(t.length-1,1),t},queryString:function(t){if(t!==e){var n=this.hash();return this.value(this.path()+(t?"?"+t:"")+(n?"#"+n:"")),this}var a=Z.split("?");return a.slice(1,a.length).join("?").split("#")[0]},parameter:function(t,n,s){var r,i;if(n!==e){var o=this.parameterNames();for(i=[],n=n===e||n===Y?"":n.toString(),r=0;r<o.length;r++){var d=o[r],u=this.parameter(d);"string"==typeof u&&(u=[u]),d==t&&(u=n===Y||""===n?[]:s?u.concat([n]):[n]);for(var l=0;l<u.length;l++)i.push(d+"="+u[l])}return-1==a.inArray(t,o)&&n!==Y&&""!==n&&i.push(t+"="+n),this.queryString(i.join("&")),this}if(n=this.queryString()){var c=[];for(i=n.split("&"),r=0;r<i.length;r++){var _=i[r].split("=");_[0]==t&&c.push(_.slice(1).join("="))}if(0!==c.length)return 1!=c.length?c:c[0]}},parameterNames:function(){var e=this.queryString(),t=[];if(e&&-1!=e.indexOf("="))for(var n=e.split("&"),s=0;s<n.length;s++){var r=n[s].split("=")[0];-1==a.inArray(r,t)&&t.push(r)}return t},hash:function(t){if(t!==e)return this.value(Z.split("#")[0]+(t?"#"+t:"")),this;var n=Z.split("#");return n.slice(1,n.length).join("#")}}}(),a.fn.address=function(e){return a(this).each((function(t){a(this).data("address")||a(this).on("click",(function(t){if(t.shiftKey||t.ctrlKey||t.metaKey||2==t.which)return!0;var n=t.currentTarget;if(a(n).is("a")){t.preventDefault();var s=e?e.call(n):/address:/.test(a(n).attr("rel"))?a(n).attr("rel").split("address:")[1].split(" ")[0]:void 0===a.address.state()||/^\/?$/.test(a.address.state())?a(n).attr("href").replace(/^(#\!?|\.)/,""):a(n).attr("href").replace(new RegExp("^(.*"+a.address.state()+"|\\.)"),"");a.address.value(s)}})).on("submit",(function(t){var n=t.currentTarget;if(a(n).is("form")){t.preventDefault();var s=a(n).attr("action"),r=e?e.call(n):(-1!=s.indexOf("?")?s.replace(/&$/,""):s+"?")+a(n).serialize();a.address.value(r)}})).data("address",!0)})),this}},5411:function(e,t,n){e.exports=function(e){"use strict";const t=t=>{let n=t.getAttribute("data-bs-target");if(!n||"#"===n){let e=t.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;e.includes("#")&&!e.startsWith("#")&&(e=`#${e.split("#")[1]}`),n=e&&"#"!==e?e.trim():null}return n?n.split(",").map((t=>e.parseSelector(t))).join(","):null},n={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter((e=>e.matches(t))),parents(e,t){const n=[];let a=e.parentNode.closest(t);for(;a;)n.push(a),a=a.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(t){const n=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((e=>`${e}:not([tabindex^="-"])`)).join(",");return this.find(n,t).filter((t=>!e.isDisabled(t)&&e.isVisible(t)))},getSelectorFromElement(e){const a=t(e);return a&&n.findOne(a)?a:null},getElementFromSelector(e){const a=t(e);return a?n.findOne(a):null},getMultipleElementsFromSelector(e){const a=t(e);return a?n.find(a):[]}};return n}(n(4035))},7269:function(e){e.exports=function(){"use strict";const e=new Map;return{set(t,n,a){e.has(t)||e.set(t,new Map);const s=e.get(t);s.has(n)||0===s.size?s.set(n,a):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`)},get:(t,n)=>e.has(t)&&e.get(t).get(n)||null,remove(t,n){if(!e.has(t))return;const a=e.get(t);a.delete(n),0===a.size&&e.delete(t)}}}()},7855:(e,t,n)=>{var a,s,r,i=n(1669);s=[n(1669)],a=function(e){var t=function(){if(e&&e.fn&&e.fn.select2&&e.fn.select2.amd)var t=e.fn.select2.amd;var n,a,s;return t&&t.requirejs||(t?a=t:t={},function(e){var t,r,i,o,d={},u={},l={},c={},_=Object.prototype.hasOwnProperty,m=[].slice,h=/\.js$/;function p(e,t){return _.call(e,t)}function f(e,t){var n,a,s,r,i,o,d,u,c,_,m,p=t&&t.split("/"),f=l.map,y=f&&f["*"]||{};if(e){for(i=(e=e.split("/")).length-1,l.nodeIdCompat&&h.test(e[i])&&(e[i]=e[i].replace(h,"")),"."===e[0].charAt(0)&&p&&(e=p.slice(0,p.length-1).concat(e)),c=0;c<e.length;c++)if("."===(m=e[c]))e.splice(c,1),c-=1;else if(".."===m){if(0===c||1===c&&".."===e[2]||".."===e[c-1])continue;c>0&&(e.splice(c-1,2),c-=2)}e=e.join("/")}if((p||y)&&f){for(c=(n=e.split("/")).length;c>0;c-=1){if(a=n.slice(0,c).join("/"),p)for(_=p.length;_>0;_-=1)if((s=f[p.slice(0,_).join("/")])&&(s=s[a])){r=s,o=c;break}if(r)break;!d&&y&&y[a]&&(d=y[a],u=c)}!r&&d&&(r=d,o=u),r&&(n.splice(0,o,r),e=n.join("/"))}return e}function y(t,n){return function(){var a=m.call(arguments,0);return"string"!=typeof a[0]&&1===a.length&&a.push(null),r.apply(e,a.concat([t,n]))}}function M(e){return function(t){return f(t,e)}}function g(e){return function(t){d[e]=t}}function L(n){if(p(u,n)){var a=u[n];delete u[n],c[n]=!0,t.apply(e,a)}if(!p(d,n)&&!p(c,n))throw new Error("No "+n);return d[n]}function Y(e){var t,n=e?e.indexOf("!"):-1;return n>-1&&(t=e.substring(0,n),e=e.substring(n+1,e.length)),[t,e]}function v(e){return e?Y(e):[]}function w(e){return function(){return l&&l.config&&l.config[e]||{}}}i=function(e,t){var n,a=Y(e),s=a[0],r=t[1];return e=a[1],s&&(n=L(s=f(s,r))),s?e=n&&n.normalize?n.normalize(e,M(r)):f(e,r):(s=(a=Y(e=f(e,r)))[0],e=a[1],s&&(n=L(s))),{f:s?s+"!"+e:e,n:e,pr:s,p:n}},o={require:function(e){return y(e)},exports:function(e){var t=d[e];return void 0!==t?t:d[e]={}},module:function(e){return{id:e,uri:"",exports:d[e],config:w(e)}}},t=function(t,n,a,s){var r,l,_,m,h,f,M,Y=[],w=typeof a;if(f=v(s=s||t),"undefined"===w||"function"===w){for(n=!n.length&&a.length?["require","exports","module"]:n,h=0;h<n.length;h+=1)if("require"===(l=(m=i(n[h],f)).f))Y[h]=o.require(t);else if("exports"===l)Y[h]=o.exports(t),M=!0;else if("module"===l)r=Y[h]=o.module(t);else if(p(d,l)||p(u,l)||p(c,l))Y[h]=L(l);else{if(!m.p)throw new Error(t+" missing "+l);m.p.load(m.n,y(s,!0),g(l),{}),Y[h]=d[l]}_=a?a.apply(d[t],Y):void 0,t&&(r&&r.exports!==e&&r.exports!==d[t]?d[t]=r.exports:_===e&&M||(d[t]=_))}else t&&(d[t]=a)},n=a=r=function(n,a,s,d,u){if("string"==typeof n)return o[n]?o[n](a):L(i(n,v(a)).f);if(!n.splice){if((l=n).deps&&r(l.deps,l.callback),!a)return;a.splice?(n=a,a=s,s=null):n=e}return a=a||function(){},"function"==typeof s&&(s=d,d=u),d?t(e,n,a,s):setTimeout((function(){t(e,n,a,s)}),4),r},r.config=function(e){return r(e)},n._defined=d,(s=function(e,t,n){if("string"!=typeof e)throw new Error("See almond README: incorrect module build, no module name");t.splice||(n=t,t=[]),p(d,e)||p(u,e)||(u[e]=[e,t,n])}).amd={jQuery:!0}}(),t.requirejs=n,t.require=a,t.define=s),t.define("almond",(function(){})),t.define("jquery",[],(function(){var t=e||i;return null==t&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),t})),t.define("select2/utils",["jquery"],(function(e){var t={};function n(e){var t=e.prototype,n=[];for(var a in t)"function"==typeof t[a]&&"constructor"!==a&&n.push(a);return n}t.Extend=function(e,t){var n={}.hasOwnProperty;function a(){this.constructor=e}for(var s in t)n.call(t,s)&&(e[s]=t[s]);return a.prototype=t.prototype,e.prototype=new a,e.__super__=t.prototype,e},t.Decorate=function(e,t){var a=n(t),s=n(e);function r(){var n=Array.prototype.unshift,a=t.prototype.constructor.length,s=e.prototype.constructor;a>0&&(n.call(arguments,e.prototype.constructor),s=t.prototype.constructor),s.apply(this,arguments)}function i(){this.constructor=r}t.displayName=e.displayName,r.prototype=new i;for(var o=0;o<s.length;o++){var d=s[o];r.prototype[d]=e.prototype[d]}for(var u=function(e){var n=function(){};e in r.prototype&&(n=r.prototype[e]);var a=t.prototype[e];return function(){return Array.prototype.unshift.call(arguments,n),a.apply(this,arguments)}},l=0;l<a.length;l++){var c=a[l];r.prototype[c]=u(c)}return r};var a=function(){this.listeners={}};a.prototype.on=function(e,t){this.listeners=this.listeners||{},e in this.listeners?this.listeners[e].push(t):this.listeners[e]=[t]},a.prototype.trigger=function(e){var t=Array.prototype.slice,n=t.call(arguments,1);this.listeners=this.listeners||{},null==n&&(n=[]),0===n.length&&n.push({}),n[0]._type=e,e in this.listeners&&this.invoke(this.listeners[e],t.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},a.prototype.invoke=function(e,t){for(var n=0,a=e.length;n<a;n++)e[n].apply(this,t)},t.Observable=a,t.generateChars=function(e){for(var t="",n=0;n<e;n++)t+=Math.floor(36*Math.random()).toString(36);return t},t.bind=function(e,t){return function(){e.apply(t,arguments)}},t._convertData=function(e){for(var t in e){var n=t.split("-"),a=e;if(1!==n.length){for(var s=0;s<n.length;s++){var r=n[s];(r=r.substring(0,1).toLowerCase()+r.substring(1))in a||(a[r]={}),s==n.length-1&&(a[r]=e[t]),a=a[r]}delete e[t]}}return e},t.hasScroll=function(t,n){var a=e(n),s=n.style.overflowX,r=n.style.overflowY;return(s!==r||"hidden"!==r&&"visible"!==r)&&("scroll"===s||"scroll"===r||a.innerHeight()<n.scrollHeight||a.innerWidth()<n.scrollWidth)},t.escapeMarkup=function(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof e?e:String(e).replace(/[&<>"'\/\\]/g,(function(e){return t[e]}))},t.appendMany=function(t,n){if("1.7"===e.fn.jquery.substr(0,3)){var a=e();e.map(n,(function(e){a=a.add(e)})),n=a}t.append(n)},t.__cache={};var s=0;return t.GetUniqueElementId=function(e){var t=e.getAttribute("data-select2-id");return null==t&&(e.id?(t=e.id,e.setAttribute("data-select2-id",t)):(e.setAttribute("data-select2-id",++s),t=s.toString())),t},t.StoreData=function(e,n,a){var s=t.GetUniqueElementId(e);t.__cache[s]||(t.__cache[s]={}),t.__cache[s][n]=a},t.GetData=function(n,a){var s=t.GetUniqueElementId(n);return a?t.__cache[s]&&null!=t.__cache[s][a]?t.__cache[s][a]:e(n).data(a):t.__cache[s]},t.RemoveData=function(e){var n=t.GetUniqueElementId(e);null!=t.__cache[n]&&delete t.__cache[n],e.removeAttribute("data-select2-id")},t})),t.define("select2/results",["jquery","./utils"],(function(e,t){function n(e,t,a){this.$element=e,this.data=a,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<ul class="select2-results__options" role="listbox"></ul>');return this.options.get("multiple")&&t.attr("aria-multiselectable","true"),this.$results=t,t},n.prototype.clear=function(){this.$results.empty()},n.prototype.displayMessage=function(t){var n=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var a=e('<li role="alert" aria-live="assertive" class="select2-results__option"></li>'),s=this.options.get("translations").get(t.message);a.append(n(s(t.args))),a[0].className+=" select2-results__message",this.$results.append(a)},n.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},n.prototype.append=function(e){this.hideLoading();var t=[];if(null!=e.results&&0!==e.results.length){e.results=this.sort(e.results);for(var n=0;n<e.results.length;n++){var a=e.results[n],s=this.option(a);t.push(s)}this.$results.append(t)}else 0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"})},n.prototype.position=function(e,t){t.find(".select2-results").append(e)},n.prototype.sort=function(e){return this.options.get("sorter")(e)},n.prototype.highlightFirstItem=function(){var e=this.$results.find(".select2-results__option[aria-selected]"),t=e.filter("[aria-selected=true]");t.length>0?t.first().trigger("mouseenter"):e.first().trigger("mouseenter"),this.ensureHighlightVisible()},n.prototype.setClasses=function(){var n=this;this.data.current((function(a){var s=e.map(a,(function(e){return e.id.toString()}));n.$results.find(".select2-results__option[aria-selected]").each((function(){var n=e(this),a=t.GetData(this,"data"),r=""+a.id;null!=a.element&&a.element.selected||null==a.element&&e.inArray(r,s)>-1?n.attr("aria-selected","true"):n.attr("aria-selected","false")}))}))},n.prototype.showLoading=function(e){this.hideLoading();var t={disabled:!0,loading:!0,text:this.options.get("translations").get("searching")(e)},n=this.option(t);n.className+=" loading-results",this.$results.prepend(n)},n.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},n.prototype.option=function(n){var a=document.createElement("li");a.className="select2-results__option";var s={role:"option","aria-selected":"false"},r=window.Element.prototype.matches||window.Element.prototype.msMatchesSelector||window.Element.prototype.webkitMatchesSelector;for(var i in(null!=n.element&&r.call(n.element,":disabled")||null==n.element&&n.disabled)&&(delete s["aria-selected"],s["aria-disabled"]="true"),null==n.id&&delete s["aria-selected"],null!=n._resultId&&(a.id=n._resultId),n.title&&(a.title=n.title),n.children&&(s.role="group",s["aria-label"]=n.text,delete s["aria-selected"]),s){var o=s[i];a.setAttribute(i,o)}if(n.children){var d=e(a),u=document.createElement("strong");u.className="select2-results__group",e(u),this.template(n,u);for(var l=[],c=0;c<n.children.length;c++){var _=n.children[c],m=this.option(_);l.push(m)}var h=e("<ul></ul>",{class:"select2-results__options select2-results__options--nested"});h.append(l),d.append(u),d.append(h)}else this.template(n,a);return t.StoreData(a,"data",n),a},n.prototype.bind=function(n,a){var s=this,r=n.id+"-results";this.$results.attr("id",r),n.on("results:all",(function(e){s.clear(),s.append(e.data),n.isOpen()&&(s.setClasses(),s.highlightFirstItem())})),n.on("results:append",(function(e){s.append(e.data),n.isOpen()&&s.setClasses()})),n.on("query",(function(e){s.hideMessages(),s.showLoading(e)})),n.on("select",(function(){n.isOpen()&&(s.setClasses(),s.options.get("scrollAfterSelect")&&s.highlightFirstItem())})),n.on("unselect",(function(){n.isOpen()&&(s.setClasses(),s.options.get("scrollAfterSelect")&&s.highlightFirstItem())})),n.on("open",(function(){s.$results.attr("aria-expanded","true"),s.$results.attr("aria-hidden","false"),s.setClasses(),s.ensureHighlightVisible()})),n.on("close",(function(){s.$results.attr("aria-expanded","false"),s.$results.attr("aria-hidden","true"),s.$results.removeAttr("aria-activedescendant")})),n.on("results:toggle",(function(){var e=s.getHighlightedResults();0!==e.length&&e.trigger("mouseup")})),n.on("results:select",(function(){var e=s.getHighlightedResults();if(0!==e.length){var n=t.GetData(e[0],"data");"true"==e.attr("aria-selected")?s.trigger("close",{}):s.trigger("select",{data:n})}})),n.on("results:previous",(function(){var e=s.getHighlightedResults(),t=s.$results.find("[aria-selected]"),n=t.index(e);if(!(n<=0)){var a=n-1;0===e.length&&(a=0);var r=t.eq(a);r.trigger("mouseenter");var i=s.$results.offset().top,o=r.offset().top,d=s.$results.scrollTop()+(o-i);0===a?s.$results.scrollTop(0):o-i<0&&s.$results.scrollTop(d)}})),n.on("results:next",(function(){var e=s.getHighlightedResults(),t=s.$results.find("[aria-selected]"),n=t.index(e)+1;if(!(n>=t.length)){var a=t.eq(n);a.trigger("mouseenter");var r=s.$results.offset().top+s.$results.outerHeight(!1),i=a.offset().top+a.outerHeight(!1),o=s.$results.scrollTop()+i-r;0===n?s.$results.scrollTop(0):i>r&&s.$results.scrollTop(o)}})),n.on("results:focus",(function(e){e.element.addClass("select2-results__option--highlighted")})),n.on("results:message",(function(e){s.displayMessage(e)})),e.fn.mousewheel&&this.$results.on("mousewheel",(function(e){var t=s.$results.scrollTop(),n=s.$results.get(0).scrollHeight-t+e.deltaY,a=e.deltaY>0&&t-e.deltaY<=0,r=e.deltaY<0&&n<=s.$results.height();a?(s.$results.scrollTop(0),e.preventDefault(),e.stopPropagation()):r&&(s.$results.scrollTop(s.$results.get(0).scrollHeight-s.$results.height()),e.preventDefault(),e.stopPropagation())})),this.$results.on("mouseup",".select2-results__option[aria-selected]",(function(n){var a=e(this),r=t.GetData(this,"data");"true"!==a.attr("aria-selected")?s.trigger("select",{originalEvent:n,data:r}):s.options.get("multiple")?s.trigger("unselect",{originalEvent:n,data:r}):s.trigger("close",{})})),this.$results.on("mouseenter",".select2-results__option[aria-selected]",(function(n){var a=t.GetData(this,"data");s.getHighlightedResults().removeClass("select2-results__option--highlighted"),s.trigger("results:focus",{data:a,element:e(this)})}))},n.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},n.prototype.destroy=function(){this.$results.remove()},n.prototype.ensureHighlightVisible=function(){var e=this.getHighlightedResults();if(0!==e.length){var t=this.$results.find("[aria-selected]").index(e),n=this.$results.offset().top,a=e.offset().top,s=this.$results.scrollTop()+(a-n),r=a-n;s-=2*e.outerHeight(!1),t<=2?this.$results.scrollTop(0):(r>this.$results.outerHeight()||r<0)&&this.$results.scrollTop(s)}},n.prototype.template=function(t,n){var a=this.options.get("templateResult"),s=this.options.get("escapeMarkup"),r=a(t,n);null==r?n.style.display="none":"string"==typeof r?n.innerHTML=s(r):e(n).append(r)},n})),t.define("select2/keys",[],(function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}})),t.define("select2/selection/base",["jquery","../utils","../keys"],(function(e,t,n){function a(e,t){this.$element=e,this.options=t,a.__super__.constructor.call(this)}return t.Extend(a,t.Observable),a.prototype.render=function(){var n=e('<span class="select2-selection" role="combobox"  aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=t.GetData(this.$element[0],"old-tabindex")?this._tabindex=t.GetData(this.$element[0],"old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),n.attr("title",this.$element.attr("title")),n.attr("tabindex",this._tabindex),n.attr("aria-disabled","false"),this.$selection=n,n},a.prototype.bind=function(e,t){var a=this,s=e.id+"-results";this.container=e,this.$selection.on("focus",(function(e){a.trigger("focus",e)})),this.$selection.on("blur",(function(e){a._handleBlur(e)})),this.$selection.on("keydown",(function(e){a.trigger("keypress",e),e.which===n.SPACE&&e.preventDefault()})),e.on("results:focus",(function(e){a.$selection.attr("aria-activedescendant",e.data._resultId)})),e.on("selection:update",(function(e){a.update(e.data)})),e.on("open",(function(){a.$selection.attr("aria-expanded","true"),a.$selection.attr("aria-owns",s),a._attachCloseHandler(e)})),e.on("close",(function(){a.$selection.attr("aria-expanded","false"),a.$selection.removeAttr("aria-activedescendant"),a.$selection.removeAttr("aria-owns"),a.$selection.trigger("focus"),a._detachCloseHandler(e)})),e.on("enable",(function(){a.$selection.attr("tabindex",a._tabindex),a.$selection.attr("aria-disabled","false")})),e.on("disable",(function(){a.$selection.attr("tabindex","-1"),a.$selection.attr("aria-disabled","true")}))},a.prototype._handleBlur=function(t){var n=this;window.setTimeout((function(){document.activeElement==n.$selection[0]||e.contains(n.$selection[0],document.activeElement)||n.trigger("blur",t)}),1)},a.prototype._attachCloseHandler=function(n){e(document.body).on("mousedown.select2."+n.id,(function(n){var a=e(n.target).closest(".select2");e(".select2.select2-container--open").each((function(){this!=a[0]&&t.GetData(this,"element").select2("close")}))}))},a.prototype._detachCloseHandler=function(t){e(document.body).off("mousedown.select2."+t.id)},a.prototype.position=function(e,t){t.find(".selection").append(e)},a.prototype.destroy=function(){this._detachCloseHandler(this.container)},a.prototype.update=function(e){throw new Error("The `update` method must be defined in child classes.")},a.prototype.isEnabled=function(){return!this.isDisabled()},a.prototype.isDisabled=function(){return this.options.get("disabled")},a})),t.define("select2/selection/single",["jquery","./base","../utils","../keys"],(function(e,t,n,a){function s(){s.__super__.constructor.apply(this,arguments)}return n.Extend(s,t),s.prototype.render=function(){var e=s.__super__.render.call(this);return e.addClass("select2-selection--single"),e.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>'),e},s.prototype.bind=function(e,t){var n=this;s.__super__.bind.apply(this,arguments);var a=e.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",a).attr("role","textbox").attr("aria-readonly","true"),this.$selection.attr("aria-labelledby",a),this.$selection.on("mousedown",(function(e){1===e.which&&n.trigger("toggle",{originalEvent:e})})),this.$selection.on("focus",(function(e){})),this.$selection.on("blur",(function(e){})),e.on("focus",(function(t){e.isOpen()||n.$selection.trigger("focus")}))},s.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title")},s.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},s.prototype.selectionContainer=function(){return e("<span></span>")},s.prototype.update=function(e){if(0!==e.length){var t=e[0],n=this.$selection.find(".select2-selection__rendered"),a=this.display(t,n);n.empty().append(a);var s=t.title||t.text;s?n.attr("title",s):n.removeAttr("title")}else this.clear()},s})),t.define("select2/selection/multiple",["jquery","./base","../utils"],(function(e,t,n){function a(e,t){a.__super__.constructor.apply(this,arguments)}return n.Extend(a,t),a.prototype.render=function(){var e=a.__super__.render.call(this);return e.addClass("select2-selection--multiple"),e.html('<ul class="select2-selection__rendered"></ul>'),e},a.prototype.bind=function(t,s){var r=this;a.__super__.bind.apply(this,arguments),this.$selection.on("click",(function(e){r.trigger("toggle",{originalEvent:e})})),this.$selection.on("click",".select2-selection__choice__remove",(function(t){if(!r.isDisabled()){var a=e(this).parent(),s=n.GetData(a[0],"data");r.trigger("unselect",{originalEvent:t,data:s})}}))},a.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title")},a.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},a.prototype.selectionContainer=function(){return e('<li class="select2-selection__choice"><span class="select2-selection__choice__remove" role="presentation">&times;</span></li>')},a.prototype.update=function(e){if(this.clear(),0!==e.length){for(var t=[],a=0;a<e.length;a++){var s=e[a],r=this.selectionContainer(),i=this.display(s,r);r.append(i);var o=s.title||s.text;o&&r.attr("title",o),n.StoreData(r[0],"data",s),t.push(r)}var d=this.$selection.find(".select2-selection__rendered");n.appendMany(d,t)}},a})),t.define("select2/selection/placeholder",["../utils"],(function(e){function t(e,t,n){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n)}return t.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},t.prototype.createPlaceholder=function(e,t){var n=this.selectionContainer();return n.html(this.display(t)),n.addClass("select2-selection__placeholder").removeClass("select2-selection__choice"),n},t.prototype.update=function(e,t){var n=1==t.length&&t[0].id!=this.placeholder.id;if(t.length>1||n)return e.call(this,t);this.clear();var a=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(a)},t})),t.define("select2/selection/allowClear",["jquery","../keys","../utils"],(function(e,t,n){function a(){}return a.prototype.bind=function(e,t,n){var a=this;e.call(this,t,n),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option."),this.$selection.on("mousedown",".select2-selection__clear",(function(e){a._handleClear(e)})),t.on("keypress",(function(e){a._handleKeyboardClear(e,t)}))},a.prototype._handleClear=function(e,t){if(!this.isDisabled()){var a=this.$selection.find(".select2-selection__clear");if(0!==a.length){t.stopPropagation();var s=n.GetData(a[0],"data"),r=this.$element.val();this.$element.val(this.placeholder.id);var i={data:s};if(this.trigger("clear",i),i.prevented)this.$element.val(r);else{for(var o=0;o<s.length;o++)if(i={data:s[o]},this.trigger("unselect",i),i.prevented)return void this.$element.val(r);this.$element.trigger("input").trigger("change"),this.trigger("toggle",{})}}}},a.prototype._handleKeyboardClear=function(e,n,a){a.isOpen()||n.which!=t.DELETE&&n.which!=t.BACKSPACE||this._handleClear(n)},a.prototype.update=function(t,a){if(t.call(this,a),!(this.$selection.find(".select2-selection__placeholder").length>0||0===a.length)){var s=this.options.get("translations").get("removeAllItems"),r=e('<span class="select2-selection__clear" title="'+s()+'">&times;</span>');n.StoreData(r[0],"data",a),this.$selection.find(".select2-selection__rendered").prepend(r)}},a})),t.define("select2/selection/search",["jquery","../utils","../keys"],(function(e,t,n){function a(e,t,n){e.call(this,t,n)}return a.prototype.render=function(t){var n=e('<li class="select2-search select2-search--inline"><input class="select2-search__field" type="search" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" /></li>');this.$searchContainer=n,this.$search=n.find("input");var a=t.call(this);return this._transferTabIndex(),a},a.prototype.bind=function(e,a,s){var r=this,i=a.id+"-results";e.call(this,a,s),a.on("open",(function(){r.$search.attr("aria-controls",i),r.$search.trigger("focus")})),a.on("close",(function(){r.$search.val(""),r.$search.removeAttr("aria-controls"),r.$search.removeAttr("aria-activedescendant"),r.$search.trigger("focus")})),a.on("enable",(function(){r.$search.prop("disabled",!1),r._transferTabIndex()})),a.on("disable",(function(){r.$search.prop("disabled",!0)})),a.on("focus",(function(e){r.$search.trigger("focus")})),a.on("results:focus",(function(e){e.data._resultId?r.$search.attr("aria-activedescendant",e.data._resultId):r.$search.removeAttr("aria-activedescendant")})),this.$selection.on("focusin",".select2-search--inline",(function(e){r.trigger("focus",e)})),this.$selection.on("focusout",".select2-search--inline",(function(e){r._handleBlur(e)})),this.$selection.on("keydown",".select2-search--inline",(function(e){if(e.stopPropagation(),r.trigger("keypress",e),r._keyUpPrevented=e.isDefaultPrevented(),e.which===n.BACKSPACE&&""===r.$search.val()){var a=r.$searchContainer.prev(".select2-selection__choice");if(a.length>0){var s=t.GetData(a[0],"data");r.searchRemoveChoice(s),e.preventDefault()}}})),this.$selection.on("click",".select2-search--inline",(function(e){r.$search.val()&&e.stopPropagation()}));var o=document.documentMode,d=o&&o<=11;this.$selection.on("input.searchcheck",".select2-search--inline",(function(e){d?r.$selection.off("input.search input.searchcheck"):r.$selection.off("keyup.search")})),this.$selection.on("keyup.search input.search",".select2-search--inline",(function(e){if(d&&"input"===e.type)r.$selection.off("input.search input.searchcheck");else{var t=e.which;t!=n.SHIFT&&t!=n.CTRL&&t!=n.ALT&&t!=n.TAB&&r.handleSearch(e)}}))},a.prototype._transferTabIndex=function(e){this.$search.attr("tabindex",this.$selection.attr("tabindex")),this.$selection.attr("tabindex","-1")},a.prototype.createPlaceholder=function(e,t){this.$search.attr("placeholder",t.text)},a.prototype.update=function(e,t){var n=this.$search[0]==document.activeElement;this.$search.attr("placeholder",""),e.call(this,t),this.$selection.find(".select2-selection__rendered").append(this.$searchContainer),this.resizeSearch(),n&&this.$search.trigger("focus")},a.prototype.handleSearch=function(){if(this.resizeSearch(),!this._keyUpPrevented){var e=this.$search.val();this.trigger("query",{term:e})}this._keyUpPrevented=!1},a.prototype.searchRemoveChoice=function(e,t){this.trigger("unselect",{data:t}),this.$search.val(t.text),this.handleSearch()},a.prototype.resizeSearch=function(){this.$search.css("width","25px");var e="";e=""!==this.$search.attr("placeholder")?this.$selection.find(".select2-selection__rendered").width():.75*(this.$search.val().length+1)+"em",this.$search.css("width",e)},a})),t.define("select2/selection/eventRelay",["jquery"],(function(e){function t(){}return t.prototype.bind=function(t,n,a){var s=this,r=["open","opening","close","closing","select","selecting","unselect","unselecting","clear","clearing"],i=["opening","closing","selecting","unselecting","clearing"];t.call(this,n,a),n.on("*",(function(t,n){if(-1!==e.inArray(t,r)){n=n||{};var a=e.Event("select2:"+t,{params:n});s.$element.trigger(a),-1!==e.inArray(t,i)&&(n.prevented=a.isDefaultPrevented())}}))},t})),t.define("select2/translation",["jquery","require"],(function(e,t){function n(e){this.dict=e||{}}return n.prototype.all=function(){return this.dict},n.prototype.get=function(e){return this.dict[e]},n.prototype.extend=function(t){this.dict=e.extend({},t.all(),this.dict)},n._cache={},n.loadPath=function(e){if(!(e in n._cache)){var a=t(e);n._cache[e]=a}return new n(n._cache[e])},n})),t.define("select2/diacritics",[],(function(){return{"Ⓐ":"A",Ａ:"A",À:"A",Á:"A",Â:"A",Ầ:"A",Ấ:"A",Ẫ:"A",Ẩ:"A",Ã:"A",Ā:"A",Ă:"A",Ằ:"A",Ắ:"A",Ẵ:"A",Ẳ:"A",Ȧ:"A",Ǡ:"A",Ä:"A",Ǟ:"A",Ả:"A",Å:"A",Ǻ:"A",Ǎ:"A",Ȁ:"A",Ȃ:"A",Ạ:"A",Ậ:"A",Ặ:"A",Ḁ:"A",Ą:"A",Ⱥ:"A",Ɐ:"A",Ꜳ:"AA",Æ:"AE",Ǽ:"AE",Ǣ:"AE",Ꜵ:"AO",Ꜷ:"AU",Ꜹ:"AV",Ꜻ:"AV",Ꜽ:"AY","Ⓑ":"B",Ｂ:"B",Ḃ:"B",Ḅ:"B",Ḇ:"B",Ƀ:"B",Ƃ:"B",Ɓ:"B","Ⓒ":"C",Ｃ:"C",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",Ç:"C",Ḉ:"C",Ƈ:"C",Ȼ:"C",Ꜿ:"C","Ⓓ":"D",Ｄ:"D",Ḋ:"D",Ď:"D",Ḍ:"D",Ḑ:"D",Ḓ:"D",Ḏ:"D",Đ:"D",Ƌ:"D",Ɗ:"D",Ɖ:"D",Ꝺ:"D",Ǳ:"DZ",Ǆ:"DZ",ǲ:"Dz",ǅ:"Dz","Ⓔ":"E",Ｅ:"E",È:"E",É:"E",Ê:"E",Ề:"E",Ế:"E",Ễ:"E",Ể:"E",Ẽ:"E",Ē:"E",Ḕ:"E",Ḗ:"E",Ĕ:"E",Ė:"E",Ë:"E",Ẻ:"E",Ě:"E",Ȅ:"E",Ȇ:"E",Ẹ:"E",Ệ:"E",Ȩ:"E",Ḝ:"E",Ę:"E",Ḙ:"E",Ḛ:"E",Ɛ:"E",Ǝ:"E","Ⓕ":"F",Ｆ:"F",Ḟ:"F",Ƒ:"F",Ꝼ:"F","Ⓖ":"G",Ｇ:"G",Ǵ:"G",Ĝ:"G",Ḡ:"G",Ğ:"G",Ġ:"G",Ǧ:"G",Ģ:"G",Ǥ:"G",Ɠ:"G",Ꞡ:"G",Ᵹ:"G",Ꝿ:"G","Ⓗ":"H",Ｈ:"H",Ĥ:"H",Ḣ:"H",Ḧ:"H",Ȟ:"H",Ḥ:"H",Ḩ:"H",Ḫ:"H",Ħ:"H",Ⱨ:"H",Ⱶ:"H",Ɥ:"H","Ⓘ":"I",Ｉ:"I",Ì:"I",Í:"I",Î:"I",Ĩ:"I",Ī:"I",Ĭ:"I",İ:"I",Ï:"I",Ḯ:"I",Ỉ:"I",Ǐ:"I",Ȉ:"I",Ȋ:"I",Ị:"I",Į:"I",Ḭ:"I",Ɨ:"I","Ⓙ":"J",Ｊ:"J",Ĵ:"J",Ɉ:"J","Ⓚ":"K",Ｋ:"K",Ḱ:"K",Ǩ:"K",Ḳ:"K",Ķ:"K",Ḵ:"K",Ƙ:"K",Ⱪ:"K",Ꝁ:"K",Ꝃ:"K",Ꝅ:"K",Ꞣ:"K","Ⓛ":"L",Ｌ:"L",Ŀ:"L",Ĺ:"L",Ľ:"L",Ḷ:"L",Ḹ:"L",Ļ:"L",Ḽ:"L",Ḻ:"L",Ł:"L",Ƚ:"L",Ɫ:"L",Ⱡ:"L",Ꝉ:"L",Ꝇ:"L",Ꞁ:"L",Ǉ:"LJ",ǈ:"Lj","Ⓜ":"M",Ｍ:"M",Ḿ:"M",Ṁ:"M",Ṃ:"M",Ɱ:"M",Ɯ:"M","Ⓝ":"N",Ｎ:"N",Ǹ:"N",Ń:"N",Ñ:"N",Ṅ:"N",Ň:"N",Ṇ:"N",Ņ:"N",Ṋ:"N",Ṉ:"N",Ƞ:"N",Ɲ:"N",Ꞑ:"N",Ꞥ:"N",Ǌ:"NJ",ǋ:"Nj","Ⓞ":"O",Ｏ:"O",Ò:"O",Ó:"O",Ô:"O",Ồ:"O",Ố:"O",Ỗ:"O",Ổ:"O",Õ:"O",Ṍ:"O",Ȭ:"O",Ṏ:"O",Ō:"O",Ṑ:"O",Ṓ:"O",Ŏ:"O",Ȯ:"O",Ȱ:"O",Ö:"O",Ȫ:"O",Ỏ:"O",Ő:"O",Ǒ:"O",Ȍ:"O",Ȏ:"O",Ơ:"O",Ờ:"O",Ớ:"O",Ỡ:"O",Ở:"O",Ợ:"O",Ọ:"O",Ộ:"O",Ǫ:"O",Ǭ:"O",Ø:"O",Ǿ:"O",Ɔ:"O",Ɵ:"O",Ꝋ:"O",Ꝍ:"O",Œ:"OE",Ƣ:"OI",Ꝏ:"OO",Ȣ:"OU","Ⓟ":"P",Ｐ:"P",Ṕ:"P",Ṗ:"P",Ƥ:"P",Ᵽ:"P",Ꝑ:"P",Ꝓ:"P",Ꝕ:"P","Ⓠ":"Q",Ｑ:"Q",Ꝗ:"Q",Ꝙ:"Q",Ɋ:"Q","Ⓡ":"R",Ｒ:"R",Ŕ:"R",Ṙ:"R",Ř:"R",Ȑ:"R",Ȓ:"R",Ṛ:"R",Ṝ:"R",Ŗ:"R",Ṟ:"R",Ɍ:"R",Ɽ:"R",Ꝛ:"R",Ꞧ:"R",Ꞃ:"R","Ⓢ":"S",Ｓ:"S",ẞ:"S",Ś:"S",Ṥ:"S",Ŝ:"S",Ṡ:"S",Š:"S",Ṧ:"S",Ṣ:"S",Ṩ:"S",Ș:"S",Ş:"S",Ȿ:"S",Ꞩ:"S",Ꞅ:"S","Ⓣ":"T",Ｔ:"T",Ṫ:"T",Ť:"T",Ṭ:"T",Ț:"T",Ţ:"T",Ṱ:"T",Ṯ:"T",Ŧ:"T",Ƭ:"T",Ʈ:"T",Ⱦ:"T",Ꞇ:"T",Ꜩ:"TZ","Ⓤ":"U",Ｕ:"U",Ù:"U",Ú:"U",Û:"U",Ũ:"U",Ṹ:"U",Ū:"U",Ṻ:"U",Ŭ:"U",Ü:"U",Ǜ:"U",Ǘ:"U",Ǖ:"U",Ǚ:"U",Ủ:"U",Ů:"U",Ű:"U",Ǔ:"U",Ȕ:"U",Ȗ:"U",Ư:"U",Ừ:"U",Ứ:"U",Ữ:"U",Ử:"U",Ự:"U",Ụ:"U",Ṳ:"U",Ų:"U",Ṷ:"U",Ṵ:"U",Ʉ:"U","Ⓥ":"V",Ｖ:"V",Ṽ:"V",Ṿ:"V",Ʋ:"V",Ꝟ:"V",Ʌ:"V",Ꝡ:"VY","Ⓦ":"W",Ｗ:"W",Ẁ:"W",Ẃ:"W",Ŵ:"W",Ẇ:"W",Ẅ:"W",Ẉ:"W",Ⱳ:"W","Ⓧ":"X",Ｘ:"X",Ẋ:"X",Ẍ:"X","Ⓨ":"Y",Ｙ:"Y",Ỳ:"Y",Ý:"Y",Ŷ:"Y",Ỹ:"Y",Ȳ:"Y",Ẏ:"Y",Ÿ:"Y",Ỷ:"Y",Ỵ:"Y",Ƴ:"Y",Ɏ:"Y",Ỿ:"Y","Ⓩ":"Z",Ｚ:"Z",Ź:"Z",Ẑ:"Z",Ż:"Z",Ž:"Z",Ẓ:"Z",Ẕ:"Z",Ƶ:"Z",Ȥ:"Z",Ɀ:"Z",Ⱬ:"Z",Ꝣ:"Z","ⓐ":"a",ａ:"a",ẚ:"a",à:"a",á:"a",â:"a",ầ:"a",ấ:"a",ẫ:"a",ẩ:"a",ã:"a",ā:"a",ă:"a",ằ:"a",ắ:"a",ẵ:"a",ẳ:"a",ȧ:"a",ǡ:"a",ä:"a",ǟ:"a",ả:"a",å:"a",ǻ:"a",ǎ:"a",ȁ:"a",ȃ:"a",ạ:"a",ậ:"a",ặ:"a",ḁ:"a",ą:"a",ⱥ:"a",ɐ:"a",ꜳ:"aa",æ:"ae",ǽ:"ae",ǣ:"ae",ꜵ:"ao",ꜷ:"au",ꜹ:"av",ꜻ:"av",ꜽ:"ay","ⓑ":"b",ｂ:"b",ḃ:"b",ḅ:"b",ḇ:"b",ƀ:"b",ƃ:"b",ɓ:"b","ⓒ":"c",ｃ:"c",ć:"c",ĉ:"c",ċ:"c",č:"c",ç:"c",ḉ:"c",ƈ:"c",ȼ:"c",ꜿ:"c",ↄ:"c","ⓓ":"d",ｄ:"d",ḋ:"d",ď:"d",ḍ:"d",ḑ:"d",ḓ:"d",ḏ:"d",đ:"d",ƌ:"d",ɖ:"d",ɗ:"d",ꝺ:"d",ǳ:"dz",ǆ:"dz","ⓔ":"e",ｅ:"e",è:"e",é:"e",ê:"e",ề:"e",ế:"e",ễ:"e",ể:"e",ẽ:"e",ē:"e",ḕ:"e",ḗ:"e",ĕ:"e",ė:"e",ë:"e",ẻ:"e",ě:"e",ȅ:"e",ȇ:"e",ẹ:"e",ệ:"e",ȩ:"e",ḝ:"e",ę:"e",ḙ:"e",ḛ:"e",ɇ:"e",ɛ:"e",ǝ:"e","ⓕ":"f",ｆ:"f",ḟ:"f",ƒ:"f",ꝼ:"f","ⓖ":"g",ｇ:"g",ǵ:"g",ĝ:"g",ḡ:"g",ğ:"g",ġ:"g",ǧ:"g",ģ:"g",ǥ:"g",ɠ:"g",ꞡ:"g",ᵹ:"g",ꝿ:"g","ⓗ":"h",ｈ:"h",ĥ:"h",ḣ:"h",ḧ:"h",ȟ:"h",ḥ:"h",ḩ:"h",ḫ:"h",ẖ:"h",ħ:"h",ⱨ:"h",ⱶ:"h",ɥ:"h",ƕ:"hv","ⓘ":"i",ｉ:"i",ì:"i",í:"i",î:"i",ĩ:"i",ī:"i",ĭ:"i",ï:"i",ḯ:"i",ỉ:"i",ǐ:"i",ȉ:"i",ȋ:"i",ị:"i",į:"i",ḭ:"i",ɨ:"i",ı:"i","ⓙ":"j",ｊ:"j",ĵ:"j",ǰ:"j",ɉ:"j","ⓚ":"k",ｋ:"k",ḱ:"k",ǩ:"k",ḳ:"k",ķ:"k",ḵ:"k",ƙ:"k",ⱪ:"k",ꝁ:"k",ꝃ:"k",ꝅ:"k",ꞣ:"k","ⓛ":"l",ｌ:"l",ŀ:"l",ĺ:"l",ľ:"l",ḷ:"l",ḹ:"l",ļ:"l",ḽ:"l",ḻ:"l",ſ:"l",ł:"l",ƚ:"l",ɫ:"l",ⱡ:"l",ꝉ:"l",ꞁ:"l",ꝇ:"l",ǉ:"lj","ⓜ":"m",ｍ:"m",ḿ:"m",ṁ:"m",ṃ:"m",ɱ:"m",ɯ:"m","ⓝ":"n",ｎ:"n",ǹ:"n",ń:"n",ñ:"n",ṅ:"n",ň:"n",ṇ:"n",ņ:"n",ṋ:"n",ṉ:"n",ƞ:"n",ɲ:"n",ŉ:"n",ꞑ:"n",ꞥ:"n",ǌ:"nj","ⓞ":"o",ｏ:"o",ò:"o",ó:"o",ô:"o",ồ:"o",ố:"o",ỗ:"o",ổ:"o",õ:"o",ṍ:"o",ȭ:"o",ṏ:"o",ō:"o",ṑ:"o",ṓ:"o",ŏ:"o",ȯ:"o",ȱ:"o",ö:"o",ȫ:"o",ỏ:"o",ő:"o",ǒ:"o",ȍ:"o",ȏ:"o",ơ:"o",ờ:"o",ớ:"o",ỡ:"o",ở:"o",ợ:"o",ọ:"o",ộ:"o",ǫ:"o",ǭ:"o",ø:"o",ǿ:"o",ɔ:"o",ꝋ:"o",ꝍ:"o",ɵ:"o",œ:"oe",ƣ:"oi",ȣ:"ou",ꝏ:"oo","ⓟ":"p",ｐ:"p",ṕ:"p",ṗ:"p",ƥ:"p",ᵽ:"p",ꝑ:"p",ꝓ:"p",ꝕ:"p","ⓠ":"q",ｑ:"q",ɋ:"q",ꝗ:"q",ꝙ:"q","ⓡ":"r",ｒ:"r",ŕ:"r",ṙ:"r",ř:"r",ȑ:"r",ȓ:"r",ṛ:"r",ṝ:"r",ŗ:"r",ṟ:"r",ɍ:"r",ɽ:"r",ꝛ:"r",ꞧ:"r",ꞃ:"r","ⓢ":"s",ｓ:"s",ß:"s",ś:"s",ṥ:"s",ŝ:"s",ṡ:"s",š:"s",ṧ:"s",ṣ:"s",ṩ:"s",ș:"s",ş:"s",ȿ:"s",ꞩ:"s",ꞅ:"s",ẛ:"s","ⓣ":"t",ｔ:"t",ṫ:"t",ẗ:"t",ť:"t",ṭ:"t",ț:"t",ţ:"t",ṱ:"t",ṯ:"t",ŧ:"t",ƭ:"t",ʈ:"t",ⱦ:"t",ꞇ:"t",ꜩ:"tz","ⓤ":"u",ｕ:"u",ù:"u",ú:"u",û:"u",ũ:"u",ṹ:"u",ū:"u",ṻ:"u",ŭ:"u",ü:"u",ǜ:"u",ǘ:"u",ǖ:"u",ǚ:"u",ủ:"u",ů:"u",ű:"u",ǔ:"u",ȕ:"u",ȗ:"u",ư:"u",ừ:"u",ứ:"u",ữ:"u",ử:"u",ự:"u",ụ:"u",ṳ:"u",ų:"u",ṷ:"u",ṵ:"u",ʉ:"u","ⓥ":"v",ｖ:"v",ṽ:"v",ṿ:"v",ʋ:"v",ꝟ:"v",ʌ:"v",ꝡ:"vy","ⓦ":"w",ｗ:"w",ẁ:"w",ẃ:"w",ŵ:"w",ẇ:"w",ẅ:"w",ẘ:"w",ẉ:"w",ⱳ:"w","ⓧ":"x",ｘ:"x",ẋ:"x",ẍ:"x","ⓨ":"y",ｙ:"y",ỳ:"y",ý:"y",ŷ:"y",ỹ:"y",ȳ:"y",ẏ:"y",ÿ:"y",ỷ:"y",ẙ:"y",ỵ:"y",ƴ:"y",ɏ:"y",ỿ:"y","ⓩ":"z",ｚ:"z",ź:"z",ẑ:"z",ż:"z",ž:"z",ẓ:"z",ẕ:"z",ƶ:"z",ȥ:"z",ɀ:"z",ⱬ:"z",ꝣ:"z",Ά:"Α",Έ:"Ε",Ή:"Η",Ί:"Ι",Ϊ:"Ι",Ό:"Ο",Ύ:"Υ",Ϋ:"Υ",Ώ:"Ω",ά:"α",έ:"ε",ή:"η",ί:"ι",ϊ:"ι",ΐ:"ι",ό:"ο",ύ:"υ",ϋ:"υ",ΰ:"υ",ώ:"ω",ς:"σ","’":"'"}})),t.define("select2/data/base",["../utils"],(function(e){function t(e,n){t.__super__.constructor.call(this)}return e.Extend(t,e.Observable),t.prototype.current=function(e){throw new Error("The `current` method must be defined in child classes.")},t.prototype.query=function(e,t){throw new Error("The `query` method must be defined in child classes.")},t.prototype.bind=function(e,t){},t.prototype.destroy=function(){},t.prototype.generateResultId=function(t,n){var a=t.id+"-result-";return a+=e.generateChars(4),null!=n.id?a+="-"+n.id.toString():a+="-"+e.generateChars(4),a},t})),t.define("select2/data/select",["./base","../utils","jquery"],(function(e,t,n){function a(e,t){this.$element=e,this.options=t,a.__super__.constructor.call(this)}return t.Extend(a,e),a.prototype.current=function(e){var t=[],a=this;this.$element.find(":selected").each((function(){var e=n(this),s=a.item(e);t.push(s)})),e(t)},a.prototype.select=function(e){var t=this;if(e.selected=!0,n(e.element).is("option"))return e.element.selected=!0,void this.$element.trigger("input").trigger("change");if(this.$element.prop("multiple"))this.current((function(a){var s=[];(e=[e]).push.apply(e,a);for(var r=0;r<e.length;r++){var i=e[r].id;-1===n.inArray(i,s)&&s.push(i)}t.$element.val(s),t.$element.trigger("input").trigger("change")}));else{var a=e.id;this.$element.val(a),this.$element.trigger("input").trigger("change")}},a.prototype.unselect=function(e){var t=this;if(this.$element.prop("multiple")){if(e.selected=!1,n(e.element).is("option"))return e.element.selected=!1,void this.$element.trigger("input").trigger("change");this.current((function(a){for(var s=[],r=0;r<a.length;r++){var i=a[r].id;i!==e.id&&-1===n.inArray(i,s)&&s.push(i)}t.$element.val(s),t.$element.trigger("input").trigger("change")}))}},a.prototype.bind=function(e,t){var n=this;this.container=e,e.on("select",(function(e){n.select(e.data)})),e.on("unselect",(function(e){n.unselect(e.data)}))},a.prototype.destroy=function(){this.$element.find("*").each((function(){t.RemoveData(this)}))},a.prototype.query=function(e,t){var a=[],s=this;this.$element.children().each((function(){var t=n(this);if(t.is("option")||t.is("optgroup")){var r=s.item(t),i=s.matches(e,r);null!==i&&a.push(i)}})),t({results:a})},a.prototype.addOptions=function(e){t.appendMany(this.$element,e)},a.prototype.option=function(e){var a;e.children?(a=document.createElement("optgroup")).label=e.text:void 0!==(a=document.createElement("option")).textContent?a.textContent=e.text:a.innerText=e.text,void 0!==e.id&&(a.value=e.id),e.disabled&&(a.disabled=!0),e.selected&&(a.selected=!0),e.title&&(a.title=e.title);var s=n(a),r=this._normalizeItem(e);return r.element=a,t.StoreData(a,"data",r),s},a.prototype.item=function(e){var a={};if(null!=(a=t.GetData(e[0],"data")))return a;if(e.is("option"))a={id:e.val(),text:e.text(),disabled:e.prop("disabled"),selected:e.prop("selected"),title:e.prop("title")};else if(e.is("optgroup")){a={text:e.prop("label"),children:[],title:e.prop("title")};for(var s=e.children("option"),r=[],i=0;i<s.length;i++){var o=n(s[i]),d=this.item(o);r.push(d)}a.children=r}return(a=this._normalizeItem(a)).element=e[0],t.StoreData(e[0],"data",a),a},a.prototype._normalizeItem=function(e){e!==Object(e)&&(e={id:e,text:e});var t={selected:!1,disabled:!1};return null!=(e=n.extend({},{text:""},e)).id&&(e.id=e.id.toString()),null!=e.text&&(e.text=e.text.toString()),null==e._resultId&&e.id&&null!=this.container&&(e._resultId=this.generateResultId(this.container,e)),n.extend({},t,e)},a.prototype.matches=function(e,t){return this.options.get("matcher")(e,t)},a})),t.define("select2/data/array",["./select","../utils","jquery"],(function(e,t,n){function a(e,t){this._dataToConvert=t.get("data")||[],a.__super__.constructor.call(this,e,t)}return t.Extend(a,e),a.prototype.bind=function(e,t){a.__super__.bind.call(this,e,t),this.addOptions(this.convertToOptions(this._dataToConvert))},a.prototype.select=function(e){var t=this.$element.find("option").filter((function(t,n){return n.value==e.id.toString()}));0===t.length&&(t=this.option(e),this.addOptions(t)),a.__super__.select.call(this,e)},a.prototype.convertToOptions=function(e){var a=this,s=this.$element.find("option"),r=s.map((function(){return a.item(n(this)).id})).get(),i=[];function o(e){return function(){return n(this).val()==e.id}}for(var d=0;d<e.length;d++){var u=this._normalizeItem(e[d]);if(n.inArray(u.id,r)>=0){var l=s.filter(o(u)),c=this.item(l),_=n.extend(!0,{},u,c),m=this.option(_);l.replaceWith(m)}else{var h=this.option(u);if(u.children){var p=this.convertToOptions(u.children);t.appendMany(h,p)}i.push(h)}}return i},a})),t.define("select2/data/ajax",["./array","../utils","jquery"],(function(e,t,n){function a(e,t){this.ajaxOptions=this._applyDefaults(t.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),a.__super__.constructor.call(this,e,t)}return t.Extend(a,e),a.prototype._applyDefaults=function(e){var t={data:function(e){return n.extend({},e,{q:e.term})},transport:function(e,t,a){var s=n.ajax(e);return s.then(t),s.fail(a),s}};return n.extend({},t,e,!0)},a.prototype.processResults=function(e){return e},a.prototype.query=function(e,t){var a=this;null!=this._request&&(n.isFunction(this._request.abort)&&this._request.abort(),this._request=null);var s=n.extend({type:"GET"},this.ajaxOptions);function r(){var r=s.transport(s,(function(s){var r=a.processResults(s,e);a.options.get("debug")&&window.console&&console.error&&(r&&r.results&&n.isArray(r.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response.")),t(r)}),(function(){(!("status"in r)||0!==r.status&&"0"!==r.status)&&a.trigger("results:message",{message:"errorLoading"})}));a._request=r}"function"==typeof s.url&&(s.url=s.url.call(this.$element,e)),"function"==typeof s.data&&(s.data=s.data.call(this.$element,e)),this.ajaxOptions.delay&&null!=e.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(r,this.ajaxOptions.delay)):r()},a})),t.define("select2/data/tags",["jquery"],(function(e){function t(t,n,a){var s=a.get("tags"),r=a.get("createTag");void 0!==r&&(this.createTag=r);var i=a.get("insertTag");if(void 0!==i&&(this.insertTag=i),t.call(this,n,a),e.isArray(s))for(var o=0;o<s.length;o++){var d=s[o],u=this._normalizeItem(d),l=this.option(u);this.$element.append(l)}}return t.prototype.query=function(e,t,n){var a=this;function s(e,r){for(var i=e.results,o=0;o<i.length;o++){var d=i[o],u=null!=d.children&&!s({results:d.children},!0);if((d.text||"").toUpperCase()===(t.term||"").toUpperCase()||u)return!r&&(e.data=i,void n(e))}if(r)return!0;var l=a.createTag(t);if(null!=l){var c=a.option(l);c.attr("data-select2-tag",!0),a.addOptions([c]),a.insertTag(i,l)}e.results=i,n(e)}this._removeOldTags(),null!=t.term&&null==t.page?e.call(this,t,s):e.call(this,t,n)},t.prototype.createTag=function(t,n){var a=e.trim(n.term);return""===a?null:{id:a,text:a}},t.prototype.insertTag=function(e,t,n){t.unshift(n)},t.prototype._removeOldTags=function(t){this.$element.find("option[data-select2-tag]").each((function(){this.selected||e(this).remove()}))},t})),t.define("select2/data/tokenizer",["jquery"],(function(e){function t(e,t,n){var a=n.get("tokenizer");void 0!==a&&(this.tokenizer=a),e.call(this,t,n)}return t.prototype.bind=function(e,t,n){e.call(this,t,n),this.$search=t.dropdown.$search||t.selection.$search||n.find(".select2-search__field")},t.prototype.query=function(t,n,a){var s=this;function r(t){var n=s._normalizeItem(t);if(!s.$element.find("option").filter((function(){return e(this).val()===n.id})).length){var a=s.option(n);a.attr("data-select2-tag",!0),s._removeOldTags(),s.addOptions([a])}i(n)}function i(e){s.trigger("select",{data:e})}n.term=n.term||"";var o=this.tokenizer(n,this.options,r);o.term!==n.term&&(this.$search.length&&(this.$search.val(o.term),this.$search.trigger("focus")),n.term=o.term),t.call(this,n,a)},t.prototype.tokenizer=function(t,n,a,s){for(var r=a.get("tokenSeparators")||[],i=n.term,o=0,d=this.createTag||function(e){return{id:e.term,text:e.term}};o<i.length;){var u=i[o];if(-1!==e.inArray(u,r)){var l=i.substr(0,o),c=d(e.extend({},n,{term:l}));null!=c?(s(c),i=i.substr(o+1)||"",o=0):o++}else o++}return{term:i}},t})),t.define("select2/data/minimumInputLength",[],(function(){function e(e,t,n){this.minimumInputLength=n.get("minimumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",t.term.length<this.minimumInputLength?this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e})),t.define("select2/data/maximumInputLength",[],(function(){function e(e,t,n){this.maximumInputLength=n.get("maximumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",this.maximumInputLength>0&&t.term.length>this.maximumInputLength?this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e})),t.define("select2/data/maximumSelectionLength",[],(function(){function e(e,t,n){this.maximumSelectionLength=n.get("maximumSelectionLength"),e.call(this,t,n)}return e.prototype.bind=function(e,t,n){var a=this;e.call(this,t,n),t.on("select",(function(){a._checkIfMaximumSelected()}))},e.prototype.query=function(e,t,n){var a=this;this._checkIfMaximumSelected((function(){e.call(a,t,n)}))},e.prototype._checkIfMaximumSelected=function(e,t){var n=this;this.current((function(e){var a=null!=e?e.length:0;n.maximumSelectionLength>0&&a>=n.maximumSelectionLength?n.trigger("results:message",{message:"maximumSelected",args:{maximum:n.maximumSelectionLength}}):t&&t()}))},e})),t.define("select2/dropdown",["jquery","./utils"],(function(e,t){function n(e,t){this.$element=e,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<span class="select2-dropdown"><span class="select2-results"></span></span>');return t.attr("dir",this.options.get("dir")),this.$dropdown=t,t},n.prototype.bind=function(){},n.prototype.position=function(e,t){},n.prototype.destroy=function(){this.$dropdown.remove()},n})),t.define("select2/dropdown/search",["jquery","../utils"],(function(e,t){function n(){}return n.prototype.render=function(t){var n=t.call(this),a=e('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" /></span>');return this.$searchContainer=a,this.$search=a.find("input"),n.prepend(a),n},n.prototype.bind=function(t,n,a){var s=this,r=n.id+"-results";t.call(this,n,a),this.$search.on("keydown",(function(e){s.trigger("keypress",e),s._keyUpPrevented=e.isDefaultPrevented()})),this.$search.on("input",(function(t){e(this).off("keyup")})),this.$search.on("keyup input",(function(e){s.handleSearch(e)})),n.on("open",(function(){s.$search.attr("tabindex",0),s.$search.attr("aria-controls",r),s.$search.trigger("focus"),window.setTimeout((function(){s.$search.trigger("focus")}),0)})),n.on("close",(function(){s.$search.attr("tabindex",-1),s.$search.removeAttr("aria-controls"),s.$search.removeAttr("aria-activedescendant"),s.$search.val(""),s.$search.trigger("blur")})),n.on("focus",(function(){n.isOpen()||s.$search.trigger("focus")})),n.on("results:all",(function(e){null!=e.query.term&&""!==e.query.term||(s.showSearch(e)?s.$searchContainer.removeClass("select2-search--hide"):s.$searchContainer.addClass("select2-search--hide"))})),n.on("results:focus",(function(e){e.data._resultId?s.$search.attr("aria-activedescendant",e.data._resultId):s.$search.removeAttr("aria-activedescendant")}))},n.prototype.handleSearch=function(e){if(!this._keyUpPrevented){var t=this.$search.val();this.trigger("query",{term:t})}this._keyUpPrevented=!1},n.prototype.showSearch=function(e,t){return!0},n})),t.define("select2/dropdown/hidePlaceholder",[],(function(){function e(e,t,n,a){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n,a)}return e.prototype.append=function(e,t){t.results=this.removePlaceholder(t.results),e.call(this,t)},e.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},e.prototype.removePlaceholder=function(e,t){for(var n=t.slice(0),a=t.length-1;a>=0;a--){var s=t[a];this.placeholder.id===s.id&&n.splice(a,1)}return n},e})),t.define("select2/dropdown/infiniteScroll",["jquery"],(function(e){function t(e,t,n,a){this.lastParams={},e.call(this,t,n,a),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return t.prototype.append=function(e,t){this.$loadingMore.remove(),this.loading=!1,e.call(this,t),this.showLoadingMore(t)&&(this.$results.append(this.$loadingMore),this.loadMoreIfNeeded())},t.prototype.bind=function(e,t,n){var a=this;e.call(this,t,n),t.on("query",(function(e){a.lastParams=e,a.loading=!0})),t.on("query:append",(function(e){a.lastParams=e,a.loading=!0})),this.$results.on("scroll",this.loadMoreIfNeeded.bind(this))},t.prototype.loadMoreIfNeeded=function(){var t=e.contains(document.documentElement,this.$loadingMore[0]);!this.loading&&t&&this.$results.offset().top+this.$results.outerHeight(!1)+50>=this.$loadingMore.offset().top+this.$loadingMore.outerHeight(!1)&&this.loadMore()},t.prototype.loadMore=function(){this.loading=!0;var t=e.extend({},{page:1},this.lastParams);t.page++,this.trigger("query:append",t)},t.prototype.showLoadingMore=function(e,t){return t.pagination&&t.pagination.more},t.prototype.createLoadingMore=function(){var t=e('<li class="select2-results__option select2-results__option--load-more"role="option" aria-disabled="true"></li>'),n=this.options.get("translations").get("loadingMore");return t.html(n(this.lastParams)),t},t})),t.define("select2/dropdown/attachBody",["jquery","../utils"],(function(e,t){function n(t,n,a){this.$dropdownParent=e(a.get("dropdownParent")||document.body),t.call(this,n,a)}return n.prototype.bind=function(e,t,n){var a=this;e.call(this,t,n),t.on("open",(function(){a._showDropdown(),a._attachPositioningHandler(t),a._bindContainerResultHandlers(t)})),t.on("close",(function(){a._hideDropdown(),a._detachPositioningHandler(t)})),this.$dropdownContainer.on("mousedown",(function(e){e.stopPropagation()}))},n.prototype.destroy=function(e){e.call(this),this.$dropdownContainer.remove()},n.prototype.position=function(e,t,n){t.attr("class",n.attr("class")),t.removeClass("select2"),t.addClass("select2-container--open"),t.css({position:"absolute",top:-999999}),this.$container=n},n.prototype.render=function(t){var n=e("<span></span>"),a=t.call(this);return n.append(a),this.$dropdownContainer=n,n},n.prototype._hideDropdown=function(e){this.$dropdownContainer.detach()},n.prototype._bindContainerResultHandlers=function(e,t){if(!this._containerResultsHandlersBound){var n=this;t.on("results:all",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("results:append",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("results:message",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("select",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("unselect",(function(){n._positionDropdown(),n._resizeDropdown()})),this._containerResultsHandlersBound=!0}},n.prototype._attachPositioningHandler=function(n,a){var s=this,r="scroll.select2."+a.id,i="resize.select2."+a.id,o="orientationchange.select2."+a.id,d=this.$container.parents().filter(t.hasScroll);d.each((function(){t.StoreData(this,"select2-scroll-position",{x:e(this).scrollLeft(),y:e(this).scrollTop()})})),d.on(r,(function(n){var a=t.GetData(this,"select2-scroll-position");e(this).scrollTop(a.y)})),e(window).on(r+" "+i+" "+o,(function(e){s._positionDropdown(),s._resizeDropdown()}))},n.prototype._detachPositioningHandler=function(n,a){var s="scroll.select2."+a.id,r="resize.select2."+a.id,i="orientationchange.select2."+a.id;this.$container.parents().filter(t.hasScroll).off(s),e(window).off(s+" "+r+" "+i)},n.prototype._positionDropdown=function(){var t=e(window),n=this.$dropdown.hasClass("select2-dropdown--above"),a=this.$dropdown.hasClass("select2-dropdown--below"),s=null,r=this.$container.offset();r.bottom=r.top+this.$container.outerHeight(!1);var i={height:this.$container.outerHeight(!1)};i.top=r.top,i.bottom=r.top+i.height;var o={height:this.$dropdown.outerHeight(!1)},d={top:t.scrollTop(),bottom:t.scrollTop()+t.height()},u=d.top<r.top-o.height,l=d.bottom>r.bottom+o.height,c={left:r.left,top:i.bottom},_=this.$dropdownParent;"static"===_.css("position")&&(_=_.offsetParent());var m={top:0,left:0};(e.contains(document.body,_[0])||_[0].isConnected)&&(m=_.offset()),c.top-=m.top,c.left-=m.left,n||a||(s="below"),l||!u||n?!u&&l&&n&&(s="below"):s="above",("above"==s||n&&"below"!==s)&&(c.top=i.top-m.top-o.height),null!=s&&(this.$dropdown.removeClass("select2-dropdown--below select2-dropdown--above").addClass("select2-dropdown--"+s),this.$container.removeClass("select2-container--below select2-container--above").addClass("select2-container--"+s)),this.$dropdownContainer.css(c)},n.prototype._resizeDropdown=function(){var e={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(e.minWidth=e.width,e.position="relative",e.width="auto"),this.$dropdown.css(e)},n.prototype._showDropdown=function(e){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},n})),t.define("select2/dropdown/minimumResultsForSearch",[],(function(){function e(t){for(var n=0,a=0;a<t.length;a++){var s=t[a];s.children?n+=e(s.children):n++}return n}function t(e,t,n,a){this.minimumResultsForSearch=n.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),e.call(this,t,n,a)}return t.prototype.showSearch=function(t,n){return!(e(n.data.results)<this.minimumResultsForSearch)&&t.call(this,n)},t})),t.define("select2/dropdown/selectOnClose",["../utils"],(function(e){function t(){}return t.prototype.bind=function(e,t,n){var a=this;e.call(this,t,n),t.on("close",(function(e){a._handleSelectOnClose(e)}))},t.prototype._handleSelectOnClose=function(t,n){if(n&&null!=n.originalSelect2Event){var a=n.originalSelect2Event;if("select"===a._type||"unselect"===a._type)return}var s=this.getHighlightedResults();if(!(s.length<1)){var r=e.GetData(s[0],"data");null!=r.element&&r.element.selected||null==r.element&&r.selected||this.trigger("select",{data:r})}},t})),t.define("select2/dropdown/closeOnSelect",[],(function(){function e(){}return e.prototype.bind=function(e,t,n){var a=this;e.call(this,t,n),t.on("select",(function(e){a._selectTriggered(e)})),t.on("unselect",(function(e){a._selectTriggered(e)}))},e.prototype._selectTriggered=function(e,t){var n=t.originalEvent;n&&(n.ctrlKey||n.metaKey)||this.trigger("close",{originalEvent:n,originalSelect2Event:t})},e})),t.define("select2/i18n/en",[],(function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(e){var t=e.input.length-e.maximum,n="Please delete "+t+" character";return 1!=t&&(n+="s"),n},inputTooShort:function(e){return"Please enter "+(e.minimum-e.input.length)+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(e){var t="You can only select "+e.maximum+" item";return 1!=e.maximum&&(t+="s"),t},noResults:function(){return"No results found"},searching:function(){return"Searching…"},removeAllItems:function(){return"Remove all items"}}})),t.define("select2/defaults",["jquery","require","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./i18n/en"],(function(e,t,n,a,s,r,i,o,d,u,l,c,_,m,h,p,f,y,M,g,L,Y,v,w,b,k,D,T,S){function x(){this.reset()}return x.prototype.apply=function(l){if(null==(l=e.extend(!0,{},this.defaults,l)).dataAdapter){if(null!=l.ajax?l.dataAdapter=h:null!=l.data?l.dataAdapter=m:l.dataAdapter=_,l.minimumInputLength>0&&(l.dataAdapter=u.Decorate(l.dataAdapter,y)),l.maximumInputLength>0&&(l.dataAdapter=u.Decorate(l.dataAdapter,M)),l.maximumSelectionLength>0&&(l.dataAdapter=u.Decorate(l.dataAdapter,g)),l.tags&&(l.dataAdapter=u.Decorate(l.dataAdapter,p)),null==l.tokenSeparators&&null==l.tokenizer||(l.dataAdapter=u.Decorate(l.dataAdapter,f)),null!=l.query){var c=t(l.amdBase+"compat/query");l.dataAdapter=u.Decorate(l.dataAdapter,c)}if(null!=l.initSelection){var S=t(l.amdBase+"compat/initSelection");l.dataAdapter=u.Decorate(l.dataAdapter,S)}}if(null==l.resultsAdapter&&(l.resultsAdapter=n,null!=l.ajax&&(l.resultsAdapter=u.Decorate(l.resultsAdapter,w)),null!=l.placeholder&&(l.resultsAdapter=u.Decorate(l.resultsAdapter,v)),l.selectOnClose&&(l.resultsAdapter=u.Decorate(l.resultsAdapter,D))),null==l.dropdownAdapter){if(l.multiple)l.dropdownAdapter=L;else{var x=u.Decorate(L,Y);l.dropdownAdapter=x}if(0!==l.minimumResultsForSearch&&(l.dropdownAdapter=u.Decorate(l.dropdownAdapter,k)),l.closeOnSelect&&(l.dropdownAdapter=u.Decorate(l.dropdownAdapter,T)),null!=l.dropdownCssClass||null!=l.dropdownCss||null!=l.adaptDropdownCssClass){var H=t(l.amdBase+"compat/dropdownCss");l.dropdownAdapter=u.Decorate(l.dropdownAdapter,H)}l.dropdownAdapter=u.Decorate(l.dropdownAdapter,b)}if(null==l.selectionAdapter){if(l.multiple?l.selectionAdapter=s:l.selectionAdapter=a,null!=l.placeholder&&(l.selectionAdapter=u.Decorate(l.selectionAdapter,r)),l.allowClear&&(l.selectionAdapter=u.Decorate(l.selectionAdapter,i)),l.multiple&&(l.selectionAdapter=u.Decorate(l.selectionAdapter,o)),null!=l.containerCssClass||null!=l.containerCss||null!=l.adaptContainerCssClass){var j=t(l.amdBase+"compat/containerCss");l.selectionAdapter=u.Decorate(l.selectionAdapter,j)}l.selectionAdapter=u.Decorate(l.selectionAdapter,d)}l.language=this._resolveLanguage(l.language),l.language.push("en");for(var O=[],P=0;P<l.language.length;P++){var E=l.language[P];-1===O.indexOf(E)&&O.push(E)}return l.language=O,l.translations=this._processTranslations(l.language,l.debug),l},x.prototype.reset=function(){function t(e){function t(e){return c[e]||e}return e.replace(/[^\u0000-\u007E]/g,t)}function n(a,s){if(""===e.trim(a.term))return s;if(s.children&&s.children.length>0){for(var r=e.extend(!0,{},s),i=s.children.length-1;i>=0;i--)null==n(a,s.children[i])&&r.children.splice(i,1);return r.children.length>0?r:n(a,r)}var o=t(s.text).toUpperCase(),d=t(a.term).toUpperCase();return o.indexOf(d)>-1?s:null}this.defaults={amdBase:"./",amdLanguageBase:"./i18n/",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:u.escapeMarkup,language:{},matcher:n,minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,scrollAfterSelect:!1,sorter:function(e){return e},templateResult:function(e){return e.text},templateSelection:function(e){return e.text},theme:"default",width:"resolve"}},x.prototype.applyFromElement=function(e,t){var n=e.language,a=this.defaults.language,s=t.prop("lang"),r=t.closest("[lang]").prop("lang"),i=Array.prototype.concat.call(this._resolveLanguage(s),this._resolveLanguage(n),this._resolveLanguage(a),this._resolveLanguage(r));return e.language=i,e},x.prototype._resolveLanguage=function(t){if(!t)return[];if(e.isEmptyObject(t))return[];if(e.isPlainObject(t))return[t];var n;n=e.isArray(t)?t:[t];for(var a=[],s=0;s<n.length;s++)if(a.push(n[s]),"string"==typeof n[s]&&n[s].indexOf("-")>0){var r=n[s].split("-")[0];a.push(r)}return a},x.prototype._processTranslations=function(t,n){for(var a=new l,s=0;s<t.length;s++){var r=new l,i=t[s];if("string"==typeof i)try{r=l.loadPath(i)}catch(e){try{i=this.defaults.amdLanguageBase+i,r=l.loadPath(i)}catch(e){n&&window.console&&console.warn&&console.warn('Select2: The language file for "'+i+'" could not be automatically loaded. A fallback will be used instead.')}}else r=e.isPlainObject(i)?new l(i):i;a.extend(r)}return a},x.prototype.set=function(t,n){var a={};a[e.camelCase(t)]=n;var s=u._convertData(a);e.extend(!0,this.defaults,s)},new x})),t.define("select2/options",["require","jquery","./defaults","./utils"],(function(e,t,n,a){function s(t,s){if(this.options=t,null!=s&&this.fromElement(s),null!=s&&(this.options=n.applyFromElement(this.options,s)),this.options=n.apply(this.options),s&&s.is("input")){var r=e(this.get("amdBase")+"compat/inputData");this.options.dataAdapter=a.Decorate(this.options.dataAdapter,r)}}return s.prototype.fromElement=function(e){var n=["select2"];null==this.options.multiple&&(this.options.multiple=e.prop("multiple")),null==this.options.disabled&&(this.options.disabled=e.prop("disabled")),null==this.options.dir&&(e.prop("dir")?this.options.dir=e.prop("dir"):e.closest("[dir]").prop("dir")?this.options.dir=e.closest("[dir]").prop("dir"):this.options.dir="ltr"),e.prop("disabled",this.options.disabled),e.prop("multiple",this.options.multiple),a.GetData(e[0],"select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),a.StoreData(e[0],"data",a.GetData(e[0],"select2Tags")),a.StoreData(e[0],"tags",!0)),a.GetData(e[0],"ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),e.attr("ajax--url",a.GetData(e[0],"ajaxUrl")),a.StoreData(e[0],"ajax-Url",a.GetData(e[0],"ajaxUrl")));var s={};function r(e,t){return t.toUpperCase()}for(var i=0;i<e[0].attributes.length;i++){var o=e[0].attributes[i].name,d="data-";if(o.substr(0,d.length)==d){var u=o.substring(d.length),l=a.GetData(e[0],u);s[u.replace(/-([a-z])/g,r)]=l}}t.fn.jquery&&"1."==t.fn.jquery.substr(0,2)&&e[0].dataset&&(s=t.extend(!0,{},e[0].dataset,s));var c=t.extend(!0,{},a.GetData(e[0]),s);for(var _ in c=a._convertData(c))t.inArray(_,n)>-1||(t.isPlainObject(this.options[_])?t.extend(this.options[_],c[_]):this.options[_]=c[_]);return this},s.prototype.get=function(e){return this.options[e]},s.prototype.set=function(e,t){this.options[e]=t},s})),t.define("select2/core",["jquery","./options","./utils","./keys"],(function(e,t,n,a){var s=function(e,a){null!=n.GetData(e[0],"select2")&&n.GetData(e[0],"select2").destroy(),this.$element=e,this.id=this._generateId(e),a=a||{},this.options=new t(a,e),s.__super__.constructor.call(this);var r=e.attr("tabindex")||0;n.StoreData(e[0],"old-tabindex",r),e.attr("tabindex","-1");var i=this.options.get("dataAdapter");this.dataAdapter=new i(e,this.options);var o=this.render();this._placeContainer(o);var d=this.options.get("selectionAdapter");this.selection=new d(e,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,o);var u=this.options.get("dropdownAdapter");this.dropdown=new u(e,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,o);var l=this.options.get("resultsAdapter");this.results=new l(e,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown);var c=this;this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),this.dataAdapter.current((function(e){c.trigger("selection:update",{data:e})})),e.addClass("select2-hidden-accessible"),e.attr("aria-hidden","true"),this._syncAttributes(),n.StoreData(e[0],"select2",this),e.data("select2",this)};return n.Extend(s,n.Observable),s.prototype._generateId=function(e){return"select2-"+(null!=e.attr("id")?e.attr("id"):null!=e.attr("name")?e.attr("name")+"-"+n.generateChars(2):n.generateChars(4)).replace(/(:|\.|\[|\]|,)/g,"")},s.prototype._placeContainer=function(e){e.insertAfter(this.$element);var t=this._resolveWidth(this.$element,this.options.get("width"));null!=t&&e.css("width",t)},s.prototype._resolveWidth=function(e,t){var n=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==t){var a=this._resolveWidth(e,"style");return null!=a?a:this._resolveWidth(e,"element")}if("element"==t){var s=e.outerWidth(!1);return s<=0?"auto":s+"px"}if("style"==t){var r=e.attr("style");if("string"!=typeof r)return null;for(var i=r.split(";"),o=0,d=i.length;o<d;o+=1){var u=i[o].replace(/\s/g,"").match(n);if(null!==u&&u.length>=1)return u[1]}return null}return"computedstyle"==t?window.getComputedStyle(e[0]).width:t},s.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},s.prototype._registerDomEvents=function(){var e=this;this.$element.on("change.select2",(function(){e.dataAdapter.current((function(t){e.trigger("selection:update",{data:t})}))})),this.$element.on("focus.select2",(function(t){e.trigger("focus",t)})),this._syncA=n.bind(this._syncAttributes,this),this._syncS=n.bind(this._syncSubtree,this),this.$element[0].attachEvent&&this.$element[0].attachEvent("onpropertychange",this._syncA);var t=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;null!=t?(this._observer=new t((function(t){e._syncA(),e._syncS(null,t)})),this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})):this.$element[0].addEventListener&&(this.$element[0].addEventListener("DOMAttrModified",e._syncA,!1),this.$element[0].addEventListener("DOMNodeInserted",e._syncS,!1),this.$element[0].addEventListener("DOMNodeRemoved",e._syncS,!1))},s.prototype._registerDataEvents=function(){var e=this;this.dataAdapter.on("*",(function(t,n){e.trigger(t,n)}))},s.prototype._registerSelectionEvents=function(){var t=this,n=["toggle","focus"];this.selection.on("toggle",(function(){t.toggleDropdown()})),this.selection.on("focus",(function(e){t.focus(e)})),this.selection.on("*",(function(a,s){-1===e.inArray(a,n)&&t.trigger(a,s)}))},s.prototype._registerDropdownEvents=function(){var e=this;this.dropdown.on("*",(function(t,n){e.trigger(t,n)}))},s.prototype._registerResultsEvents=function(){var e=this;this.results.on("*",(function(t,n){e.trigger(t,n)}))},s.prototype._registerEvents=function(){var e=this;this.on("open",(function(){e.$container.addClass("select2-container--open")})),this.on("close",(function(){e.$container.removeClass("select2-container--open")})),this.on("enable",(function(){e.$container.removeClass("select2-container--disabled")})),this.on("disable",(function(){e.$container.addClass("select2-container--disabled")})),this.on("blur",(function(){e.$container.removeClass("select2-container--focus")})),this.on("query",(function(t){e.isOpen()||e.trigger("open",{}),this.dataAdapter.query(t,(function(n){e.trigger("results:all",{data:n,query:t})}))})),this.on("query:append",(function(t){this.dataAdapter.query(t,(function(n){e.trigger("results:append",{data:n,query:t})}))})),this.on("keypress",(function(t){var n=t.which;e.isOpen()?n===a.ESC||n===a.TAB||n===a.UP&&t.altKey?(e.close(t),t.preventDefault()):n===a.ENTER?(e.trigger("results:select",{}),t.preventDefault()):n===a.SPACE&&t.ctrlKey?(e.trigger("results:toggle",{}),t.preventDefault()):n===a.UP?(e.trigger("results:previous",{}),t.preventDefault()):n===a.DOWN&&(e.trigger("results:next",{}),t.preventDefault()):(n===a.ENTER||n===a.SPACE||n===a.DOWN&&t.altKey)&&(e.open(),t.preventDefault())}))},s.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.isDisabled()?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},s.prototype._isChangeMutation=function(t,n){var a=!1,s=this;if(!t||!t.target||"OPTION"===t.target.nodeName||"OPTGROUP"===t.target.nodeName){if(n)if(n.addedNodes&&n.addedNodes.length>0)for(var r=0;r<n.addedNodes.length;r++)n.addedNodes[r].selected&&(a=!0);else n.removedNodes&&n.removedNodes.length>0?a=!0:e.isArray(n)&&e.each(n,(function(e,t){if(s._isChangeMutation(e,t))return a=!0,!1}));else a=!0;return a}},s.prototype._syncSubtree=function(e,t){var n=this._isChangeMutation(e,t),a=this;n&&this.dataAdapter.current((function(e){a.trigger("selection:update",{data:e})}))},s.prototype.trigger=function(e,t){var n=s.__super__.trigger,a={open:"opening",close:"closing",select:"selecting",unselect:"unselecting",clear:"clearing"};if(void 0===t&&(t={}),e in a){var r=a[e],i={prevented:!1,name:e,args:t};if(n.call(this,r,i),i.prevented)return void(t.prevented=!0)}n.call(this,e,t)},s.prototype.toggleDropdown=function(){this.isDisabled()||(this.isOpen()?this.close():this.open())},s.prototype.open=function(){this.isOpen()||this.isDisabled()||this.trigger("query",{})},s.prototype.close=function(e){this.isOpen()&&this.trigger("close",{originalEvent:e})},s.prototype.isEnabled=function(){return!this.isDisabled()},s.prototype.isDisabled=function(){return this.options.get("disabled")},s.prototype.isOpen=function(){return this.$container.hasClass("select2-container--open")},s.prototype.hasFocus=function(){return this.$container.hasClass("select2-container--focus")},s.prototype.focus=function(e){this.hasFocus()||(this.$container.addClass("select2-container--focus"),this.trigger("focus",{}))},s.prototype.enable=function(e){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.'),null!=e&&0!==e.length||(e=[!0]);var t=!e[0];this.$element.prop("disabled",t)},s.prototype.data=function(){this.options.get("debug")&&arguments.length>0&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var e=[];return this.dataAdapter.current((function(t){e=t})),e},s.prototype.val=function(t){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),null==t||0===t.length)return this.$element.val();var n=t[0];e.isArray(n)&&(n=e.map(n,(function(e){return e.toString()}))),this.$element.val(n).trigger("input").trigger("change")},s.prototype.destroy=function(){this.$container.remove(),this.$element[0].detachEvent&&this.$element[0].detachEvent("onpropertychange",this._syncA),null!=this._observer?(this._observer.disconnect(),this._observer=null):this.$element[0].removeEventListener&&(this.$element[0].removeEventListener("DOMAttrModified",this._syncA,!1),this.$element[0].removeEventListener("DOMNodeInserted",this._syncS,!1),this.$element[0].removeEventListener("DOMNodeRemoved",this._syncS,!1)),this._syncA=null,this._syncS=null,this.$element.off(".select2"),this.$element.attr("tabindex",n.GetData(this.$element[0],"old-tabindex")),this.$element.removeClass("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),n.RemoveData(this.$element[0]),this.$element.removeData("select2"),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},s.prototype.render=function(){var t=e('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return t.attr("dir",this.options.get("dir")),this.$container=t,this.$container.addClass("select2-container--"+this.options.get("theme")),n.StoreData(t[0],"element",this.$element),t},s})),t.define("jquery-mousewheel",["jquery"],(function(e){return e})),t.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults","./select2/utils"],(function(e,t,n,a,s){if(null==e.fn.select2){var r=["open","close","destroy"];e.fn.select2=function(t){if("object"==typeof(t=t||{}))return this.each((function(){var a=e.extend(!0,{},t);new n(e(this),a)})),this;if("string"==typeof t){var a,i=Array.prototype.slice.call(arguments,1);return this.each((function(){var e=s.GetData(this,"select2");null==e&&window.console&&console.error&&console.error("The select2('"+t+"') method was called on an element that is not using Select2."),a=e[t].apply(e,i)})),e.inArray(t,r)>-1?this:a}throw new Error("Invalid arguments for Select2: "+t)}}return null==e.fn.select2.defaults&&(e.fn.select2.defaults=a),n})),{define:t.define,require:t.require}}(),n=t.require("jquery.select2");return e.fn.select2.amd=t,n},void 0===(r="function"==typeof a?a.apply(t,s):a)||(e.exports=r)},7956:function(e,t,n){e.exports=function(e){"use strict";const t=/[^.]*(?=\..*)\.|.*/,n=/\..*/,a=/::\d+$/,s={};let r=1;const i={mouseenter:"mouseover",mouseleave:"mouseout"},o=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function d(e,t){return t&&`${t}::${r++}`||e.uidEvent||r++}function u(e){const t=d(e);return e.uidEvent=t,s[t]=s[t]||{},s[t]}function l(e,t){return function n(a){return g(a,{delegateTarget:e}),n.oneOff&&M.off(e,a.type,t),t.apply(e,[a])}}function c(e,t,n){return function a(s){const r=e.querySelectorAll(t);for(let{target:i}=s;i&&i!==this;i=i.parentNode)for(const o of r)if(o===i)return g(s,{delegateTarget:i}),a.oneOff&&M.off(e,s.type,t,n),n.apply(i,[s])}}function _(e,t,n=null){return Object.values(e).find((e=>e.callable===t&&e.delegationSelector===n))}function m(e,t,n){const a="string"==typeof t,s=a?n:t||n;let r=y(e);return o.has(r)||(r=e),[a,s,r]}function h(e,n,a,s,r){if("string"!=typeof n||!e)return;let[o,h,p]=m(n,a,s);if(n in i){const e=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)};h=e(h)}const f=u(e),y=f[p]||(f[p]={}),M=_(y,h,o?a:null);if(M)return void(M.oneOff=M.oneOff&&r);const g=d(h,n.replace(t,"")),L=o?c(e,a,h):l(e,h);L.delegationSelector=o?a:null,L.callable=h,L.oneOff=r,L.uidEvent=g,y[g]=L,e.addEventListener(p,L,o)}function p(e,t,n,a,s){const r=_(t[n],a,s);r&&(e.removeEventListener(n,r,Boolean(s)),delete t[n][r.uidEvent])}function f(e,t,n,a){const s=t[n]||{};for(const[r,i]of Object.entries(s))r.includes(a)&&p(e,t,n,i.callable,i.delegationSelector)}function y(e){return e=e.replace(n,""),i[e]||e}const M={on(e,t,n,a){h(e,t,n,a,!1)},one(e,t,n,a){h(e,t,n,a,!0)},off(e,t,n,s){if("string"!=typeof t||!e)return;const[r,i,o]=m(t,n,s),d=o!==t,l=u(e),c=l[o]||{},_=t.startsWith(".");if(void 0===i){if(_)for(const n of Object.keys(l))f(e,l,n,t.slice(1));for(const[n,s]of Object.entries(c)){const r=n.replace(a,"");d&&!t.includes(r)||p(e,l,o,s.callable,s.delegationSelector)}}else{if(!Object.keys(c).length)return;p(e,l,o,i,r?n:null)}},trigger(t,n,a){if("string"!=typeof n||!t)return null;const s=e.getjQuery();let r=null,i=!0,o=!0,d=!1;n!==y(n)&&s&&(r=s.Event(n,a),s(t).trigger(r),i=!r.isPropagationStopped(),o=!r.isImmediatePropagationStopped(),d=r.isDefaultPrevented());const u=g(new Event(n,{bubbles:i,cancelable:!0}),a);return d&&u.preventDefault(),o&&t.dispatchEvent(u),u.defaultPrevented&&r&&r.preventDefault(),u}};function g(e,t={}){for(const[n,a]of Object.entries(t))try{e[n]=a}catch(t){Object.defineProperty(e,n,{configurable:!0,get:()=>a})}return e}return M}(n(4035))},9011:function(e,t,n){e.exports=function(e,t,n,a){"use strict";const s="5.3.7";class r extends n{constructor(t,n){super(),(t=a.getElement(t))&&(this._element=t,this._config=this._getConfig(n),e.set(this._element,this.constructor.DATA_KEY,this))}dispose(){e.remove(this._element,this.constructor.DATA_KEY),t.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){a.executeAfterTransition(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(t){return e.get(a.getElement(t),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return s}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}return r}(n(7269),n(7956),n(2105),n(4035))}},t={};function n(a){var s=t[a];if(void 0!==s)return s.exports;var r=t[a]={id:a,loaded:!1,exports:{}};return e[a].call(r.exports,r,r.exports,n),r.loaded=!0,r.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{"use strict";n(7855);var e=n(1669),t=function(){return t=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},t.apply(this,arguments)};const a=function(){function n(t){void 0===t&&(t=null);var n=this;this.addAtumClasses(t),e("body").on("wc-enhanced-select-init",(function(){return n.addAtumClasses(t)}))}return n.prototype.maybeRestoreEnhancedSelect=function(){e(".select2-container--open").remove(),e("body").trigger("wc-enhanced-select-init")},n.prototype.doSelect2=function(n,a,s){var r=this;void 0===a&&(a={}),void 0===s&&(s=!1),"function"==typeof e.fn.select2&&(a=Object.assign({minimumResultsForSearch:10},a),n.each((function(n,i){var o=e(i),d=t({},a);o.hasClass("atum-select-multiple")&&!1===o.prop("multiple")&&o.prop("multiple",!0),o.hasClass("atum-select2")||(o.addClass("atum-select2"),r.addAtumClasses(o)),s&&o.on("select2:selecting",(function(t){var n=e(t.currentTarget),a=n.val();Array.isArray(a)&&(e.inArray("",a)>-1||e.inArray("-1",a)>-1)&&(e.each(a,(function(e,t){""!==t&&"-1"!==t||a.splice(e,1)})),n.val(a))})),o.select2(d),o.siblings(".select2-container").addClass("atum-select2"),r.maybeAddTooltip(o)})))},n.prototype.addAtumClasses=function(t){var n=this;void 0===t&&(t=null),(t=t||e("select").filter(".atum-select2, .atum-enhanced-select")).length&&t.each((function(t,a){var s=e(a),r=s.siblings(".select2-container").not(".atum-select2, .atum-enhanced-select");r.length&&(r.addClass(s.hasClass("atum-select2")?"atum-select2":"atum-enhanced-select"),n.maybeAddTooltip(s))})).on("select2:opening",(function(t){var n=e(t.currentTarget).data();if(n.hasOwnProperty("select2")){var a=n.select2.dropdown.$dropdown;a.length&&a.addClass("atum-select2-dropdown")}}))},n.prototype.maybeAddTooltip=function(e){e.hasClass("atum-tooltip")&&e.siblings(".select2-container").find(".select2-selection__rendered").addClass("atum-tooltip")},n}();const s=function(){function e(e,t){void 0===t&&(t={}),this.varName=e,this.defaults=t,this.settings={};var n=void 0!==window[e]?window[e]:{};Object.assign(this.settings,t,n)}return e.prototype.get=function(e){if(void 0!==this.settings[e])return this.settings[e]},e.prototype.getAll=function(){return this.settings},e.prototype.delete=function(e){this.settings.hasOwnProperty(e)&&delete this.settings[e]},e}();var r=n(1669);const i={doButtonGroups:function(e){var t=this;e.on("click",".btn-group .btn",(function(e){var n=r(e.currentTarget);return n.find(":checkbox").length?n.toggleClass("active"):(n.siblings(".active").removeClass("active"),n.addClass("active")),t.updateChecked(n.closest(".btn-group")),n.find("input").trigger("change"),!1}))},updateChecked:function(e){e.find(".btn").each((function(e,t){var n=r(t);n.find("input").prop("checked",n.hasClass("active"))}))}};var o=n(1669);const d={doColorPickers:function(e){o(".atum-color").each((function(e,t){o(t).wpColorPicker({change:function(e,t){var n=t.color.toString(),a=o(e.target).closest(".wp-picker-container");a.find(".color-picker-preview").css("background-color",n),a.find(".wp-color-result-text").html(n)},clear:function(e){var t=o(e.target).closest(".wp-picker-container");t.find(".color-picker-preview").css("background-color",""),t.find(".wp-color-result-text").html("")}})})),o(".wp-color-result").prepend('<span class="color-picker-preview"></span>'),o(".wp-picker-container").addClass("atum-color-picker"),this.updatePreviewValues()},updateColorPicker:function(e,t){e.wpColorPicker("color",t),this.updatePreviewValues()},updatePreviewValues:function(){o(".wp-picker-container").each((function(e,t){var n=o(t),a=n.find(".atum-color").val();n.find(".color-picker-preview").css("background-color",a)}))}};var u=n(1669),l=function(){return l=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},l.apply(this,arguments)};const c=function(){function e(e,t,n){void 0===n&&(n=!1),this.$buttons=e,this.options=t,this.preview=n,this.defaultOptions={frame:"select",multiple:!1},this.wpHooks=window.wp.hooks,this.doFileUploaders()}return e.prototype.doFileUploaders=function(){var e=this;window.wp.hasOwnProperty("media")&&this.$buttons.on("click",(function(t){var n=u(t.currentTarget),a=l(l({},e.defaultOptions),e.options);n.data("modal-title")&&(a.title=n.data("modal-title")),n.data("modal-button")&&(a.button={text:n.data("modal-button")});var s=window.wp.media(a).on("select",(function(){var t=s.state().get("selection"),r=a.multiple?t.toJSON():t.first().toJSON(),i=n.siblings("input:hidden");if(a.multiple){var o=[];r.forEach((function(e){o.push(e.id)})),i.val(JSON.stringify(e.wpHooks.applyFilters("atum_fileUploader_inputVal",o,i)))}else i.val(e.wpHooks.applyFilters("atum_fileUploader_inputVal",r.id,i));e.preview&&(!a.library.type||a.library.type.indexOf("image")>-1)&&(n.siblings("img").remove(),a.multiple?r.forEach((function(e){n.after('<img class="atum-file-uploader__preview" src="'.concat(e.url,'">'))})):n.after('<img class="atum-file-uploader__preview" src="'.concat(r.url,'">'))),e.wpHooks.doAction("atum_fileUploader_selected",s,n)})).open()}))},e}();var _=n(1669);const m={addPrompt:function(e){_(window).on("beforeunload.atum",(function(){if(!e()){if(navigator.userAgent.toLowerCase().match(/msie|chrome/)){if(window.aysHasPrompted)return;window.aysHasPrompted=!0,window.setTimeout((function(){return window.aysHasPrompted=!1}),900)}return!1}}))},removePrompt:function(){_(window).off("beforeunload.atum")}};var h=n(1669);const p=function(){function e(e,t,n){var a=this;this.$form=e,this.atumPrefix=t,this.dependantWrapperSelector=n,this.$form.on("change",":input",(function(e){h(".atum-nav-link.active").parent().hasClass("no-submit")||h(e.currentTarget).addClass("dirty")})).on("click","input[type=submit]",(function(){e.find(".dirty").removeClass("dirty"),e.find(".form-settings-wrapper").addClass("overlay")})).on("change","[data-dependency]",(function(e){var t=h(e.currentTarget),n=t.val(),s=t.data("dependency");Array.isArray(s)?h.each(s,(function(e,s){return a.checkDependency(t,s,n)})):a.checkDependency(t,s,n)})).find("[data-dependency]").each((function(e,t){h(t).trigger("change").removeClass("dirty")})),m.addPrompt((function(){return!e.find(".dirty").length}))}return e.prototype.checkDependency=function(e,t,n){var a,s,r;if((!e.is(":radio")||e.is(":checked"))&&(r=e.is(":checkbox")||e.is(":radio")?n===t.value&&e.is(":checked")||n!==t.value&&!e.is(":checked"):n===t.value,t.hasOwnProperty("section")?s=this.$form.find('[data-section="'.concat(t.section,'"]')):t.hasOwnProperty("field")&&(a=h("#".concat(this.atumPrefix+t.field))).length&&(s=this.dependantWrapperSelector?a.closest(this.dependantWrapperSelector):a.closest("tr").find("th, td")),void 0!==s&&s.length))if(!0===r)t.hasOwnProperty("animated")&&!0!==t.animated?s.show():s.slideDown("fast");else if(t.hasOwnProperty("animated")&&!0!==t.animated?s.hide():s.slideUp("fast"),t.hasOwnProperty("resetDefault")&&!0===t.resetDefault){var i=a.data("default"),o=a.val();a.is(":radio")||a.is(":checkbox")?a.prop("checked",i===o):a.val(i),a.trigger("change"),this.$form.trigger("atum-smart-form-reset-default",[a])}},e}(),f=Swal;var y=n.n(f),M=(n(4174),n(1669));const g=function(){function e(e,t){var n=this;this.$tabsWrapper=e,this.$nav=t,this.navigationReady=!1,this.numHashParameters=0,void 0!==M.address&&M.address.change((function(e){var t=M.address.pathNames(),a=t.length;!0!==n.navigationReady||!a&&n.numHashParameters===a||n.clickTab(t[0]),n.navigationReady=!0})).init((function(){var e=M.address.pathNames();e.length?n.clickTab(e[0]):n.$tabsWrapper.trigger("atum-tab-loader-init");var t=location.search.substr(1),a={};t.split("&").forEach((function(e){var t=e.split("=");a[t[0]]=decodeURIComponent(t[1])})),a.hasOwnProperty("tab")&&n.$tabsWrapper.trigger("atum-tab-loader-page-loaded",[a.tab])}))}return e.prototype.clickTab=function(e){var t=this.$nav.find('.atum-nav-link[data-tab="'.concat(e,'"]'));this.$tabsWrapper.trigger("atum-tab-loader-clicked-tab",[t,e])},e.getCurrentTab=function(){var e=M.address.pathNames();return e.length?e[0]:""},e}();var L=n(1669),Y=function(){return Y=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},Y.apply(this,arguments)},v=function(){function e(e,t,n,a){this.settings=e,this.enhancedSelect=t,this.tooltip=n,this.dateTimePicker=a,this.navigationReady=!1,this.numHashParameters=0,this.wpHooks=window.wp.hooks,this.$settingsWrapper=L(".atum-settings-wrapper"),this.$nav=this.$settingsWrapper.find(".atum-nav"),this.$form=this.$settingsWrapper.find("#atum-settings"),this.setupNavigation(),this.initRangeDateTimePicker(),this.tooltip.addTooltips(this.$form),d.doColorPickers(this.settings.get("selectColor")),this.enhancedSelect.doSelect2(this.$settingsWrapper.find("select"),{},!0),i.doButtonGroups(this.$form),this.doFileUploaders(),this.doImageSelector(),this.doEditors(),this.toggleMenu(),new p(this.$form,this.settings.get("atumPrefix")),this.bindEvents()}return e.prototype.bindEvents=function(){var e=this;this.$form.on("change","#atum_out_stock_threshold",(function(t){return e.maybeClearOutStockThreshold(L(t.currentTarget))})).on("click",".script-runner .tool-runner",(function(t){return e.runScript(L(t.currentTarget))})).on("click",".selector-box",(function(t){return e.doThemeSelector(L(t.currentTarget))})).on("click",".atum-settings-input[type=checkbox]",(function(t){return e.clickCheckbox(L(t.currentTarget))})).on("click",".reset-default-colors",(function(t){return e.doResetDefaultColors(L(t.currentTarget))})).on("change",".atum-multi-checkbox-main",(function(t){return e.toggleMultiCheckboxPanel(L(t.currentTarget))})).on("change",".remove-datepicker-range",(function(t){return e.toggleRangeDateTimeRemove(L(t.currentTarget))})).on("change update blur",".range-datepicker.range-from, .range-datepicker.range-to, .remove-datepicker-range",(function(){return e.setRangeDateTimeInputs()})),L(window).on("load",(function(){L(".footer-box").hasClass("no-style")&&(L("#wpfooter").css("position","relative").show(),L("#wpcontent").css("min-height","95vh"))}))},e.prototype.setupNavigation=function(){var e=this;this.tabLoader=new g(this.$settingsWrapper,this.$nav),this.$settingsWrapper.on("atum-tab-loader-init",(function(){return e.$form.show()})).on("atum-tab-loader-clicked-tab",(function(t,n,a){e.$form.find(".dirty").length?y().fire({title:e.settings.get("areYouSure"),text:e.settings.get("unsavedData"),icon:"warning",showCancelButton:!0,confirmButtonText:e.settings.get("continue"),cancelButtonText:e.settings.get("cancel"),reverseButtons:!0,allowOutsideClick:!1}).then((function(t){t.isConfirmed?e.moveToTab(n):n.blur()})):e.moveToTab(n)}))},e.prototype.hideColors=function(){var e=L("#atum_setting_color_scheme #atum-table-color-settings");if(e.length>0){var t=e.data("display");e.find(".atum-settings-input.atum-color").not("[data-display="+t+"]").closest("tr").hide(),e.find("tr").each((function(t,n){"none"===L(n).css("display")&&L(n).prependTo(e.find("tbody"))}))}},e.prototype.moveToTab=function(e){var t=this,n=this.$form.find(".form-settings-wrapper");this.$nav.find(".atum-nav-link.active").not(e).removeClass("active"),e.addClass("active"),n.addClass("overlay"),this.$form.load("".concat(e.attr("href")," .form-settings-wrapper"),(function(){d.doColorPickers(t.settings.get("selectColor")),t.initRangeDateTimePicker(),t.enhancedSelect.maybeRestoreEnhancedSelect(),t.enhancedSelect.doSelect2(t.$settingsWrapper.find("select"),{},!0),t.doFileUploaders(),t.doImageSelector(),t.doEditors(),t.$form.find("[data-dependency]").trigger("change").removeClass("dirty"),t.$form.show(),t.$form.find("input:submit").toggle(!e.parent().hasClass("no-submit")),t.tooltip.addTooltips(t.$form),t.$settingsWrapper.trigger("atum-settings-page-loaded",[e.data("tab")]),"visual_settings"===e.data("tab")&&t.hideColors(),t.wpHooks.doAction("atum_settingsPage_moveToTab",e)}))},e.prototype.toggleMenu=function(){var e=this.$nav.find(".atum-nav-list");L(".toogle-menu, .atum-nav-link").on("click",(function(){return e.toggleClass("expand-menu")})),L(window).on("resize",(function(){return e.removeClass("expand-menu")}))},e.prototype.maybeClearOutStockThreshold=function(e){var t=this;e.is(":checked")&&this.settings.get("isAnyOostSet")?y().fire({title:"",text:this.settings.get("oostSetClearText"),icon:"question",showCancelButton:!0,confirmButtonText:this.settings.get("startFresh"),cancelButtonText:this.settings.get("useSavedValues"),reverseButtons:!0,allowOutsideClick:!1,allowEscapeKey:!1,allowEnterKey:!1,showLoaderOnConfirm:!0,preConfirm:function(){return new Promise((function(e,n){L.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:t.settings.get("oostSetClearScript"),security:t.settings.get("runnerNonce")},success:function(t){!0!==t.success&&y().showValidationMessage(t.data),e(t.data)}})}))}}).then((function(e){e.isConfirmed&&y().fire({title:t.settings.get("done"),icon:"success",text:e.value,confirmButtonText:t.settings.get("ok")})})):e.is(":checked")||y().fire({title:"",text:this.settings.get("oostDisableText"),icon:"info",confirmButtonText:this.settings.get("ok")})},e.prototype.runScript=function(e){var t=e.closest(".script-runner");t.is(".recurrent")?this.runRecurrentScript(e,t):this.runSingleScript(e,t)},e.prototype.runSingleScript=function(e,t){var n=this;y().fire({title:this.settings.get("areYouSure"),text:t.data("confirm"),icon:"warning",showCancelButton:!0,confirmButtonText:this.settings.get("run"),cancelButtonText:this.settings.get("cancel"),reverseButtons:!0,allowOutsideClick:!1,showLoaderOnConfirm:!0,preConfirm:function(){return new Promise((function(a){var s=t.find('[data-tool="'.concat(t.data("input"),'"]')),r={action:t.data("action"),security:n.settings.get("runnerNonce")};s.length>1?(r.option={},s.each((function(e,t){var n=L(t);r.option[n.attr("name")]=n.val()}))):s.length&&(r.option=s.val()),L.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:r,beforeSend:function(){return e.prop("disabled",!0)},success:function(t){e.prop("disabled",!1),!0!==t.success&&y().showValidationMessage(t.data),a(t.data)},error:function(){e.prop("disabled",!1),y().showValidationMessage(n.settings.get("unexpectedError")),a()}})}))}}).then((function(e){e.isConfirmed&&y().fire({title:n.settings.get("done"),icon:"success",text:e.value,confirmButtonText:n.settings.get("ok")}).then((function(){n.$settingsWrapper.trigger("atum-settings-script-runner-done",[t])}))}))},e.prototype.runRecurrentScript=function(e,t){var n=this;y().fire({title:this.settings.get("areYouSure"),text:t.data("confirm"),icon:"warning",showCancelButton:!0,confirmButtonText:this.settings.get("run"),cancelButtonText:this.settings.get("cancel"),reverseButtons:!0,allowOutsideClick:!1,showLoaderOnConfirm:!0,preConfirm:function(){return new Promise((function(a){var s=t.find('[data-tool="'.concat(t.data("input"),'"]')),r={action:t.data("action"),security:n.settings.get("runnerNonce")};s.length>1?(r.option={},s.each((function(e,t){var n=L(t);r.option[n.attr("name")]=n.val()}))):s.length&&(r.option=s.val());e.prop("disabled",!0);var i=function(s){return void 0===s&&(s=0),function(e){return void 0===e&&(e=0),L.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:Y(Y({},r),{offset:e}),error:function(){return y().showValidationMessage(n.settings.get("unexpectedError"))}})}(s).done((function(n){if(!0===n.success){if(y().update({text:t.data("processing").replace("{processed}",n.data.limit).replace("{total}",n.data.total),showConfirmButton:!1}),void 0===n.data.finished||!0!==n.data.finished)return s=void 0!==n.data.limit?n.data.limit:100,i(s);e.prop("disabled",!1),a(n.data)}else e.prop("disabled",!1),y().showValidationMessage(n.data),a(n.data)}))};i()}))}}).then((function(e){e.isConfirmed&&y().fire({title:n.settings.get("done"),icon:"success",text:t.data("processed").replace("{processed}",e.value.total),confirmButtonText:n.settings.get("ok")}).then((function(){n.$settingsWrapper.trigger("atum-settings-script-runner-done",[t])}))}))},e.prototype.doThemeSelector=function(e){var t=this,n=this.$form.find(".form-settings-wrapper"),a=this.$form.find(".theme-selector-wrapper").find(".selector-container .selector-box img"),s=e.data("value"),r=e.data("reset"),i=this.$form.find("#".concat(s)),o=this.$form.find(".reset-default-colors");i.prop("checked",!0),a.removeClass("active"),e.find("img").addClass("active"),o.data("value",s),L.ajax({url:window.ajaxurl,method:"POST",data:{action:this.settings.get("getColorScheme"),security:this.settings.get("colorSchemeNonce"),theme:s,reset:r},beforeSend:function(){return n.addClass("overlay")},success:function(e){if(!0===e.success){for(var a in e.data)d.updateColorPicker(t.$form.find("#atum_".concat(a)),e.data[a]);var r="";r="dark_mode"===s?t.settings.get("dark"):"hc_mode"===s?t.settings.get("highContrast"):t.settings.get("branded"),t.$form.find(".section-title h2 span").html(r),n.removeClass("overlay"),t.$form.find("input:submit").trigger("click")}}})},e.prototype.doResetDefaultColors=function(e){var t=e.data("value");this.$settingsWrapper.find("#atum_setting_color_scheme").find("input.atum-settings-input[data-display='".concat(t,"']")).each((function(e,t){var n=L(t);n.val(n.data("default")).trigger("change")}))},e.prototype.toggleMultiCheckboxPanel=function(e){e.closest("td").find(".atum-settings-multi-checkbox").css("display",e.is(":checked")?"block":"none")},e.prototype.clickCheckbox=function(e){var t=e.parents(".atum-multi-checkbox-option");e.is(":checked")?t.addClass("setting-checked"):t.removeClass("setting-checked")},e.prototype.initRangeDateTimePicker=function(){var e=this.$form.find(".range-datepicker.range-from"),t=this.$form.find(".range-datepicker.range-to");e.length&&t.length&&(this.dateTimePicker.addDateTimePickers(e,{minDate:!1,maxDate:new Date}),this.dateTimePicker.addDateTimePickers(t,{minDate:!1})),this.dateTimePicker.addDateTimePickers(this.$form.find(".atum-datepicker"))},e.prototype.setRangeDateTimeInputs=function(){var e=this.$form.find(".range-datepicker.range-from"),t=this.$form.find(".range-datepicker.range-to"),n=this.$form.find(".range-value"),a=this.$form.find(".remove-datepicker-range");n.val(JSON.stringify({checked:a.is(":checked"),dateFrom:e.val(),dateTo:t.val()}))},e.prototype.toggleRangeDateTimeRemove=function(e){var t=e.parent().siblings(".range-fields-block"),n=e.parent().siblings(".tool-runner");t.css("display",e.is(":checked")?"block":"none"),n.text(e.is(":checked")?this.settings.get("removeRange"):this.settings.get("removeAll"))},e.prototype.doFileUploaders=function(){new c(this.$settingsWrapper.find(".atum-file-uploader"),{library:{type:"image"}},!0)},e.prototype.doImageSelector=function(){L(".atum-image-selector").on("change","input",(function(e){var t=L(e.currentTarget).closest(".atum-image-radio");t.siblings(".active").removeClass("active"),t.addClass("active")}))},e.prototype.doEditors=function(){window.hasOwnProperty("wp")&&window.wp.hasOwnProperty("editor")&&L(".atum-settings-editor").find("textarea").each((function(e,t){var n=L(t).closest(".atum-settings-editor"),a=window.wp.editor.getDefaultSettings();void 0!==n.data("tiny-mce")&&(a={tinymce:n.data("tiny-mce")}),window.wp.editor.initialize(L(t).attr("id"),a)}))},e}();const w=v;var b=n(3029),k=n.n(b),D=n(1669),T=function(){function e(e){void 0===e&&(e=!0),e&&this.addTooltips()}return e.prototype.addTooltips=function(e){var t=this;e||(e=D("body")),e.find(".tips, .atum-tooltip").each((function(e,n){var a=D(n),s=a.data("tip")||a.attr("title")||a.attr("data-bs-original-title");if(s){if(t.getInstance(a))return;new(k())(a.get(0),{html:!0,title:s,container:"body",delay:{show:100,hide:200}}),a.on("inserted.bs.tooltip",(function(e){var t=D(e.currentTarget).attr("aria-describedby");D('.tooltip[class*="bs-tooltip-"]').not("#".concat(t)).remove()}))}}))},e.prototype.destroyTooltips=function(e){var t=this;e||(e=D("body")),e.find(".tips, .atum-tooltip").each((function(e,n){var a=t.getInstance(D(n));a&&a.dispose()}))},e.prototype.getInstance=function(e){return k().getInstance(e.get(0))},e}();const S=T;var x=n(3941),H=n.n(x);!function(e,t){if(!t)throw new Error("bootstrap-datetimepicker requires Moment.js to be loaded first");var n=function(n,a){var s,r,i,o,d,u,l,c,_,m={},h=!0,p=!1,f=!1,y=0,M=[{clsName:"days",navFnc:"M",navStep:1},{clsName:"months",navFnc:"y",navStep:1},{clsName:"years",navFnc:"y",navStep:10},{clsName:"decades",navFnc:"y",navStep:100}],g=["days","months","years","decades"],L=["top","bottom","auto"],Y=["left","right","auto"],v=["default","top","bottom"],w={up:38,38:"up",down:40,40:"down",left:37,37:"left",right:39,39:"right",tab:9,9:"tab",escape:27,27:"escape",enter:13,13:"enter",pageUp:33,33:"pageUp",pageDown:34,34:"pageDown",shift:16,16:"shift",control:17,17:"control",space:32,32:"space",t:84,84:"t",delete:46,46:"delete"},b={},k=function(){return void 0!==t.tz&&void 0!==a.timeZone&&null!==a.timeZone&&""!==a.timeZone},D=function(e){var n;return n=null==e?t():t.isDate(e)||t.isMoment(e)?t(e):k()?t.tz(e,u,a.useStrict,a.timeZone):t(e,u,a.useStrict),k()&&n.tz(a.timeZone),n},T=function(e){if("string"!=typeof e||e.length>1)throw new TypeError("isEnabled expects a single character string parameter");switch(e){case"y":return-1!==d.indexOf("Y");case"M":return-1!==d.indexOf("M");case"d":return-1!==d.toLowerCase().indexOf("d");case"h":case"H":return-1!==d.toLowerCase().indexOf("h");case"m":return-1!==d.indexOf("m");case"s":return-1!==d.indexOf("s");default:return!1}},S=function(){return T("h")||T("m")||T("s")},x=function(){return T("y")||T("M")||T("d")},H=function(){var t,n,s,r,i,d,u,l,c,_,m=e("<div>").addClass("bootstrap-datetimepicker-widget dropdown-menu"),h=e("<div>").addClass("bs-datepicker").append((c=e("<thead>").append(e("<tr>").append(e("<th>").addClass("prev").attr("data-action","previous").append(e("<span>").addClass(a.icons.previous))).append(e("<th>").addClass("picker-switch").attr("data-action","pickerSwitch").attr("colspan",a.calendarWeeks?"6":"5")).append(e("<th>").addClass("next").attr("data-action","next").append(e("<span>").addClass(a.icons.next)))),_=e("<tbody>").append(e("<tr>").append(e("<td>").attr("colspan",a.calendarWeeks?"8":"7"))),[e("<div>").addClass("datepicker-days").append(e("<table>").addClass("table-condensed").append(c).append(e("<tbody>"))),e("<div>").addClass("datepicker-months").append(e("<table>").addClass("table-condensed").append(c.clone()).append(_.clone())),e("<div>").addClass("datepicker-years").append(e("<table>").addClass("table-condensed").append(c.clone()).append(_.clone())),e("<div>").addClass("datepicker-decades").append(e("<table>").addClass("table-condensed").append(c.clone()).append(_.clone()))])),p=e("<div>").addClass("bs-timepicker").append((i=e("<div>").addClass("timepicker-hours").append(e("<table>").addClass("table-condensed")),d=e("<div>").addClass("timepicker-minutes").append(e("<table>").addClass("table-condensed")),u=e("<div>").addClass("timepicker-seconds").append(e("<table>").addClass("table-condensed")),l=[(n=e("<tr>"),s=e("<tr>"),r=e("<tr>"),T("h")&&(n.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:a.tooltips.incrementHour}).addClass("btn").attr("data-action","incrementHours").append(e("<span>").addClass(a.icons.up)))),s.append(e("<td>").append(e("<span>").addClass("timepicker-hour").attr({"data-time-component":"hours",title:a.tooltips.pickHour}).attr("data-action","showHours"))),r.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:a.tooltips.decrementHour}).addClass("btn").attr("data-action","decrementHours").append(e("<span>").addClass(a.icons.down))))),T("m")&&(T("h")&&(n.append(e("<td>").addClass("separator")),s.append(e("<td>").addClass("separator").html(":")),r.append(e("<td>").addClass("separator"))),n.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:a.tooltips.incrementMinute}).addClass("btn").attr("data-action","incrementMinutes").append(e("<span>").addClass(a.icons.up)))),s.append(e("<td>").append(e("<span>").addClass("timepicker-minute").attr({"data-time-component":"minutes",title:a.tooltips.pickMinute}).attr("data-action","showMinutes"))),r.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:a.tooltips.decrementMinute}).addClass("btn").attr("data-action","decrementMinutes").append(e("<span>").addClass(a.icons.down))))),T("s")&&(T("m")&&(n.append(e("<td>").addClass("separator")),s.append(e("<td>").addClass("separator").html(":")),r.append(e("<td>").addClass("separator"))),n.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:a.tooltips.incrementSecond}).addClass("btn").attr("data-action","incrementSeconds").append(e("<span>").addClass(a.icons.up)))),s.append(e("<td>").append(e("<span>").addClass("timepicker-second").attr({"data-time-component":"seconds",title:a.tooltips.pickSecond}).attr("data-action","showSeconds"))),r.append(e("<td>").append(e("<a>").attr({href:"#",tabindex:"-1",title:a.tooltips.decrementSecond}).addClass("btn").attr("data-action","decrementSeconds").append(e("<span>").addClass(a.icons.down))))),o||(n.append(e("<td>").addClass("separator")),s.append(e("<td>").append(e("<button>").addClass("btn btn-primary").attr({"data-action":"togglePeriod",tabindex:"-1",title:a.tooltips.togglePeriod}))),r.append(e("<td>").addClass("separator"))),e("<div>").addClass("timepicker-picker").append(e("<table>").addClass("table-condensed").append([n,s,r])))],T("h")&&l.push(i),T("m")&&l.push(d),T("s")&&l.push(u),l)),f=e("<ul>").addClass("list-unstyled"),y=e("<li>").addClass("picker-switch"+(a.collapse?" accordion-toggle":"")).append((t=[],a.showTodayButton&&t.push(e("<td>").append(e("<a>").attr({"data-action":"today",title:a.tooltips.today}).append(e("<span>").addClass(a.icons.today)))),!a.sideBySide&&x()&&S()&&t.push(e("<td>").append(e("<a>").attr({"data-action":"togglePicker",title:a.tooltips.selectTime}).append(e("<span>").addClass(a.icons.time)))),a.showClear&&t.push(e("<td>").append(e("<a>").attr({"data-action":"clear",title:a.tooltips.clear}).append(e("<span>").addClass(a.icons.clear)))),a.showClose&&t.push(e("<td>").append(e("<a>").attr({"data-action":"close",title:a.tooltips.close}).append(e("<span>").addClass(a.icons.close)))),e("<table>").addClass("table-condensed").append(e("<tbody>").append(e("<tr>").append(t)))));return a.inline&&m.removeClass("dropdown-menu"),o&&m.addClass("usetwentyfour"),T("s")&&!o&&m.addClass("wider"),a.sideBySide&&x()&&S()?(m.addClass("timepicker-sbs"),"top"===a.toolbarPlacement&&m.append(y),m.append(e("<div>").addClass("row").append(h.addClass("col-md-6")).append(p.addClass("col-md-6"))),"bottom"===a.toolbarPlacement&&m.append(y),m):("top"===a.toolbarPlacement&&f.append(y),x()&&f.append(e("<li>").addClass(a.collapse&&S()?"collapse in":"").append(h)),"default"===a.toolbarPlacement&&f.append(y),S()&&f.append(e("<li>").addClass(a.collapse&&x()?"collapse":"").append(p)),"bottom"===a.toolbarPlacement&&f.append(y),m.append(f))},j=function(){var t,s=(p||n).position(),r=(p||n).offset(),i=a.widgetPositioning.vertical,o=a.widgetPositioning.horizontal;if(a.widgetParent)t=a.widgetParent.append(f);else if(n.is("input"))t=n.after(f).parent();else{if(a.inline)return void(t=n.append(f));t=n,n.children().first().after(f)}if("auto"===i&&(i=r.top+1.5*f.height()>=e(window).height()+e(window).scrollTop()&&f.height()+n.outerHeight()<r.top?"top":"bottom"),"auto"===o&&(o=t.width()<r.left+f.outerWidth()/2&&r.left+f.outerWidth()>e(window).width()?"right":"left"),"top"===i?f.addClass("top").removeClass("bottom"):f.addClass("bottom").removeClass("top"),"right"===o?f.addClass("pull-right"):f.removeClass("pull-right"),"static"===t.css("position")&&(t=t.parents().filter((function(){return"static"!==e(this).css("position")})).first()),0===t.length)throw new Error("datetimepicker component should be placed within a non-static positioned container");f.css({top:"top"===i?"auto":s.top+n.outerHeight(),bottom:"top"===i?t.outerHeight()-(t===n?0:s.top):"auto",left:"left"===o?t===n?0:s.left:"auto",right:"left"===o?"auto":t.outerWidth()-n.outerWidth()-(t===n?0:s.left)})},O=function(e){"dp.change"===e.type&&(e.date&&e.date.isSame(e.oldDate)||!e.date&&!e.oldDate)||n.trigger(e)},P=function(e){"y"===e&&(e="YYYY"),O({type:"dp.update",change:e,viewDate:r.clone()})},E=function(e){f&&(e&&(l=Math.max(y,Math.min(3,l+e))),f.find(".bs-datepicker > div").hide().filter(".datepicker-"+M[l].clsName).show())},A=function(t,n){if(!t.isValid())return!1;if(a.disabledDates&&"d"===n&&(s=t,!0===a.disabledDates[s.format("YYYY-MM-DD")]))return!1;var s;if(a.enabledDates&&"d"===n&&!function(e){return!0===a.enabledDates[e.format("YYYY-MM-DD")]}(t))return!1;if(a.minDate&&t.isBefore(a.minDate,n))return!1;if(a.maxDate&&t.isAfter(a.maxDate,n))return!1;if(a.daysOfWeekDisabled&&"d"===n&&-1!==a.daysOfWeekDisabled.indexOf(t.day()))return!1;if(a.disabledHours&&("h"===n||"m"===n||"s"===n)&&function(e){return!0===a.disabledHours[e.format("H")]}(t))return!1;if(a.enabledHours&&("h"===n||"m"===n||"s"===n)&&!function(e){return!0===a.enabledHours[e.format("H")]}(t))return!1;if(a.disabledTimeIntervals&&("h"===n||"m"===n||"s"===n)){var r=!1;if(e.each(a.disabledTimeIntervals,(function(){if(t.isBetween(this[0],this[1]))return r=!0,!1})),r)return!1}return!0},C=function(){var n,i,o,d=f.find(".datepicker-days"),u=d.find("th"),l=[],c=[];if(x()){for(u.eq(0).find("span").attr("title",a.tooltips.prevMonth),u.eq(1).attr("title",a.tooltips.selectMonth),u.eq(2).find("span").attr("title",a.tooltips.nextMonth),d.find(".disabled").removeClass("disabled"),u.eq(1).text(r.format(a.dayViewHeaderFormat)),A(r.clone().subtract(1,"M"),"M")||u.eq(0).addClass("disabled"),A(r.clone().add(1,"M"),"M")||u.eq(2).addClass("disabled"),n=r.clone().startOf("M").startOf("w").startOf("d"),o=0;o<42;o++)0===n.weekday()&&(i=e("<tr>"),a.calendarWeeks&&i.append('<td class="cw">'+n.week()+"</td>"),l.push(i)),c=["day"],n.isBefore(r,"M")&&c.push("old"),n.isAfter(r,"M")&&c.push("new"),n.isSame(s,"d")&&!h&&c.push("active"),A(n,"d")||c.push("disabled"),n.isSame(D(),"d")&&c.push("today"),0!==n.day()&&6!==n.day()||c.push("weekend"),O({type:"dp.classify",date:n,classNames:c}),i.append('<td data-action="selectDay" data-day="'+n.format("L")+'" class="'+c.join(" ")+'">'+n.date()+"</td>"),n.add(1,"d");var _,m,p;d.find("tbody").empty().append(l),_=f.find(".datepicker-months"),m=_.find("th"),p=_.find("tbody").find("span"),m.eq(0).find("span").attr("title",a.tooltips.prevYear),m.eq(1).attr("title",a.tooltips.selectYear),m.eq(2).find("span").attr("title",a.tooltips.nextYear),_.find(".disabled").removeClass("disabled"),A(r.clone().subtract(1,"y"),"y")||m.eq(0).addClass("disabled"),m.eq(1).text(r.year()),A(r.clone().add(1,"y"),"y")||m.eq(2).addClass("disabled"),p.removeClass("active"),s.isSame(r,"y")&&!h&&p.eq(s.month()).addClass("active"),p.each((function(t){A(r.clone().month(t),"M")||e(this).addClass("disabled")})),function(){var e=f.find(".datepicker-years"),t=e.find("th"),n=r.clone().subtract(5,"y"),i=r.clone().add(6,"y"),o="";for(t.eq(0).find("span").attr("title",a.tooltips.prevDecade),t.eq(1).attr("title",a.tooltips.selectDecade),t.eq(2).find("span").attr("title",a.tooltips.nextDecade),e.find(".disabled").removeClass("disabled"),a.minDate&&a.minDate.isAfter(n,"y")&&t.eq(0).addClass("disabled"),t.eq(1).text(n.year()+"-"+i.year()),a.maxDate&&a.maxDate.isBefore(i,"y")&&t.eq(2).addClass("disabled");!n.isAfter(i,"y");)o+='<span data-action="selectYear" class="year'+(n.isSame(s,"y")&&!h?" active":"")+(A(n,"y")?"":" disabled")+'">'+n.year()+"</span>",n.add(1,"y");e.find("td").html(o)}(),function(){var e,n=f.find(".datepicker-decades"),i=n.find("th"),o=t({y:r.year()-r.year()%100-1}),d=o.clone().add(100,"y"),u=o.clone(),l=!1,c=!1,_="";for(i.eq(0).find("span").attr("title",a.tooltips.prevCentury),i.eq(2).find("span").attr("title",a.tooltips.nextCentury),n.find(".disabled").removeClass("disabled"),(o.isSame(t({y:1900}))||a.minDate&&a.minDate.isAfter(o,"y"))&&i.eq(0).addClass("disabled"),i.eq(1).text(o.year()+"-"+d.year()),(o.isSame(t({y:2e3}))||a.maxDate&&a.maxDate.isBefore(d,"y"))&&i.eq(2).addClass("disabled");!o.isAfter(d,"y");)e=o.year()+12,l=a.minDate&&a.minDate.isAfter(o,"y")&&a.minDate.year()<=e,c=a.maxDate&&a.maxDate.isAfter(o,"y")&&a.maxDate.year()<=e,_+='<span data-action="selectDecade" class="decade'+(s.isAfter(o)&&s.year()<=e?" active":"")+(A(o,"y")||l||c?"":" disabled")+'" data-selection="'+(o.year()+6)+'">'+(o.year()+1)+" - "+(o.year()+12)+"</span>",o.add(12,"y");_+="<span></span><span></span><span></span>",n.find("td").html(_),i.eq(1).text(u.year()+1+"-"+o.year())}()}},W=function(){var t,n,i=f.find(".bs-timepicker span[data-time-component]");o||(t=f.find(".bs-timepicker [data-action=togglePeriod]"),n=s.clone().add(s.hours()>=12?-12:12,"h"),t.text(s.format("A")),A(n,"h")?t.removeClass("disabled"):t.addClass("disabled")),i.filter("[data-time-component=hours]").text(s.format(o?"HH":"hh")),i.filter("[data-time-component=minutes]").text(s.format("mm")),i.filter("[data-time-component=seconds]").text(s.format("ss")),function(){var t=f.find(".timepicker-hours table"),n=r.clone().startOf("d"),a=[],s=e("<tr>");for(r.hour()>11&&!o&&n.hour(12);n.isSame(r,"d")&&(o||r.hour()<12&&n.hour()<12||r.hour()>11);)n.hour()%4==0&&(s=e("<tr>"),a.push(s)),s.append('<td data-action="selectHour" class="hour'+(A(n,"h")?"":" disabled")+'">'+n.format(o?"HH":"hh")+"</td>"),n.add(1,"h");t.empty().append(a)}(),function(){for(var t=f.find(".timepicker-minutes table"),n=r.clone().startOf("h"),s=[],i=e("<tr>"),o=1===a.stepping?5:a.stepping;r.isSame(n,"h");)n.minute()%(4*o)==0&&(i=e("<tr>"),s.push(i)),i.append('<td data-action="selectMinute" class="minute'+(A(n,"m")?"":" disabled")+'">'+n.format("mm")+"</td>"),n.add(o,"m");t.empty().append(s)}(),function(){for(var t=f.find(".timepicker-seconds table"),n=r.clone().startOf("m"),a=[],s=e("<tr>");r.isSame(n,"m");)n.second()%20==0&&(s=e("<tr>"),a.push(s)),s.append('<td data-action="selectSecond" class="second'+(A(n,"s")?"":" disabled")+'">'+n.format("ss")+"</td>"),n.add(5,"s");t.empty().append(a)}()},F=function(){f&&(C(),W())},$=function(e){var t=h?null:s;if(!e)return h=!0,i.val(""),n.data("date",""),O({type:"dp.change",date:!1,oldDate:t}),void F();if(e=e.clone().locale(a.locale),k()&&e.tz(a.timeZone),1!==a.stepping)for(e.minutes(Math.round(e.minutes()/a.stepping)*a.stepping).seconds(0);a.minDate&&e.isBefore(a.minDate);)e.add(a.stepping,"minutes");A(e)?(r=(s=e).clone(),i.val(s.format(d)),n.data("date",s.format(d)),h=!1,F(),O({type:"dp.change",date:s.clone(),oldDate:t})):(a.keepInvalid?O({type:"dp.change",date:e,oldDate:t}):i.val(h?"":s.format(d)),O({type:"dp.error",date:e,oldDate:t}))},N=function(){var t=!1;return f?(f.find(".collapse").each((function(){var n=e(this).data("collapse");return!n||!n.transitioning||(t=!0,!1)})),t||(p&&p.hasClass("btn")&&p.toggleClass("active"),f.hide(),e(window).off("resize",j),f.off("click","[data-action]"),f.off("mousedown",!1),f.remove(),f=!1,O({type:"dp.hide",date:s.clone()}),i.blur(),r=s.clone()),m):m},z=function(){$(null),O({type:"dp.clear"})},I=function(e){return void 0===a.parseInputDate?(!t.isMoment(e)||e instanceof Date)&&(e=D(e)):e=a.parseInputDate(e),e},R={next:function(){var e=M[l].navFnc;r.add(M[l].navStep,e),C(),P(e)},previous:function(){var e=M[l].navFnc;r.subtract(M[l].navStep,e),C(),P(e)},pickerSwitch:function(){E(1)},selectMonth:function(t){var n=e(t.target).closest("tbody").find("span").index(e(t.target));r.month(n),l===y?($(s.clone().year(r.year()).month(r.month())),a.inline||N()):(E(-1),C()),P("M")},selectYear:function(t){var n=parseInt(e(t.target).text(),10)||0;r.year(n),l===y?($(s.clone().year(r.year())),a.inline||N()):(E(-1),C()),P("YYYY")},selectDecade:function(t){var n=parseInt(e(t.target).data("selection"),10)||0;r.year(n),l===y?($(s.clone().year(r.year())),a.inline||N()):(E(-1),C()),P("YYYY")},selectDay:function(t){var n=r.clone();e(t.target).is(".old")&&n.subtract(1,"M"),e(t.target).is(".new")&&n.add(1,"M"),$(n.date(parseInt(e(t.target).text(),10))),S()||a.keepOpen||a.inline||N()},incrementHours:function(){var e=s.clone().add(1,"h");A(e,"h")&&$(e)},incrementMinutes:function(){var e=s.clone().add(a.stepping,"m");A(e,"m")&&$(e)},incrementSeconds:function(){var e=s.clone().add(1,"s");A(e,"s")&&$(e)},decrementHours:function(){var e=s.clone().subtract(1,"h");A(e,"h")&&$(e)},decrementMinutes:function(){var e=s.clone().subtract(a.stepping,"m");A(e,"m")&&$(e)},decrementSeconds:function(){var e=s.clone().subtract(1,"s");A(e,"s")&&$(e)},togglePeriod:function(){$(s.clone().add(s.hours()>=12?-12:12,"h"))},togglePicker:function(t){var n,s=e(t.target),r=s.closest("ul"),i=r.find(".in"),o=r.find(".collapse:not(.in)");if(i&&i.length){if((n=i.data("collapse"))&&n.transitioning)return;i.collapse?(i.collapse("hide"),o.collapse("show")):(i.removeClass("in"),o.addClass("in")),s.is("span")?s.toggleClass(a.icons.time+" "+a.icons.date):s.find("span").toggleClass(a.icons.time+" "+a.icons.date)}},showPicker:function(){f.find(".bs-timepicker > div:not(.timepicker-picker)").hide(),f.find(".bs-timepicker .timepicker-picker").show()},showHours:function(){f.find(".bs-timepicker .timepicker-picker").hide(),f.find(".bs-timepicker .timepicker-hours").show()},showMinutes:function(){f.find(".bs-timepicker .timepicker-picker").hide(),f.find(".bs-timepicker .timepicker-minutes").show()},showSeconds:function(){f.find(".bs-timepicker .timepicker-picker").hide(),f.find(".bs-timepicker .timepicker-seconds").show()},selectHour:function(t){var n=parseInt(e(t.target).text(),10);o||(s.hours()>=12?12!==n&&(n+=12):12===n&&(n=0)),$(s.clone().hours(n)),R.showPicker.call(m)},selectMinute:function(t){$(s.clone().minutes(parseInt(e(t.target).text(),10))),R.showPicker.call(m)},selectSecond:function(t){$(s.clone().seconds(parseInt(e(t.target).text(),10))),R.showPicker.call(m)},clear:z,today:function(){var e=D();A(e,"d")&&$(e)},close:N},U=function(t){return e(t.currentTarget).is(".disabled")||R[e(t.currentTarget).data("action")].apply(m,arguments),!1},J=function(){var t;return i.prop("disabled")||!a.ignoreReadonly&&i.prop("readonly")||f||(void 0!==i.val()&&0!==i.val().trim().length?$(I(i.val().trim())):h&&a.useCurrent&&(a.inline||i.is("input")&&0===i.val().trim().length)&&(t=D(),"string"==typeof a.useCurrent&&(t={year:function(e){return e.month(0).date(1).hours(0).seconds(0).minutes(0)},month:function(e){return e.date(1).hours(0).seconds(0).minutes(0)},day:function(e){return e.hours(0).seconds(0).minutes(0)},hour:function(e){return e.seconds(0).minutes(0)},minute:function(e){return e.seconds(0)}}[a.useCurrent](t)),$(t)),f=H(),function(){var t=e("<tr>"),n=r.clone().startOf("w").startOf("d");for(!0===a.calendarWeeks&&t.append(e("<th>").addClass("cw").text("#"));n.isBefore(r.clone().endOf("w"));)t.append(e("<th>").addClass("dow").text(n.format("dd"))),n.add(1,"d");f.find(".datepicker-days thead").append(t)}(),function(){for(var t=[],n=r.clone().startOf("y").startOf("d");n.isSame(r,"y");)t.push(e("<span>").attr("data-action","selectMonth").addClass("month").text(n.format("MMM"))),n.add(1,"M");f.find(".datepicker-months td").empty().append(t)}(),f.find(".timepicker-hours").hide(),f.find(".timepicker-minutes").hide(),f.find(".timepicker-seconds").hide(),F(),E(),e(window).on("resize",j),f.on("click","[data-action]",U),f.on("mousedown",!1),p&&p.hasClass("btn")&&p.toggleClass("active"),j(),f.show(),a.focusOnShow&&!i.is(":focus")&&i.focus(),O({type:"dp.show"})),m},q=function(){return f?N():J()},B=function(e){var t,n,s,r,i=null,o=[],d={},u=e.which;for(t in b[u]="p",b)b.hasOwnProperty(t)&&"p"===b[t]&&(o.push(t),parseInt(t,10)!==u&&(d[t]=!0));for(t in a.keyBinds)if(a.keyBinds.hasOwnProperty(t)&&"function"==typeof a.keyBinds[t]&&(s=t.split(" ")).length===o.length&&w[u]===s[s.length-1]){for(r=!0,n=s.length-2;n>=0;n--)if(!(w[s[n]]in d)){r=!1;break}if(r){i=a.keyBinds[t];break}}i&&(i.call(m,f),e.stopPropagation(),e.preventDefault())},G=function(e){b[e.which]="r",e.stopPropagation(),e.preventDefault()},V=function(t){var n=e(t.target).val().trim(),a=n?I(n):null;return $(a),t.stopImmediatePropagation(),!1},K=function(t){var n={};return e.each(t,(function(){var e=I(this);e.isValid()&&(n[e.format("YYYY-MM-DD")]=!0)})),!!Object.keys(n).length&&n},Z=function(t){var n={};return e.each(t,(function(){n[this]=!0})),!!Object.keys(n).length&&n},Q=function(){var e=a.format||"L LT";d=e.replace(/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,(function(e){return(s.localeData().longDateFormat(e)||e).replace(/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,(function(e){return s.localeData().longDateFormat(e)||e}))})),(u=a.extraFormats?a.extraFormats.slice():[]).indexOf(e)<0&&u.indexOf(d)<0&&u.push(d),o=d.toLowerCase().indexOf("a")<1&&d.replace(/\[.*?\]/g,"").indexOf("h")<1,T("y")&&(y=2),T("M")&&(y=1),T("d")&&(y=0),l=Math.max(y,l),h||$(s)};if(m.destroy=function(){N(),i.off({change:V,blur,keydown:B,keyup:G,focus:a.allowInputToggle?N:""}),n.is("input")?i.off({focus:J}):p&&(p.off("click",q),p.off("mousedown",!1)),n.removeData("DateTimePicker"),n.removeData("date")},m.toggle=q,m.show=J,m.hide=N,m.disable=function(){return N(),p&&p.hasClass("btn")&&p.addClass("disabled"),i.prop("disabled",!0),m},m.enable=function(){return p&&p.hasClass("btn")&&p.removeClass("disabled"),i.prop("disabled",!1),m},m.ignoreReadonly=function(e){if(0===arguments.length)return a.ignoreReadonly;if("boolean"!=typeof e)throw new TypeError("ignoreReadonly () expects a boolean parameter");return a.ignoreReadonly=e,m},m.options=function(t){if(0===arguments.length)return e.extend(!0,{},a);if(!(t instanceof Object))throw new TypeError("options() options parameter should be an object");return e.extend(!0,a,t),e.each(a,(function(e,t){if(void 0!==m[e])m[e](t);else if(a.debug)throw new TypeError("option "+e+" is not recognized!")})),m},m.date=function(e){if(0===arguments.length)return h?null:s.clone();if(!(null===e||"string"==typeof e||t.isMoment(e)||e instanceof Date))throw new TypeError("date() parameter must be one of [null, string, moment or Date]");return $(null===e?null:I(e)),m},m.format=function(e){if(0===arguments.length)return a.format;if("string"!=typeof e&&("boolean"!=typeof e||!1!==e))throw new TypeError("format() expects a string or boolean:false parameter "+e);return a.format=e,d&&Q(),m},m.timeZone=function(e){if(0===arguments.length)return a.timeZone;if("string"!=typeof e)throw new TypeError("newZone() expects a string parameter");return a.timeZone=e,m},m.dayViewHeaderFormat=function(e){if(0===arguments.length)return a.dayViewHeaderFormat;if("string"!=typeof e)throw new TypeError("dayViewHeaderFormat() expects a string parameter");return a.dayViewHeaderFormat=e,m},m.extraFormats=function(e){if(0===arguments.length)return a.extraFormats;if(!1!==e&&!(e instanceof Array))throw new TypeError("extraFormats() expects an array or false parameter");return a.extraFormats=e,u&&Q(),m},m.disabledDates=function(t){if(0===arguments.length)return a.disabledDates?e.extend({},a.disabledDates):a.disabledDates;if(!t)return a.disabledDates=!1,F(),m;if(!(t instanceof Array))throw new TypeError("disabledDates() expects an array parameter");return a.disabledDates=K(t),a.enabledDates=!1,F(),m},m.enabledDates=function(t){if(0===arguments.length)return a.enabledDates?e.extend({},a.enabledDates):a.enabledDates;if(!t)return a.enabledDates=!1,F(),m;if(!(t instanceof Array))throw new TypeError("enabledDates() expects an array parameter");return a.enabledDates=K(t),a.disabledDates=!1,F(),m},m.daysOfWeekDisabled=function(e){if(0===arguments.length)return a.daysOfWeekDisabled.splice(0);if("boolean"==typeof e&&!e)return a.daysOfWeekDisabled=!1,F(),m;if(!(e instanceof Array))throw new TypeError("daysOfWeekDisabled() expects an array parameter");if(a.daysOfWeekDisabled=e.reduce((function(e,t){return(t=parseInt(t,10))>6||t<0||isNaN(t)||-1===e.indexOf(t)&&e.push(t),e}),[]).sort(),a.useCurrent&&!a.keepInvalid){for(var t=0;!A(s,"d");){if(s.add(1,"d"),31===t)throw"Tried 31 times to find a valid date";t++}$(s)}return F(),m},m.maxDate=function(e){if(0===arguments.length)return a.maxDate?a.maxDate.clone():a.maxDate;if("boolean"==typeof e&&!1===e)return a.maxDate=!1,F(),m;"string"==typeof e&&("moment+1"===e?e=D(t().add(1,"minutes")):"now"!==e&&"moment"!==e||(e=D()));var n=I(e);if(!n.isValid())throw new TypeError("maxDate() Could not parse date parameter: "+e);if(a.minDate&&n.isBefore(a.minDate))throw new TypeError("maxDate() date parameter is before options.minDate: "+n.format(d));return a.maxDate=n,a.useCurrent&&!a.keepInvalid&&s.isAfter(e)&&$(a.maxDate),r.isAfter(n)&&(r=n.clone().subtract(a.stepping,"m")),F(),m},m.minDate=function(e){if(0===arguments.length)return a.minDate?a.minDate.clone():a.minDate;if("boolean"==typeof e&&!1===e)return a.minDate=!1,F(),m;"string"==typeof e&&("now"!==e&&"moment"!==e||(e=D()));var t=I(e);if(!t.isValid())throw new TypeError("minDate() Could not parse date parameter: "+e);if(a.maxDate&&t.isAfter(a.maxDate))throw new TypeError("minDate() date parameter is after options.maxDate: "+t.format(d));return a.minDate=t,a.useCurrent&&!a.keepInvalid&&s.isBefore(e)&&$(a.minDate),r.isBefore(t)&&(r=t.clone().add(a.stepping,"m")),F(),m},m.defaultDate=function(e){if(0===arguments.length)return a.defaultDate?a.defaultDate.clone():a.defaultDate;if(!e)return a.defaultDate=!1,m;"string"==typeof e&&(e="now"===e||"moment"===e?D():D(e));var t=I(e);if(!t.isValid())throw new TypeError("defaultDate() Could not parse date parameter: "+e);if(!A(t))throw new TypeError("defaultDate() date passed is invalid according to component setup validations");return a.defaultDate=t,(a.defaultDate&&a.inline||""===i.val().trim())&&$(a.defaultDate),m},m.locale=function(e){if(0===arguments.length)return a.locale;if(!t.localeData(e))throw new TypeError("locale() locale "+e+" is not loaded from moment locales!");return a.locale=e,s.locale(a.locale),r.locale(a.locale),d&&Q(),f&&(N(),J()),m},m.stepping=function(e){return 0===arguments.length?a.stepping:(e=parseInt(e,10),(isNaN(e)||e<1)&&(e=1),a.stepping=e,m)},m.useCurrent=function(e){var t=["year","month","day","hour","minute"];if(0===arguments.length)return a.useCurrent;if("boolean"!=typeof e&&"string"!=typeof e)throw new TypeError("useCurrent() expects a boolean or string parameter");if("string"==typeof e&&-1===t.indexOf(e.toLowerCase()))throw new TypeError("useCurrent() expects a string parameter of "+t.join(", "));return a.useCurrent=e,m},m.collapse=function(e){if(0===arguments.length)return a.collapse;if("boolean"!=typeof e)throw new TypeError("collapse() expects a boolean parameter");return a.collapse===e||(a.collapse=e,f&&(N(),J())),m},m.icons=function(t){if(0===arguments.length)return e.extend({},a.icons);if(!(t instanceof Object))throw new TypeError("icons() expects parameter to be an Object");return e.extend(a.icons,t),f&&(N(),J()),m},m.tooltips=function(t){if(0===arguments.length)return e.extend({},a.tooltips);if(!(t instanceof Object))throw new TypeError("tooltips() expects parameter to be an Object");return e.extend(a.tooltips,t),f&&(N(),J()),m},m.useStrict=function(e){if(0===arguments.length)return a.useStrict;if("boolean"!=typeof e)throw new TypeError("useStrict() expects a boolean parameter");return a.useStrict=e,m},m.sideBySide=function(e){if(0===arguments.length)return a.sideBySide;if("boolean"!=typeof e)throw new TypeError("sideBySide() expects a boolean parameter");return a.sideBySide=e,f&&(N(),J()),m},m.viewMode=function(e){if(0===arguments.length)return a.viewMode;if("string"!=typeof e)throw new TypeError("viewMode() expects a string parameter");if(-1===g.indexOf(e))throw new TypeError("viewMode() parameter must be one of ("+g.join(", ")+") value");return a.viewMode=e,l=Math.max(g.indexOf(e),y),E(),m},m.toolbarPlacement=function(e){if(0===arguments.length)return a.toolbarPlacement;if("string"!=typeof e)throw new TypeError("toolbarPlacement() expects a string parameter");if(-1===v.indexOf(e))throw new TypeError("toolbarPlacement() parameter must be one of ("+v.join(", ")+") value");return a.toolbarPlacement=e,f&&(N(),J()),m},m.widgetPositioning=function(t){if(0===arguments.length)return e.extend({},a.widgetPositioning);if("[object Object]"!=={}.toString.call(t))throw new TypeError("widgetPositioning() expects an object variable");if(t.horizontal){if("string"!=typeof t.horizontal)throw new TypeError("widgetPositioning() horizontal variable must be a string");if(t.horizontal=t.horizontal.toLowerCase(),-1===Y.indexOf(t.horizontal))throw new TypeError("widgetPositioning() expects horizontal parameter to be one of ("+Y.join(", ")+")");a.widgetPositioning.horizontal=t.horizontal}if(t.vertical){if("string"!=typeof t.vertical)throw new TypeError("widgetPositioning() vertical variable must be a string");if(t.vertical=t.vertical.toLowerCase(),-1===L.indexOf(t.vertical))throw new TypeError("widgetPositioning() expects vertical parameter to be one of ("+L.join(", ")+")");a.widgetPositioning.vertical=t.vertical}return F(),m},m.calendarWeeks=function(e){if(0===arguments.length)return a.calendarWeeks;if("boolean"!=typeof e)throw new TypeError("calendarWeeks() expects parameter to be a boolean value");return a.calendarWeeks=e,F(),m},m.showTodayButton=function(e){if(0===arguments.length)return a.showTodayButton;if("boolean"!=typeof e)throw new TypeError("showTodayButton() expects a boolean parameter");return a.showTodayButton=e,f&&(N(),J()),m},m.showClear=function(e){if(0===arguments.length)return a.showClear;if("boolean"!=typeof e)throw new TypeError("showClear() expects a boolean parameter");return a.showClear=e,f&&(N(),J()),m},m.widgetParent=function(t){if(0===arguments.length)return a.widgetParent;if("string"==typeof t&&(t=e(t)),null!==t&&"string"!=typeof t&&!(t instanceof e))throw new TypeError("widgetParent() expects a string or a jQuery object parameter");return a.widgetParent=t,f&&(N(),J()),m},m.keepOpen=function(e){if(0===arguments.length)return a.keepOpen;if("boolean"!=typeof e)throw new TypeError("keepOpen() expects a boolean parameter");return a.keepOpen=e,m},m.focusOnShow=function(e){if(0===arguments.length)return a.focusOnShow;if("boolean"!=typeof e)throw new TypeError("focusOnShow() expects a boolean parameter");return a.focusOnShow=e,m},m.inline=function(e){if(0===arguments.length)return a.inline;if("boolean"!=typeof e)throw new TypeError("inline() expects a boolean parameter");return a.inline=e,m},m.clear=function(){return z(),m},m.keyBinds=function(e){return 0===arguments.length?a.keyBinds:(a.keyBinds=e,m)},m.getMoment=function(e){return D(e)},m.debug=function(e){if("boolean"!=typeof e)throw new TypeError("debug() expects a boolean parameter");return a.debug=e,m},m.allowInputToggle=function(e){if(0===arguments.length)return a.allowInputToggle;if("boolean"!=typeof e)throw new TypeError("allowInputToggle() expects a boolean parameter");return a.allowInputToggle=e,m},m.showClose=function(e){if(0===arguments.length)return a.showClose;if("boolean"!=typeof e)throw new TypeError("showClose() expects a boolean parameter");return a.showClose=e,m},m.keepInvalid=function(e){if(0===arguments.length)return a.keepInvalid;if("boolean"!=typeof e)throw new TypeError("keepInvalid() expects a boolean parameter");return a.keepInvalid=e,m},m.datepickerInput=function(e){if(0===arguments.length)return a.datepickerInput;if("string"!=typeof e)throw new TypeError("datepickerInput() expects a string parameter");return a.datepickerInput=e,m},m.parseInputDate=function(e){if(0===arguments.length)return a.parseInputDate;if("function"!=typeof e)throw new TypeError("parseInputDate() sholud be as function");return a.parseInputDate=e,m},m.disabledTimeIntervals=function(t){if(0===arguments.length)return a.disabledTimeIntervals?e.extend({},a.disabledTimeIntervals):a.disabledTimeIntervals;if(!t)return a.disabledTimeIntervals=!1,F(),m;if(!(t instanceof Array))throw new TypeError("disabledTimeIntervals() expects an array parameter");return a.disabledTimeIntervals=t,F(),m},m.disabledHours=function(t){if(0===arguments.length)return a.disabledHours?e.extend({},a.disabledHours):a.disabledHours;if(!t)return a.disabledHours=!1,F(),m;if(!(t instanceof Array))throw new TypeError("disabledHours() expects an array parameter");if(a.disabledHours=Z(t),a.enabledHours=!1,a.useCurrent&&!a.keepInvalid){for(var n=0;!A(s,"h");){if(s.add(1,"h"),24===n)throw"Tried 24 times to find a valid date";n++}$(s)}return F(),m},m.enabledHours=function(t){if(0===arguments.length)return a.enabledHours?e.extend({},a.enabledHours):a.enabledHours;if(!t)return a.enabledHours=!1,F(),m;if(!(t instanceof Array))throw new TypeError("enabledHours() expects an array parameter");if(a.enabledHours=Z(t),a.disabledHours=!1,a.useCurrent&&!a.keepInvalid){for(var n=0;!A(s,"h");){if(s.add(1,"h"),24===n)throw"Tried 24 times to find a valid date";n++}$(s)}return F(),m},m.viewDate=function(e){if(0===arguments.length)return r.clone();if(!e)return r=s.clone(),m;if(!("string"==typeof e||t.isMoment(e)||e instanceof Date))throw new TypeError("viewDate() parameter must be one of [string, moment or Date]");return r=I(e),P(),m},n.is("input"))i=n;else if(0===(i=n.find(a.datepickerInput)).length)i=n.find("input");else if(!i.is("input"))throw new Error('CSS class "'+a.datepickerInput+'" cannot be applied to non input element');if(n.hasClass("input-group")&&(p=0===n.find(".datepickerbutton").length?n.find(".input-group-addon"):n.find(".datepickerbutton")),!a.inline&&!i.is("input"))throw new Error("Could not initialize DateTimePicker without an input element");return s=D(),r=s.clone(),e.extend(!0,a,(_={},(c=n.is("input")||a.inline?n.data():n.find("input").data()).dateOptions&&c.dateOptions instanceof Object&&(_=e.extend(!0,_,c.dateOptions)),e.each(a,(function(e){var t="date"+e.charAt(0).toUpperCase()+e.slice(1);void 0!==c[t]&&(_[e]=c[t])})),_)),m.options(a),Q(),i.on({change:V,blur:a.debug?"":N,keydown:B,keyup:G,focus:a.allowInputToggle?J:""}),n.is("input")?i.on({focus:J}):p&&(p.on("click",q),p.on("mousedown",!1)),i.prop("disabled")&&m.disable(),i.is("input")&&0!==i.val().trim().length?$(I(i.val().trim())):a.defaultDate&&void 0===i.attr("placeholder")&&$(a.defaultDate),a.inline&&J(),m};e.fn.bsDatetimepicker=function(t){t=t||{};var a,s=Array.prototype.slice.call(arguments,1),r=!0;if("object"==typeof t)return this.each((function(){var a,s=e(this);s.data("DateTimePicker")||(a=e.extend(!0,{},e.fn.bsDatetimepicker.defaults,t),s.data("DateTimePicker",n(s,a)))}));if("string"==typeof t)return this.each((function(){var n=e(this).data("DateTimePicker");if(!n)throw new Error('bootstrap-datetimepicker("'+t+'") method was called on an element that is not using DateTimePicker');a=n[t].apply(n,s),r=a===n})),r||e.inArray(t,["destroy","hide","show","toggle"])>-1?this:a;throw new TypeError("Invalid arguments for DateTimePicker: "+t)},e.fn.bsDatetimepicker.defaults={timeZone:"",format:!1,dayViewHeaderFormat:"MMMM YYYY",extraFormats:!1,stepping:1,minDate:!1,maxDate:!1,useCurrent:!0,collapse:!0,locale:t.locale(),defaultDate:!1,disabledDates:!1,enabledDates:!1,icons:{time:"glyphicon glyphicon-time",date:"glyphicon glyphicon-calendar",up:"glyphicon glyphicon-chevron-up",down:"glyphicon glyphicon-chevron-down",previous:"glyphicon glyphicon-chevron-left",next:"glyphicon glyphicon-chevron-right",today:"glyphicon glyphicon-screenshot",clear:"glyphicon glyphicon-trash",close:"glyphicon glyphicon-remove"},tooltips:{today:"Go to today",clear:"Clear selection",close:"Close the picker",selectMonth:"Select Month",prevMonth:"Previous Month",nextMonth:"Next Month",selectYear:"Select Year",prevYear:"Previous Year",nextYear:"Next Year",selectDecade:"Select Decade",prevDecade:"Previous Decade",nextDecade:"Next Decade",prevCentury:"Previous Century",nextCentury:"Next Century",pickHour:"Pick Hour",incrementHour:"Increment Hour",decrementHour:"Decrement Hour",pickMinute:"Pick Minute",incrementMinute:"Increment Minute",decrementMinute:"Decrement Minute",pickSecond:"Pick Second",incrementSecond:"Increment Second",decrementSecond:"Decrement Second",togglePeriod:"Toggle Period",selectTime:"Select Time"},useStrict:!1,sideBySide:!1,daysOfWeekDisabled:!1,calendarWeeks:!1,viewMode:"days",toolbarPlacement:"default",showTodayButton:!1,showClear:!1,showClose:!1,widgetPositioning:{horizontal:"auto",vertical:"auto"},widgetParent:null,ignoreReadonly:!1,keepOpen:!1,focusOnShow:!0,inline:!1,keepInvalid:!1,datepickerInput:".datepickerinput",keyBinds:{up:function(e){if(e){var t=this.date()||this.getMoment();e.find(".bs-datepicker").is(":visible")?this.date(t.clone().subtract(7,"d")):this.date(t.clone().add(this.stepping(),"m"))}},down:function(e){if(e){var t=this.date()||this.getMoment();e.find(".bs-datepicker").is(":visible")?this.date(t.clone().add(7,"d")):this.date(t.clone().subtract(this.stepping(),"m"))}else this.show()},"control up":function(e){if(e){var t=this.date()||this.getMoment();e.find(".bs-datepicker").is(":visible")?this.date(t.clone().subtract(1,"y")):this.date(t.clone().add(1,"h"))}},"control down":function(e){if(e){var t=this.date()||this.getMoment();e.find(".bs-datepicker").is(":visible")?this.date(t.clone().add(1,"y")):this.date(t.clone().subtract(1,"h"))}},left:function(e){if(e){var t=this.date()||this.getMoment();e.find(".bs-datepicker").is(":visible")&&this.date(t.clone().subtract(1,"d"))}},right:function(e){if(e){var t=this.date()||this.getMoment();e.find(".bs-datepicker").is(":visible")&&this.date(t.clone().add(1,"d"))}},pageUp:function(e){if(e){var t=this.date()||this.getMoment();e.find(".bs-datepicker").is(":visible")&&this.date(t.clone().subtract(1,"M"))}},pageDown:function(e){if(e){var t=this.date()||this.getMoment();e.find(".bs-datepicker").is(":visible")&&this.date(t.clone().add(1,"M"))}},enter:function(){this.hide()},escape:function(){this.hide()},"control space":function(e){e&&e.find(".bs-timepicker").is(":visible")&&e.find('.btn[data-action="togglePeriod"]').click()},t:function(){this.date(this.getMoment())},delete:function(){this.clear()}},debug:!1,allowInputToggle:!1,disabledTimeIntervals:!1,disabledHours:!1,enabledHours:!1,viewDate:!1},e.fn.bsDatetimepicker}(n(1669),H());var j=n(1669),O=function(){return O=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},O.apply(this,arguments)};const P=function(){function e(e){this.settings=e,this.defaults={};var t=["haz","as","ar","as","azb","bo","dz","fa","gu","he","hi","hy","ka","kk","km","kn","ko","ku","lo","ml","mr","my","ne","pa","ps","sd","si","skr","ta","ur"].includes(this.settings.get("calendarLocale"))?"en":this.settings.get("calendarLocale"),n=new Date;n.setHours(0,0,0,0),this.defaults={format:this.settings.get("dateFormat"),useCurrent:!1,showClose:!0,icons:{time:"atum-icon atmi-clock",date:"atum-icon atmi-calendar-full",up:"atum-icon atmi-chevron-up",down:"atum-icon atmi-chevron-down",previous:"atum-icon atmi-chevron-left",next:"atum-icon atmi-chevron-right",today:"atum-icon atmi-frame-expand",clear:"atum-icon atmi-trash",close:"atum-icon atmi-ok"},minDate:n,showClear:!0,showTodayButton:!0,widgetPositioning:{horizontal:"right",vertical:"bottom"},tooltips:{today:this.settings.get("goToToday"),clear:this.settings.get("clearSelection"),close:this.settings.get("closePicker"),selectMonth:this.settings.get("selectMonth"),prevMonth:this.settings.get("prevMonth"),nextMonth:this.settings.get("nextMonth"),selectYear:this.settings.get("selectYear"),prevYear:this.settings.get("prevYear"),nextYear:this.settings.get("nextYear"),selectDecade:this.settings.get("selectDecade"),prevDecade:this.settings.get("prevDecade"),nextDecade:this.settings.get("nextDecade"),prevCentury:this.settings.get("prevCentury"),nextCentury:this.settings.get("nextCentury"),incrementHour:this.settings.get("incrementHour"),pickHour:this.settings.get("pickHour"),decrementHour:this.settings.get("decrementHour"),incrementMinute:this.settings.get("incrementMinute"),pickMinute:this.settings.get("pickMinute"),decrementMinute:this.settings.get("decrementMinute"),incrementSecond:this.settings.get("incrementSecond"),pickSecond:this.settings.get("pickSecond"),decrementSecond:this.settings.get("decrementSecond")},locale:t||"en"}}return e.prototype.addDateTimePickers=function(e,t){var n=this;void 0===t&&(t={}),e.each((function(e,a){var s=j(a),r=s.data()||{};r.hasOwnProperty("DateTimePicker")&&n.destroyDateTimePickers(s),s.bsDatetimepicker(O(O(O({},n.defaults),r),t))})).on("dp.change dp.clear",(function(e){var t;e.stopImmediatePropagation();var a=j(e.currentTarget),s=a.siblings(".field-label");if(s.length){var r=s.text().trim(),i=a.data("DateTimePicker"),o="object"==typeof e.date?e.date.format((null===(t=null==i?void 0:i.options())||void 0===t?void 0:t.format)||n.settings.get("dateFormat")):n.settings.get("none");o!==r?s.addClass("unsaved").text(o):s.removeClass("unsaved")}a.trigger("atum-dp-change")})).on("dp.show",(function(t){var a=j(t.currentTarget);e.not(a).filter((function(e,t){return!!j(t).children(".bootstrap-datetimepicker-widget").length})).each((function(e,t){j(t).data("DateTimePicker").hide()})),(a.data("range-max")||a.data("range-min"))&&n.checkRange(a)}))},e.prototype.destroyDateTimePickers=function(e){e.each((function(e,t){var n=j(t).data("DateTimePicker");void 0!==n&&n.destroy()}))},e.prototype.checkRange=function(e){var t=e.data("DateTimePicker");if(e.data("range-max")){var n=j(e.data("range-max"));if(n.length){var a=n.data("DateTimePicker");t.maxDate(a.date()||!1)}}else if(e.data("range-min")){var s=j(e.data("range-min"));if(s.length){var r=s.data("DateTimePicker");t.minDate(r.date()||!1)}}},e}();n(1669)((function(e){var t=new s("atumSettingsVars"),n=new a,r=new S,i=new P(t);new w(t,n,r,i)}))})()})();