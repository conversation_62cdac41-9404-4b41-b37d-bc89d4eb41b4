(()=>{"use strict";var t={1669:t=>{t.exports=jQuery}},e={};function n(i){var s=e[i];if(void 0!==s)return s.exports;var o=e[i]={exports:{}};return t[i](o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);const i=function(){function t(t,e){void 0===e&&(e={}),this.varName=t,this.defaults=e,this.settings={};var n=void 0!==window[t]?window[t]:{};Object.assign(this.settings,e,n)}return t.prototype.get=function(t){if(void 0!==this.settings[t])return this.settings[t]},t.prototype.getAll=function(){return this.settings},t.prototype.delete=function(t){this.settings.hasOwnProperty(t)&&delete this.settings[t]},t}(),s=Swal;var o=n.n(s),r=n(1669);const a=function(){function t(t,e){this.settings=t,this.successCallback=e,this.bindEvents()}return t.prototype.bindEvents=function(){var t=this;r("body").on("click",".extend-atum-trial",(function(e){e.preventDefault(),e.stopImmediatePropagation();var n=r(e.currentTarget);t.extendTrialConfirmation(n.closest(".atum-addon").data("addon"),n.data("key"))}))},t.prototype.extendTrialConfirmation=function(t,e){var n=this;o().fire({title:this.settings.get("trialExtension"),text:this.settings.get("trialWillExtend"),icon:"info",showCancelButton:!0,confirmButtonText:this.settings.get("extend"),cancelButtonText:this.settings.get("cancel"),showCloseButton:!0,allowEnterKey:!1,reverseButtons:!0,showLoaderOnConfirm:!0,preConfirm:function(){return n.extendTrial(t,e,!0,(function(t){t.success?o().fire({title:n.settings.get("success"),html:t.data,icon:"success",confirmButtonText:n.settings.get("ok")}).then((function(t){n.successCallback&&t.isConfirmed&&n.successCallback()})):o().showValidationMessage(t.data)}))}})},t.prototype.extendTrial=function(t,e,n,i){var s=this;return void 0===n&&(n=!1),void 0===i&&(i=null),new Promise((function(n){r.ajax({url:window.ajaxurl,method:"POST",dataType:"json",data:{action:"atum_extend_trial",security:s.settings.get("nonce"),addon:t,key:e},success:function(t){i&&i(t),n()}})}))},t}();n(1669)((function(t){var e=new i("atumTrialsModal");new a(e)}))})();