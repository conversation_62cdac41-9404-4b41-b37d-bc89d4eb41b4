(()=>{var e={1234:()=>{},1377:function(e){!function(){"use strict";var t={s:1,n:0,d:1};function n(e,t){if(isNaN(e=parseInt(e,10)))throw f();return e*t}function r(e,t){if(0===t)throw s();var n=Object.create(o.prototype);n.s=e<0?-1:1;var r=a(e=e<0?-e:e,t);return n.n=e/r,n.d=t/r,n}function i(e){for(var t={},n=e,r=2,i=4;i<=n;){for(;n%r==0;)n/=r,t[r]=(t[r]||0)+1;i+=1+2*r++}return n!==e?n>1&&(t[n]=(t[n]||0)+1):t[e]=(t[e]||0)+1,t}var u=function(e,r){var i,u=0,a=1,o=1,l=0,p=0,h=0,m=1,d=1,D=0,v=1,g=1,y=1,E=1e7;if(null==e);else if(void 0!==r){if(o=(u=e)*(a=r),u%1!=0||a%1!=0)throw c()}else switch(typeof e){case"object":if("d"in e&&"n"in e)u=e.n,a=e.d,"s"in e&&(u*=e.s);else{if(!(0 in e))throw f();u=e[0],1 in e&&(a=e[1])}o=u*a;break;case"number":if(e<0&&(o=e,e=-e),e%1==0)u=e;else if(e>0){for(e>=1&&(e/=d=Math.pow(10,Math.floor(1+Math.log(e)/Math.LN10)));v<=E&&y<=E;){if(e===(i=(D+g)/(v+y))){v+y<=E?(u=D+g,a=v+y):y>v?(u=g,a=y):(u=D,a=v);break}e>i?(D+=g,v+=y):(g+=D,y+=v),v>E?(u=g,a=y):(u=D,a=v)}u*=d}else(isNaN(e)||isNaN(r))&&(a=u=NaN);break;case"string":if(null===(v=e.match(/\d+|./g)))throw f();if("-"===v[D]?(o=-1,D++):"+"===v[D]&&D++,v.length===D+1?p=n(v[D++],o):"."===v[D+1]||"."===v[D]?("."!==v[D]&&(l=n(v[D++],o)),(++D+1===v.length||"("===v[D+1]&&")"===v[D+3]||"'"===v[D+1]&&"'"===v[D+3])&&(p=n(v[D],o),m=Math.pow(10,v[D].length),D++),("("===v[D]&&")"===v[D+2]||"'"===v[D]&&"'"===v[D+2])&&(h=n(v[D+1],o),d=Math.pow(10,v[D+1].length)-1,D+=3)):"/"===v[D+1]||":"===v[D+1]?(p=n(v[D],o),m=n(v[D+2],1),D+=3):"/"===v[D+3]&&" "===v[D+1]&&(l=n(v[D],o),p=n(v[D+2],o),m=n(v[D+4],1),D+=5),v.length<=D){o=u=h+(a=m*d)*l+d*p;break}default:throw f()}if(0===a)throw s();t.s=o<0?-1:1,t.n=Math.abs(u),t.d=Math.abs(a)};function a(e,t){if(!e)return t;if(!t)return e;for(;;){if(!(e%=t))return t;if(!(t%=e))return e}}function o(e,n){if(u(e,n),!(this instanceof o))return r(t.s*t.n,t.d);e=a(t.d,t.n),this.s=t.s,this.n=t.n/e,this.d=t.d/e}var s=function(){return new Error("Division by Zero")},f=function(){return new Error("Invalid argument")},c=function(){return new Error("Parameters must be integer")};o.prototype={s:1,n:0,d:1,abs:function(){return r(this.n,this.d)},neg:function(){return r(-this.s*this.n,this.d)},add:function(e,n){return u(e,n),r(this.s*this.n*t.d+t.s*this.d*t.n,this.d*t.d)},sub:function(e,n){return u(e,n),r(this.s*this.n*t.d-t.s*this.d*t.n,this.d*t.d)},mul:function(e,n){return u(e,n),r(this.s*t.s*this.n*t.n,this.d*t.d)},div:function(e,n){return u(e,n),r(this.s*t.s*this.n*t.d,this.d*t.n)},clone:function(){return r(this.s*this.n,this.d)},mod:function(e,n){if(isNaN(this.n)||isNaN(this.d))return new o(NaN);if(void 0===e)return r(this.s*this.n%this.d,1);if(u(e,n),0===t.n&&0===this.d)throw s();return r(this.s*(t.d*this.n)%(t.n*this.d),t.d*this.d)},gcd:function(e,n){return u(e,n),r(a(t.n,this.n)*a(t.d,this.d),t.d*this.d)},lcm:function(e,n){return u(e,n),0===t.n&&0===this.n?r(0,1):r(t.n*this.n,a(t.n,this.n)*a(t.d,this.d))},ceil:function(e){return e=Math.pow(10,e||0),isNaN(this.n)||isNaN(this.d)?new o(NaN):r(Math.ceil(e*this.s*this.n/this.d),e)},floor:function(e){return e=Math.pow(10,e||0),isNaN(this.n)||isNaN(this.d)?new o(NaN):r(Math.floor(e*this.s*this.n/this.d),e)},round:function(e){return e=Math.pow(10,e||0),isNaN(this.n)||isNaN(this.d)?new o(NaN):r(Math.round(e*this.s*this.n/this.d),e)},inverse:function(){return r(this.s*this.d,this.n)},pow:function(e,n){if(u(e,n),1===t.d)return t.s<0?r(Math.pow(this.s*this.d,t.n),Math.pow(this.n,t.n)):r(Math.pow(this.s*this.n,t.n),Math.pow(this.d,t.n));if(this.s<0)return null;var a=i(this.n),o=i(this.d),s=1,f=1;for(var c in a)if("1"!==c){if("0"===c){s=0;break}if(a[c]*=t.n,a[c]%t.d!=0)return null;a[c]/=t.d,s*=Math.pow(c,a[c])}for(var c in o)if("1"!==c){if(o[c]*=t.n,o[c]%t.d!=0)return null;o[c]/=t.d,f*=Math.pow(c,o[c])}return t.s<0?r(f,s):r(s,f)},equals:function(e,n){return u(e,n),this.s*this.n*t.d==t.s*t.n*this.d},compare:function(e,n){u(e,n);var r=this.s*this.n*t.d-t.s*t.n*this.d;return(0<r)-(r<0)},simplify:function(e){if(isNaN(this.n)||isNaN(this.d))return this;e=e||.001;for(var t=this.abs(),n=t.toContinued(),i=1;i<n.length;i++){for(var u=r(n[i-1],1),a=i-2;a>=0;a--)u=u.inverse().add(n[a]);if(Math.abs(u.sub(t).valueOf())<e)return u.mul(this.s)}return this},divisible:function(e,n){return u(e,n),!(!(t.n*this.d)||this.n*t.d%(t.n*this.d))},valueOf:function(){return this.s*this.n/this.d},toFraction:function(e){var t,n="",r=this.n,i=this.d;return this.s<0&&(n+="-"),1===i?n+=r:(e&&(t=Math.floor(r/i))>0&&(n+=t,n+=" ",r%=i),n+=r,n+="/",n+=i),n},toLatex:function(e){var t,n="",r=this.n,i=this.d;return this.s<0&&(n+="-"),1===i?n+=r:(e&&(t=Math.floor(r/i))>0&&(n+=t,r%=i),n+="\\frac{",n+=r,n+="}{",n+=i,n+="}"),n},toContinued:function(){var e,t=this.n,n=this.d,r=[];if(isNaN(t)||isNaN(n))return r;do{r.push(Math.floor(t/n)),e=t%n,t=n,n=e}while(1!==t);return r},toString:function(e){var t=this.n,n=this.d;if(isNaN(t)||isNaN(n))return"NaN";e=e||15;var r=function(e,t){for(;t%2==0;t/=2);for(;t%5==0;t/=5);if(1===t)return 0;for(var n=10%t,r=1;1!==n;r++)if(n=10*n%t,r>2e3)return 0;return r}(0,n),i=function(e,t,n){for(var r=1,i=function(e,t,n){for(var r=1;t>0;e=e*e%n,t>>=1)1&t&&(r=r*e%n);return r}(10,n,t),u=0;u<300;u++){if(r===i)return u;r=10*r%t,i=10*i%t}return 0}(0,n,r),u=this.s<0?"-":"";if(u+=t/n|0,t%=n,(t*=10)&&(u+="."),r){for(var a=i;a--;)u+=t/n|0,t%=n,t*=10;u+="(";for(a=r;a--;)u+=t/n|0,t%=n,t*=10;u+=")"}else for(a=e;t&&a--;)u+=t/n|0,t%=n,t*=10;return u}},Object.defineProperty(o,"__esModule",{value:!0}),o.default=o,o.Fraction=o,e.exports=o}()},1669:e=>{"use strict";e.exports=jQuery},1880:e=>{e.exports=function e(t,n){"use strict";var r,i,u=/(^([+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,a=/(^[ ]*|[ ]*$)/g,o=/(^([\w ]+,?[\w ]+)?[\w ]+,?[\w ]+\d+:\d+(:\d+)?[\w ]?|^\d{1,4}[\/\-]\d{1,4}[\/\-]\d{1,4}|^\w+, \w+ \d+, \d{4})/,s=/^0x[0-9a-f]+$/i,f=/^0/,c=function(t){return e.insensitive&&(""+t).toLowerCase()||""+t},l=c(t).replace(a,"")||"",p=c(n).replace(a,"")||"",h=l.replace(u,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),m=p.replace(u,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),d=parseInt(l.match(s),16)||1!==h.length&&l.match(o)&&Date.parse(l),D=parseInt(p.match(s),16)||d&&p.match(o)&&Date.parse(p)||null;if(D){if(d<D)return-1;if(d>D)return 1}for(var v=0,g=Math.max(h.length,m.length);v<g;v++){if(r=!(h[v]||"").match(f)&&parseFloat(h[v])||h[v]||0,i=!(m[v]||"").match(f)&&parseFloat(m[v])||m[v]||0,isNaN(r)!==isNaN(i))return isNaN(r)?1:-1;if(typeof r!=typeof i&&(r+="",i+=""),r<i)return-1;if(r>i)return 1}return 0}},2369:function(e){e.exports=function(){"use strict";function e(){return!0}function t(){return!1}function n(){}const r="Argument is not a typed-function.";function i(){function u(e){return"object"==typeof e&&null!==e&&e.constructor===Object}const a=[{name:"number",test:function(e){return"number"==typeof e}},{name:"string",test:function(e){return"string"==typeof e}},{name:"boolean",test:function(e){return"boolean"==typeof e}},{name:"Function",test:function(e){return"function"==typeof e}},{name:"Array",test:Array.isArray},{name:"Date",test:function(e){return e instanceof Date}},{name:"RegExp",test:function(e){return e instanceof RegExp}},{name:"Object",test:u},{name:"null",test:function(e){return null===e}},{name:"undefined",test:function(e){return void 0===e}}],o={name:"any",test:e,isAny:!0};let s,f,c=0,l={createCount:0};function p(e){const t=s.get(e);if(t)return t;let n='Unknown type "'+e+'"';const r=e.toLowerCase();let i;for(i of f)if(i.toLowerCase()===r){n+='. Did you mean "'+i+'" ?';break}throw new TypeError(n)}function h(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"any";const n=t?p(t).index:f.length,r=[];for(let t=0;t<e.length;++t){if(!e[t]||"string"!=typeof e[t].name||"function"!=typeof e[t].test)throw new TypeError("Object with properties {name: string, test: function} expected");const i=e[t].name;if(s.has(i))throw new TypeError('Duplicate type name "'+i+'"');r.push(i),s.set(i,{name:i,test:e[t].test,isAny:e[t].isAny,index:n+t,conversionsTo:[]})}const i=f.slice(n);f=f.slice(0,n).concat(r).concat(i);for(let e=n+r.length;e<f.length;++e)s.get(f[e]).index=e}function m(){s=new Map,f=[],c=0,h([o],!1)}function d(){let e;for(e of f)s.get(e).conversionsTo=[];c=0}function D(e){const t=f.filter((t=>{const n=s.get(t);return!n.isAny&&n.test(e)}));return t.length?t:["any"]}function v(e){return e&&"function"==typeof e&&"_typedFunctionData"in e}function g(e,t,n){if(!v(e))throw new TypeError(r);const i=n&&n.exact,u=F(Array.isArray(t)?t.join(","):t),a=w(u);if(!i||a in e.signatures){const t=e._typedFunctionData.signatureMap.get(a);if(t)return t}const o=u.length;let s,f;if(i){let t;for(t in s=[],e.signatures)s.push(e._typedFunctionData.signatureMap.get(t))}else s=e._typedFunctionData.signatures;for(let e=0;e<o;++e){const t=u[e],n=[];let r;for(r of s){const i=S(r.params,e);if(i&&(!t.restParam||i.restParam)){if(!i.hasAny){const e=A(i);if(t.types.some((t=>!e.has(t.name))))continue}n.push(r)}}if(s=n,0===s.length)break}for(f of s)if(f.params.length<=o)return f;throw new TypeError("Signature not found (signature: "+(e.name||"unnamed")+"("+w(u,", ")+"))")}function y(e,t,n){return g(e,t,n).implementation}function E(e,t){const n=p(t);if(n.test(e))return e;const r=n.conversionsTo;if(0===r.length)throw new Error("There are no conversions to "+t+" defined.");for(let t=0;t<r.length;t++)if(p(r[t].from).test(e))return r[t].convert(e);throw new Error("Cannot convert "+e+" to "+t)}function w(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:",";return e.map((e=>e.name)).join(t)}function x(e){const t=0===e.indexOf("..."),n=(t?e.length>3?e.slice(3):"any":e).split("|").map((e=>p(e.trim())));let r=!1,i=t?"...":"";return{types:n.map((function(e){return r=e.isAny||r,i+=e.name+"|",{name:e.name,typeIndex:e.index,test:e.test,isAny:e.isAny,conversion:null,conversionIndex:-1}})),name:i.slice(0,-1),hasAny:r,hasConversion:!1,restParam:t}}function b(e){const t=P(e.types.map((e=>e.name)));let n=e.hasAny,r=e.name;const i=t.map((function(e){const t=p(e.from);return n=t.isAny||n,r+="|"+e.from,{name:e.from,typeIndex:t.index,test:t.test,isAny:t.isAny,conversion:e,conversionIndex:e.index}}));return{types:e.types.concat(i),name:r,hasAny:n,hasConversion:i.length>0,restParam:e.restParam}}function A(e){return e.typeSet||(e.typeSet=new Set,e.types.forEach((t=>e.typeSet.add(t.name)))),e.typeSet}function F(e){const t=[];if("string"!=typeof e)throw new TypeError("Signatures must be strings");const n=e.trim();if(""===n)return t;const r=n.split(",");for(let e=0;e<r.length;++e){const n=x(r[e].trim());if(n.restParam&&e!==r.length-1)throw new SyntaxError('Unexpected rest parameter "'+r[e]+'": only allowed for the last parameter');if(0===n.types.length)return null;t.push(n)}return t}function C(e){const t=J(e);return!!t&&t.restParam}function N(t){if(t&&0!==t.types.length){if(1===t.types.length)return p(t.types[0].name).test;if(2===t.types.length){const e=p(t.types[0].name).test,n=p(t.types[1].name).test;return function(t){return e(t)||n(t)}}{const e=t.types.map((function(e){return p(e.name).test}));return function(t){for(let n=0;n<e.length;n++)if(e[n](t))return!0;return!1}}}return e}function M(e){let t,n,r;if(C(e)){t=$(e).map(N);const n=t.length,r=N(J(e)),i=function(e){for(let t=n;t<e.length;t++)if(!r(e[t]))return!1;return!0};return function(e){for(let n=0;n<t.length;n++)if(!t[n](e[n]))return!1;return i(e)&&e.length>=n+1}}return 0===e.length?function(e){return 0===e.length}:1===e.length?(n=N(e[0]),function(e){return n(e[0])&&1===e.length}):2===e.length?(n=N(e[0]),r=N(e[1]),function(e){return n(e[0])&&r(e[1])&&2===e.length}):(t=e.map(N),function(e){for(let n=0;n<t.length;n++)if(!t[n](e[n]))return!1;return e.length===t.length})}function S(e,t){return t<e.length?e[t]:C(e)?J(e):null}function B(e,t){const n=S(e,t);return n?A(n):new Set}function _(e){return null===e.conversion||void 0===e.conversion}function T(e,t){const n=new Set;return e.forEach((e=>{const r=B(e.params,t);let i;for(i of r)n.add(i)})),n.has("any")?["any"]:Array.from(n)}function O(e,t,n){let r,i;const u=e||"unnamed";let a,o=n;for(a=0;a<t.length;a++){const e=[];if(o.forEach((n=>{const r=N(S(n.params,a));(a<n.params.length||C(n.params))&&r(t[a])&&e.push(n)})),0===e.length){if(i=T(o,a),i.length>0){const e=D(t[a]);return r=new TypeError("Unexpected type of argument in function "+u+" (expected: "+i.join(" or ")+", actual: "+e.join(" | ")+", index: "+a+")"),r.data={category:"wrongType",fn:u,index:a,actual:e,expected:i},r}}else o=e}const s=o.map((function(e){return C(e.params)?1/0:e.params.length}));if(t.length<Math.min.apply(null,s))return i=T(o,a),r=new TypeError("Too few arguments in function "+u+" (expected: "+i.join(" or ")+", index: "+t.length+")"),r.data={category:"tooFewArgs",fn:u,index:t.length,expected:i},r;const f=Math.max.apply(null,s);if(t.length>f)return r=new TypeError("Too many arguments in function "+u+" (expected: "+f+", actual: "+t.length+")"),r.data={category:"tooManyArgs",fn:u,index:t.length,expectedLength:f},r;const c=[];for(let e=0;e<t.length;++e)c.push(D(t[e]).join("|"));return r=new TypeError('Arguments of type "'+c.join(", ")+'" do not match any of the defined signatures of function '+u+"."),r.data={category:"mismatch",actual:c},r}function R(e){let t=f.length+1;for(let n=0;n<e.types.length;n++)_(e.types[n])&&(t=Math.min(t,e.types[n].typeIndex));return t}function I(e){let t=c+1;for(let n=0;n<e.types.length;n++)_(e.types[n])||(t=Math.min(t,e.types[n].conversionIndex));return t}function z(e,t){if(e.hasAny){if(!t.hasAny)return 1}else if(t.hasAny)return-1;if(e.restParam){if(!t.restParam)return 1}else if(t.restParam)return-1;if(e.hasConversion){if(!t.hasConversion)return 1}else if(t.hasConversion)return-1;const n=R(e)-R(t);if(n<0)return-1;if(n>0)return 1;const r=I(e)-I(t);return r<0?-1:r>0?1:0}function U(e,t){const n=e.params,r=t.params,i=J(n),u=J(r),a=C(n),o=C(r);if(a&&i.hasAny){if(!o||!u.hasAny)return 1}else if(o&&u.hasAny)return-1;let s,f=0,c=0;for(s of n)s.hasAny&&++f,s.hasConversion&&++c;let l=0,p=0;for(s of r)s.hasAny&&++l,s.hasConversion&&++p;if(f!==l)return f-l;if(a&&i.hasConversion){if(!o||!u.hasConversion)return 1}else if(o&&u.hasConversion)return-1;if(c!==p)return c-p;if(a){if(!o)return 1}else if(o)return-1;const h=(n.length-r.length)*(a?-1:1);if(0!==h)return h;const m=[];let d,D=0;for(let e=0;e<n.length;++e){const t=z(n[e],r[e]);m.push(t),D+=t}if(0!==D)return D;for(d of m)if(0!==d)return d;return 0}function P(e){if(0===e.length)return[];const t=e.map(p);e.length>1&&t.sort(((e,t)=>e.index-t.index));let n=t[0].conversionsTo;if(1===e.length)return n;n=n.concat([]);const r=new Set(e);for(let e=1;e<t.length;++e){let i;for(i of t[e].conversionsTo)r.has(i.from)||(n.push(i),r.add(i.from))}return n}function L(e,t){let n=t;if(e.some((e=>e.hasConversion))){const r=C(e),i=e.map(k);n=function(){const e=[],n=r?arguments.length-1:arguments.length;for(let t=0;t<n;t++)e[t]=i[t](arguments[t]);return r&&(e[n]=arguments[n].map(i[n])),t.apply(this,e)}}let r=n;if(C(e)){const t=e.length-1;r=function(){return n.apply(this,X(arguments,0,t).concat([X(arguments,t)]))}}return r}function k(e){let t,n,r,i;const u=[],a=[];switch(e.types.forEach((function(e){e.conversion&&(u.push(p(e.conversion.from).test),a.push(e.conversion.convert))})),a.length){case 0:return function(e){return e};case 1:return t=u[0],r=a[0],function(e){return t(e)?r(e):e};case 2:return t=u[0],n=u[1],r=a[0],i=a[1],function(e){return t(e)?r(e):n(e)?i(e):e};default:return function(e){for(let t=0;t<a.length;t++)if(u[t](e))return a[t](e);return e}}}function q(e){function t(e,n,r){if(n<e.length){const i=e[n];let u=[];if(i.restParam){const e=i.types.filter(_);e.length<i.types.length&&u.push({types:e,name:"..."+e.map((e=>e.name)).join("|"),hasAny:e.some((e=>e.isAny)),hasConversion:!1,restParam:!0}),u.push(i)}else u=i.types.map((function(e){return{types:[e],name:e.name,hasAny:e.isAny,hasConversion:e.conversion,restParam:!1}}));return K(u,(function(i){return t(e,n+1,r.concat([i]))}))}return[r]}return t(e,0,[])}function j(e,t){const n=Math.max(e.length,t.length);for(let r=0;r<n;r++){const n=B(e,r),i=B(t,r);let u,a=!1;for(u of i)if(n.has(u)){a=!0;break}if(!a)return!1}const r=e.length,i=t.length,u=C(e),a=C(t);return u?a?r===i:i>=r:a?r>=i:r===i}function H(e){return e.map((e=>ie(e)?ne(e.referToSelf.callback):re(e)?te(e.referTo.references,e.referTo.callback):e))}function G(e,t,n){const r=[];let i;for(i of e){let e=n[i];if("number"!=typeof e)throw new TypeError('No definition for referenced signature "'+i+'"');if(e=t[e],"function"!=typeof e)return!1;r.push(e)}return r}function V(e,t,n){const r=H(e),i=new Array(r.length).fill(!1);let u=!0;for(;u;){u=!1;let e=!0;for(let a=0;a<r.length;++a){if(i[a])continue;const o=r[a];if(ie(o))r[a]=o.referToSelf.callback(n),r[a].referToSelf=o.referToSelf,i[a]=!0,e=!1;else if(re(o)){const n=G(o.referTo.references,r,t);n?(r[a]=o.referTo.callback.apply(this,n),r[a].referTo=o.referTo,i[a]=!0,e=!1):u=!0}}if(e&&u)throw new SyntaxError("Circular reference detected in resolving typed.referTo")}return r}function Y(e){const t=/\bthis(\(|\.signatures\b)/;Object.keys(e).forEach((n=>{const r=e[n];if(t.test(r.toString()))throw new SyntaxError("Using `this` to self-reference a function is deprecated since typed-function@3. Use typed.referTo and typed.referToSelf instead.")}))}function Z(e,r){if(l.createCount++,0===Object.keys(r).length)throw new SyntaxError("No signatures provided");l.warnAgainstDeprecatedThis&&Y(r);const i=[],u=[],a={},o=[];let s;for(s in r){if(!Object.prototype.hasOwnProperty.call(r,s))continue;const e=F(s);if(!e)continue;i.forEach((function(t){if(j(t,e))throw new TypeError('Conflicting signatures "'+w(t)+'" and "'+w(e)+'".')})),i.push(e);const t=u.length;u.push(r[s]);const n=e.map(b);let f;for(f of q(n)){const e=w(f);o.push({params:f,name:e,fn:t}),f.every((e=>!e.hasConversion))&&(a[e]=t)}}o.sort(U);const f=V(u,a,se);let c;for(c in a)Object.prototype.hasOwnProperty.call(a,c)&&(a[c]=f[a[c]]);const p=[],h=new Map;for(c of o)h.has(c.name)||(c.fn=f[c.fn],p.push(c),h.set(c.name,c));const m=p[0]&&p[0].params.length<=2&&!C(p[0].params),d=p[1]&&p[1].params.length<=2&&!C(p[1].params),D=p[2]&&p[2].params.length<=2&&!C(p[2].params),v=p[3]&&p[3].params.length<=2&&!C(p[3].params),g=p[4]&&p[4].params.length<=2&&!C(p[4].params),y=p[5]&&p[5].params.length<=2&&!C(p[5].params),E=m&&d&&D&&v&&g&&y;for(let e=0;e<p.length;++e)p[e].test=M(p[e].params);const x=m?N(p[0].params[0]):t,A=d?N(p[1].params[0]):t,S=D?N(p[2].params[0]):t,B=v?N(p[3].params[0]):t,_=g?N(p[4].params[0]):t,T=y?N(p[5].params[0]):t,O=m?N(p[0].params[1]):t,R=d?N(p[1].params[1]):t,I=D?N(p[2].params[1]):t,z=v?N(p[3].params[1]):t,P=g?N(p[4].params[1]):t,k=y?N(p[5].params[1]):t;for(let e=0;e<p.length;++e)p[e].implementation=L(p[e].params,p[e].fn);const H=m?p[0].implementation:n,G=d?p[1].implementation:n,Z=D?p[2].implementation:n,W=v?p[3].implementation:n,$=g?p[4].implementation:n,J=y?p[5].implementation:n,X=m?p[0].params.length:-1,Q=d?p[1].params.length:-1,K=D?p[2].params.length:-1,ee=v?p[3].params.length:-1,te=g?p[4].params.length:-1,ne=y?p[5].params.length:-1,re=E?6:0,ie=p.length,ue=p.map((e=>e.test)),ae=p.map((e=>e.implementation)),oe=function(){for(let e=re;e<ie;e++)if(ue[e](arguments))return ae[e].apply(this,arguments);return l.onMismatch(e,arguments,p)};function se(e,t){return arguments.length===X&&x(e)&&O(t)?H.apply(this,arguments):arguments.length===Q&&A(e)&&R(t)?G.apply(this,arguments):arguments.length===K&&S(e)&&I(t)?Z.apply(this,arguments):arguments.length===ee&&B(e)&&z(t)?W.apply(this,arguments):arguments.length===te&&_(e)&&P(t)?$.apply(this,arguments):arguments.length===ne&&T(e)&&k(t)?J.apply(this,arguments):oe.apply(this,arguments)}try{Object.defineProperty(se,"name",{value:e})}catch(e){}return se.signatures=a,se._typedFunctionData={signatures:p,signatureMap:h},se}function W(e,t,n){throw O(e,t,n)}function $(e){return X(e,0,e.length-1)}function J(e){return e[e.length-1]}function X(e,t,n){return Array.prototype.slice.call(e,t,n)}function Q(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return e[n]}function K(e,t){return Array.prototype.concat.apply([],e.map(t))}function ee(){const e=$(arguments).map((e=>w(F(e)))),t=J(arguments);if("function"!=typeof t)throw new TypeError("Callback function expected as last argument");return te(e,t)}function te(e,t){return{referTo:{references:e,callback:t}}}function ne(e){if("function"!=typeof e)throw new TypeError("Callback function expected as first argument");return{referToSelf:{callback:e}}}function re(e){return e&&"object"==typeof e.referTo&&Array.isArray(e.referTo.references)&&"function"==typeof e.referTo.callback}function ie(e){return e&&"object"==typeof e.referToSelf&&"function"==typeof e.referToSelf.callback}function ue(e,t){if(!e)return t;if(t&&t!==e){const n=new Error("Function names do not match (expected: "+e+", actual: "+t+")");throw n.data={actual:t,expected:e},n}return e}function ae(e){let t;for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(v(e[n])||"string"==typeof e[n].signature)&&(t=ue(t,e[n].name));return t}function oe(e,t){let n;for(n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(n in e&&t[n]!==e[n]){const r=new Error('Signature "'+n+'" is defined twice');throw r.data={signature:n,sourceFunction:t[n],destFunction:e[n]},r}e[n]=t[n]}}m(),h(a);const se=l;function fe(e){if(!e||"string"!=typeof e.from||"string"!=typeof e.to||"function"!=typeof e.convert)throw new TypeError("Object with properties {from: string, to: string, convert: function} expected");if(e.to===e.from)throw new SyntaxError('Illegal to define conversion from "'+e.from+'" to itself.')}return l=function(e){const t="string"==typeof e;let n=t?e:"";const r={};for(let e=t?1:0;e<arguments.length;++e){const i=arguments[e];let a,o={};if("function"==typeof i?(a=i.name,"string"==typeof i.signature?o[i.signature]=i:v(i)&&(o=i.signatures)):u(i)&&(o=i,t||(a=ae(i))),0===Object.keys(o).length){const t=new TypeError("Argument to 'typed' at index "+e+" is not a (typed) function, nor an object with signatures as keys and functions as values.");throw t.data={index:e,argument:i},t}t||(n=ue(n,a)),oe(r,o)}return Z(n||"",r)},l.create=i,l.createCount=se.createCount,l.onMismatch=W,l.throwMismatchError=W,l.createError=O,l.clear=m,l.clearConversions=d,l.addTypes=h,l._findType=p,l.referTo=ee,l.referToSelf=ne,l.convert=E,l.findSignature=g,l.find=y,l.isTypedFunction=v,l.warnAgainstDeprecatedThis=!0,l.addType=function(e,t){let n="any";!1!==t&&s.has("Object")&&(n="Object"),l.addTypes([e],n)},l.addConversion=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{override:!1};fe(e);const n=p(e.to),r=n.conversionsTo.find((t=>t.from===e.from));if(r){if(!t||!t.override)throw new Error('There is already a conversion from "'+e.from+'" to "'+n.name+'"');l.removeConversion({from:r.from,to:e.to,convert:r.convert})}n.conversionsTo.push({from:e.from,convert:e.convert,index:c++})},l.addConversions=function(e,t){e.forEach((e=>l.addConversion(e,t)))},l.removeConversion=function(e){fe(e);const t=p(e.to),n=Q(t.conversionsTo,(t=>t.from===e.from));if(!n)throw new Error("Attempt to remove nonexistent conversion from "+e.from+" to "+e.to);if(n.convert!==e.convert)throw new Error("Conversion to remove does not match existing conversion");const r=t.conversionsTo.indexOf(n);t.conversionsTo.splice(r,1)},l.resolve=function(e,t){if(!v(e))throw new TypeError(r);const n=e._typedFunctionData.signatures;for(let e=0;e<n.length;++e)if(n[e].test(t))return n[e];return null},l}return i()}()},3031:function(e,t,n){var r;!function(e,i){function u(e){var t=this,n="";t.next=function(){var e=t.x^t.x>>>2;return t.x=t.y,t.y=t.z,t.z=t.w,t.w=t.v,(t.d=t.d+362437|0)+(t.v=t.v^t.v<<4^e^e<<1)|0},t.x=0,t.y=0,t.z=0,t.w=0,t.v=0,e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),r==n.length&&(t.d=t.x<<10^t.x>>>4),t.next()}function a(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t.v=e.v,t.d=e.d,t}function o(e,t){var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=o:n.amdD&&n.amdO?void 0===(r=function(){return o}.call(t,n,t,i))||(i.exports=r):this.xorwow=o}(0,e=n.nmd(e),n.amdD)},3181:function(e,t,n){var r;!function(e,i){function u(e){var t=this,n="";t.x=0,t.y=0,t.z=0,t.w=0,t.next=function(){var e=t.x^t.x<<11;return t.x=t.y,t.y=t.z,t.z=t.w,t.w^=t.w>>>19^e^e>>>8},e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),t.next()}function a(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t}function o(e,t){var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=o:n.amdD&&n.amdO?void 0===(r=function(){return o}.call(t,n,t,i))||(i.exports=r):this.xor128=o}(0,e=n.nmd(e),n.amdD)},3717:function(e,t,n){var r;!function(e,i){function u(e){var t=this,n="";t.next=function(){var e=t.b,n=t.c,r=t.d,i=t.a;return e=e<<25^e>>>7^n,n=n-r|0,r=r<<24^r>>>8^i,i=i-e|0,t.b=e=e<<20^e>>>12^n,t.c=n=n-r|0,t.d=r<<16^n>>>16^i,t.a=i-e|0},t.a=0,t.b=0,t.c=-1640531527,t.d=1367130551,e===Math.floor(e)?(t.a=e/4294967296|0,t.b=0|e):n+=e;for(var r=0;r<n.length+20;r++)t.b^=0|n.charCodeAt(r),t.next()}function a(e,t){return t.a=e.a,t.b=e.b,t.c=e.c,t.d=e.d,t}function o(e,t){var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&("object"==typeof r&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=o:n.amdD&&n.amdO?void 0===(r=function(){return o}.call(t,n,t,i))||(i.exports=r):this.tychei=o}(0,e=n.nmd(e),n.amdD)},4801:function(e,t,n){var r;!function(i,u,a){var o,s=256,f=a.pow(s,6),c=a.pow(2,52),l=2*c,p=255;function h(e,t,n){var r=[],p=v(D((t=1==t?{entropy:!0}:t||{}).entropy?[e,g(u)]:null==e?function(){try{var e;return o&&(e=o.randomBytes)?e=e(s):(e=new Uint8Array(s),(i.crypto||i.msCrypto).getRandomValues(e)),g(e)}catch(e){var t=i.navigator,n=t&&t.plugins;return[+new Date,i,n,i.screen,g(u)]}}():e,3),r),h=new m(r),y=function(){for(var e=h.g(6),t=f,n=0;e<c;)e=(e+n)*s,t*=s,n=h.g(1);for(;e>=l;)e/=2,t/=2,n>>>=1;return(e+n)/t};return y.int32=function(){return 0|h.g(4)},y.quick=function(){return h.g(4)/4294967296},y.double=y,v(g(h.S),u),(t.pass||n||function(e,t,n,r){return r&&(r.S&&d(r,h),e.state=function(){return d(h,{})}),n?(a.random=e,t):e})(y,p,"global"in t?t.global:this==a,t.state)}function m(e){var t,n=e.length,r=this,i=0,u=r.i=r.j=0,a=r.S=[];for(n||(e=[n++]);i<s;)a[i]=i++;for(i=0;i<s;i++)a[i]=a[u=p&u+e[i%n]+(t=a[i])],a[u]=t;(r.g=function(e){for(var t,n=0,i=r.i,u=r.j,a=r.S;e--;)t=a[i=p&i+1],n=n*s+a[p&(a[i]=a[u=p&u+t])+(a[u]=t)];return r.i=i,r.j=u,n})(s)}function d(e,t){return t.i=e.i,t.j=e.j,t.S=e.S.slice(),t}function D(e,t){var n,r=[],i=typeof e;if(t&&"object"==i)for(n in e)try{r.push(D(e[n],t-1))}catch(e){}return r.length?r:"string"==i?e:e+"\0"}function v(e,t){for(var n,r=e+"",i=0;i<r.length;)t[p&i]=p&(n^=19*t[p&i])+r.charCodeAt(i++);return g(t)}function g(e){return String.fromCharCode.apply(0,e)}if(v(a.random(),u),e.exports){e.exports=h;try{o=n(1234)}catch(e){}}else void 0===(r=function(){return h}.call(t,n,t,e))||(e.exports=r)}("undefined"!=typeof self?self:this,[],Math)},6833:function(e,t,n){var r;!function(e,i){function u(e){var t=this;t.next=function(){var e,n,r=t.w,i=t.X,u=t.i;return t.w=r=r+1640531527|0,n=i[u+34&127],e=i[u=u+1&127],n^=n<<13,e^=e<<17,n^=n>>>15,e^=e>>>12,n=i[u]=n^e,t.i=u,n+(r^r>>>16)|0},function(e,t){var n,r,i,u,a,o=[],s=128;for(t===(0|t)?(r=t,t=null):(t+="\0",r=0,s=Math.max(s,t.length)),i=0,u=-32;u<s;++u)t&&(r^=t.charCodeAt((u+32)%t.length)),0===u&&(a=r),r^=r<<10,r^=r>>>15,r^=r<<4,r^=r>>>13,u>=0&&(a=a+1640531527|0,i=0==(n=o[127&u]^=r+a)?i+1:0);for(i>=128&&(o[127&(t&&t.length||0)]=-1),i=127,u=512;u>0;--u)r=o[i+34&127],n=o[i=i+1&127],r^=r<<13,n^=n<<17,r^=r>>>15,n^=n>>>12,o[i]=r^n;e.w=a,e.X=o,e.i=i}(t,e)}function a(e,t){return t.i=e.i,t.w=e.w,t.X=e.X.slice(),t}function o(e,t){null==e&&(e=+new Date);var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&(r.X&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=o:n.amdD&&n.amdO?void 0===(r=function(){return o}.call(t,n,t,i))||(i.exports=r):this.xor4096=o}(0,e=n.nmd(e),n.amdD)},7180:function(e,t,n){var r;!function(e,i){function u(e){var t=this,n=function(){var e=4022871197,t=function(t){t=String(t);for(var n=0;n<t.length;n++){var r=.02519603282416938*(e+=t.charCodeAt(n));r-=e=r>>>0,e=(r*=e)>>>0,e+=4294967296*(r-=e)}return 2.3283064365386963e-10*(e>>>0)};return t}();t.next=function(){var e=2091639*t.s0+2.3283064365386963e-10*t.c;return t.s0=t.s1,t.s1=t.s2,t.s2=e-(t.c=0|e)},t.c=1,t.s0=n(" "),t.s1=n(" "),t.s2=n(" "),t.s0-=n(e),t.s0<0&&(t.s0+=1),t.s1-=n(e),t.s1<0&&(t.s1+=1),t.s2-=n(e),t.s2<0&&(t.s2+=1),n=null}function a(e,t){return t.c=e.c,t.s0=e.s0,t.s1=e.s1,t.s2=e.s2,t}function o(e,t){var n=new u(e),r=t&&t.state,i=n.next;return i.int32=function(){return 4294967296*n.next()|0},i.double=function(){return i()+11102230246251565e-32*(2097152*i()|0)},i.quick=i,r&&("object"==typeof r&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=o:n.amdD&&n.amdO?void 0===(r=function(){return o}.call(t,n,t,i))||(i.exports=r):this.alea=o}(0,e=n.nmd(e),n.amdD)},7391:(e,t,n)=>{var r=n(7180),i=n(3181),u=n(3031),a=n(9067),o=n(6833),s=n(3717),f=n(4801);f.alea=r,f.xor128=i,f.xorwow=u,f.xorshift7=a,f.xor4096=o,f.tychei=s,e.exports=f},9067:function(e,t,n){var r;!function(e,i){function u(e){var t=this;t.next=function(){var e,n,r=t.x,i=t.i;return e=r[i],n=(e^=e>>>7)^e<<24,n^=(e=r[i+1&7])^e>>>10,n^=(e=r[i+3&7])^e>>>3,n^=(e=r[i+4&7])^e<<7,e=r[i+7&7],n^=(e^=e<<13)^e<<9,r[i]=n,t.i=i+1&7,n},function(e,t){var n,r=[];if(t===(0|t))r[0]=t;else for(t=""+t,n=0;n<t.length;++n)r[7&n]=r[7&n]<<15^t.charCodeAt(n)+r[n+1&7]<<13;for(;r.length<8;)r.push(0);for(n=0;n<8&&0===r[n];++n);for(8==n?r[7]=-1:r[n],e.x=r,e.i=0,n=256;n>0;--n)e.next()}(t,e)}function a(e,t){return t.x=e.x.slice(),t.i=e.i,t}function o(e,t){null==e&&(e=+new Date);var n=new u(e),r=t&&t.state,i=function(){return(n.next()>>>0)/4294967296};return i.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},i.int32=n.next,i.quick=i,r&&(r.x&&a(r,n),i.state=function(){return a(n,{})}),i}i&&i.exports?i.exports=o:n.amdD&&n.amdO?void 0===(r=function(){return o}.call(t,n,t,i))||(i.exports=r):this.xorshift7=o}(0,e=n.nmd(e),n.amdD)}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var u=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(u.exports,u,u.exports,n),u.loaded=!0,u.exports}n.amdD=function(){throw new Error("define cannot be used indirect")},n.amdO={},n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{"use strict";const e=function(){function e(e,t){void 0===t&&(t={}),this.varName=e,this.defaults=t,this.settings={};var n=void 0!==window[e]?window[e]:{};Object.assign(this.settings,t,n)}return e.prototype.get=function(e){if(void 0!==this.settings[e])return this.settings[e]},e.prototype.getAll=function(){return this.settings},e.prototype.delete=function(e){this.settings.hasOwnProperty(e)&&delete this.settings[e]},e}();function t(){return t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},t.apply(null,arguments)}var r={epsilon:1e-12,matrix:"Matrix",number:"number",precision:64,predictable:!1,randomSeed:null},i=["Matrix","Array"],u=["number","BigNumber","Fraction"];var a=function(e){if(e)throw new Error("The global config is readonly. \nPlease create a mathjs instance if you want to change the default configuration. \nExample:\n\n  import { create, all } from 'mathjs';\n  const mathjs = create(all);\n  mathjs.config({ number: 'BigNumber' });\n");return Object.freeze(r)};t(a,r,{MATRIX_OPTIONS:i,NUMBER_OPTIONS:u});var o,s,f=9e15,c=1e9,l="0123456789abcdef",p="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",h="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",m={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-f,maxE:f,crypto:!1},d=!0,D="[DecimalError] ",v=D+"Invalid argument: ",g=D+"Precision limit exceeded",y=D+"crypto unavailable",E="[object Decimal]",w=Math.floor,x=Math.pow,b=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,A=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,F=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,C=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,N=1e7,M=p.length-1,S=h.length-1,B={toStringTag:E};function _(e){var t,n,r,i=e.length-1,u="",a=e[0];if(i>0){for(u+=a,t=1;t<i;t++)(n=7-(r=e[t]+"").length)&&(u+=j(n)),u+=r;(n=7-(r=(a=e[t])+"").length)&&(u+=j(n))}else if(0===a)return"0";for(;a%10==0;)a/=10;return u+a}function T(e,t,n){if(e!==~~e||e<t||e>n)throw Error(v+e)}function O(e,t,n,r){var i,u,a,o;for(u=e[0];u>=10;u/=10)--t;return--t<0?(t+=7,i=0):(i=Math.ceil((t+1)/7),t%=7),u=x(10,7-t),o=e[i]%u|0,null==r?t<3?(0==t?o=o/100|0:1==t&&(o=o/10|0),a=n<4&&99999==o||n>3&&49999==o||5e4==o||0==o):a=(n<4&&o+1==u||n>3&&o+1==u/2)&&(e[i+1]/u/100|0)==x(10,t-2)-1||(o==u/2||0==o)&&!(e[i+1]/u/100|0):t<4?(0==t?o=o/1e3|0:1==t?o=o/100|0:2==t&&(o=o/10|0),a=(r||n<4)&&9999==o||!r&&n>3&&4999==o):a=((r||n<4)&&o+1==u||!r&&n>3&&o+1==u/2)&&(e[i+1]/u/1e3|0)==x(10,t-3)-1,a}function R(e,t,n){for(var r,i,u=[0],a=0,o=e.length;a<o;){for(i=u.length;i--;)u[i]*=t;for(u[0]+=l.indexOf(e.charAt(a++)),r=0;r<u.length;r++)u[r]>n-1&&(void 0===u[r+1]&&(u[r+1]=0),u[r+1]+=u[r]/n|0,u[r]%=n)}return u.reverse()}B.absoluteValue=B.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),z(e)},B.ceil=function(){return z(new this.constructor(this),this.e+1,2)},B.clampedTo=B.clamp=function(e,t){var n=this,r=n.constructor;if(e=new r(e),t=new r(t),!e.s||!t.s)return new r(NaN);if(e.gt(t))throw Error(v+t);return n.cmp(e)<0?e:n.cmp(t)>0?t:new r(n)},B.comparedTo=B.cmp=function(e){var t,n,r,i,u=this,a=u.d,o=(e=new u.constructor(e)).d,s=u.s,f=e.s;if(!a||!o)return s&&f?s!==f?s:a===o?0:!a^s<0?1:-1:NaN;if(!a[0]||!o[0])return a[0]?s:o[0]?-f:0;if(s!==f)return s;if(u.e!==e.e)return u.e>e.e^s<0?1:-1;for(t=0,n=(r=a.length)<(i=o.length)?r:i;t<n;++t)if(a[t]!==o[t])return a[t]>o[t]^s<0?1:-1;return r===i?0:r>i^s<0?1:-1},B.cosine=B.cos=function(){var e,t,n=this,r=n.constructor;return n.d?n.d[0]?(e=r.precision,t=r.rounding,r.precision=e+Math.max(n.e,n.sd())+7,r.rounding=1,n=function(e,t){var n,r,i;if(t.isZero())return t;r=t.d.length,r<32?i=(1/Q(4,n=Math.ceil(r/3))).toString():(n=16,i="2.3283064365386962890625e-10");e.precision+=n,t=X(e,1,t.times(i),new e(1));for(var u=n;u--;){var a=t.times(t);t=a.times(a).minus(a).times(8).plus(1)}return e.precision-=n,t}(r,K(r,n)),r.precision=e,r.rounding=t,z(2==s||3==s?n.neg():n,e,t,!0)):new r(1):new r(NaN)},B.cubeRoot=B.cbrt=function(){var e,t,n,r,i,u,a,o,s,f,c=this,l=c.constructor;if(!c.isFinite()||c.isZero())return new l(c);for(d=!1,(u=c.s*x(c.s*c,1/3))&&Math.abs(u)!=1/0?r=new l(u.toString()):(n=_(c.d),(u=((e=c.e)-n.length+1)%3)&&(n+=1==u||-2==u?"0":"00"),u=x(n,1/3),e=w((e+1)/3)-(e%3==(e<0?-1:2)),(r=new l(n=u==1/0?"5e"+e:(n=u.toExponential()).slice(0,n.indexOf("e")+1)+e)).s=c.s),a=(e=l.precision)+3;;)if(f=(s=(o=r).times(o).times(o)).plus(c),r=I(f.plus(c).times(o),f.plus(s),a+2,1),_(o.d).slice(0,a)===(n=_(r.d)).slice(0,a)){if("9999"!=(n=n.slice(a-3,a+1))&&(i||"4999"!=n)){+n&&(+n.slice(1)||"5"!=n.charAt(0))||(z(r,e+1,1),t=!r.times(r).times(r).eq(c));break}if(!i&&(z(o,e+1,0),o.times(o).times(o).eq(c))){r=o;break}a+=4,i=1}return d=!0,z(r,e,l.rounding,t)},B.decimalPlaces=B.dp=function(){var e,t=this.d,n=NaN;if(t){if(n=7*((e=t.length-1)-w(this.e/7)),e=t[e])for(;e%10==0;e/=10)n--;n<0&&(n=0)}return n},B.dividedBy=B.div=function(e){return I(this,new this.constructor(e))},B.dividedToIntegerBy=B.divToInt=function(e){var t=this.constructor;return z(I(this,new t(e),0,1,1),t.precision,t.rounding)},B.equals=B.eq=function(e){return 0===this.cmp(e)},B.floor=function(){return z(new this.constructor(this),this.e+1,3)},B.greaterThan=B.gt=function(e){return this.cmp(e)>0},B.greaterThanOrEqualTo=B.gte=function(e){var t=this.cmp(e);return 1==t||0===t},B.hyperbolicCosine=B.cosh=function(){var e,t,n,r,i,u=this,a=u.constructor,o=new a(1);if(!u.isFinite())return new a(u.s?1/0:NaN);if(u.isZero())return o;n=a.precision,r=a.rounding,a.precision=n+Math.max(u.e,u.sd())+4,a.rounding=1,(i=u.d.length)<32?t=(1/Q(4,e=Math.ceil(i/3))).toString():(e=16,t="2.3283064365386962890625e-10"),u=X(a,1,u.times(t),new a(1),!0);for(var s,f=e,c=new a(8);f--;)s=u.times(u),u=o.minus(s.times(c.minus(s.times(c))));return z(u,a.precision=n,a.rounding=r,!0)},B.hyperbolicSine=B.sinh=function(){var e,t,n,r,i=this,u=i.constructor;if(!i.isFinite()||i.isZero())return new u(i);if(t=u.precision,n=u.rounding,u.precision=t+Math.max(i.e,i.sd())+4,u.rounding=1,(r=i.d.length)<3)i=X(u,2,i,i,!0);else{e=(e=1.4*Math.sqrt(r))>16?16:0|e,i=X(u,2,i=i.times(1/Q(5,e)),i,!0);for(var a,o=new u(5),s=new u(16),f=new u(20);e--;)a=i.times(i),i=i.times(o.plus(a.times(s.times(a).plus(f))))}return u.precision=t,u.rounding=n,z(i,t,n,!0)},B.hyperbolicTangent=B.tanh=function(){var e,t,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+7,r.rounding=1,I(n.sinh(),n.cosh(),r.precision=e,r.rounding=t)):new r(n.s)},B.inverseCosine=B.acos=function(){var e=this,t=e.constructor,n=e.abs().cmp(1),r=t.precision,i=t.rounding;return-1!==n?0===n?e.isNeg()?k(t,r,i):new t(0):new t(NaN):e.isZero()?k(t,r+4,i).times(.5):(t.precision=r+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=r,t.rounding=i,e.times(2))},B.inverseHyperbolicCosine=B.acosh=function(){var e,t,n=this,r=n.constructor;return n.lte(1)?new r(n.eq(1)?0:NaN):n.isFinite()?(e=r.precision,t=r.rounding,r.precision=e+Math.max(Math.abs(n.e),n.sd())+4,r.rounding=1,d=!1,n=n.times(n).minus(1).sqrt().plus(n),d=!0,r.precision=e,r.rounding=t,n.ln()):new r(n)},B.inverseHyperbolicSine=B.asinh=function(){var e,t,n=this,r=n.constructor;return!n.isFinite()||n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+2*Math.max(Math.abs(n.e),n.sd())+6,r.rounding=1,d=!1,n=n.times(n).plus(1).sqrt().plus(n),d=!0,r.precision=e,r.rounding=t,n.ln())},B.inverseHyperbolicTangent=B.atanh=function(){var e,t,n,r,i=this,u=i.constructor;return i.isFinite()?i.e>=0?new u(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=u.precision,t=u.rounding,r=i.sd(),Math.max(r,e)<2*-i.e-1?z(new u(i),e,t,!0):(u.precision=n=r-i.e,i=I(i.plus(1),new u(1).minus(i),n+e,1),u.precision=e+4,u.rounding=1,i=i.ln(),u.precision=e,u.rounding=t,i.times(.5))):new u(NaN)},B.inverseSine=B.asin=function(){var e,t,n,r,i=this,u=i.constructor;return i.isZero()?new u(i):(t=i.abs().cmp(1),n=u.precision,r=u.rounding,-1!==t?0===t?((e=k(u,n+4,r).times(.5)).s=i.s,e):new u(NaN):(u.precision=n+6,u.rounding=1,i=i.div(new u(1).minus(i.times(i)).sqrt().plus(1)).atan(),u.precision=n,u.rounding=r,i.times(2)))},B.inverseTangent=B.atan=function(){var e,t,n,r,i,u,a,o,s,f=this,c=f.constructor,l=c.precision,p=c.rounding;if(f.isFinite()){if(f.isZero())return new c(f);if(f.abs().eq(1)&&l+4<=S)return(a=k(c,l+4,p).times(.25)).s=f.s,a}else{if(!f.s)return new c(NaN);if(l+4<=S)return(a=k(c,l+4,p).times(.5)).s=f.s,a}for(c.precision=o=l+10,c.rounding=1,e=n=Math.min(28,o/7+2|0);e;--e)f=f.div(f.times(f).plus(1).sqrt().plus(1));for(d=!1,t=Math.ceil(o/7),r=1,s=f.times(f),a=new c(f),i=f;-1!==e;)if(i=i.times(s),u=a.minus(i.div(r+=2)),i=i.times(s),void 0!==(a=u.plus(i.div(r+=2))).d[t])for(e=t;a.d[e]===u.d[e]&&e--;);return n&&(a=a.times(2<<n-1)),d=!0,z(a,c.precision=l,c.rounding=p,!0)},B.isFinite=function(){return!!this.d},B.isInteger=B.isInt=function(){return!!this.d&&w(this.e/7)>this.d.length-2},B.isNaN=function(){return!this.s},B.isNegative=B.isNeg=function(){return this.s<0},B.isPositive=B.isPos=function(){return this.s>0},B.isZero=function(){return!!this.d&&0===this.d[0]},B.lessThan=B.lt=function(e){return this.cmp(e)<0},B.lessThanOrEqualTo=B.lte=function(e){return this.cmp(e)<1},B.logarithm=B.log=function(e){var t,n,r,i,u,a,o,s,f=this,c=f.constructor,l=c.precision,p=c.rounding;if(null==e)e=new c(10),t=!0;else{if(n=(e=new c(e)).d,e.s<0||!n||!n[0]||e.eq(1))return new c(NaN);t=e.eq(10)}if(n=f.d,f.s<0||!n||!n[0]||f.eq(1))return new c(n&&!n[0]?-1/0:1!=f.s?NaN:n?0:1/0);if(t)if(n.length>1)u=!0;else{for(i=n[0];i%10==0;)i/=10;u=1!==i}if(d=!1,a=Z(f,o=l+5),r=t?L(c,o+10):Z(e,o),O((s=I(a,r,o,1)).d,i=l,p))do{if(a=Z(f,o+=10),r=t?L(c,o+10):Z(e,o),s=I(a,r,o,1),!u){+_(s.d).slice(i+1,i+15)+1==1e14&&(s=z(s,l+1,0));break}}while(O(s.d,i+=10,p));return d=!0,z(s,l,p)},B.minus=B.sub=function(e){var t,n,r,i,u,a,o,s,f,c,l,p,h=this,m=h.constructor;if(e=new m(e),!h.d||!e.d)return h.s&&e.s?h.d?e.s=-e.s:e=new m(e.d||h.s!==e.s?h:NaN):e=new m(NaN),e;if(h.s!=e.s)return e.s=-e.s,h.plus(e);if(f=h.d,p=e.d,o=m.precision,s=m.rounding,!f[0]||!p[0]){if(p[0])e.s=-e.s;else{if(!f[0])return new m(3===s?-0:0);e=new m(h)}return d?z(e,o,s):e}if(n=w(e.e/7),c=w(h.e/7),f=f.slice(),u=c-n){for((l=u<0)?(t=f,u=-u,a=p.length):(t=p,n=c,a=f.length),u>(r=Math.max(Math.ceil(o/7),a)+2)&&(u=r,t.length=1),t.reverse(),r=u;r--;)t.push(0);t.reverse()}else{for((l=(r=f.length)<(a=p.length))&&(a=r),r=0;r<a;r++)if(f[r]!=p[r]){l=f[r]<p[r];break}u=0}for(l&&(t=f,f=p,p=t,e.s=-e.s),a=f.length,r=p.length-a;r>0;--r)f[a++]=0;for(r=p.length;r>u;){if(f[--r]<p[r]){for(i=r;i&&0===f[--i];)f[i]=N-1;--f[i],f[r]+=N}f[r]-=p[r]}for(;0===f[--a];)f.pop();for(;0===f[0];f.shift())--n;return f[0]?(e.d=f,e.e=P(f,n),d?z(e,o,s):e):new m(3===s?-0:0)},B.modulo=B.mod=function(e){var t,n=this,r=n.constructor;return e=new r(e),!n.d||!e.s||e.d&&!e.d[0]?new r(NaN):!e.d||n.d&&!n.d[0]?z(new r(n),r.precision,r.rounding):(d=!1,9==r.modulo?(t=I(n,e.abs(),0,3,1)).s*=e.s:t=I(n,e,0,r.modulo,1),t=t.times(e),d=!0,n.minus(t))},B.naturalExponential=B.exp=function(){return Y(this)},B.naturalLogarithm=B.ln=function(){return Z(this)},B.negated=B.neg=function(){var e=new this.constructor(this);return e.s=-e.s,z(e)},B.plus=B.add=function(e){var t,n,r,i,u,a,o,s,f,c,l=this,p=l.constructor;if(e=new p(e),!l.d||!e.d)return l.s&&e.s?l.d||(e=new p(e.d||l.s===e.s?l:NaN)):e=new p(NaN),e;if(l.s!=e.s)return e.s=-e.s,l.minus(e);if(f=l.d,c=e.d,o=p.precision,s=p.rounding,!f[0]||!c[0])return c[0]||(e=new p(l)),d?z(e,o,s):e;if(u=w(l.e/7),r=w(e.e/7),f=f.slice(),i=u-r){for(i<0?(n=f,i=-i,a=c.length):(n=c,r=u,a=f.length),i>(a=(u=Math.ceil(o/7))>a?u+1:a+1)&&(i=a,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((a=f.length)-(i=c.length)<0&&(i=a,n=c,c=f,f=n),t=0;i;)t=(f[--i]=f[i]+c[i]+t)/N|0,f[i]%=N;for(t&&(f.unshift(t),++r),a=f.length;0==f[--a];)f.pop();return e.d=f,e.e=P(f,r),d?z(e,o,s):e},B.precision=B.sd=function(e){var t,n=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(v+e);return n.d?(t=q(n.d),e&&n.e+1>t&&(t=n.e+1)):t=NaN,t},B.round=function(){var e=this,t=e.constructor;return z(new t(e),e.e+1,t.rounding)},B.sine=B.sin=function(){var e,t,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+Math.max(n.e,n.sd())+7,r.rounding=1,n=function(e,t){var n,r=t.d.length;if(r<3)return t.isZero()?t:X(e,2,t,t);n=(n=1.4*Math.sqrt(r))>16?16:0|n,t=t.times(1/Q(5,n)),t=X(e,2,t,t);for(var i,u=new e(5),a=new e(16),o=new e(20);n--;)i=t.times(t),t=t.times(u.plus(i.times(a.times(i).minus(o))));return t}(r,K(r,n)),r.precision=e,r.rounding=t,z(s>2?n.neg():n,e,t,!0)):new r(NaN)},B.squareRoot=B.sqrt=function(){var e,t,n,r,i,u,a=this,o=a.d,s=a.e,f=a.s,c=a.constructor;if(1!==f||!o||!o[0])return new c(!f||f<0&&(!o||o[0])?NaN:o?a:1/0);for(d=!1,0==(f=Math.sqrt(+a))||f==1/0?(((t=_(o)).length+s)%2==0&&(t+="0"),f=Math.sqrt(t),s=w((s+1)/2)-(s<0||s%2),r=new c(t=f==1/0?"5e"+s:(t=f.toExponential()).slice(0,t.indexOf("e")+1)+s)):r=new c(f.toString()),n=(s=c.precision)+3;;)if(r=(u=r).plus(I(a,u,n+2,1)).times(.5),_(u.d).slice(0,n)===(t=_(r.d)).slice(0,n)){if("9999"!=(t=t.slice(n-3,n+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(z(r,s+1,1),e=!r.times(r).eq(a));break}if(!i&&(z(u,s+1,0),u.times(u).eq(a))){r=u;break}n+=4,i=1}return d=!0,z(r,s,c.rounding,e)},B.tangent=B.tan=function(){var e,t,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,t=r.rounding,r.precision=e+10,r.rounding=1,(n=n.sin()).s=1,n=I(n,new r(1).minus(n.times(n)).sqrt(),e+10,0),r.precision=e,r.rounding=t,z(2==s||4==s?n.neg():n,e,t,!0)):new r(NaN)},B.times=B.mul=function(e){var t,n,r,i,u,a,o,s,f,c=this,l=c.constructor,p=c.d,h=(e=new l(e)).d;if(e.s*=c.s,!(p&&p[0]&&h&&h[0]))return new l(!e.s||p&&!p[0]&&!h||h&&!h[0]&&!p?NaN:p&&h?0*e.s:e.s/0);for(n=w(c.e/7)+w(e.e/7),(s=p.length)<(f=h.length)&&(u=p,p=h,h=u,a=s,s=f,f=a),u=[],r=a=s+f;r--;)u.push(0);for(r=f;--r>=0;){for(t=0,i=s+r;i>r;)o=u[i]+h[r]*p[i-r-1]+t,u[i--]=o%N|0,t=o/N|0;u[i]=(u[i]+t)%N|0}for(;!u[--a];)u.pop();return t?++n:u.shift(),e.d=u,e.e=P(u,n),d?z(e,l.precision,l.rounding):e},B.toBinary=function(e,t){return ee(this,2,e,t)},B.toDecimalPlaces=B.toDP=function(e,t){var n=this,r=n.constructor;return n=new r(n),void 0===e?n:(T(e,0,c),void 0===t?t=r.rounding:T(t,0,8),z(n,e+n.e+1,t))},B.toExponential=function(e,t){var n,r=this,i=r.constructor;return void 0===e?n=U(r,!0):(T(e,0,c),void 0===t?t=i.rounding:T(t,0,8),n=U(r=z(new i(r),e+1,t),!0,e+1)),r.isNeg()&&!r.isZero()?"-"+n:n},B.toFixed=function(e,t){var n,r,i=this,u=i.constructor;return void 0===e?n=U(i):(T(e,0,c),void 0===t?t=u.rounding:T(t,0,8),n=U(r=z(new u(i),e+i.e+1,t),!1,e+r.e+1)),i.isNeg()&&!i.isZero()?"-"+n:n},B.toFraction=function(e){var t,n,r,i,u,a,o,s,f,c,l,p,h=this,m=h.d,D=h.constructor;if(!m)return new D(h);if(f=n=new D(1),r=s=new D(0),a=(u=(t=new D(r)).e=q(m)-h.e-1)%7,t.d[0]=x(10,a<0?7+a:a),null==e)e=u>0?t:f;else{if(!(o=new D(e)).isInt()||o.lt(f))throw Error(v+o);e=o.gt(t)?u>0?t:f:o}for(d=!1,o=new D(_(m)),c=D.precision,D.precision=u=7*m.length*2;l=I(o,t,0,1,1),1!=(i=n.plus(l.times(r))).cmp(e);)n=r,r=i,i=f,f=s.plus(l.times(i)),s=i,i=t,t=o.minus(l.times(i)),o=i;return i=I(e.minus(n),r,0,1,1),s=s.plus(i.times(f)),n=n.plus(i.times(r)),s.s=f.s=h.s,p=I(f,r,u,1).minus(h).abs().cmp(I(s,n,u,1).minus(h).abs())<1?[f,r]:[s,n],D.precision=c,d=!0,p},B.toHexadecimal=B.toHex=function(e,t){return ee(this,16,e,t)},B.toNearest=function(e,t){var n=this,r=n.constructor;if(n=new r(n),null==e){if(!n.d)return n;e=new r(1),t=r.rounding}else{if(e=new r(e),void 0===t?t=r.rounding:T(t,0,8),!n.d)return e.s?n:e;if(!e.d)return e.s&&(e.s=n.s),e}return e.d[0]?(d=!1,n=I(n,e,0,t,1).times(e),d=!0,z(n)):(e.s=n.s,n=e),n},B.toNumber=function(){return+this},B.toOctal=function(e,t){return ee(this,8,e,t)},B.toPower=B.pow=function(e){var t,n,r,i,u,a,o=this,s=o.constructor,f=+(e=new s(e));if(!(o.d&&e.d&&o.d[0]&&e.d[0]))return new s(x(+o,f));if((o=new s(o)).eq(1))return o;if(r=s.precision,u=s.rounding,e.eq(1))return z(o,r,u);if((t=w(e.e/7))>=e.d.length-1&&(n=f<0?-f:f)<=9007199254740991)return i=H(s,o,n,r),e.s<0?new s(1).div(i):z(i,r,u);if((a=o.s)<0){if(t<e.d.length-1)return new s(NaN);if(1&e.d[t]||(a=1),0==o.e&&1==o.d[0]&&1==o.d.length)return o.s=a,o}return(t=0!=(n=x(+o,f))&&isFinite(n)?new s(n+"").e:w(f*(Math.log("0."+_(o.d))/Math.LN10+o.e+1)))>s.maxE+1||t<s.minE-1?new s(t>0?a/0:0):(d=!1,s.rounding=o.s=1,n=Math.min(12,(t+"").length),(i=Y(e.times(Z(o,r+n)),r)).d&&O((i=z(i,r+5,1)).d,r,u)&&(t=r+10,+_((i=z(Y(e.times(Z(o,t+n)),t),t+5,1)).d).slice(r+1,r+15)+1==1e14&&(i=z(i,r+1,0))),i.s=a,d=!0,s.rounding=u,z(i,r,u))},B.toPrecision=function(e,t){var n,r=this,i=r.constructor;return void 0===e?n=U(r,r.e<=i.toExpNeg||r.e>=i.toExpPos):(T(e,1,c),void 0===t?t=i.rounding:T(t,0,8),n=U(r=z(new i(r),e,t),e<=r.e||r.e<=i.toExpNeg,e)),r.isNeg()&&!r.isZero()?"-"+n:n},B.toSignificantDigits=B.toSD=function(e,t){var n=this.constructor;return void 0===e?(e=n.precision,t=n.rounding):(T(e,1,c),void 0===t?t=n.rounding:T(t,0,8)),z(new n(this),e,t)},B.toString=function(){var e=this,t=e.constructor,n=U(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+n:n},B.truncated=B.trunc=function(){return z(new this.constructor(this),this.e+1,1)},B.valueOf=B.toJSON=function(){var e=this,t=e.constructor,n=U(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+n:n};var I=function(){function e(e,t,n){var r,i=0,u=e.length;for(e=e.slice();u--;)r=e[u]*t+i,e[u]=r%n|0,i=r/n|0;return i&&e.unshift(i),e}function t(e,t,n,r){var i,u;if(n!=r)u=n>r?1:-1;else for(i=u=0;i<n;i++)if(e[i]!=t[i]){u=e[i]>t[i]?1:-1;break}return u}function n(e,t,n,r){for(var i=0;n--;)e[n]-=i,i=e[n]<t[n]?1:0,e[n]=i*r+e[n]-t[n];for(;!e[0]&&e.length>1;)e.shift()}return function(r,i,u,a,s,f){var c,l,p,h,m,d,D,v,g,y,E,x,b,A,F,C,M,S,B,_,T=r.constructor,O=r.s==i.s?1:-1,R=r.d,I=i.d;if(!(R&&R[0]&&I&&I[0]))return new T(r.s&&i.s&&(R?!I||R[0]!=I[0]:I)?R&&0==R[0]||!I?0*O:O/0:NaN);for(f?(m=1,l=r.e-i.e):(f=N,m=7,l=w(r.e/m)-w(i.e/m)),B=I.length,M=R.length,y=(g=new T(O)).d=[],p=0;I[p]==(R[p]||0);p++);if(I[p]>(R[p]||0)&&l--,null==u?(A=u=T.precision,a=T.rounding):A=s?u+(r.e-i.e)+1:u,A<0)y.push(1),d=!0;else{if(A=A/m+2|0,p=0,1==B){for(h=0,I=I[0],A++;(p<M||h)&&A--;p++)F=h*f+(R[p]||0),y[p]=F/I|0,h=F%I|0;d=h||p<M}else{for((h=f/(I[0]+1)|0)>1&&(I=e(I,h,f),R=e(R,h,f),B=I.length,M=R.length),C=B,x=(E=R.slice(0,B)).length;x<B;)E[x++]=0;(_=I.slice()).unshift(0),S=I[0],I[1]>=f/2&&++S;do{h=0,(c=t(I,E,B,x))<0?(b=E[0],B!=x&&(b=b*f+(E[1]||0)),(h=b/S|0)>1?(h>=f&&(h=f-1),1==(c=t(D=e(I,h,f),E,v=D.length,x=E.length))&&(h--,n(D,B<v?_:I,v,f))):(0==h&&(c=h=1),D=I.slice()),(v=D.length)<x&&D.unshift(0),n(E,D,x,f),-1==c&&(c=t(I,E,B,x=E.length))<1&&(h++,n(E,B<x?_:I,x,f)),x=E.length):0===c&&(h++,E=[0]),y[p++]=h,c&&E[0]?E[x++]=R[C]||0:(E=[R[C]],x=1)}while((C++<M||void 0!==E[0])&&A--);d=void 0!==E[0]}y[0]||y.shift()}if(1==m)g.e=l,o=d;else{for(p=1,h=y[0];h>=10;h/=10)p++;g.e=p+l*m-1,z(g,s?u+g.e+1:u,a,d)}return g}}();function z(e,t,n,r){var i,u,a,o,s,f,c,l,p,h=e.constructor;e:if(null!=t){if(!(l=e.d))return e;for(i=1,o=l[0];o>=10;o/=10)i++;if((u=t-i)<0)u+=7,a=t,s=(c=l[p=0])/x(10,i-a-1)%10|0;else if((p=Math.ceil((u+1)/7))>=(o=l.length)){if(!r)break e;for(;o++<=p;)l.push(0);c=s=0,i=1,a=(u%=7)-7+1}else{for(c=o=l[p],i=1;o>=10;o/=10)i++;s=(a=(u%=7)-7+i)<0?0:c/x(10,i-a-1)%10|0}if(r=r||t<0||void 0!==l[p+1]||(a<0?c:c%x(10,i-a-1)),f=n<4?(s||r)&&(0==n||n==(e.s<0?3:2)):s>5||5==s&&(4==n||r||6==n&&(u>0?a>0?c/x(10,i-a):0:l[p-1])%10&1||n==(e.s<0?8:7)),t<1||!l[0])return l.length=0,f?(t-=e.e+1,l[0]=x(10,(7-t%7)%7),e.e=-t||0):l[0]=e.e=0,e;if(0==u?(l.length=p,o=1,p--):(l.length=p+1,o=x(10,7-u),l[p]=a>0?(c/x(10,i-a)%x(10,a)|0)*o:0),f)for(;;){if(0==p){for(u=1,a=l[0];a>=10;a/=10)u++;for(a=l[0]+=o,o=1;a>=10;a/=10)o++;u!=o&&(e.e++,l[0]==N&&(l[0]=1));break}if(l[p]+=o,l[p]!=N)break;l[p--]=0,o=1}for(u=l.length;0===l[--u];)l.pop()}return d&&(e.e>h.maxE?(e.d=null,e.e=NaN):e.e<h.minE&&(e.e=0,e.d=[0])),e}function U(e,t,n){if(!e.isFinite())return W(e);var r,i=e.e,u=_(e.d),a=u.length;return t?(n&&(r=n-a)>0?u=u.charAt(0)+"."+u.slice(1)+j(r):a>1&&(u=u.charAt(0)+"."+u.slice(1)),u=u+(e.e<0?"e":"e+")+e.e):i<0?(u="0."+j(-i-1)+u,n&&(r=n-a)>0&&(u+=j(r))):i>=a?(u+=j(i+1-a),n&&(r=n-i-1)>0&&(u=u+"."+j(r))):((r=i+1)<a&&(u=u.slice(0,r)+"."+u.slice(r)),n&&(r=n-a)>0&&(i+1===a&&(u+="."),u+=j(r))),u}function P(e,t){var n=e[0];for(t*=7;n>=10;n/=10)t++;return t}function L(e,t,n){if(t>M)throw d=!0,n&&(e.precision=n),Error(g);return z(new e(p),t,1,!0)}function k(e,t,n){if(t>S)throw Error(g);return z(new e(h),t,n,!0)}function q(e){var t=e.length-1,n=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)n--;for(t=e[0];t>=10;t/=10)n++}return n}function j(e){for(var t="";e--;)t+="0";return t}function H(e,t,n,r){var i,u=new e(1),a=Math.ceil(r/7+4);for(d=!1;;){if(n%2&&te((u=u.times(t)).d,a)&&(i=!0),0===(n=w(n/2))){n=u.d.length-1,i&&0===u.d[n]&&++u.d[n];break}te((t=t.times(t)).d,a)}return d=!0,u}function G(e){return 1&e.d[e.d.length-1]}function V(e,t,n){for(var r,i,u=new e(t[0]),a=0;++a<t.length;){if(!(i=new e(t[a])).s){u=i;break}((r=u.cmp(i))===n||0===r&&u.s===n)&&(u=i)}return u}function Y(e,t){var n,r,i,u,a,o,s,f=0,c=0,l=0,p=e.constructor,h=p.rounding,m=p.precision;if(!e.d||!e.d[0]||e.e>17)return new p(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(d=!1,s=m):s=t,o=new p(.03125);e.e>-2;)e=e.times(o),l+=5;for(s+=r=Math.log(x(2,l))/Math.LN10*2+5|0,n=u=a=new p(1),p.precision=s;;){if(u=z(u.times(e),s,1),n=n.times(++c),_((o=a.plus(I(u,n,s,1))).d).slice(0,s)===_(a.d).slice(0,s)){for(i=l;i--;)a=z(a.times(a),s,1);if(null!=t)return p.precision=m,a;if(!(f<3&&O(a.d,s-r,h,f)))return z(a,p.precision=m,h,d=!0);p.precision=s+=10,n=u=o=new p(1),c=0,f++}a=o}}function Z(e,t){var n,r,i,u,a,o,s,f,c,l,p,h=1,m=e,D=m.d,v=m.constructor,g=v.rounding,y=v.precision;if(m.s<0||!D||!D[0]||!m.e&&1==D[0]&&1==D.length)return new v(D&&!D[0]?-1/0:1!=m.s?NaN:D?0:m);if(null==t?(d=!1,c=y):c=t,v.precision=c+=10,r=(n=_(D)).charAt(0),!(Math.abs(u=m.e)<15e14))return f=L(v,c+2,y).times(u+""),m=Z(new v(r+"."+n.slice(1)),c-10).plus(f),v.precision=y,null==t?z(m,y,g,d=!0):m;for(;r<7&&1!=r||1==r&&n.charAt(1)>3;)r=(n=_((m=m.times(e)).d)).charAt(0),h++;for(u=m.e,r>1?(m=new v("0."+n),u++):m=new v(r+"."+n.slice(1)),l=m,s=a=m=I(m.minus(1),m.plus(1),c,1),p=z(m.times(m),c,1),i=3;;){if(a=z(a.times(p),c,1),_((f=s.plus(I(a,new v(i),c,1))).d).slice(0,c)===_(s.d).slice(0,c)){if(s=s.times(2),0!==u&&(s=s.plus(L(v,c+2,y).times(u+""))),s=I(s,new v(h),c,1),null!=t)return v.precision=y,s;if(!O(s.d,c-10,g,o))return z(s,v.precision=y,g,d=!0);v.precision=c+=10,f=a=m=I(l.minus(1),l.plus(1),c,1),p=z(m.times(m),c,1),i=o=1}s=f,i+=2}}function W(e){return String(e.s*e.s/0)}function $(e,t){var n,r,i;for((n=t.indexOf("."))>-1&&(t=t.replace(".","")),(r=t.search(/e/i))>0?(n<0&&(n=r),n+=+t.slice(r+1),t=t.substring(0,r)):n<0&&(n=t.length),r=0;48===t.charCodeAt(r);r++);for(i=t.length;48===t.charCodeAt(i-1);--i);if(t=t.slice(r,i)){if(i-=r,e.e=n=n-r-1,e.d=[],r=(n+1)%7,n<0&&(r+=7),r<i){for(r&&e.d.push(+t.slice(0,r)),i-=7;r<i;)e.d.push(+t.slice(r,r+=7));r=7-(t=t.slice(r)).length}else r-=i;for(;r--;)t+="0";e.d.push(+t),d&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function J(e,t){var n,r,i,u,a,o,s,f,c;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),C.test(t))return $(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(A.test(t))n=16,t=t.toLowerCase();else if(b.test(t))n=2;else{if(!F.test(t))throw Error(v+t);n=8}for((u=t.search(/p/i))>0?(s=+t.slice(u+1),t=t.substring(2,u)):t=t.slice(2),a=(u=t.indexOf("."))>=0,r=e.constructor,a&&(u=(o=(t=t.replace(".","")).length)-u,i=H(r,new r(n),u,2*u)),u=c=(f=R(t,n,N)).length-1;0===f[u];--u)f.pop();return u<0?new r(0*e.s):(e.e=P(f,c),e.d=f,d=!1,a&&(e=I(e,i,4*o)),s&&(e=e.times(Math.abs(s)<54?x(2,s):je.pow(2,s))),d=!0,e)}function X(e,t,n,r,i){var u,a,o,s,f=e.precision,c=Math.ceil(f/7);for(d=!1,s=n.times(n),o=new e(r);;){if(a=I(o.times(s),new e(t++*t++),f,1),o=i?r.plus(a):r.minus(a),r=I(a.times(s),new e(t++*t++),f,1),void 0!==(a=o.plus(r)).d[c]){for(u=c;a.d[u]===o.d[u]&&u--;);if(-1==u)break}u=o,o=r,r=a,a=u}return d=!0,a.d.length=c+1,a}function Q(e,t){for(var n=e;--t;)n*=e;return n}function K(e,t){var n,r=t.s<0,i=k(e,e.precision,1),u=i.times(.5);if((t=t.abs()).lte(u))return s=r?4:1,t;if((n=t.divToInt(i)).isZero())s=r?3:2;else{if((t=t.minus(n.times(i))).lte(u))return s=G(n)?r?2:3:r?4:1,t;s=G(n)?r?1:4:r?3:2}return t.minus(i).abs()}function ee(e,t,n,r){var i,u,a,s,f,p,h,m,d,D=e.constructor,v=void 0!==n;if(v?(T(n,1,c),void 0===r?r=D.rounding:T(r,0,8)):(n=D.precision,r=D.rounding),e.isFinite()){for(v?(i=2,16==t?n=4*n-3:8==t&&(n=3*n-2)):i=t,(a=(h=U(e)).indexOf("."))>=0&&(h=h.replace(".",""),(d=new D(1)).e=h.length-a,d.d=R(U(d),10,i),d.e=d.d.length),u=f=(m=R(h,10,i)).length;0==m[--f];)m.pop();if(m[0]){if(a<0?u--:((e=new D(e)).d=m,e.e=u,m=(e=I(e,d,n,r,0,i)).d,u=e.e,p=o),a=m[n],s=i/2,p=p||void 0!==m[n+1],p=r<4?(void 0!==a||p)&&(0===r||r===(e.s<0?3:2)):a>s||a===s&&(4===r||p||6===r&&1&m[n-1]||r===(e.s<0?8:7)),m.length=n,p)for(;++m[--n]>i-1;)m[n]=0,n||(++u,m.unshift(1));for(f=m.length;!m[f-1];--f);for(a=0,h="";a<f;a++)h+=l.charAt(m[a]);if(v){if(f>1)if(16==t||8==t){for(a=16==t?4:3,--f;f%a;f++)h+="0";for(f=(m=R(h,i,t)).length;!m[f-1];--f);for(a=1,h="1.";a<f;a++)h+=l.charAt(m[a])}else h=h.charAt(0)+"."+h.slice(1);h=h+(u<0?"p":"p+")+u}else if(u<0){for(;++u;)h="0"+h;h="0."+h}else if(++u>f)for(u-=f;u--;)h+="0";else u<f&&(h=h.slice(0,u)+"."+h.slice(u))}else h=v?"0p+0":"0";h=(16==t?"0x":2==t?"0b":8==t?"0o":"")+h}else h=W(e);return e.s<0?"-"+h:h}function te(e,t){if(e.length>t)return e.length=t,!0}function ne(e){return new this(e).abs()}function re(e){return new this(e).acos()}function ie(e){return new this(e).acosh()}function ue(e,t){return new this(e).plus(t)}function ae(e){return new this(e).asin()}function oe(e){return new this(e).asinh()}function se(e){return new this(e).atan()}function fe(e){return new this(e).atanh()}function ce(e,t){e=new this(e),t=new this(t);var n,r=this.precision,i=this.rounding,u=r+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(n=t.s<0?k(this,r,i):new this(0)).s=e.s:!e.d||t.isZero()?(n=k(this,u,1).times(.5)).s=e.s:t.s<0?(this.precision=u,this.rounding=1,n=this.atan(I(e,t,u,1)),t=k(this,u,1),this.precision=r,this.rounding=i,n=e.s<0?n.minus(t):n.plus(t)):n=this.atan(I(e,t,u,1)):(n=k(this,u,1).times(t.s>0?.25:.75)).s=e.s:n=new this(NaN),n}function le(e){return new this(e).cbrt()}function pe(e){return z(e=new this(e),e.e+1,2)}function he(e,t,n){return new this(e).clamp(t,n)}function me(e){if(!e||"object"!=typeof e)throw Error(D+"Object expected");var t,n,r,i=!0===e.defaults,u=["precision",1,c,"rounding",0,8,"toExpNeg",-f,0,"toExpPos",0,f,"maxE",0,f,"minE",-f,0,"modulo",0,9];for(t=0;t<u.length;t+=3)if(n=u[t],i&&(this[n]=m[n]),void 0!==(r=e[n])){if(!(w(r)===r&&r>=u[t+1]&&r<=u[t+2]))throw Error(v+n+": "+r);this[n]=r}if(n="crypto",i&&(this[n]=m[n]),void 0!==(r=e[n])){if(!0!==r&&!1!==r&&0!==r&&1!==r)throw Error(v+n+": "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw Error(y);this[n]=!0}else this[n]=!1}return this}function de(e){return new this(e).cos()}function De(e){return new this(e).cosh()}function ve(e,t){return new this(e).div(t)}function ge(e){return new this(e).exp()}function ye(e){return z(e=new this(e),e.e+1,3)}function Ee(){var e,t,n=new this(0);for(d=!1,e=0;e<arguments.length;)if((t=new this(arguments[e++])).d)n.d&&(n=n.plus(t.times(t)));else{if(t.s)return d=!0,new this(1/0);n=t}return d=!0,n.sqrt()}function we(e){return e instanceof je||e&&e.toStringTag===E||!1}function xe(e){return new this(e).ln()}function be(e,t){return new this(e).log(t)}function Ae(e){return new this(e).log(2)}function Fe(e){return new this(e).log(10)}function Ce(){return V(this,arguments,-1)}function Ne(){return V(this,arguments,1)}function Me(e,t){return new this(e).mod(t)}function Se(e,t){return new this(e).mul(t)}function Be(e,t){return new this(e).pow(t)}function _e(e){var t,n,r,i,u=0,a=new this(1),o=[];if(void 0===e?e=this.precision:T(e,1,c),r=Math.ceil(e/7),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(r));u<r;)(i=t[u])>=429e7?t[u]=crypto.getRandomValues(new Uint32Array(1))[0]:o[u++]=i%1e7;else{if(!crypto.randomBytes)throw Error(y);for(t=crypto.randomBytes(r*=4);u<r;)(i=t[u]+(t[u+1]<<8)+(t[u+2]<<16)+((127&t[u+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,u):(o.push(i%1e7),u+=4);u=r/4}else for(;u<r;)o[u++]=1e7*Math.random()|0;for(e%=7,(r=o[--u])&&e&&(i=x(10,7-e),o[u]=(r/i|0)*i);0===o[u];u--)o.pop();if(u<0)n=0,o=[0];else{for(n=-1;0===o[0];n-=7)o.shift();for(r=1,i=o[0];i>=10;i/=10)r++;r<7&&(n-=7-r)}return a.e=n,a.d=o,a}function Te(e){return z(e=new this(e),e.e+1,this.rounding)}function Oe(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function Re(e){return new this(e).sin()}function Ie(e){return new this(e).sinh()}function ze(e){return new this(e).sqrt()}function Ue(e,t){return new this(e).sub(t)}function Pe(){var e=0,t=arguments,n=new this(t[e]);for(d=!1;n.s&&++e<t.length;)n=n.plus(t[e]);return d=!0,z(n,this.precision,this.rounding)}function Le(e){return new this(e).tan()}function ke(e){return new this(e).tanh()}function qe(e){return z(e=new this(e),e.e+1,1)}B[Symbol.for("nodejs.util.inspect.custom")]=B.toString,B[Symbol.toStringTag]="Decimal";var je=B.constructor=function e(t){var n,r,i;function u(e){var t,n,r,i=this;if(!(i instanceof u))return new u(e);if(i.constructor=u,we(e))return i.s=e.s,void(d?!e.d||e.e>u.maxE?(i.e=NaN,i.d=null):e.e<u.minE?(i.e=0,i.d=[0]):(i.e=e.e,i.d=e.d.slice()):(i.e=e.e,i.d=e.d?e.d.slice():e.d));if("number"===(r=typeof e)){if(0===e)return i.s=1/e<0?-1:1,i.e=0,void(i.d=[0]);if(e<0?(e=-e,i.s=-1):i.s=1,e===~~e&&e<1e7){for(t=0,n=e;n>=10;n/=10)t++;return void(d?t>u.maxE?(i.e=NaN,i.d=null):t<u.minE?(i.e=0,i.d=[0]):(i.e=t,i.d=[e]):(i.e=t,i.d=[e]))}return 0*e!=0?(e||(i.s=NaN),i.e=NaN,void(i.d=null)):$(i,e.toString())}if("string"===r)return 45===(n=e.charCodeAt(0))?(e=e.slice(1),i.s=-1):(43===n&&(e=e.slice(1)),i.s=1),C.test(e)?$(i,e):J(i,e);if("bigint"===r)return e<0?(e=-e,i.s=-1):i.s=1,$(i,e.toString());throw Error(v+e)}if(u.prototype=B,u.ROUND_UP=0,u.ROUND_DOWN=1,u.ROUND_CEIL=2,u.ROUND_FLOOR=3,u.ROUND_HALF_UP=4,u.ROUND_HALF_DOWN=5,u.ROUND_HALF_EVEN=6,u.ROUND_HALF_CEIL=7,u.ROUND_HALF_FLOOR=8,u.EUCLID=9,u.config=u.set=me,u.clone=e,u.isDecimal=we,u.abs=ne,u.acos=re,u.acosh=ie,u.add=ue,u.asin=ae,u.asinh=oe,u.atan=se,u.atanh=fe,u.atan2=ce,u.cbrt=le,u.ceil=pe,u.clamp=he,u.cos=de,u.cosh=De,u.div=ve,u.exp=ge,u.floor=ye,u.hypot=Ee,u.ln=xe,u.log=be,u.log10=Fe,u.log2=Ae,u.max=Ce,u.min=Ne,u.mod=Me,u.mul=Se,u.pow=Be,u.random=_e,u.round=Te,u.sign=Oe,u.sin=Re,u.sinh=Ie,u.sqrt=ze,u.sub=Ue,u.sum=Pe,u.tan=Le,u.tanh=ke,u.trunc=qe,void 0===t&&(t={}),t&&!0!==t.defaults)for(i=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],n=0;n<i.length;)t.hasOwnProperty(r=i[n++])||(t[r]=this[r]);return u.config(t),u}(m);p=new je(p),h=new je(h);const He=je;function Ge(e){return"number"==typeof e}function Ve(e){return!(!e||"object"!=typeof e||"function"!=typeof e.constructor)&&(!0===e.isBigNumber&&"object"==typeof e.constructor.prototype&&!0===e.constructor.prototype.isBigNumber||"function"==typeof e.constructor.isDecimal&&!0===e.constructor.isDecimal(e))}function Ye(e){return e&&"object"==typeof e&&!0===Object.getPrototypeOf(e).isComplex||!1}function Ze(e){return e&&"object"==typeof e&&!0===Object.getPrototypeOf(e).isFraction||!1}function We(e){return e&&!0===e.constructor.prototype.isUnit||!1}function $e(e){return"string"==typeof e}var Je=Array.isArray;function Xe(e){return e&&!0===e.constructor.prototype.isMatrix||!1}function Qe(e){return Array.isArray(e)||Xe(e)}function Ke(e){return e&&e.isDenseMatrix&&!0===e.constructor.prototype.isMatrix||!1}function et(e){return e&&e.isSparseMatrix&&!0===e.constructor.prototype.isMatrix||!1}function tt(e){return e&&!0===e.constructor.prototype.isRange||!1}function nt(e){return e&&!0===e.constructor.prototype.isIndex||!1}function rt(e){return"boolean"==typeof e}function it(e){return e&&!0===e.constructor.prototype.isResultSet||!1}function ut(e){return e&&!0===e.constructor.prototype.isHelp||!1}function at(e){return"function"==typeof e}function ot(e){return e instanceof Date}function st(e){return e instanceof RegExp}function ft(e){return!(!e||"object"!=typeof e||e.constructor!==Object||Ye(e)||Ze(e))}function ct(e){return null===e}function lt(e){return void 0===e}function pt(e){return e&&!0===e.isAccessorNode&&!0===e.constructor.prototype.isNode||!1}function ht(e){return e&&!0===e.isArrayNode&&!0===e.constructor.prototype.isNode||!1}function mt(e){return e&&!0===e.isAssignmentNode&&!0===e.constructor.prototype.isNode||!1}function dt(e){return e&&!0===e.isBlockNode&&!0===e.constructor.prototype.isNode||!1}function Dt(e){return e&&!0===e.isConditionalNode&&!0===e.constructor.prototype.isNode||!1}function vt(e){return e&&!0===e.isConstantNode&&!0===e.constructor.prototype.isNode||!1}function gt(e){return e&&!0===e.isFunctionAssignmentNode&&!0===e.constructor.prototype.isNode||!1}function yt(e){return e&&!0===e.isFunctionNode&&!0===e.constructor.prototype.isNode||!1}function Et(e){return e&&!0===e.isIndexNode&&!0===e.constructor.prototype.isNode||!1}function wt(e){return e&&!0===e.isNode&&!0===e.constructor.prototype.isNode||!1}function xt(e){return e&&!0===e.isObjectNode&&!0===e.constructor.prototype.isNode||!1}function bt(e){return e&&!0===e.isOperatorNode&&!0===e.constructor.prototype.isNode||!1}function At(e){return e&&!0===e.isParenthesisNode&&!0===e.constructor.prototype.isNode||!1}function Ft(e){return e&&!0===e.isRangeNode&&!0===e.constructor.prototype.isNode||!1}function Ct(e){return e&&!0===e.isRelationalNode&&!0===e.constructor.prototype.isNode||!1}function Nt(e){return e&&!0===e.isSymbolNode&&!0===e.constructor.prototype.isNode||!1}function Mt(e){return e&&!0===e.constructor.prototype.isChain||!1}function St(e){var t=typeof e;return"object"===t?null===e?"null":Ve(e)?"BigNumber":e.constructor&&e.constructor.name?e.constructor.name:"Object":t}function Bt(e){var t=typeof e;if("number"===t||"string"===t||"boolean"===t||null==e)return e;if("function"==typeof e.clone)return e.clone();if(Array.isArray(e))return e.map((function(e){return Bt(e)}));if(e instanceof Date)return new Date(e.valueOf());if(Ve(e))return e;if(ft(e))return function(e,t){var n={};for(var r in e)Ot(e,r)&&(n[r]=t(e[r]));return n}(e,Bt);throw new TypeError("Cannot clone: unknown type of value (value: ".concat(e,")"))}function _t(e,t){for(var n in t)Ot(t,n)&&(e[n]=t[n]);return e}function Tt(e,t){var n,r,i;if(Array.isArray(e)){if(!Array.isArray(t))return!1;if(e.length!==t.length)return!1;for(r=0,i=e.length;r<i;r++)if(!Tt(e[r],t[r]))return!1;return!0}if("function"==typeof e)return e===t;if(e instanceof Object){if(Array.isArray(t)||!(t instanceof Object))return!1;for(n in e)if(!(n in t)||!Tt(e[n],t[n]))return!1;for(n in t)if(!(n in e))return!1;return!0}return e===t}function Ot(e,t){return e&&Object.hasOwnProperty.call(e,t)}function Rt(e,t,n,r){function i(r){var i=function(e,t){for(var n={},r=0;r<t.length;r++){var i=t[r],u=e[i];void 0!==u&&(n[i]=u)}return n}(r,t.map(It));return function(e,t,n){var r=t.filter((e=>!function(e){return e&&"?"===e[0]}(e))).every((e=>void 0!==n[e]));if(!r){var i=t.filter((e=>void 0===n[e]));throw new Error('Cannot create function "'.concat(e,'", ')+"some dependencies are missing: ".concat(i.map((e=>'"'.concat(e,'"'))).join(", "),"."))}}(e,t,r),n(i)}return i.isFactory=!0,i.fn=e,i.dependencies=t.slice().sort(),r&&(i.meta=r),i}function It(e){return e&&"?"===e[0]?e.slice(1):e}var zt=Rt("BigNumber",["?on","config"],(e=>{var{on:t,config:n}=e,r=He.clone({precision:n.precision,modulo:He.EUCLID});return r.prototype=Object.create(r.prototype),r.prototype.type="BigNumber",r.prototype.isBigNumber=!0,r.prototype.toJSON=function(){return{mathjs:"BigNumber",value:this.toString()}},r.fromJSON=function(e){return new r(e.value)},t&&t("config",(function(e,t){e.precision!==t.precision&&r.config({precision:e.precision})})),r}),{isClass:!0});const Ut=Math.cosh||function(e){return Math.abs(e)<1e-9?1-e:.5*(Math.exp(e)+Math.exp(-e))},Pt=Math.sinh||function(e){return Math.abs(e)<1e-9?e:.5*(Math.exp(e)-Math.exp(-e))},Lt=function(e,t){return(e=Math.abs(e))<(t=Math.abs(t))&&([e,t]=[t,e]),e<1e8?Math.sqrt(e*e+t*t):(t/=e,e*Math.sqrt(1+t*t))},kt=function(){throw SyntaxError("Invalid Param")};function qt(e,t){const n=Math.abs(e),r=Math.abs(t);return 0===e?Math.log(r):0===t?Math.log(n):n<3e3&&r<3e3?.5*Math.log(e*e+t*t):(e*=.5,t*=.5,.5*Math.log(e*e+t*t)+Math.LN2)}const jt={re:0,im:0},Ht=function(e,t){const n=jt;if(null==e)n.re=n.im=0;else if(void 0!==t)n.re=e,n.im=t;else switch(typeof e){case"object":if("im"in e&&"re"in e)n.re=e.re,n.im=e.im;else if("abs"in e&&"arg"in e){if(!isFinite(e.abs)&&isFinite(e.arg))return Gt.INFINITY;n.re=e.abs*Math.cos(e.arg),n.im=e.abs*Math.sin(e.arg)}else if("r"in e&&"phi"in e){if(!isFinite(e.r)&&isFinite(e.phi))return Gt.INFINITY;n.re=e.r*Math.cos(e.phi),n.im=e.r*Math.sin(e.phi)}else 2===e.length?(n.re=e[0],n.im=e[1]):kt();break;case"string":n.im=n.re=0;const t=e.replace(/_/g,"").match(/\d+\.?\d*e[+-]?\d+|\d+\.?\d*|\.\d+|./g);let r=1,i=0;null===t&&kt();for(let e=0;e<t.length;e++){const u=t[e];" "===u||"\t"===u||"\n"===u||("+"===u?r++:"-"===u?i++:"i"===u||"I"===u?(r+i===0&&kt()," "===t[e+1]||isNaN(t[e+1])?n.im+=parseFloat((i%2?"-":"")+"1"):(n.im+=parseFloat((i%2?"-":"")+t[e+1]),e++),r=i=0):((r+i===0||isNaN(u))&&kt(),"i"===t[e+1]||"I"===t[e+1]?(n.im+=parseFloat((i%2?"-":"")+u),e++):n.re+=parseFloat((i%2?"-":"")+u),r=i=0))}r+i>0&&kt();break;case"number":n.im=0,n.re=e;break;default:kt()}return isNaN(n.re)||isNaN(n.im),n};function Gt(e,t){if(!(this instanceof Gt))return new Gt(e,t);const n=Ht(e,t);this.re=n.re,this.im=n.im}function Vt(e){return"boolean"==typeof e||!!isFinite(e)&&e===Math.round(e)}Gt.prototype={re:0,im:0,sign:function(){const e=Lt(this.re,this.im);return new Gt(this.re/e,this.im/e)},add:function(e,t){const n=Ht(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im));return r||i?r&&i?Gt.NAN:Gt.INFINITY:new Gt(this.re+n.re,this.im+n.im)},sub:function(e,t){const n=Ht(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im));return r||i?r&&i?Gt.NAN:Gt.INFINITY:new Gt(this.re-n.re,this.im-n.im)},mul:function(e,t){const n=Ht(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im)),u=0===this.re&&0===this.im,a=0===n.re&&0===n.im;return r&&a||i&&u?Gt.NAN:r||i?Gt.INFINITY:0===n.im&&0===this.im?new Gt(this.re*n.re,0):new Gt(this.re*n.re-this.im*n.im,this.re*n.im+this.im*n.re)},div:function(e,t){const n=Ht(e,t),r=this.isInfinite(),i=!(isFinite(n.re)&&isFinite(n.im)),u=0===this.re&&0===this.im,a=0===n.re&&0===n.im;if(u&&a||r&&i)return Gt.NAN;if(a||r)return Gt.INFINITY;if(u||i)return Gt.ZERO;if(0===n.im)return new Gt(this.re/n.re,this.im/n.re);if(Math.abs(n.re)<Math.abs(n.im)){const e=n.re/n.im,t=n.re*e+n.im;return new Gt((this.re*e+this.im)/t,(this.im*e-this.re)/t)}{const e=n.im/n.re,t=n.im*e+n.re;return new Gt((this.re+this.im*e)/t,(this.im-this.re*e)/t)}},pow:function(e,t){const n=Ht(e,t),r=0===this.re&&0===this.im;if(0===n.re&&0===n.im)return Gt.ONE;if(0===n.im){if(0===this.im&&this.re>0)return new Gt(Math.pow(this.re,n.re),0);if(0===this.re)switch((n.re%4+4)%4){case 0:return new Gt(Math.pow(this.im,n.re),0);case 1:return new Gt(0,Math.pow(this.im,n.re));case 2:return new Gt(-Math.pow(this.im,n.re),0);case 3:return new Gt(0,-Math.pow(this.im,n.re))}}if(r&&n.re>0)return Gt.ZERO;const i=Math.atan2(this.im,this.re),u=qt(this.re,this.im);let a=Math.exp(n.re*u-n.im*i),o=n.im*u+n.re*i;return new Gt(a*Math.cos(o),a*Math.sin(o))},sqrt:function(){const e=this.re,t=this.im;if(0===t)return e>=0?new Gt(Math.sqrt(e),0):new Gt(0,Math.sqrt(-e));const n=Lt(e,t);let r=Math.sqrt(.5*(n+Math.abs(e))),i=Math.abs(t)/(2*r);return e>=0?new Gt(r,t<0?-i:i):new Gt(i,t<0?-r:r)},exp:function(){const e=Math.exp(this.re);return 0===this.im?new Gt(e,0):new Gt(e*Math.cos(this.im),e*Math.sin(this.im))},expm1:function(){const e=this.re,t=this.im;return new Gt(Math.expm1(e)*Math.cos(t)+function(e){const t=Math.PI/4;if(-t>e||e>t)return Math.cos(e)-1;const n=e*e;return n*(n*(n*(n*(n*(n*(n*(n/20922789888e3-1/87178291200)+1/479001600)-1/3628800)+1/40320)-1/720)+1/24)-.5)}(t),Math.exp(e)*Math.sin(t))},log:function(){const e=this.re,t=this.im;return 0===t&&e>0?new Gt(Math.log(e),0):new Gt(qt(e,t),Math.atan2(t,e))},abs:function(){return Lt(this.re,this.im)},arg:function(){return Math.atan2(this.im,this.re)},sin:function(){const e=this.re,t=this.im;return new Gt(Math.sin(e)*Ut(t),Math.cos(e)*Pt(t))},cos:function(){const e=this.re,t=this.im;return new Gt(Math.cos(e)*Ut(t),-Math.sin(e)*Pt(t))},tan:function(){const e=2*this.re,t=2*this.im,n=Math.cos(e)+Ut(t);return new Gt(Math.sin(e)/n,Pt(t)/n)},cot:function(){const e=2*this.re,t=2*this.im,n=Math.cos(e)-Ut(t);return new Gt(-Math.sin(e)/n,Pt(t)/n)},sec:function(){const e=this.re,t=this.im,n=.5*Ut(2*t)+.5*Math.cos(2*e);return new Gt(Math.cos(e)*Ut(t)/n,Math.sin(e)*Pt(t)/n)},csc:function(){const e=this.re,t=this.im,n=.5*Ut(2*t)-.5*Math.cos(2*e);return new Gt(Math.sin(e)*Ut(t)/n,-Math.cos(e)*Pt(t)/n)},asin:function(){const e=this.re,t=this.im,n=new Gt(t*t-e*e+1,-2*e*t).sqrt(),r=new Gt(n.re-t,n.im+e).log();return new Gt(r.im,-r.re)},acos:function(){const e=this.re,t=this.im,n=new Gt(t*t-e*e+1,-2*e*t).sqrt(),r=new Gt(n.re-t,n.im+e).log();return new Gt(Math.PI/2-r.im,r.re)},atan:function(){const e=this.re,t=this.im;if(0===e){if(1===t)return new Gt(0,1/0);if(-1===t)return new Gt(0,-1/0)}const n=e*e+(1-t)*(1-t),r=new Gt((1-t*t-e*e)/n,-2*e/n).log();return new Gt(-.5*r.im,.5*r.re)},acot:function(){const e=this.re,t=this.im;if(0===t)return new Gt(Math.atan2(1,e),0);const n=e*e+t*t;return 0!==n?new Gt(e/n,-t/n).atan():new Gt(0!==e?e/0:0,0!==t?-t/0:0).atan()},asec:function(){const e=this.re,t=this.im;if(0===e&&0===t)return new Gt(0,1/0);const n=e*e+t*t;return 0!==n?new Gt(e/n,-t/n).acos():new Gt(0!==e?e/0:0,0!==t?-t/0:0).acos()},acsc:function(){const e=this.re,t=this.im;if(0===e&&0===t)return new Gt(Math.PI/2,1/0);const n=e*e+t*t;return 0!==n?new Gt(e/n,-t/n).asin():new Gt(0!==e?e/0:0,0!==t?-t/0:0).asin()},sinh:function(){const e=this.re,t=this.im;return new Gt(Pt(e)*Math.cos(t),Ut(e)*Math.sin(t))},cosh:function(){const e=this.re,t=this.im;return new Gt(Ut(e)*Math.cos(t),Pt(e)*Math.sin(t))},tanh:function(){const e=2*this.re,t=2*this.im,n=Ut(e)+Math.cos(t);return new Gt(Pt(e)/n,Math.sin(t)/n)},coth:function(){const e=2*this.re,t=2*this.im,n=Ut(e)-Math.cos(t);return new Gt(Pt(e)/n,-Math.sin(t)/n)},csch:function(){const e=this.re,t=this.im,n=Math.cos(2*t)-Ut(2*e);return new Gt(-2*Pt(e)*Math.cos(t)/n,2*Ut(e)*Math.sin(t)/n)},sech:function(){const e=this.re,t=this.im,n=Math.cos(2*t)+Ut(2*e);return new Gt(2*Ut(e)*Math.cos(t)/n,-2*Pt(e)*Math.sin(t)/n)},asinh:function(){let e=this.im;this.im=-this.re,this.re=e;const t=this.asin();return this.re=-this.im,this.im=e,e=t.re,t.re=-t.im,t.im=e,t},acosh:function(){const e=this.acos();if(e.im<=0){const t=e.re;e.re=-e.im,e.im=t}else{const t=e.im;e.im=-e.re,e.re=t}return e},atanh:function(){const e=this.re,t=this.im,n=e>1&&0===t,r=1-e,i=1+e,u=r*r+t*t,a=0!==u?new Gt((i*r-t*t)/u,(t*r+i*t)/u):new Gt(-1!==e?e/0:0,0!==t?t/0:0),o=a.re;return a.re=qt(a.re,a.im)/2,a.im=Math.atan2(a.im,o)/2,n&&(a.im=-a.im),a},acoth:function(){const e=this.re,t=this.im;if(0===e&&0===t)return new Gt(0,Math.PI/2);const n=e*e+t*t;return 0!==n?new Gt(e/n,-t/n).atanh():new Gt(0!==e?e/0:0,0!==t?-t/0:0).atanh()},acsch:function(){const e=this.re,t=this.im;if(0===t)return new Gt(0!==e?Math.log(e+Math.sqrt(e*e+1)):1/0,0);const n=e*e+t*t;return 0!==n?new Gt(e/n,-t/n).asinh():new Gt(0!==e?e/0:0,0!==t?-t/0:0).asinh()},asech:function(){const e=this.re,t=this.im;if(this.isZero())return Gt.INFINITY;const n=e*e+t*t;return 0!==n?new Gt(e/n,-t/n).acosh():new Gt(0!==e?e/0:0,0!==t?-t/0:0).acosh()},inverse:function(){if(this.isZero())return Gt.INFINITY;if(this.isInfinite())return Gt.ZERO;const e=this.re,t=this.im,n=e*e+t*t;return new Gt(e/n,-t/n)},conjugate:function(){return new Gt(this.re,-this.im)},neg:function(){return new Gt(-this.re,-this.im)},ceil:function(e){return e=Math.pow(10,e||0),new Gt(Math.ceil(this.re*e)/e,Math.ceil(this.im*e)/e)},floor:function(e){return e=Math.pow(10,e||0),new Gt(Math.floor(this.re*e)/e,Math.floor(this.im*e)/e)},round:function(e){return e=Math.pow(10,e||0),new Gt(Math.round(this.re*e)/e,Math.round(this.im*e)/e)},equals:function(e,t){const n=Ht(e,t);return Math.abs(n.re-this.re)<=Gt.EPSILON&&Math.abs(n.im-this.im)<=Gt.EPSILON},clone:function(){return new Gt(this.re,this.im)},toString:function(){let e=this.re,t=this.im,n="";return this.isNaN()?"NaN":this.isInfinite()?"Infinity":(Math.abs(e)<Gt.EPSILON&&(e=0),Math.abs(t)<Gt.EPSILON&&(t=0),0===t?n+e:(0!==e?(n+=e,n+=" ",t<0?(t=-t,n+="-"):n+="+",n+=" "):t<0&&(t=-t,n+="-"),1!==t&&(n+=t),n+"i"))},toVector:function(){return[this.re,this.im]},valueOf:function(){return 0===this.im?this.re:null},isNaN:function(){return isNaN(this.re)||isNaN(this.im)},isZero:function(){return 0===this.im&&0===this.re},isFinite:function(){return isFinite(this.re)&&isFinite(this.im)},isInfinite:function(){return!this.isFinite()}},Gt.ZERO=new Gt(0,0),Gt.ONE=new Gt(1,0),Gt.I=new Gt(0,1),Gt.PI=new Gt(Math.PI,0),Gt.E=new Gt(Math.E,0),Gt.INFINITY=new Gt(1/0,1/0),Gt.NAN=new Gt(NaN,NaN),Gt.EPSILON=1e-15;var Yt=Math.sign||function(e){return e>0?1:e<0?-1:0},Zt=Math.log2||function(e){return Math.log(e)/Math.LN2},Wt=Math.log10||function(e){return Math.log(e)/Math.LN10},$t=(Math.log1p,Math.cbrt||function(e){if(0===e)return e;var t,n=e<0;return n&&(e=-e),t=isFinite(e)?(e/((t=Math.exp(Math.log(e)/3))*t)+2*t)/3:e,n?-t:t}),Jt=Math.expm1||function(e){return e>=2e-4||e<=-2e-4?Math.exp(e)-1:e+e*e/2+e*e*e/6};function Xt(e,t,n){var r={2:"0b",8:"0o",16:"0x"}[t],i="";if(n){if(n<1)throw new Error("size must be in greater than 0");if(!Vt(n))throw new Error("size must be an integer");if(e>2**(n-1)-1||e<-(2**(n-1)))throw new Error("Value must be in range [-2^".concat(n-1,", 2^").concat(n-1,"-1]"));if(!Vt(e))throw new Error("Value must be an integer");e<0&&(e+=2**n),i="i".concat(n)}var u="";return e<0&&(e=-e,u="-"),"".concat(u).concat(r).concat(e.toString(t)).concat(i)}function Qt(e,t){if("function"==typeof t)return t(e);if(e===1/0)return"Infinity";if(e===-1/0)return"-Infinity";if(isNaN(e))return"NaN";var{notation:n,precision:r,wordSize:i}=Kt(t);switch(n){case"fixed":return tn(e,r);case"exponential":return nn(e,r);case"engineering":return function(e,t){if(isNaN(e)||!isFinite(e))return String(e);var n=en(e),r=rn(n,t),i=r.exponent,u=r.coefficients,a=i%3==0?i:i<0?i-3-i%3:i-i%3;if(Ge(t))for(;t>u.length||i-a+1>u.length;)u.push(0);else for(var o=Math.abs(i-a)-(u.length-1),s=0;s<o;s++)u.push(0);var f=Math.abs(i-a),c=1;for(;f>0;)c++,f--;var l=u.slice(c).join(""),p=Ge(t)&&l.length||l.match(/[1-9]/)?"."+l:"",h=u.slice(0,c).join("")+p+"e"+(i>=0?"+":"")+a.toString();return r.sign+h}(e,r);case"bin":return Xt(e,2,i);case"oct":return Xt(e,8,i);case"hex":return Xt(e,16,i);case"auto":return function(e,t,n){if(isNaN(e)||!isFinite(e))return String(e);var r=hn(null==n?void 0:n.lowerExp,-3),i=hn(null==n?void 0:n.upperExp,5),u=en(e),a=t?rn(u,t):u;if(a.exponent<r||a.exponent>=i)return nn(e,t);var o=a.coefficients,s=a.exponent;o.length<t&&(o=o.concat(un(t-o.length))),o=o.concat(un(s-o.length+1+(o.length<t?t-o.length:0)));var f=s>0?s:0;return f<(o=un(-s).concat(o)).length-1&&o.splice(f+1,0,"."),a.sign+o.join("")}(e,r,t).replace(/((\.\d*?)(0+))($|e)/,(function(){var e=arguments[2],t=arguments[4];return"."!==e?e+t:t}));default:throw new Error('Unknown notation "'+n+'". Choose "auto", "exponential", "fixed", "bin", "oct", or "hex.')}}function Kt(e){var t,n,r="auto";if(void 0!==e)if(Ge(e))t=e;else if(Ve(e))t=e.toNumber();else{if(!ft(e))throw new Error("Unsupported type of options, number, BigNumber, or object expected");void 0!==e.precision&&(t=pn(e.precision,(()=>{throw new Error('Option "precision" must be a number or BigNumber')}))),void 0!==e.wordSize&&(n=pn(e.wordSize,(()=>{throw new Error('Option "wordSize" must be a number or BigNumber')}))),e.notation&&(r=e.notation)}return{notation:r,precision:t,wordSize:n}}function en(e){var t=String(e).toLowerCase().match(/^(-?)(\d+\.?\d*)(e([+-]?\d+))?$/);if(!t)throw new SyntaxError("Invalid number "+e);var n=t[1],r=t[2],i=parseFloat(t[4]||"0"),u=r.indexOf(".");i+=-1!==u?u-1:r.length-1;var a=r.replace(".","").replace(/^0*/,(function(e){return i-=e.length,""})).replace(/0*$/,"").split("").map((function(e){return parseInt(e)}));return 0===a.length&&(a.push(0),i++),{sign:n,coefficients:a,exponent:i}}function tn(e,t){if(isNaN(e)||!isFinite(e))return String(e);var n=en(e),r="number"==typeof t?rn(n,n.exponent+1+t):n,i=r.coefficients,u=r.exponent+1,a=u+(t||0);return i.length<a&&(i=i.concat(un(a-i.length))),u<0&&(i=un(1-u).concat(i),u=1),u<i.length&&i.splice(u,0,0===u?"0.":"."),r.sign+i.join("")}function nn(e,t){if(isNaN(e)||!isFinite(e))return String(e);var n=en(e),r=t?rn(n,t):n,i=r.coefficients,u=r.exponent;i.length<t&&(i=i.concat(un(t-i.length)));var a=i.shift();return r.sign+a+(i.length>0?"."+i.join(""):"")+"e"+(u>=0?"+":"")+u}function rn(e,t){for(var n={sign:e.sign,coefficients:e.coefficients,exponent:e.exponent},r=n.coefficients;t<=0;)r.unshift(0),n.exponent++,t++;if(r.length>t&&r.splice(t,r.length-t)[0]>=5){var i=t-1;for(r[i]++;10===r[i];)r.pop(),0===i&&(r.unshift(0),n.exponent++,i++),r[--i]++}return n}function un(e){for(var t=[],n=0;n<e;n++)t.push(0);return t}var an=Number.EPSILON||2220446049250313e-31;function on(e,t,n){if(null==n)return e===t;if(e===t)return!0;if(isNaN(e)||isNaN(t))return!1;if(isFinite(e)&&isFinite(t)){var r=Math.abs(e-t);return r<=an||r<=Math.max(Math.abs(e),Math.abs(t))*n}return!1}var sn=Math.acosh||function(e){return Math.log(Math.sqrt(e*e-1)+e)},fn=Math.asinh||function(e){return Math.log(Math.sqrt(e*e+1)+e)},cn=Math.atanh||function(e){return Math.log((1+e)/(1-e))/2},ln=(Math.cosh,Math.sinh||function(e){return(Math.exp(e)-Math.exp(-e))/2});Math.tanh;function pn(e,t){return Ge(e)?e:Ve(e)?e.toNumber():void t()}function hn(e,t){return Ge(e)?e:Ve(e)?e.toNumber():t}var mn=Rt("Complex",[],(()=>(Object.defineProperty(Gt,"name",{value:"Complex"}),Gt.prototype.constructor=Gt,Gt.prototype.type="Complex",Gt.prototype.isComplex=!0,Gt.prototype.toJSON=function(){return{mathjs:"Complex",re:this.re,im:this.im}},Gt.prototype.toPolar=function(){return{r:this.abs(),phi:this.arg()}},Gt.prototype.format=function(e){var t=this.im,n=this.re,r=Qt(this.re,e),i=Qt(this.im,e),u=Ge(e)?e:e?e.precision:null;if(null!==u){var a=Math.pow(10,-u);Math.abs(n/t)<a&&(n=0),Math.abs(t/n)<a&&(t=0)}return 0===t?r:0===n?1===t?"i":-1===t?"-i":i+"i":t<0?-1===t?r+" - i":r+" - "+i.substring(1)+"i":1===t?r+" + i":r+" + "+i+"i"},Gt.fromPolar=function(e){switch(arguments.length){case 1:var t=arguments[0];if("object"==typeof t)return Gt(t);throw new TypeError("Input has to be an object with r and phi keys.");case 2:var n=arguments[0],r=arguments[1];if(Ge(n)){if(We(r)&&r.hasBase("ANGLE")&&(r=r.toNumber("rad")),Ge(r))return new Gt({r:n,phi:r});throw new TypeError("Phi is not a number nor an angle unit.")}throw new TypeError("Radius r is not a number.");default:throw new SyntaxError("Wrong number of arguments in function fromPolar")}},Gt.prototype.valueOf=Gt.prototype.toString,Gt.fromJSON=function(e){return new Gt(e)},Gt.compare=function(e,t){return e.re>t.re?1:e.re<t.re?-1:e.im>t.im?1:e.im<t.im?-1:0},Gt)),{isClass:!0});function dn(e){var t=0,n=1,r=Object.create(null),i=Object.create(null),u=0,a=function(e){var a=i[e];if(a&&(delete r[a],delete i[e],--t,n===a)){if(!t)return u=0,void(n=1);for(;!Object.prototype.hasOwnProperty.call(r,++n););}};return e=Math.abs(e),{hit:function(o){var s=i[o],f=++u;if(r[f]=o,i[o]=f,!s){if(++t<=e)return;return o=r[n],a(o),o}if(delete r[s],n===s)for(;!Object.prototype.hasOwnProperty.call(r,++n););},delete:a,clear:function(){t=u=0,n=1,r=Object.create(null),i=Object.create(null)}}}function Dn(e){var{hasher:t,limit:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n=null==n?Number.POSITIVE_INFINITY:n,t=null==t?JSON.stringify:t,function r(){"object"!=typeof r.cache&&(r.cache={values:new Map,lru:dn(n||Number.POSITIVE_INFINITY)});for(var i=[],u=0;u<arguments.length;u++)i[u]=arguments[u];var a=t(i);if(r.cache.values.has(a))return r.cache.lru.hit(a),r.cache.values.get(a);var o=e.apply(e,i);return r.cache.values.set(a,o),r.cache.values.delete(r.cache.lru.hit(a)),o}}function vn(e){return Object.keys(e.signatures||{}).reduce((function(e,t){var n=(t.match(/,/g)||[]).length+1;return Math.max(e,n)}),-1)}Dn((function(e){return new e(1).exp()}),{hasher:yn}),Dn((function(e){return new e(1).plus(new e(5).sqrt()).div(2)}),{hasher:yn});var gn=Dn((function(e){return e.acos(-1)}),{hasher:yn});Dn((function(e){return gn(e).times(2)}),{hasher:yn});function yn(e){return e[0].precision}Math.PI,Math.PI,Math.E;En("fineStructure",.0072973525693),En("weakMixingAngle",.2229),En("efimovFactor",22.7),En("sackurTetrode",-1.16487052358);function En(e,t){return Rt(e,["config","BigNumber"],(e=>{var{config:n,BigNumber:r}=e;return"BigNumber"===n.number?new r(t):t}))}var wn=n(1377),xn=Rt("Fraction",[],(()=>(Object.defineProperty(wn,"name",{value:"Fraction"}),wn.prototype.constructor=wn,wn.prototype.type="Fraction",wn.prototype.isFraction=!0,wn.prototype.toJSON=function(){return{mathjs:"Fraction",n:this.s*this.n,d:this.d}},wn.fromJSON=function(e){return new wn(e)},wn)),{isClass:!0}),bn=Rt("Matrix",[],(()=>{function e(){if(!(this instanceof e))throw new SyntaxError("Constructor must be called with the new operator")}return e.prototype.type="Matrix",e.prototype.isMatrix=!0,e.prototype.storage=function(){throw new Error("Cannot invoke storage on a Matrix interface")},e.prototype.datatype=function(){throw new Error("Cannot invoke datatype on a Matrix interface")},e.prototype.create=function(e,t){throw new Error("Cannot invoke create on a Matrix interface")},e.prototype.subset=function(e,t,n){throw new Error("Cannot invoke subset on a Matrix interface")},e.prototype.get=function(e){throw new Error("Cannot invoke get on a Matrix interface")},e.prototype.set=function(e,t,n){throw new Error("Cannot invoke set on a Matrix interface")},e.prototype.resize=function(e,t){throw new Error("Cannot invoke resize on a Matrix interface")},e.prototype.reshape=function(e,t){throw new Error("Cannot invoke reshape on a Matrix interface")},e.prototype.clone=function(){throw new Error("Cannot invoke clone on a Matrix interface")},e.prototype.size=function(){throw new Error("Cannot invoke size on a Matrix interface")},e.prototype.map=function(e,t){throw new Error("Cannot invoke map on a Matrix interface")},e.prototype.forEach=function(e){throw new Error("Cannot invoke forEach on a Matrix interface")},e.prototype[Symbol.iterator]=function(){throw new Error("Cannot iterate a Matrix interface")},e.prototype.toArray=function(){throw new Error("Cannot invoke toArray on a Matrix interface")},e.prototype.valueOf=function(){throw new Error("Cannot invoke valueOf on a Matrix interface")},e.prototype.format=function(e){throw new Error("Cannot invoke format on a Matrix interface")},e.prototype.toString=function(){throw new Error("Cannot invoke toString on a Matrix interface")},e}),{isClass:!0});function An(e,t,n){var r=new(0,e.constructor)(2),i="";if(n){if(n<1)throw new Error("size must be in greater than 0");if(!Vt(n))throw new Error("size must be an integer");if(e.greaterThan(r.pow(n-1).sub(1))||e.lessThan(r.pow(n-1).mul(-1)))throw new Error("Value must be in range [-2^".concat(n-1,", 2^").concat(n-1,"-1]"));if(!e.isInteger())throw new Error("Value must be an integer");e.lessThan(0)&&(e=e.add(r.pow(n))),i="i".concat(n)}switch(t){case 2:return"".concat(e.toBinary()).concat(i);case 8:return"".concat(e.toOctal()).concat(i);case 16:return"".concat(e.toHexadecimal()).concat(i);default:throw new Error("Base ".concat(t," not supported "))}}function Fn(e,t){if("function"==typeof t)return t(e);if(!e.isFinite())return e.isNaN()?"NaN":e.gt(0)?"Infinity":"-Infinity";var{notation:n,precision:r,wordSize:i}=Kt(t);switch(n){case"fixed":return function(e,t){return e.toFixed(t)}(e,r);case"exponential":return Cn(e,r);case"engineering":return function(e,t){var n=e.e,r=n%3==0?n:n<0?n-3-n%3:n-n%3,i=e.mul(Math.pow(10,-r)),u=i.toPrecision(t);if(u.includes("e")){u=new(0,e.constructor)(u).toFixed()}return u+"e"+(n>=0?"+":"")+r.toString()}(e,r);case"bin":return An(e,2,i);case"oct":return An(e,8,i);case"hex":return An(e,16,i);case"auto":var u=Nn(null==t?void 0:t.lowerExp,-3),a=Nn(null==t?void 0:t.upperExp,5);if(e.isZero())return"0";var o=e.toSignificantDigits(r),s=o.e;return(s>=u&&s<a?o.toFixed():Cn(e,r)).replace(/((\.\d*?)(0+))($|e)/,(function(){var e=arguments[2],t=arguments[4];return"."!==e?e+t:t}));default:throw new Error('Unknown notation "'+n+'". Choose "auto", "exponential", "fixed", "bin", "oct", or "hex.')}}function Cn(e,t){return void 0!==t?e.toExponential(t-1):e.toExponential()}function Nn(e,t){return Ge(e)?e:Ve(e)?e.toNumber():t}function Mn(e,t){var n=e.length-t.length,r=e.length;return e.substring(n,r)===t}function Sn(e,t){var n=function(e,t){if("number"==typeof e)return Qt(e,t);if(Ve(e))return Fn(e,t);if(function(e){return e&&"object"==typeof e&&"number"==typeof e.s&&"number"==typeof e.n&&"number"==typeof e.d||!1}(e))return t&&"decimal"===t.fraction?e.toString():e.s*e.n+"/"+e.d;if(Array.isArray(e))return Tn(e,t);if($e(e))return Bn(e);if("function"==typeof e)return e.syntax?String(e.syntax):"function";if(e&&"object"==typeof e){return"function"==typeof e.format?e.format(t):e&&e.toString(t)!=={}.toString()?e.toString(t):"{"+Object.keys(e).map((n=>Bn(n)+": "+Sn(e[n],t))).join(", ")+"}"}return String(e)}(e,t);return t&&"object"==typeof t&&"truncate"in t&&n.length>t.truncate?n.substring(0,t.truncate-3)+"...":n}function Bn(e){for(var t=String(e),n="",r=0;r<t.length;){var i=t.charAt(r);n+=i in _n?_n[i]:i,r++}return'"'+n+'"'}var _n={'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t"};function Tn(e,t){if(Array.isArray(e)){for(var n="[",r=e.length,i=0;i<r;i++)0!==i&&(n+=", "),n+=Tn(e[i],t);return n+="]"}return Sn(e,t)}function On(e,t){if(!$e(e))throw new TypeError("Unexpected type of argument in function compareText (expected: string or Array or Matrix, actual: "+St(e)+", index: 0)");if(!$e(t))throw new TypeError("Unexpected type of argument in function compareText (expected: string or Array or Matrix, actual: "+St(t)+", index: 1)");return e===t?0:e>t?1:-1}function Rn(e,t,n){if(!(this instanceof Rn))throw new SyntaxError("Constructor must be called with the new operator");this.actual=e,this.expected=t,this.relation=n,this.message="Dimension mismatch ("+(Array.isArray(e)?"["+e.join(", ")+"]":e)+" "+(this.relation||"!=")+" "+(Array.isArray(t)?"["+t.join(", ")+"]":t)+")",this.stack=(new Error).stack}function In(e,t,n){if(!(this instanceof In))throw new SyntaxError("Constructor must be called with the new operator");this.index=e,arguments.length<3?(this.min=0,this.max=t):(this.min=t,this.max=n),void 0!==this.min&&this.index<this.min?this.message="Index out of range ("+this.index+" < "+this.min+")":void 0!==this.max&&this.index>=this.max?this.message="Index out of range ("+this.index+" > "+(this.max-1)+")":this.message="Index out of range ("+this.index+")",this.stack=(new Error).stack}function zn(e){for(var t=[];Array.isArray(e);)t.push(e.length),e=e[0];return t}function Un(e,t,n){var r,i=e.length;if(i!==t[n])throw new Rn(i,t[n]);if(n<t.length-1){var u=n+1;for(r=0;r<i;r++){var a=e[r];if(!Array.isArray(a))throw new Rn(t.length-1,t.length,"<");Un(e[r],t,u)}}else for(r=0;r<i;r++)if(Array.isArray(e[r]))throw new Rn(t.length+1,t.length,">")}function Pn(e,t){if(0===t.length){if(Array.isArray(e))throw new Rn(e.length,0)}else Un(e,t,0)}function Ln(e,t){if(void 0!==e){if(!Ge(e)||!Vt(e))throw new TypeError("Index must be an integer (value: "+e+")");if(e<0||"number"==typeof t&&e>=t)throw new In(e,t)}}function kn(e,t,n){if(!Array.isArray(t))throw new TypeError("Array expected");if(0===t.length)throw new Error("Resizing to scalar is not supported");return t.forEach((function(e){if(!Ge(e)||!Vt(e)||e<0)throw new TypeError("Invalid size, must contain positive integers (size: "+Sn(t)+")")})),(Ge(e)||Ve(e))&&(e=[e]),qn(e,t,0,void 0!==n?n:0),e}function qn(e,t,n,r){var i,u,a=e.length,o=t[n],s=Math.min(a,o);if(e.length=o,n<t.length-1){var f=n+1;for(i=0;i<s;i++)u=e[i],Array.isArray(u)||(u=[u],e[i]=u),qn(u,t,f,r);for(i=s;i<o;i++)u=[],e[i]=u,qn(u,t,f,r)}else{for(i=0;i<s;i++)for(;Array.isArray(e[i]);)e[i]=e[i][0];for(i=s;i<o;i++)e[i]=r}}function jn(e,t){var n=Zn(e),r=n.length;if(!Array.isArray(e)||!Array.isArray(t))throw new TypeError("Array expected");if(0===t.length)throw new Rn(0,r,"!=");var i=Gn(t=Hn(t,r));if(r!==i)throw new Rn(i,r,"!=");try{return function(e,t){for(var n,r=e,i=t.length-1;i>0;i--){var u=t[i];n=[];for(var a=r.length/u,o=0;o<a;o++)n.push(r.slice(o*u,(o+1)*u));r=n}return r}(n,t)}catch(e){if(e instanceof Rn)throw new Rn(i,r,"!=");throw e}}function Hn(e,t){var n=Gn(e),r=e.slice(),i=e.indexOf(-1);if(e.indexOf(-1,i+1)>=0)throw new Error("More than one wildcard in sizes");if(i>=0){if(!(t%n==0))throw new Error("Could not replace wildcard, since "+t+" is no multiple of "+-n);r[i]=-t/n}return r}function Gn(e){return e.reduce(((e,t)=>e*t),1)}function Vn(e,t,n,r){var i=r||zn(e);if(n)for(var u=0;u<n;u++)e=[e],i.unshift(1);for(e=Yn(e,t,0);i.length<t;)i.push(1);return e}function Yn(e,t,n){var r,i;if(Array.isArray(e)){var u=n+1;for(r=0,i=e.length;r<i;r++)e[r]=Yn(e[r],t,u)}else for(var a=n;a<t;a++)e=[e];return e}function Zn(e){if(!Array.isArray(e))return e;var t=[];return e.forEach((function e(n){Array.isArray(n)?n.forEach(e):t.push(n)})),t}function Wn(e,t){for(var n,r=0,i=0;i<e.length;i++){var u=e[i],a=Array.isArray(u);if(0===i&&a&&(r=u.length),a&&u.length!==r)return;var o=a?Wn(u,t):t(u);if(void 0===n)n=o;else if(n!==o)return"mixed"}return n}function $n(e,t,n,r){if(r<n){if(e.length!==t.length)throw new Rn(e.length,t.length);for(var i=[],u=0;u<e.length;u++)i[u]=$n(e[u],t[u],n,r+1);return i}return e.concat(t)}function Jn(){var e=Array.prototype.slice.call(arguments,0,-1),t=Array.prototype.slice.call(arguments,-1);if(1===e.length)return e[0];if(e.length>1)return e.slice(1).reduce((function(e,n){return $n(e,n,t,0)}),e[0]);throw new Error("Wrong number of arguments in function concat")}function Xn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var r=t.map((e=>e.length)),i=Math.max(...r),u=new Array(i).fill(null),a=0;a<t.length;a++)for(var o=t[a],s=r[a],f=0;f<s;f++){var c=i-s+f;o[f]>u[c]&&(u[c]=o[f])}for(var l=0;l<t.length;l++)Qn(t[l],u);return u}function Qn(e,t){for(var n=t.length,r=e.length,i=0;i<r;i++){var u=n-r+i;if(e[i]<t[u]&&e[i]>1||e[i]>t[u])throw new Error("shape missmatch: missmatch is found in arg with shape (".concat(e,") not possible to broadcast dimension ").concat(r," with size ").concat(e[i]," to size ").concat(t[u]))}}function Kn(e,n){var r=zn(e);if(Tt(r,n))return e;Qn(r,n);var i,u,a,o=Xn(r,n),s=o.length,f=[...Array(s-r.length).fill(1),...r],c=function(e){return t([],e)}(e);r.length<s&&(r=zn(c=jn(c,f)));for(var l=0;l<s;l++)r[l]<o[l]&&(i=c,u=o[l],a=l,r=zn(c=Jn(...Array(u).fill(i),a)));return c}Rn.prototype=new RangeError,Rn.prototype.constructor=RangeError,Rn.prototype.name="DimensionError",Rn.prototype.isDimensionError=!0,In.prototype=new RangeError,In.prototype.constructor=RangeError,In.prototype.name="IndexError",In.prototype.isIndexError=!0;var er=Rt("DenseMatrix",["Matrix"],(e=>{var{Matrix:t}=e;function n(e,t){if(!(this instanceof n))throw new SyntaxError("Constructor must be called with the new operator");if(t&&!$e(t))throw new Error("Invalid datatype: "+t);if(Xe(e))"DenseMatrix"===e.type?(this._data=Bt(e._data),this._size=Bt(e._size),this._datatype=t||e._datatype):(this._data=e.toArray(),this._size=e.size(),this._datatype=t||e._datatype);else if(e&&Je(e.data)&&Je(e.size))this._data=e.data,this._size=e.size,Pn(this._data,this._size),this._datatype=t||e.datatype;else if(Je(e))this._data=o(e),this._size=zn(this._data),Pn(this._data,this._size),this._datatype=t;else{if(e)throw new TypeError("Unsupported type of data ("+St(e)+")");this._data=[],this._size=[0],this._datatype=t}}function r(e,t,n,i){var u=i===n-1,a=t.dimension(i);return u?a.map((function(t){return Ln(t,e.length),e[t]})).valueOf():a.map((function(u){return Ln(u,e.length),r(e[u],t,n,i+1)})).valueOf()}function i(e,t,n,r,u){var a=u===r-1,o=t.dimension(u);a?o.forEach((function(t,r){Ln(t),e[t]=n[r[0]]})):o.forEach((function(a,o){Ln(a),i(e[a],t,n[o[0]],r,u+1)}))}function u(e,t,n){if(0===t.length){for(var r=e._data;Je(r);)r=r[0];return r}return e._size=t.slice(0),e._data=kn(e._data,e._size,n),e}function a(e,t,n){for(var r=e._size.slice(0),i=!1;r.length<t.length;)r.push(0),i=!0;for(var a=0,o=t.length;a<o;a++)t[a]>r[a]&&(r[a]=t[a],i=!0);i&&u(e,r,n)}function o(e){return Xe(e)?o(e.valueOf()):Je(e)?e.map(o):e}return n.prototype=new t,n.prototype.createDenseMatrix=function(e,t){return new n(e,t)},Object.defineProperty(n,"name",{value:"DenseMatrix"}),n.prototype.constructor=n,n.prototype.type="DenseMatrix",n.prototype.isDenseMatrix=!0,n.prototype.getDataType=function(){return Wn(this._data,St)},n.prototype.storage=function(){return"dense"},n.prototype.datatype=function(){return this._datatype},n.prototype.create=function(e,t){return new n(e,t)},n.prototype.subset=function(e,t,u){switch(arguments.length){case 1:return function(e,t){if(!nt(t))throw new TypeError("Invalid index");var i=t.isScalar();if(i)return e.get(t.min());var u=t.size();if(u.length!==e._size.length)throw new Rn(u.length,e._size.length);for(var a=t.min(),o=t.max(),s=0,f=e._size.length;s<f;s++)Ln(a[s],e._size[s]),Ln(o[s],e._size[s]);return new n(r(e._data,t,u.length,0),e._datatype)}(this,e);case 2:case 3:return function(e,t,n,r){if(!t||!0!==t.isIndex)throw new TypeError("Invalid index");var u,o=t.size(),s=t.isScalar();Xe(n)?(u=n.size(),n=n.valueOf()):u=zn(n);if(s){if(0!==u.length)throw new TypeError("Scalar expected");e.set(t.min(),n,r)}else{if(!Tt(u,o))try{u=zn(n=0===u.length?Kn([n],o):Kn(n,o))}catch(e){}if(o.length<e._size.length)throw new Rn(o.length,e._size.length,"<");if(u.length<o.length){for(var f=0,c=0;1===o[f]&&1===u[f];)f++;for(;1===o[f];)c++,f++;n=Vn(n,o.length,c,u)}if(!Tt(o,u))throw new Rn(o,u,">");var l=t.max().map((function(e){return e+1}));a(e,l,r);var p=o.length,h=0;i(e._data,t,n,p,h)}return e}(this,e,t,u);default:throw new SyntaxError("Wrong number of arguments")}},n.prototype.get=function(e){if(!Je(e))throw new TypeError("Array expected");if(e.length!==this._size.length)throw new Rn(e.length,this._size.length);for(var t=0;t<e.length;t++)Ln(e[t],this._size[t]);for(var n=this._data,r=0,i=e.length;r<i;r++){var u=e[r];Ln(u,n.length),n=n[u]}return n},n.prototype.set=function(e,t,n){if(!Je(e))throw new TypeError("Array expected");if(e.length<this._size.length)throw new Rn(e.length,this._size.length,"<");var r,i,u,o=e.map((function(e){return e+1}));a(this,o,n);var s=this._data;for(r=0,i=e.length-1;r<i;r++)Ln(u=e[r],s.length),s=s[u];return Ln(u=e[e.length-1],s.length),s[u]=t,this},n.prototype.resize=function(e,t,n){if(!Qe(e))throw new TypeError("Array or Matrix expected");var r=e.valueOf().map((e=>Array.isArray(e)&&1===e.length?e[0]:e));return u(n?this.clone():this,r,t)},n.prototype.reshape=function(e,t){var n=t?this.clone():this;n._data=jn(n._data,e);var r=n._size.reduce(((e,t)=>e*t));return n._size=Hn(e,r),n},n.prototype.clone=function(){return new n({data:Bt(this._data),size:Bt(this._size),datatype:this._datatype})},n.prototype.size=function(){return this._size.slice(0)},n.prototype.map=function(e){var t=this,r=vn(e),i=function n(i,u){return Je(i)?i.map((function(e,t){return n(e,u.concat(t))})):1===r?e(i):2===r?e(i,u):e(i,u,t)}(this._data,[]);return new n(i,void 0!==this._datatype?Wn(i,St):void 0)},n.prototype.forEach=function(e){var t=this;!function n(r,i){Je(r)?r.forEach((function(e,t){n(e,i.concat(t))})):e(r,i,t)}(this._data,[])},n.prototype[Symbol.iterator]=function*(){yield*function*e(t,n){if(Je(t))for(var r=0;r<t.length;r++)yield*e(t[r],n.concat(r));else yield{value:t,index:n}}(this._data,[])},n.prototype.rows=function(){var e=[];if(2!==this.size().length)throw new TypeError("Rows can only be returned for a 2D matrix.");var t=this._data;for(var r of t)e.push(new n([r],this._datatype));return e},n.prototype.columns=function(){var e=this,t=[],r=this.size();if(2!==r.length)throw new TypeError("Rows can only be returned for a 2D matrix.");for(var i=this._data,u=function(r){var u=i.map((e=>[e[r]]));t.push(new n(u,e._datatype))},a=0;a<r[1];a++)u(a);return t},n.prototype.toArray=function(){return Bt(this._data)},n.prototype.valueOf=function(){return this._data},n.prototype.format=function(e){return Sn(this._data,e)},n.prototype.toString=function(){return Sn(this._data)},n.prototype.toJSON=function(){return{mathjs:"DenseMatrix",data:this._data,size:this._size,datatype:this._datatype}},n.prototype.diagonal=function(e){if(e){if(Ve(e)&&(e=e.toNumber()),!Ge(e)||!Vt(e))throw new TypeError("The parameter k must be an integer number")}else e=0;for(var t=e>0?e:0,r=e<0?-e:0,i=this._size[0],u=this._size[1],a=Math.min(i-r,u-t),o=[],s=0;s<a;s++)o[s]=this._data[s+r][s+t];return new n({data:o,size:[a],datatype:this._datatype})},n.diagonal=function(e,t,r,i){if(!Je(e))throw new TypeError("Array expected, size parameter");if(2!==e.length)throw new Error("Only two dimensions matrix are supported");if(e=e.map((function(e){if(Ve(e)&&(e=e.toNumber()),!Ge(e)||!Vt(e)||e<1)throw new Error("Size values must be positive integers");return e})),r){if(Ve(r)&&(r=r.toNumber()),!Ge(r)||!Vt(r))throw new TypeError("The parameter k must be an integer number")}else r=0;var u,a=r>0?r:0,o=r<0?-r:0,s=e[0],f=e[1],c=Math.min(s-o,f-a);if(Je(t)){if(t.length!==c)throw new Error("Invalid value array length");u=function(e){return t[e]}}else if(Xe(t)){var l=t.size();if(1!==l.length||l[0]!==c)throw new Error("Invalid matrix length");u=function(e){return t.get([e])}}else u=function(){return t};i||(i=Ve(u(0))?u(0).mul(0):0);var p=[];if(e.length>0){p=kn(p,e,i);for(var h=0;h<c;h++)p[h+o][h+a]=u(h)}return new n({data:p,size:[s,f]})},n.fromJSON=function(e){return new n(e)},n.prototype.swapRows=function(e,t){if(!(Ge(e)&&Vt(e)&&Ge(t)&&Vt(t)))throw new Error("Row index must be positive integers");if(2!==this._size.length)throw new Error("Only two dimensional matrix is supported");return Ln(e,this._size[0]),Ln(t,this._size[0]),n._swapRows(e,t,this._data),this},n._swapRows=function(e,t,n){var r=n[e];n[e]=n[t],n[t]=r},n}),{isClass:!0}),tr=n(2369);function nr(e,t){if(ar(e)&&ir(e,t))return e[t];if("function"==typeof e[t]&&ur(e,t))throw new Error('Cannot access method "'+t+'" as a property');throw new Error('No access to property "'+t+'"')}function rr(e,t,n){if(ar(e)&&ir(e,t))return e[t]=n,n;throw new Error('No access to property "'+t+'"')}function ir(e,t){return!(!e||"object"!=typeof e)&&(!!Ot(or,t)||!(t in Object.prototype)&&!(t in Function.prototype))}function ur(e,t){return null!=e&&"function"==typeof e[t]&&(!(Ot(e,t)&&Object.getPrototypeOf&&t in Object.getPrototypeOf(e))&&(!!Ot(sr,t)||!(t in Object.prototype)&&!(t in Function.prototype)))}function ar(e){return"object"==typeof e&&e&&e.constructor===Object}var or={length:!0,name:!0},sr={toString:!0,valueOf:!0,toLocaleString:!0};class fr{constructor(e){this.wrappedObject=e,this[Symbol.iterator]=this.entries}keys(){return Object.keys(this.wrappedObject).values()}get(e){return nr(this.wrappedObject,e)}set(e,t){return rr(this.wrappedObject,e,t),this}has(e){return t=this.wrappedObject,e in t;var t}entries(){return cr(this.keys(),(e=>[e,this.get(e)]))}forEach(e){for(var t of this.keys())e(this.get(t),t,this)}delete(e){delete this.wrappedObject[e]}clear(){for(var e of this.keys())this.delete(e)}get size(){return Object.keys(this.wrappedObject).length}}function cr(e,t){return{next:()=>{var n=e.next();return n.done?n:{value:t(n.value),done:!1}}}}function lr(e){return!!e&&(e instanceof Map||e instanceof fr||"function"==typeof e.set&&"function"==typeof e.get&&"function"==typeof e.keys&&"function"==typeof e.has)}var pr=function(){return pr=tr.create,tr},hr=Rt("typed",["?BigNumber","?Complex","?DenseMatrix","?Fraction"],(function(e){var{BigNumber:t,Complex:n,DenseMatrix:r,Fraction:i}=e,u=pr();return u.clear(),u.addTypes([{name:"number",test:Ge},{name:"Complex",test:Ye},{name:"BigNumber",test:Ve},{name:"Fraction",test:Ze},{name:"Unit",test:We},{name:"identifier",test:e=>$e&&/^(?:[A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDCD0-\uDCEB\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDCD0-\uDCEB\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])*$/.test(e)},{name:"string",test:$e},{name:"Chain",test:Mt},{name:"Array",test:Je},{name:"Matrix",test:Xe},{name:"DenseMatrix",test:Ke},{name:"SparseMatrix",test:et},{name:"Range",test:tt},{name:"Index",test:nt},{name:"boolean",test:rt},{name:"ResultSet",test:it},{name:"Help",test:ut},{name:"function",test:at},{name:"Date",test:ot},{name:"RegExp",test:st},{name:"null",test:ct},{name:"undefined",test:lt},{name:"AccessorNode",test:pt},{name:"ArrayNode",test:ht},{name:"AssignmentNode",test:mt},{name:"BlockNode",test:dt},{name:"ConditionalNode",test:Dt},{name:"ConstantNode",test:vt},{name:"FunctionNode",test:yt},{name:"FunctionAssignmentNode",test:gt},{name:"IndexNode",test:Et},{name:"Node",test:wt},{name:"ObjectNode",test:xt},{name:"OperatorNode",test:bt},{name:"ParenthesisNode",test:At},{name:"RangeNode",test:Ft},{name:"RelationalNode",test:Ct},{name:"SymbolNode",test:Nt},{name:"Map",test:lr},{name:"Object",test:ft}]),u.addConversions([{from:"number",to:"BigNumber",convert:function(e){if(t||mr(e),e.toExponential().replace(/e.*$/,"").replace(/^0\.?0*|\./,"").length>15)throw new TypeError("Cannot implicitly convert a number with >15 significant digits to BigNumber (value: "+e+"). Use function bignumber(x) to convert to BigNumber.");return new t(e)}},{from:"number",to:"Complex",convert:function(e){return n||dr(e),new n(e,0)}},{from:"BigNumber",to:"Complex",convert:function(e){return n||dr(e),new n(e.toNumber(),0)}},{from:"Fraction",to:"BigNumber",convert:function(e){throw new TypeError("Cannot implicitly convert a Fraction to BigNumber or vice versa. Use function bignumber(x) to convert to BigNumber or fraction(x) to convert to Fraction.")}},{from:"Fraction",to:"Complex",convert:function(e){return n||dr(e),new n(e.valueOf(),0)}},{from:"number",to:"Fraction",convert:function(e){i||Dr(e);var t=new i(e);if(t.valueOf()!==e)throw new TypeError("Cannot implicitly convert a number to a Fraction when there will be a loss of precision (value: "+e+"). Use function fraction(x) to convert to Fraction.");return t}},{from:"string",to:"number",convert:function(e){var t=Number(e);if(isNaN(t))throw new Error('Cannot convert "'+e+'" to a number');return t}},{from:"string",to:"BigNumber",convert:function(e){t||mr(e);try{return new t(e)}catch(t){throw new Error('Cannot convert "'+e+'" to BigNumber')}}},{from:"string",to:"Fraction",convert:function(e){i||Dr(e);try{return new i(e)}catch(t){throw new Error('Cannot convert "'+e+'" to Fraction')}}},{from:"string",to:"Complex",convert:function(e){n||dr(e);try{return new n(e)}catch(t){throw new Error('Cannot convert "'+e+'" to Complex')}}},{from:"boolean",to:"number",convert:function(e){return+e}},{from:"boolean",to:"BigNumber",convert:function(e){return t||mr(e),new t(+e)}},{from:"boolean",to:"Fraction",convert:function(e){return i||Dr(e),new i(+e)}},{from:"boolean",to:"string",convert:function(e){return String(e)}},{from:"Array",to:"Matrix",convert:function(e){return r||function(){throw new Error("Cannot convert array into a Matrix: no class 'DenseMatrix' provided")}(),new r(e)}},{from:"Matrix",to:"Array",convert:function(e){return e.valueOf()}}]),u.onMismatch=(e,t,n)=>{var r=u.createError(e,t,n);if(["wrongType","mismatch"].includes(r.data.category)&&1===t.length&&Qe(t[0])&&n.some((e=>!e.params.includes(",")))){var i=new TypeError("Function '".concat(e,"' doesn't apply to matrices. To call it ")+"elementwise on a matrix 'M', try 'map(M, ".concat(e,")'."));throw i.data=r.data,i}throw r},u.onMismatch=(e,t,n)=>{var r=u.createError(e,t,n);if(["wrongType","mismatch"].includes(r.data.category)&&1===t.length&&Qe(t[0])&&n.some((e=>!e.params.includes(",")))){var i=new TypeError("Function '".concat(e,"' doesn't apply to matrices. To call it ")+"elementwise on a matrix 'M', try 'map(M, ".concat(e,")'."));throw i.data=r.data,i}throw r},u}));function mr(e){throw new Error("Cannot convert value ".concat(e," into a BigNumber: no class 'BigNumber' provided"))}function dr(e){throw new Error("Cannot convert value ".concat(e," into a Complex number: no class 'Complex' provided"))}function Dr(e){throw new Error("Cannot convert value ".concat(e," into a Fraction, no class 'Fraction' provided."))}function vr(e,t,n){return e&&"function"==typeof e.map?e.map((function(e){return vr(e,t,n)})):t(e)}var gr="number",yr="number, number";function Er(e){return Math.abs(e)}function wr(e,t){return e+t}function xr(e,t){return e-t}function br(e,t){return e*t}function Ar(e){return-e}function Fr(e){return e}function Cr(e){return $t(e)}function Nr(e){return e*e*e}function Mr(e){return Math.exp(e)}function Sr(e){return Jt(e)}function Br(e,t){if(!Vt(e)||!Vt(t))throw new Error("Parameters in function lcm must be integer numbers");if(0===e||0===t)return 0;for(var n,r=e*t;0!==t;)t=e%(n=t),e=n;return Math.abs(r/e)}function _r(e){return Wt(e)}function Tr(e){return Zt(e)}function Or(e){return Yt(e)}function Rr(e){return e*e}function Ir(e,t){var n,r,i,u=0,a=1,o=1,s=0;if(!Vt(e)||!Vt(t))throw new Error("Parameters in function xgcd must be integer numbers");for(;t;)i=e-(r=Math.floor(e/t))*t,n=u,u=a-r*u,a=n,n=o,o=s-r*o,s=n,e=t,t=i;return e<0?[-e,-a,-s]:[e,e?a:0,s]}function zr(e,t){return e*e<1&&t===1/0||e*e>1&&t===-1/0?0:Math.pow(e,t)}function Ur(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!Vt(t)||t<0||t>15)throw new Error("Number of decimals in function round must be an integer from 0 to 15 inclusive");return parseFloat(tn(e,t))}Er.signature=gr,wr.signature=yr,xr.signature=yr,br.signature=yr,Ar.signature=gr,Fr.signature=gr,Cr.signature=gr,Nr.signature=gr,Mr.signature=gr,Sr.signature=gr,Br.signature=yr,_r.signature=gr,Tr.signature=gr,Or.signature=gr,Rr.signature=gr,Ir.signature=yr,zr.signature=yr;var Pr=Rt("abs",["typed"],(e=>{var{typed:t}=e;return t("abs",{number:Er,"Complex | BigNumber | Fraction | Unit":e=>e.abs(),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0)))})})),Lr="number";function kr(e){return sn(e)}function qr(e){return Math.atan(1/e)}function jr(e){return isFinite(e)?(Math.log((e+1)/e)+Math.log(e/(e-1)))/2:0}function Hr(e){return Math.asin(1/e)}function Gr(e){var t=1/e;return Math.log(t+Math.sqrt(t*t+1))}function Vr(e){return Math.acos(1/e)}function Yr(e){var t=1/e,n=Math.sqrt(t*t-1);return Math.log(n+t)}function Zr(e){return fn(e)}function Wr(e){return cn(e)}function $r(e){return 1/Math.tan(e)}function Jr(e){var t=Math.exp(2*e);return(t+1)/(t-1)}function Xr(e){return 1/Math.sin(e)}function Qr(e){return 0===e?Number.POSITIVE_INFINITY:Math.abs(2/(Math.exp(e)-Math.exp(-e)))*Yt(e)}function Kr(e){return 1/Math.cos(e)}function ei(e){return 2/(Math.exp(e)+Math.exp(-e))}function ti(e){return ln(e)}kr.signature=Lr,qr.signature=Lr,jr.signature=Lr,Hr.signature=Lr,Gr.signature=Lr,Vr.signature=Lr,Yr.signature=Lr,Zr.signature=Lr,Wr.signature=Lr,$r.signature=Lr,Jr.signature=Lr,Xr.signature=Lr,Qr.signature=Lr,Kr.signature=Lr,ei.signature=Lr,ti.signature=Lr;var ni="addScalar",ri=Rt(ni,["typed"],(e=>{var{typed:t}=e;return t(ni,{"number, number":wr,"Complex, Complex":function(e,t){return e.add(t)},"BigNumber, BigNumber":function(e,t){return e.plus(t)},"Fraction, Fraction":function(e,t){return e.add(t)},"Unit, Unit":t.referToSelf((e=>(n,r)=>{if(null===n.value||void 0===n.value)throw new Error("Parameter x contains a unit with undefined value");if(null===r.value||void 0===r.value)throw new Error("Parameter y contains a unit with undefined value");if(!n.equalBase(r))throw new Error("Units do not match");var i=n.clone();return i.value=t.find(e,[i.valueType(),r.valueType()])(i.value,r.value),i.fixPrefix=!1,i}))})})),ii=Rt("bignumber",["typed","BigNumber"],(e=>{var{typed:t,BigNumber:n}=e;return t("bignumber",{"":function(){return new n(0)},number:function(e){return new n(e+"")},string:function(e){var t=e.match(/(0[box][0-9a-fA-F]*)i([0-9]*)/);if(t){var r=t[2],i=n(t[1]),u=new n(2).pow(Number(r));if(i.gt(u.sub(1)))throw new SyntaxError('String "'.concat(e,'" is out of range'));var a=new n(2).pow(Number(r)-1);return i.gte(a)?i.sub(u):i}return new n(e)},BigNumber:function(e){return e},Unit:t.referToSelf((e=>t=>{var n=t.clone();return n.value=e(t.value),n})),Fraction:function(e){return new n(e.n).div(e.d).times(e.s)},null:function(e){return new n(0)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})}));var ui="number, number";function ai(e,t){if(!Vt(e)||!Vt(t))throw new Error("Integers expected in function bitAnd");return e&t}function oi(e){if(!Vt(e))throw new Error("Integer expected in function bitNot");return~e}function si(e,t){if(!Vt(e)||!Vt(t))throw new Error("Integers expected in function bitOr");return e|t}function fi(e,t){if(!Vt(e)||!Vt(t))throw new Error("Integers expected in function bitXor");return e^t}function ci(e,t){if(!Vt(e)||!Vt(t))throw new Error("Integers expected in function leftShift");return e<<t}function li(e,t){if(!Vt(e)||!Vt(t))throw new Error("Integers expected in function rightArithShift");return e>>t}function pi(e,t){if(!Vt(e)||!Vt(t))throw new Error("Integers expected in function rightLogShift");return e>>>t}ai.signature=ui,oi.signature="number",si.signature=ui,fi.signature=ui,ci.signature=ui,li.signature=ui,pi.signature=ui;function hi(e,t){if(t<e)return 1;if(t===e)return t;var n=t+e>>1;return hi(e,n)*hi(n+1,t)}function mi(e,t){if(!Vt(e)||e<0)throw new TypeError("Positive integer value expected in function combinations");if(!Vt(t)||t<0)throw new TypeError("Positive integer value expected in function combinations");if(t>e)throw new TypeError("k must be less than or equal to n");for(var n=e-t,r=1,i=2,u=t<n?t:n,a=t<n?n+1:t+1;a<=e;++a)for(r*=a;i<=u&&r%i==0;)r/=i,++i;return i<=u&&(r/=hi(i,u)),r}mi.signature="number, number";var di="conj",Di=Rt(di,["typed"],(e=>{var{typed:t}=e;return t(di,{"number | BigNumber | Fraction":e=>e,Complex:e=>e.conjugate(),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})}));function vi(e,t,n){if(null==n)return e.eq(t);if(e.eq(t))return!0;if(e.isNaN()||t.isNaN())return!1;if(e.isFinite()&&t.isFinite()){var r=e.minus(t).abs();if(r.isZero())return!0;var i=e.constructor.max(e.abs(),t.abs());return r.lte(i.times(n))}return!1}var gi=Rt("compareUnits",["typed"],(e=>{var{typed:t}=e;return{"Unit, Unit":t.referToSelf((e=>(n,r)=>{if(!n.equalBase(r))throw new Error("Cannot compare units with different base");return t.find(e,[n.valueType(),r.valueType()])(n.value,r.value)}))}})),yi="equalScalar",Ei=Rt(yi,["typed","config"],(e=>{var{typed:t,config:n}=e,r=gi({typed:t});return t(yi,{"boolean, boolean":function(e,t){return e===t},"number, number":function(e,t){return on(e,t,n.epsilon)},"BigNumber, BigNumber":function(e,t){return e.eq(t)||vi(e,t,n.epsilon)},"Fraction, Fraction":function(e,t){return e.equals(t)},"Complex, Complex":function(e,t){return function(e,t,n){return on(e.re,t.re,n)&&on(e.im,t.im,n)}(e,t,n.epsilon)}},r)}));Rt(yi,["typed","config"],(e=>{var{typed:t,config:n}=e;return t(yi,{"number, number":function(e,t){return on(e,t,n.epsilon)}})})),Math.pow(2,53);var wi="format",xi=Rt(wi,["typed"],(e=>{var{typed:t}=e;return t(wi,{any:Sn,"any, Object | function | number | BigNumber":Sn})})),bi=(Rt("hex",["typed","format"],(e=>{var{typed:t,format:n}=e;return t("hex",{"number | BigNumber":function(e){return n(e,{notation:"hex"})},"number | BigNumber, number | BigNumber":function(e,t){return n(e,{notation:"hex",wordSize:t})}})})),"isInteger"),Ai=Rt(bi,["typed"],(e=>{var{typed:t}=e;return t(bi,{number:Vt,BigNumber:function(e){return e.isInt()},Fraction:function(e){return 1===e.d&&isFinite(e.n)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})})),Fi="number";function Ci(e){return e<0}function Ni(e){return e>0}function Mi(e){return 0===e}function Si(e){return Number.isNaN(e)}Ci.signature=Fi,Ni.signature=Fi,Mi.signature=Fi,Si.signature=Fi;var Bi="isZero",_i=Rt(Bi,["typed"],(e=>{var{typed:t}=e;return t(Bi,{number:Mi,BigNumber:function(e){return e.isZero()},Complex:function(e){return 0===e.re&&0===e.im},Fraction:function(e){return 1===e.d&&0===e.n},Unit:t.referToSelf((e=>n=>t.find(e,n.valueType())(n.value))),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})}));function Ti(e){var t;if(Vt(e))return e<=0?isFinite(e)?1/0:NaN:e>171?1/0:hi(1,e-1);if(e<.5)return Math.PI/(Math.sin(Math.PI*e)*Ti(1-e));if(e>=171.35)return 1/0;if(e>85){var n=e*e,r=n*e,i=r*e,u=i*e;return Math.sqrt(2*Math.PI/e)*Math.pow(e/Math.E,e)*(1+1/(12*e)+1/(288*n)-139/(51840*r)-571/(2488320*i)+163879/(209018880*u)+5246819/(75246796800*u*e))}--e,t=Ri[0];for(var a=1;a<Ri.length;++a)t+=Ri[a]/(e+a);var o=e+Oi+.5;return Math.sqrt(2*Math.PI)*Math.pow(o,e+.5)*Math.exp(-o)*t}Ti.signature="number";var Oi=4.7421875,Ri=[.9999999999999971,57.15623566586292,-59.59796035547549,14.136097974741746,-.4919138160976202,3399464998481189e-20,4652362892704858e-20,-9837447530487956e-20,.0001580887032249125,-.00021026444172410488,.00021743961811521265,-.0001643181065367639,8441822398385275e-20,-26190838401581408e-21,36899182659531625e-22],Ii=.9189385332046728,zi=[1.000000000190015,76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18];function Ui(e){if(e<0)return NaN;if(0===e)return 1/0;if(!isFinite(e))return e;if(e<.5)return Math.log(Math.PI/Math.sin(Math.PI*e))-Ui(1-e);for(var t=(e-=1)+5+.5,n=zi[0],r=6;r>=1;r--)n+=zi[r]/(e+r);return Ii+(e+.5)*Math.log(t)-t+Math.log(n)}Ui.signature="number";var Pi=Rt("multiplyScalar",["typed"],(e=>{var{typed:t}=e;return t("multiplyScalar",{"number, number":br,"Complex, Complex":function(e,t){return e.mul(t)},"BigNumber, BigNumber":function(e,t){return e.times(t)},"Fraction, Fraction":function(e,t){return e.mul(t)},"number | Fraction | BigNumber | Complex, Unit":(e,t)=>t.multiply(e),"Unit, number | Fraction | BigNumber | Complex | Unit":(e,t)=>e.multiply(t)})})),Li="number, number";function ki(e){return!e}function qi(e,t){return!(!e&&!t)}function ji(e,t){return!!e!=!!t}function Hi(e,t){return!(!e||!t)}ki.signature="number",qi.signature=Li,ji.signature=Li,Hi.signature=Li;var Gi=Rt("number",["typed"],(e=>{var{typed:t}=e,n=t("number",{"":function(){return 0},number:function(e){return e},string:function(e){if("NaN"===e)return NaN;var t,n,r=(n=(t=e).match(/(0[box])([0-9a-fA-F]*)\.([0-9a-fA-F]*)/))?{input:t,radix:{"0b":2,"0o":8,"0x":16}[n[1]],integerPart:n[2],fractionalPart:n[3]}:null;if(r)return function(e){for(var t=parseInt(e.integerPart,e.radix),n=0,r=0;r<e.fractionalPart.length;r++)n+=parseInt(e.fractionalPart[r],e.radix)/Math.pow(e.radix,r+1);var i=t+n;if(isNaN(i))throw new SyntaxError('String "'+e.input+'" is not a valid number');return i}(r);var i=0,u=e.match(/(0[box][0-9a-fA-F]*)i([0-9]*)/);u&&(i=Number(u[2]),e=u[1]);var a=Number(e);if(isNaN(a))throw new SyntaxError('String "'+e+'" is not a valid number');if(u){if(a>2**i-1)throw new SyntaxError('String "'.concat(e,'" is out of range'));a>=2**(i-1)&&(a-=2**i)}return a},BigNumber:function(e){return e.toNumber()},Fraction:function(e){return e.valueOf()},Unit:t.referToSelf((e=>t=>{var n=t.clone();return n.value=e(t.value),n})),null:function(e){return 0},"Unit, string | Unit":function(e,t){return e.toNumber(t)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))});return n.fromJSON=function(e){return parseFloat(e.value)},n})),Vi=(Rt("oct",["typed","format"],(e=>{var{typed:t,format:n}=e;return t("oct",{"number | BigNumber":function(e){return n(e,{notation:"oct"})},"number | BigNumber, number | BigNumber":function(e,t){return n(e,{notation:"oct",wordSize:t})}})})),n(7391));Date.now();var Yi=Rt("SparseMatrix",["typed","equalScalar","Matrix"],(e=>{var{typed:t,equalScalar:n,Matrix:r}=e;function i(e,t){if(!(this instanceof i))throw new SyntaxError("Constructor must be called with the new operator");if(t&&!$e(t))throw new Error("Invalid datatype: "+t);if(Xe(e))!function(e,t,n){"SparseMatrix"===t.type?(e._values=t._values?Bt(t._values):void 0,e._index=Bt(t._index),e._ptr=Bt(t._ptr),e._size=Bt(t._size),e._datatype=n||t._datatype):u(e,t.valueOf(),n||t._datatype)}(this,e,t);else if(e&&Je(e.index)&&Je(e.ptr)&&Je(e.size))this._values=e.values,this._index=e.index,this._ptr=e.ptr,this._size=e.size,this._datatype=t||e.datatype;else if(Je(e))u(this,e,t);else{if(e)throw new TypeError("Unsupported type of data ("+St(e)+")");this._values=[],this._index=[],this._ptr=[0],this._size=[0,0],this._datatype=t}}function u(e,r,i){e._values=[],e._index=[],e._ptr=[],e._datatype=i;var u=r.length,a=0,o=n,s=0;if($e(i)&&(o=t.find(n,[i,i])||n,s=t.convert(0,i)),u>0){var f=0;do{e._ptr.push(e._index.length);for(var c=0;c<u;c++){var l=r[c];if(Je(l)){if(0===f&&a<l.length&&(a=l.length),f<l.length){var p=l[f];o(p,s)||(e._values.push(p),e._index.push(c))}}else 0===f&&a<1&&(a=1),o(l,s)||(e._values.push(l),e._index.push(c))}f++}while(f<a)}e._ptr.push(e._index.length),e._size=[u,a]}function a(e,t,n,r){if(n-t==0)return n;for(var i=t;i<n;i++)if(r[i]===e)return i;return t}function o(e,t,n,r,i,u,a){i.splice(e,0,r),u.splice(e,0,t);for(var o=n+1;o<a.length;o++)a[o]++}function s(e,r,i,u){var a=u||0,o=n,s=0;$e(e._datatype)&&(o=t.find(n,[e._datatype,e._datatype])||n,s=t.convert(0,e._datatype),a=t.convert(a,e._datatype));var f,c,l,p=!o(a,s),h=e._size[0],m=e._size[1];if(i>m){for(c=m;c<i;c++)if(e._ptr[c]=e._values.length,p)for(f=0;f<h;f++)e._values.push(a),e._index.push(f);e._ptr[i]=e._values.length}else i<m&&(e._ptr.splice(i+1,m-i),e._values.splice(e._ptr[i],e._values.length),e._index.splice(e._ptr[i],e._index.length));if(m=i,r>h){if(p){var d=0;for(c=0;c<m;c++){e._ptr[c]=e._ptr[c]+d,l=e._ptr[c+1]+d;var D=0;for(f=h;f<r;f++,D++)e._values.splice(l+D,0,a),e._index.splice(l+D,0,f),d++}e._ptr[m]=e._values.length}}else if(r<h){var v=0;for(c=0;c<m;c++){e._ptr[c]=e._ptr[c]-v;var g=e._ptr[c],y=e._ptr[c+1]-v;for(l=g;l<y;l++)(f=e._index[l])>r-1&&(e._values.splice(l,1),e._index.splice(l,1),v++)}e._ptr[c]=e._values.length}return e._size[0]=r,e._size[1]=i,e}function f(e,t,n,r,i){var u,a,o=r[0],s=r[1],f=[];for(u=0;u<o;u++)for(f[u]=[],a=0;a<s;a++)f[u][a]=0;for(a=0;a<s;a++)for(var c=n[a],l=n[a+1],p=c;p<l;p++)f[u=t[p]][a]=e?i?Bt(e[p]):e[p]:1;return f}return i.prototype=new r,i.prototype.createSparseMatrix=function(e,t){return new i(e,t)},Object.defineProperty(i,"name",{value:"SparseMatrix"}),i.prototype.constructor=i,i.prototype.type="SparseMatrix",i.prototype.isSparseMatrix=!0,i.prototype.getDataType=function(){return Wn(this._values,St)},i.prototype.storage=function(){return"sparse"},i.prototype.datatype=function(){return this._datatype},i.prototype.create=function(e,t){return new i(e,t)},i.prototype.density=function(){var e=this._size[0],t=this._size[1];return 0!==e&&0!==t?this._index.length/(e*t):0},i.prototype.subset=function(e,t,n){if(!this._values)throw new Error("Cannot invoke subset on a Pattern only matrix");switch(arguments.length){case 1:return function(e,t){if(!nt(t))throw new TypeError("Invalid index");if(t.isScalar())return e.get(t.min());var n,r,u,a,o=t.size();if(o.length!==e._size.length)throw new Rn(o.length,e._size.length);var s=t.min(),f=t.max();for(n=0,r=e._size.length;n<r;n++)Ln(s[n],e._size[n]),Ln(f[n],e._size[n]);var c=e._values,l=e._index,p=e._ptr,h=t.dimension(0),m=t.dimension(1),d=[],D=[];h.forEach((function(e,t){D[e]=t[0],d[e]=!0}));var v=c?[]:void 0,g=[],y=[];return m.forEach((function(e){for(y.push(g.length),u=p[e],a=p[e+1];u<a;u++)n=l[u],!0===d[n]&&(g.push(D[n]),v&&v.push(c[u]))})),y.push(g.length),new i({values:v,index:g,ptr:y,size:o,datatype:e._datatype})}(this,e);case 2:case 3:return function(e,t,n,r){if(!t||!0!==t.isIndex)throw new TypeError("Invalid index");var i,u=t.size(),a=t.isScalar();Xe(n)?(i=n.size(),n=n.toArray()):i=zn(n);if(a){if(0!==i.length)throw new TypeError("Scalar expected");e.set(t.min(),n,r)}else{if(1!==u.length&&2!==u.length)throw new Rn(u.length,e._size.length,"<");if(i.length<u.length){for(var o=0,s=0;1===u[o]&&1===i[o];)o++;for(;1===u[o];)s++,o++;n=Vn(n,u.length,s,i)}if(!Tt(u,i))throw new Rn(u,i,">");if(1===u.length){t.dimension(0).forEach((function(t,i){Ln(t),e.set([t,0],n[i[0]],r)}))}else{var f=t.dimension(0),c=t.dimension(1);f.forEach((function(t,i){Ln(t),c.forEach((function(u,a){Ln(u),e.set([t,u],n[i[0]][a[0]],r)}))}))}}return e}(this,e,t,n);default:throw new SyntaxError("Wrong number of arguments")}},i.prototype.get=function(e){if(!Je(e))throw new TypeError("Array expected");if(e.length!==this._size.length)throw new Rn(e.length,this._size.length);if(!this._values)throw new Error("Cannot invoke get on a Pattern only matrix");var t=e[0],n=e[1];Ln(t,this._size[0]),Ln(n,this._size[1]);var r=a(t,this._ptr[n],this._ptr[n+1],this._index);return r<this._ptr[n+1]&&this._index[r]===t?this._values[r]:0},i.prototype.set=function(e,r,i){if(!Je(e))throw new TypeError("Array expected");if(e.length!==this._size.length)throw new Rn(e.length,this._size.length);if(!this._values)throw new Error("Cannot invoke set on a Pattern only matrix");var u=e[0],f=e[1],c=this._size[0],l=this._size[1],p=n,h=0;$e(this._datatype)&&(p=t.find(n,[this._datatype,this._datatype])||n,h=t.convert(0,this._datatype)),(u>c-1||f>l-1)&&(s(this,Math.max(u+1,c),Math.max(f+1,l),i),c=this._size[0],l=this._size[1]),Ln(u,c),Ln(f,l);var m=a(u,this._ptr[f],this._ptr[f+1],this._index);return m<this._ptr[f+1]&&this._index[m]===u?p(r,h)?function(e,t,n,r,i){n.splice(e,1),r.splice(e,1);for(var u=t+1;u<i.length;u++)i[u]--}(m,f,this._values,this._index,this._ptr):this._values[m]=r:p(r,h)||o(m,u,f,r,this._values,this._index,this._ptr),this},i.prototype.resize=function(e,t,n){if(!Qe(e))throw new TypeError("Array or Matrix expected");var r=e.valueOf().map((e=>Array.isArray(e)&&1===e.length?e[0]:e));if(2!==r.length)throw new Error("Only two dimensions matrix are supported");return r.forEach((function(e){if(!Ge(e)||!Vt(e)||e<0)throw new TypeError("Invalid size, must contain positive integers (size: "+Sn(r)+")")})),s(n?this.clone():this,r[0],r[1],t)},i.prototype.reshape=function(e,t){if(!Je(e))throw new TypeError("Array expected");if(2!==e.length)throw new Error("Sparse matrices can only be reshaped in two dimensions");e.forEach((function(t){if(!Ge(t)||!Vt(t)||t<=-2||0===t)throw new TypeError("Invalid size, must contain positive integers or -1 (size: "+Sn(e)+")")}));var n=this._size[0]*this._size[1];if(n!==(e=Hn(e,n))[0]*e[1])throw new Error("Reshaping sparse matrix will result in the wrong number of elements");var r=t?this.clone():this;if(this._size[0]===e[0]&&this._size[1]===e[1])return r;for(var i=[],u=0;u<r._ptr.length;u++)for(var s=0;s<r._ptr[u+1]-r._ptr[u];s++)i.push(u);for(var f=r._values.slice(),c=r._index.slice(),l=0;l<r._index.length;l++){var p=c[l],h=i[l],m=p*r._size[1]+h;i[l]=m%e[1],c[l]=Math.floor(m/e[1])}r._values.length=0,r._index.length=0,r._ptr.length=e[1]+1,r._size=e.slice();for(var d=0;d<r._ptr.length;d++)r._ptr[d]=0;for(var D=0;D<f.length;D++){var v=c[D],g=i[D],y=f[D];o(a(v,r._ptr[g],r._ptr[g+1],r._index),v,g,y,r._values,r._index,r._ptr)}return r},i.prototype.clone=function(){return new i({values:this._values?Bt(this._values):void 0,index:Bt(this._index),ptr:Bt(this._ptr),size:Bt(this._size),datatype:this._datatype})},i.prototype.size=function(){return this._size.slice(0)},i.prototype.map=function(e,r){if(!this._values)throw new Error("Cannot invoke map on a Pattern only matrix");var u=this,a=this._size[0],o=this._size[1],s=vn(e);return function(e,r,u,a,o,s,f){var c=[],l=[],p=[],h=n,m=0;$e(e._datatype)&&(h=t.find(n,[e._datatype,e._datatype])||n,m=t.convert(0,e._datatype));for(var d=function(e,t,n){e=s(e,t,n),h(e,m)||(c.push(e),l.push(t))},D=a;D<=o;D++){p.push(c.length);var v=e._ptr[D],g=e._ptr[D+1];if(f)for(var y=v;y<g;y++){var E=e._index[y];E>=r&&E<=u&&d(e._values[y],E-r,D-a)}else{for(var w={},x=v;x<g;x++){w[e._index[x]]=e._values[x]}for(var b=r;b<=u;b++){d(b in w?w[b]:0,b-r,D-a)}}}return p.push(c.length),new i({values:c,index:l,ptr:p,size:[u-r+1,o-a+1]})}(this,0,a-1,0,o-1,(function(t,n,r){return 1===s?e(t):2===s?e(t,[n,r]):e(t,[n,r],u)}),r)},i.prototype.forEach=function(e,t){if(!this._values)throw new Error("Cannot invoke forEach on a Pattern only matrix");for(var n=this._size[0],r=this._size[1],i=0;i<r;i++){var u=this._ptr[i],a=this._ptr[i+1];if(t)for(var o=u;o<a;o++){var s=this._index[o];e(this._values[o],[s,i],this)}else{for(var f={},c=u;c<a;c++){f[this._index[c]]=this._values[c]}for(var l=0;l<n;l++){e(l in f?f[l]:0,[l,i],this)}}}},i.prototype[Symbol.iterator]=function*(){if(!this._values)throw new Error("Cannot iterate a Pattern only matrix");for(var e=this._size[1],t=0;t<e;t++)for(var n=this._ptr[t],r=this._ptr[t+1],i=n;i<r;i++){var u=this._index[i];yield{value:this._values[i],index:[u,t]}}},i.prototype.toArray=function(){return f(this._values,this._index,this._ptr,this._size,!0)},i.prototype.valueOf=function(){return f(this._values,this._index,this._ptr,this._size,!1)},i.prototype.format=function(e){for(var t=this._size[0],n=this._size[1],r=this.density(),i="Sparse Matrix ["+Sn(t,e)+" x "+Sn(n,e)+"] density: "+Sn(r,e)+"\n",u=0;u<n;u++)for(var a=this._ptr[u],o=this._ptr[u+1],s=a;s<o;s++){i+="\n    ("+Sn(this._index[s],e)+", "+Sn(u,e)+") ==> "+(this._values?Sn(this._values[s],e):"X")}return i},i.prototype.toString=function(){return Sn(this.toArray())},i.prototype.toJSON=function(){return{mathjs:"SparseMatrix",values:this._values,index:this._index,ptr:this._ptr,size:this._size,datatype:this._datatype}},i.prototype.diagonal=function(e){if(e){if(Ve(e)&&(e=e.toNumber()),!Ge(e)||!Vt(e))throw new TypeError("The parameter k must be an integer number")}else e=0;var t=e>0?e:0,n=e<0?-e:0,r=this._size[0],u=this._size[1],a=Math.min(r-n,u-t),o=[],s=[],f=[];f[0]=0;for(var c=t;c<u&&o.length<a;c++)for(var l=this._ptr[c],p=this._ptr[c+1],h=l;h<p;h++){var m=this._index[h];if(m===c-t+n){o.push(this._values[h]),s[o.length-1]=m-n;break}}return f.push(o.length),new i({values:o,index:s,ptr:f,size:[a,1]})},i.fromJSON=function(e){return new i(e)},i.diagonal=function(e,r,u,a,o){if(!Je(e))throw new TypeError("Array expected, size parameter");if(2!==e.length)throw new Error("Only two dimensions matrix are supported");if(e=e.map((function(e){if(Ve(e)&&(e=e.toNumber()),!Ge(e)||!Vt(e)||e<1)throw new Error("Size values must be positive integers");return e})),u){if(Ve(u)&&(u=u.toNumber()),!Ge(u)||!Vt(u))throw new TypeError("The parameter k must be an integer number")}else u=0;var s=n,f=0;$e(o)&&(s=t.find(n,[o,o])||n,f=t.convert(0,o));var c,l=u>0?u:0,p=u<0?-u:0,h=e[0],m=e[1],d=Math.min(h-p,m-l);if(Je(r)){if(r.length!==d)throw new Error("Invalid value array length");c=function(e){return r[e]}}else if(Xe(r)){var D=r.size();if(1!==D.length||D[0]!==d)throw new Error("Invalid matrix length");c=function(e){return r.get([e])}}else c=function(){return r};for(var v=[],g=[],y=[],E=0;E<m;E++){y.push(v.length);var w=E-l;if(w>=0&&w<d){var x=c(w);s(x,f)||(g.push(w+p),v.push(x))}}return y.push(v.length),new i({values:v,index:g,ptr:y,size:[h,m]})},i.prototype.swapRows=function(e,t){if(!(Ge(e)&&Vt(e)&&Ge(t)&&Vt(t)))throw new Error("Row index must be positive integers");if(2!==this._size.length)throw new Error("Only two dimensional matrix is supported");return Ln(e,this._size[0]),Ln(t,this._size[0]),i._swapRows(e,t,this._size[1],this._values,this._index,this._ptr),this},i._forEachRow=function(e,t,n,r,i){for(var u=r[e],a=r[e+1],o=u;o<a;o++)i(n[o],t[o])},i._swapRows=function(e,t,n,r,i,u){for(var o=0;o<n;o++){var s=u[o],f=u[o+1],c=a(e,s,f,i),l=a(t,s,f,i);if(c<f&&l<f&&i[c]===e&&i[l]===t){if(r){var p=r[c];r[c]=r[l],r[l]=p}}else if(c<f&&i[c]===e&&(l>=f||i[l]!==t)){var h=r?r[c]:void 0;i.splice(l,0,t),r&&r.splice(l,0,h),i.splice(l<=c?c+1:c,1),r&&r.splice(l<=c?c+1:c,1)}else if(l<f&&i[l]===t&&(c>=f||i[c]!==e)){var m=r?r[l]:void 0;i.splice(c,0,e),r&&r.splice(c,0,m),i.splice(c<=l?l+1:l,1),r&&r.splice(c<=l?l+1:l,1)}}},i}),{isClass:!0}),Zi="subtractScalar",Wi=Rt(Zi,["typed"],(e=>{var{typed:t}=e;return t(Zi,{"number, number":xr,"Complex, Complex":function(e,t){return e.sub(t)},"BigNumber, BigNumber":function(e,t){return e.minus(t)},"Fraction, Fraction":function(e,t){return e.sub(t)},"Unit, Unit":t.referToSelf((e=>(n,r)=>{if(null===n.value||void 0===n.value)throw new Error("Parameter x contains a unit with undefined value");if(null===r.value||void 0===r.value)throw new Error("Parameter y contains a unit with undefined value");if(!n.equalBase(r))throw new Error("Units do not match");var i=n.clone();return i.value=t.find(e,[i.valueType(),r.valueType()])(i.value,r.value),i.fixPrefix=!1,i}))})}));Rt("bin",["typed","format"],(e=>{var{typed:t,format:n}=e;return t("bin",{"number | BigNumber":function(e){return n(e,{notation:"bin"})},"number | BigNumber, number | BigNumber":function(e,t){return n(e,{notation:"bin",wordSize:t})}})}));var $i="unaryMinus",Ji=Rt($i,["typed"],(e=>{var{typed:t}=e;return t($i,{number:Ar,"Complex | BigNumber | Fraction":e=>e.neg(),Unit:t.referToSelf((e=>n=>{var r=n.clone();return r.value=t.find(e,r.valueType())(n.value),r})),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0)))})})),Xi=Rt("fraction",["typed","Fraction"],(e=>{var{typed:t,Fraction:n}=e;return t("fraction",{number:function(e){if(!isFinite(e)||isNaN(e))throw new Error(e+" cannot be represented as a fraction");return new n(e)},string:function(e){return new n(e)},"number, number":function(e,t){return new n(e,t)},null:function(e){return new n(0)},BigNumber:function(e){return new n(e.toString())},Fraction:function(e){return e},Unit:t.referToSelf((e=>t=>{var n=t.clone();return n.value=e(t.value),n})),Object:function(e){return new n(e)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})})),Qi="isNumeric",Ki=Rt(Qi,["typed"],(e=>{var{typed:t}=e;return t(Qi,{"number | BigNumber | Fraction | boolean":()=>!0,"Complex | Unit | string | null | undefined | Node":()=>!1,"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})})),eu="matrix",tu=Rt(eu,["typed","Matrix","DenseMatrix","SparseMatrix"],(e=>{var{typed:t,Matrix:n,DenseMatrix:r,SparseMatrix:i}=e;return t(eu,{"":function(){return u([])},string:function(e){return u([],e)},"string, string":function(e,t){return u([],e,t)},Array:function(e){return u(e)},Matrix:function(e){return u(e,e.storage())},"Array | Matrix, string":u,"Array | Matrix, string, string":u});function u(e,t,n){if("dense"===t||"default"===t||void 0===t)return new r(e,n);if("sparse"===t)return new i(e,n);throw new TypeError("Unknown matrix type "+JSON.stringify(t)+".")}}));function nu(){throw new Error('No "bignumber" implementation available')}function ru(){throw new Error('No "fraction" implementation available')}function iu(){throw new Error('No "matrix" implementation available')}var uu=Rt("numeric",["number","?bignumber","?fraction"],(e=>{var{number:t,bignumber:n,fraction:r}=e,i={string:!0,number:!0,BigNumber:!0,Fraction:!0},u={number:e=>t(e),BigNumber:n?e=>n(e):nu,Fraction:r?e=>r(e):ru};return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"number";if(void 0!==(arguments.length>2?arguments[2]:void 0))throw new SyntaxError("numeric() takes one or two arguments");var n=St(e);if(!(n in i))throw new TypeError("Cannot convert "+e+' of type "'+n+'"; valid input types are '+Object.keys(i).join(", "));if(!(t in u))throw new TypeError("Cannot convert "+e+' to type "'+t+'"; valid output types are '+Object.keys(u).join(", "));return t===n?e:u[t](e)}}));var au="size",ou=Rt(au,["typed","config","?matrix"],(e=>{var{typed:t,config:n,matrix:r}=e;return t(au,{Matrix:function(e){return e.create(e.size())},Array:zn,string:function(e){return"Array"===n.matrix?[e.length]:r([e.length])},"number | Complex | BigNumber | Unit | boolean | null":function(e){return"Array"===n.matrix?[]:r?r([]):iu()}})})),su="zeros",fu=Rt(su,["typed","config","matrix","BigNumber"],(e=>{var{typed:t,config:n,matrix:r,BigNumber:i}=e;return t(su,{"":function(){return"Array"===n.matrix?u([]):u([],"default")},"...number | BigNumber | string":function(e){if("string"==typeof e[e.length-1]){var t=e.pop();return u(e,t)}return"Array"===n.matrix?u(e):u(e,"default")},Array:u,Matrix:function(e){var t=e.storage();return u(e.valueOf(),t)},"Array | Matrix, string":function(e,t){return u(e.valueOf(),t)}});function u(e,t){var n=function(e){var t=!1;return e.forEach((function(e,n,r){Ve(e)&&(t=!0,r[n]=e.toNumber())})),t}(e),u=n?new i(0):0;if(function(e){e.forEach((function(e){if("number"!=typeof e||!Vt(e)||e<0)throw new Error("Parameters in function zeros must be positive integers")}))}(e),t){var a=r(t);return e.length>0?a.resize(e,u):a}var o=[];return e.length>0?kn(o,e,u):o}})),cu="concat",lu=Rt(cu,["typed","matrix","isInteger"],(e=>{var{typed:t,matrix:n,isInteger:r}=e;return t(cu,{"...Array | Matrix | number | BigNumber":function(e){var t,i,u=e.length,a=-1,o=!1,s=[];for(t=0;t<u;t++){var f=e[t];if(Xe(f)&&(o=!0),Ge(f)||Ve(f)){if(t!==u-1)throw new Error("Dimension must be specified as last argument");if(i=a,a=f.valueOf(),!r(a))throw new TypeError("Integer number expected for dimension");if(a<0||t>0&&a>i)throw new In(a,i+1)}else{var c=Bt(f).valueOf(),l=zn(c);if(s[t]=c,i=a,a=l.length-1,t>0&&a!==i)throw new Rn(i+1,a+1)}}if(0===s.length)throw new SyntaxError("At least one matrix expected");for(var p=s.shift();s.length;)p=Jn(p,s.shift(),a);return o?n(p):p},"...string":function(e){return e.join("")}})})),pu="divideScalar",hu=Rt(pu,["typed","numeric"],(e=>{var{typed:t,numeric:n}=e;return t(pu,{"number, number":function(e,t){return e/t},"Complex, Complex":function(e,t){return e.div(t)},"BigNumber, BigNumber":function(e,t){return e.div(t)},"Fraction, Fraction":function(e,t){return e.div(t)},"Unit, number | Complex | Fraction | BigNumber | Unit":(e,t)=>e.divide(t),"number | Fraction | Complex | BigNumber, Unit":(e,t)=>t.divideInto(e)})})),mu=Rt("matAlgo03xDSf",["typed"],(e=>{var{typed:t}=e;return function(e,n,r,i){var u=e._data,a=e._size,o=e._datatype||e.getDataType(),s=n._values,f=n._index,c=n._ptr,l=n._size,p=n._datatype||void 0===n._data?n._datatype:n.getDataType();if(a.length!==l.length)throw new Rn(a.length,l.length);if(a[0]!==l[0]||a[1]!==l[1])throw new RangeError("Dimension mismatch. Matrix A ("+a+") must match Matrix B ("+l+")");if(!s)throw new Error("Cannot perform operation on Dense Matrix and Pattern Sparse Matrix");var h,m=a[0],d=a[1],D=0,v=r;"string"==typeof o&&o===p&&"mixed"!==o&&(h=o,D=t.convert(0,h),v=t.find(r,[h,h]));for(var g=[],y=0;y<m;y++)g[y]=[];for(var E=[],w=[],x=0;x<d;x++){for(var b=x+1,A=c[x],F=c[x+1],C=A;C<F;C++){var N=f[C];E[N]=i?v(s[C],u[N][x]):v(u[N][x],s[C]),w[N]=b}for(var M=0;M<m;M++)w[M]===b?g[M][x]=E[M]:g[M][x]=i?v(D,u[M][x]):v(u[M][x],D)}return e.createDenseMatrix({data:g,size:[m,d],datatype:o===e._datatype&&p===n._datatype?h:void 0})}})),du=Rt("matAlgo07xSSf",["typed","DenseMatrix"],(e=>{var{typed:t,DenseMatrix:n}=e;return function(e,i,u){var a=e._size,o=e._datatype||void 0===e._data?e._datatype:e.getDataType(),s=i._size,f=i._datatype||void 0===i._data?i._datatype:i.getDataType();if(a.length!==s.length)throw new Rn(a.length,s.length);if(a[0]!==s[0]||a[1]!==s[1])throw new RangeError("Dimension mismatch. Matrix A ("+a+") must match Matrix B ("+s+")");var c,l,p,h=a[0],m=a[1],d=0,D=u;"string"==typeof o&&o===f&&"mixed"!==o&&(c=o,d=t.convert(0,c),D=t.find(u,[c,c]));var v=[];for(l=0;l<h;l++)v[l]=[];var g=[],y=[],E=[],w=[];for(p=0;p<m;p++){var x=p+1;for(r(e,p,E,g,x),r(i,p,w,y,x),l=0;l<h;l++){var b=E[l]===x?g[l]:d,A=w[l]===x?y[l]:d;v[l][p]=D(b,A)}}return new n({data:v,size:[h,m],datatype:o===e._datatype&&f===i._datatype?c:void 0})};function r(e,t,n,r,i){for(var u=e._values,a=e._index,o=e._ptr,s=o[t],f=o[t+1];s<f;s++){var c=a[s];n[c]=i,r[c]=u[s]}}})),Du=Rt("matAlgo11xS0s",["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return function(e,r,i,u){var a=e._values,o=e._index,s=e._ptr,f=e._size,c=e._datatype;if(!a)throw new Error("Cannot perform operation on Pattern Sparse Matrix and Scalar value");var l,p=f[0],h=f[1],m=n,d=0,D=i;"string"==typeof c&&(l=c,m=t.find(n,[l,l]),d=t.convert(0,l),r=t.convert(r,l),D=t.find(i,[l,l]));for(var v=[],g=[],y=[],E=0;E<h;E++){y[E]=g.length;for(var w=s[E],x=s[E+1],b=w;b<x;b++){var A=o[b],F=u?D(r,a[b]):D(a[b],r);m(F,d)||(g.push(A),v.push(F))}}return y[h]=g.length,e.createSparseMatrix({values:v,index:g,ptr:y,size:[p,h],datatype:l})}})),vu=Rt("matAlgo12xSfs",["typed","DenseMatrix"],(e=>{var{typed:t,DenseMatrix:n}=e;return function(e,r,i,u){var a=e._values,o=e._index,s=e._ptr,f=e._size,c=e._datatype;if(!a)throw new Error("Cannot perform operation on Pattern Sparse Matrix and Scalar value");var l,p=f[0],h=f[1],m=i;"string"==typeof c&&(l=c,r=t.convert(r,l),m=t.find(i,[l,l]));for(var d=[],D=[],v=[],g=0;g<h;g++){for(var y=g+1,E=s[g],w=s[g+1],x=E;x<w;x++){var b=o[x];D[b]=a[x],v[b]=y}for(var A=0;A<p;A++)0===g&&(d[A]=[]),v[A]===y?d[A][g]=u?m(r,D[A]):m(D[A],r):d[A][g]=u?m(r,0):m(0,r)}return new n({data:d,size:[p,h],datatype:l})}})),gu=Rt("matAlgo13xDD",["typed"],(e=>{var{typed:t}=e;return function(e,r,i){var u,a=e._data,o=e._size,s=e._datatype,f=r._data,c=r._size,l=r._datatype,p=[];if(o.length!==c.length)throw new Rn(o.length,c.length);for(var h=0;h<o.length;h++){if(o[h]!==c[h])throw new RangeError("Dimension mismatch. Matrix A ("+o+") must match Matrix B ("+c+")");p[h]=o[h]}var m=i;"string"==typeof s&&s===l&&(u=s,m=t.find(i,[u,u]));var d=p.length>0?n(m,0,p,p[0],a,f):[];return e.createDenseMatrix({data:d,size:p,datatype:u})};function n(e,t,r,i,u,a){var o=[];if(t===r.length-1)for(var s=0;s<i;s++)o[s]=e(u[s],a[s]);else for(var f=0;f<i;f++)o[f]=n(e,t+1,r,r[t+1],u[f],a[f]);return o}})),yu=Rt("matAlgo14xDs",["typed"],(e=>{var{typed:t}=e;return function(e,r,i,u){var a,o=e._data,s=e._size,f=e._datatype,c=i;"string"==typeof f&&(a=f,r=t.convert(r,a),c=t.find(i,[a,a]));var l=s.length>0?n(c,0,s,s[0],o,r,u):[];return e.createDenseMatrix({data:l,size:Bt(s),datatype:a})};function n(e,t,r,i,u,a,o){var s=[];if(t===r.length-1)for(var f=0;f<i;f++)s[f]=o?e(a,u[f]):e(u[f],a);else for(var c=0;c<i;c++)s[c]=n(e,t+1,r,r[t+1],u[c],a,o);return s}})),Eu=Rt("broadcast",["concat"],(e=>{var{concat:t}=e;return function(e,t){var i=Math.max(e._size.length,t._size.length);if(e._size.length===t._size.length&&e._size.every(((e,n)=>e===t._size[n])))return[e,t];for(var u=n(e._size,i,0),a=n(t._size,i,0),o=[],s=0;s<i;s++)o[s]=Math.max(u[s],a[s]);Qn(u,o),Qn(a,o);var f=e.clone(),c=t.clone();f._size.length<i?f.reshape(n(f._size,i,1)):c._size.length<i&&c.reshape(n(c._size,i,1));for(var l=0;l<i;l++)f._size[l]<o[l]&&(f=r(f,o[l],l)),c._size[l]<o[l]&&(c=r(c,o[l],l));return[f,c]};function n(e,t,n){return[...Array(t-e.length).fill(n),...e]}function r(e,n,r){return t(...Array(n).fill(e),r)}})),wu=Rt("matrixAlgorithmSuite",["typed","matrix","concat"],(e=>{var{typed:t,matrix:n,concat:r}=e,i=gu({typed:t}),u=yu({typed:t}),a=Eu({concat:r});return function(e){var r,o=e.elop,s=e.SD||e.DS;o?(r={"DenseMatrix, DenseMatrix":(e,t)=>i(...a(e,t),o),"Array, Array":(e,t)=>i(...a(n(e),n(t)),o).valueOf(),"Array, DenseMatrix":(e,t)=>i(...a(n(e),t),o),"DenseMatrix, Array":(e,t)=>i(...a(e,n(t)),o)},e.SS&&(r["SparseMatrix, SparseMatrix"]=(t,n)=>e.SS(...a(t,n),o,!1)),e.DS&&(r["DenseMatrix, SparseMatrix"]=(t,n)=>e.DS(...a(t,n),o,!1),r["Array, SparseMatrix"]=(t,r)=>e.DS(...a(n(t),r),o,!1)),s&&(r["SparseMatrix, DenseMatrix"]=(e,t)=>s(...a(t,e),o,!0),r["SparseMatrix, Array"]=(e,t)=>s(...a(n(t),e),o,!0))):(r={"DenseMatrix, DenseMatrix":t.referToSelf((e=>(t,n)=>i(...a(t,n),e))),"Array, Array":t.referToSelf((e=>(t,r)=>i(...a(n(t),n(r)),e).valueOf())),"Array, DenseMatrix":t.referToSelf((e=>(t,r)=>i(...a(n(t),r),e))),"DenseMatrix, Array":t.referToSelf((e=>(t,r)=>i(...a(t,n(r)),e)))},e.SS&&(r["SparseMatrix, SparseMatrix"]=t.referToSelf((t=>(n,r)=>e.SS(...a(n,r),t,!1)))),e.DS&&(r["DenseMatrix, SparseMatrix"]=t.referToSelf((t=>(n,r)=>e.DS(...a(n,r),t,!1))),r["Array, SparseMatrix"]=t.referToSelf((t=>(r,i)=>e.DS(...a(n(r),i),t,!1)))),s&&(r["SparseMatrix, DenseMatrix"]=t.referToSelf((e=>(t,n)=>s(...a(n,t),e,!0))),r["SparseMatrix, Array"]=t.referToSelf((e=>(t,r)=>s(...a(n(r),t),e,!0)))));var f=e.scalar||"any";(e.Ds||e.Ss)&&(o?(r["DenseMatrix,"+f]=(e,t)=>u(e,t,o,!1),r[f+", DenseMatrix"]=(e,t)=>u(t,e,o,!0),r["Array,"+f]=(e,t)=>u(n(e),t,o,!1).valueOf(),r[f+", Array"]=(e,t)=>u(n(t),e,o,!0).valueOf()):(r["DenseMatrix,"+f]=t.referToSelf((e=>(t,n)=>u(t,n,e,!1))),r[f+", DenseMatrix"]=t.referToSelf((e=>(t,n)=>u(n,t,e,!0))),r["Array,"+f]=t.referToSelf((e=>(t,r)=>u(n(t),r,e,!1).valueOf())),r[f+", Array"]=t.referToSelf((e=>(t,r)=>u(n(r),t,e,!0).valueOf()))));var c=void 0!==e.sS?e.sS:e.Ss;return o?(e.Ss&&(r["SparseMatrix,"+f]=(t,n)=>e.Ss(t,n,o,!1)),c&&(r[f+", SparseMatrix"]=(e,t)=>c(t,e,o,!0))):(e.Ss&&(r["SparseMatrix,"+f]=t.referToSelf((t=>(n,r)=>e.Ss(n,r,t,!1)))),c&&(r[f+", SparseMatrix"]=t.referToSelf((e=>(t,n)=>c(n,t,e,!0))))),o&&o.signatures&&_t(r,o.signatures),r}})),xu="equal",bu=Rt(xu,["typed","matrix","equalScalar","DenseMatrix","concat"],(e=>{var{typed:t,matrix:n,equalScalar:r,DenseMatrix:i,concat:u}=e,a=mu({typed:t}),o=du({typed:t,DenseMatrix:i}),s=vu({typed:t,DenseMatrix:i}),f=wu({typed:t,matrix:n,concat:u});return t(xu,Au({typed:t,equalScalar:r}),f({elop:r,SS:o,DS:a,Ss:s}))})),Au=Rt(xu,["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return t(xu,{"any, any":function(e,t){return null===e?null===t:null===t?null===e:void 0===e?void 0===t:void 0===t?void 0===e:n(e,t)}})})),Fu="hasNumericValue",Cu=Rt(Fu,["typed","isNumeric"],(e=>{var{typed:t,isNumeric:n}=e;return t(Fu,{boolean:()=>!0,string:function(e){return e.trim().length>0&&!isNaN(Number(e))},any:function(e){return n(e)}})})),Nu="identity",Mu=Rt(Nu,["typed","config","matrix","BigNumber","DenseMatrix","SparseMatrix"],(e=>{var{typed:t,config:n,matrix:r,BigNumber:i,DenseMatrix:u,SparseMatrix:a}=e;return t(Nu,{"":function(){return"Matrix"===n.matrix?r([]):[]},string:function(e){return r(e)},"number | BigNumber":function(e){return s(e,e,"Matrix"===n.matrix?"dense":void 0)},"number | BigNumber, string":function(e,t){return s(e,e,t)},"number | BigNumber, number | BigNumber":function(e,t){return s(e,t,"Matrix"===n.matrix?"dense":void 0)},"number | BigNumber, number | BigNumber, string":function(e,t,n){return s(e,t,n)},Array:function(e){return o(e)},"Array, string":function(e,t){return o(e,t)},Matrix:function(e){return o(e.valueOf(),e.storage())},"Matrix, string":function(e,t){return o(e.valueOf(),t)}});function o(e,t){switch(e.length){case 0:return t?r(t):[];case 1:return s(e[0],e[0],t);case 2:return s(e[0],e[1],t);default:throw new Error("Vector containing two values expected")}}function s(e,t,n){var r=Ve(e)||Ve(t)?i:null;if(Ve(e)&&(e=e.toNumber()),Ve(t)&&(t=t.toNumber()),!Vt(e)||e<1)throw new Error("Parameters in function identity must be positive integers");if(!Vt(t)||t<1)throw new Error("Parameters in function identity must be positive integers");var o=r?new i(1):1,s=r?new r(0):0,f=[e,t];if(n){if("sparse"===n)return a.diagonal(f,o,0,s);if("dense"===n)return u.diagonal(f,o,0,s);throw new TypeError('Unknown matrix type "'.concat(n,'"'))}for(var c=kn([],f,s),l=e<t?e:t,p=0;p<l;p++)c[p][p]=o;return c}})),Su=Rt("matAlgo01xDSid",["typed"],(e=>{var{typed:t}=e;return function(e,n,r,i){var u=e._data,a=e._size,o=e._datatype||e.getDataType(),s=n._values,f=n._index,c=n._ptr,l=n._size,p=n._datatype||void 0===n._data?n._datatype:n.getDataType();if(a.length!==l.length)throw new Rn(a.length,l.length);if(a[0]!==l[0]||a[1]!==l[1])throw new RangeError("Dimension mismatch. Matrix A ("+a+") must match Matrix B ("+l+")");if(!s)throw new Error("Cannot perform operation on Dense Matrix and Pattern Sparse Matrix");var h,m,d=a[0],D=a[1],v="string"==typeof o&&"mixed"!==o&&o===p?o:void 0,g=v?t.find(r,[v,v]):r,y=[];for(h=0;h<d;h++)y[h]=[];var E=[],w=[];for(m=0;m<D;m++){for(var x=m+1,b=c[m],A=c[m+1],F=b;F<A;F++)E[h=f[F]]=i?g(s[F],u[h][m]):g(u[h][m],s[F]),w[h]=x;for(h=0;h<d;h++)w[h]===x?y[h][m]=E[h]:y[h][m]=u[h][m]}return e.createDenseMatrix({data:y,size:[d,D],datatype:o===e._datatype&&p===n._datatype?v:void 0})}})),Bu=Rt("matAlgo10xSids",["typed","DenseMatrix"],(e=>{var{typed:t,DenseMatrix:n}=e;return function(e,r,i,u){var a=e._values,o=e._index,s=e._ptr,f=e._size,c=e._datatype;if(!a)throw new Error("Cannot perform operation on Pattern Sparse Matrix and Scalar value");var l,p=f[0],h=f[1],m=i;"string"==typeof c&&(l=c,r=t.convert(r,l),m=t.find(i,[l,l]));for(var d=[],D=[],v=[],g=0;g<h;g++){for(var y=g+1,E=s[g],w=s[g+1],x=E;x<w;x++){var b=o[x];D[b]=a[x],v[b]=y}for(var A=0;A<p;A++)0===g&&(d[A]=[]),v[A]===y?d[A][g]=u?m(r,D[A]):m(D[A],r):d[A][g]=r}return new n({data:d,size:[p,h],datatype:l})}}));function _u(e,t,n,r){if(!(this instanceof _u))throw new SyntaxError("Constructor must be called with the new operator");this.fn=e,this.count=t,this.min=n,this.max=r,this.message="Wrong number of arguments in function "+e+" ("+t+" provided, "+n+(null!=r?"-"+r:"")+" expected)",this.stack=(new Error).stack}_u.prototype=new Error,_u.prototype.constructor=Error,_u.prototype.name="ArgumentsError",_u.prototype.isArgumentsError=!0;var Tu="Number of decimals in function round must be an integer",Ou="round",Ru=Rt(Ou,["typed","config","matrix","equalScalar","zeros","BigNumber","DenseMatrix"],(e=>{var{typed:t,config:n,matrix:r,equalScalar:i,zeros:u,BigNumber:a,DenseMatrix:o}=e,s=Du({typed:t,equalScalar:i}),f=vu({typed:t,DenseMatrix:o}),c=yu({typed:t});function l(e){return Math.abs(en(e).exponent)}return t(Ou,{number:function(e){var t=Ur(e,l(n.epsilon));return Ur(on(e,t,n.epsilon)?t:e)},"number, number":function(e,t){var r=l(n.epsilon);if(t>=r)return Ur(e,t);var i=Ur(e,r);return Ur(on(e,i,n.epsilon)?i:e,t)},"number, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(Tu);return new a(e).toDecimalPlaces(t.toNumber())},Complex:function(e){return e.round()},"Complex, number":function(e,t){if(t%1)throw new TypeError(Tu);return e.round(t)},"Complex, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(Tu);var n=t.toNumber();return e.round(n)},BigNumber:function(e){var t=new a(e).toDecimalPlaces(l(n.epsilon));return(vi(e,t,n.epsilon)?t:e).toDecimalPlaces(0)},"BigNumber, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(Tu);var r=l(n.epsilon);if(t>=r)return e.toDecimalPlaces(t.toNumber());var i=e.toDecimalPlaces(r);return(vi(e,i,n.epsilon)?i:e).toDecimalPlaces(t.toNumber())},Fraction:function(e){return e.round()},"Fraction, number":function(e,t){if(t%1)throw new TypeError(Tu);return e.round(t)},"Fraction, BigNumber":function(e,t){if(!t.isInteger())throw new TypeError(Tu);return e.round(t.toNumber())},"Unit, number, Unit":t.referToSelf((e=>function(t,n,r){var i=t.toNumeric(r);return r.multiply(e(i,n))})),"Unit, BigNumber, Unit":t.referToSelf((e=>(t,n,r)=>e(t,n.toNumber(),r))),"Unit, Unit":t.referToSelf((e=>(t,n)=>e(t,0,n))),"Array | Matrix, number, Unit":t.referToSelf((e=>(t,n,r)=>vr(t,(t=>e(t,n,r)),!0))),"Array | Matrix, BigNumber, Unit":t.referToSelf((e=>(t,n,r)=>e(t,n.toNumber(),r))),"Array | Matrix, Unit":t.referToSelf((e=>(t,n)=>e(t,0,n))),"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0))),"SparseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>s(t,n,e,!1))),"DenseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>c(t,n,e,!1))),"Array, number | BigNumber":t.referToSelf((e=>(t,n)=>c(r(t),n,e,!1).valueOf())),"number | Complex | BigNumber | Fraction, SparseMatrix":t.referToSelf((e=>(t,n)=>i(t,0)?u(n.size(),n.storage()):f(n,t,e,!0))),"number | Complex | BigNumber | Fraction, DenseMatrix":t.referToSelf((e=>(t,n)=>i(t,0)?u(n.size(),n.storage()):c(n,t,e,!0))),"number | Complex | BigNumber | Fraction, Array":t.referToSelf((e=>(t,n)=>c(r(n),t,e,!0).valueOf()))})})),Iu=Rt("matAlgo05xSfSf",["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return function(e,r,i){var u=e._values,a=e._index,o=e._ptr,s=e._size,f=e._datatype||void 0===e._data?e._datatype:e.getDataType(),c=r._values,l=r._index,p=r._ptr,h=r._size,m=r._datatype||void 0===r._data?r._datatype:r.getDataType();if(s.length!==h.length)throw new Rn(s.length,h.length);if(s[0]!==h[0]||s[1]!==h[1])throw new RangeError("Dimension mismatch. Matrix A ("+s+") must match Matrix B ("+h+")");var d,D=s[0],v=s[1],g=n,y=0,E=i;"string"==typeof f&&f===m&&"mixed"!==f&&(d=f,g=t.find(n,[d,d]),y=t.convert(0,d),E=t.find(i,[d,d]));var w,x,b,A,F=u&&c?[]:void 0,C=[],N=[],M=F?[]:void 0,S=F?[]:void 0,B=[],_=[];for(x=0;x<v;x++){N[x]=C.length;var T=x+1;for(b=o[x],A=o[x+1];b<A;b++)w=a[b],C.push(w),B[w]=T,M&&(M[w]=u[b]);for(b=p[x],A=p[x+1];b<A;b++)B[w=l[b]]!==T&&C.push(w),_[w]=T,S&&(S[w]=c[b]);if(F)for(b=N[x];b<C.length;){var O=B[w=C[b]],R=_[w];if(O===T||R===T){var I=E(O===T?M[w]:y,R===T?S[w]:y);g(I,y)?C.splice(b,1):(F.push(I),b++)}}}return N[v]=C.length,e.createSparseMatrix({values:F,index:C,ptr:N,size:[D,v],datatype:f===e._datatype&&m===r._datatype?d:void 0})}})),zu="subtract",Uu=Rt(zu,["typed","matrix","equalScalar","subtractScalar","unaryMinus","DenseMatrix","concat"],(e=>{var{typed:t,matrix:n,equalScalar:r,subtractScalar:i,unaryMinus:u,DenseMatrix:a,concat:o}=e,s=Su({typed:t}),f=mu({typed:t}),c=Iu({typed:t,equalScalar:r}),l=Bu({typed:t,DenseMatrix:a}),p=vu({typed:t,DenseMatrix:a}),h=wu({typed:t,matrix:n,concat:o});return t(zu,{"any, any":i},h({elop:i,SS:c,DS:s,SD:f,Ss:p,sS:l}))})),Pu="unequal",Lu=(Rt(Pu,["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return t(Pu,{"any, any":function(e,t){return null===e?null!==t:null===t?null!==e:void 0===e?void 0!==t:void 0===t?void 0!==e:!n(e,t)}})})),Rt("matAlgo04xSidSid",["typed","equalScalar"],(e=>{var{typed:t,equalScalar:n}=e;return function(e,r,i){var u=e._values,a=e._index,o=e._ptr,s=e._size,f=e._datatype||void 0===e._data?e._datatype:e.getDataType(),c=r._values,l=r._index,p=r._ptr,h=r._size,m=r._datatype||void 0===r._data?r._datatype:r.getDataType();if(s.length!==h.length)throw new Rn(s.length,h.length);if(s[0]!==h[0]||s[1]!==h[1])throw new RangeError("Dimension mismatch. Matrix A ("+s+") must match Matrix B ("+h+")");var d,D=s[0],v=s[1],g=n,y=0,E=i;"string"==typeof f&&f===m&&"mixed"!==f&&(d=f,g=t.find(n,[d,d]),y=t.convert(0,d),E=t.find(i,[d,d]));var w,x,b,A,F,C=u&&c?[]:void 0,N=[],M=[],S=u&&c?[]:void 0,B=u&&c?[]:void 0,_=[],T=[];for(x=0;x<v;x++){M[x]=N.length;var O=x+1;for(A=o[x],F=o[x+1],b=A;b<F;b++)w=a[b],N.push(w),_[w]=O,S&&(S[w]=u[b]);for(A=p[x],F=p[x+1],b=A;b<F;b++)if(_[w=l[b]]===O){if(S){var R=E(S[w],c[b]);g(R,y)?_[w]=null:S[w]=R}}else N.push(w),T[w]=O,B&&(B[w]=c[b]);if(S&&B)for(b=M[x];b<N.length;)_[w=N[b]]===O?(C[b]=S[w],b++):T[w]===O?(C[b]=B[w],b++):N.splice(b,1)}return M[v]=N.length,e.createSparseMatrix({values:C,index:N,ptr:M,size:[D,v],datatype:f===e._datatype&&m===r._datatype?d:void 0})}}))),ku=Rt("add",["typed","matrix","addScalar","equalScalar","DenseMatrix","SparseMatrix","concat"],(e=>{var{typed:t,matrix:n,addScalar:r,equalScalar:i,DenseMatrix:u,SparseMatrix:a,concat:o}=e,s=Su({typed:t}),f=Lu({typed:t,equalScalar:i}),c=Bu({typed:t,DenseMatrix:u}),l=wu({typed:t,matrix:n,concat:o});return t("add",{"any, any":r,"any, any, ...any":t.referToSelf((e=>(t,n,r)=>{for(var i=e(t,n),u=0;u<r.length;u++)i=e(i,r[u]);return i}))},l({elop:r,DS:s,SS:f,Ss:c}))}));On.signature="any, any";var qu=Rt("dot",["typed","addScalar","multiplyScalar","conj","size"],(e=>{var{typed:t,addScalar:n,multiplyScalar:r,conj:i,size:u}=e;return t("dot",{"Array | DenseMatrix, Array | DenseMatrix":function(e,u){var s=a(e,u),f=Xe(e)?e._data:e,c=Xe(e)?e._datatype||e.getDataType():void 0,l=Xe(u)?u._data:u,p=Xe(u)?u._datatype||u.getDataType():void 0,h=2===o(e).length,m=2===o(u).length,d=n,D=r;if(c&&p&&c===p&&"string"==typeof c&&"mixed"!==c){var v=c;d=t.find(n,[v,v]),D=t.find(r,[v,v])}if(!h&&!m){for(var g=D(i(f[0]),l[0]),y=1;y<s;y++)g=d(g,D(i(f[y]),l[y]));return g}if(!h&&m){for(var E=D(i(f[0]),l[0][0]),w=1;w<s;w++)E=d(E,D(i(f[w]),l[w][0]));return E}if(h&&!m){for(var x=D(i(f[0][0]),l[0]),b=1;b<s;b++)x=d(x,D(i(f[b][0]),l[b]));return x}if(h&&m){for(var A=D(i(f[0][0]),l[0][0]),F=1;F<s;F++)A=d(A,D(i(f[F][0]),l[F][0]));return A}},"SparseMatrix, SparseMatrix":function(e,t){a(e,t);var i=e._index,u=e._values,o=t._index,s=t._values,f=0,c=n,l=r,p=0,h=0;for(;p<i.length&&h<o.length;){var m=i[p],d=o[h];m<d?p++:m>d?h++:m===d&&(f=c(f,l(u[p],s[h])),p++,h++)}return f}});function a(e,t){var n,r,i=o(e),u=o(t);if(1===i.length)n=i[0];else{if(2!==i.length||1!==i[1])throw new RangeError("Expected a column vector, instead got a matrix of size ("+i.join(", ")+")");n=i[0]}if(1===u.length)r=u[0];else{if(2!==u.length||1!==u[1])throw new RangeError("Expected a column vector, instead got a matrix of size ("+u.join(", ")+")");r=u[0]}if(n!==r)throw new RangeError("Vectors must have equal length ("+n+" != "+r+")");if(0===n)throw new RangeError("Cannot calculate the dot product of empty vectors");return n}function o(e){return Xe(e)?e.size():u(e)}})),ju="floor",Hu=["typed","config","round","matrix","equalScalar","zeros","DenseMatrix"],Gu=Rt(ju,["typed","config","round"],(e=>{var{typed:t,config:n,round:r}=e;return t(ju,{number:function(e){return on(e,r(e),n.epsilon)?r(e):Math.floor(e)},"number, number":function(e,t){if(on(e,r(e,t),n.epsilon))return r(e,t);var[i,u]="".concat(e,"e").split("e"),a=Math.floor(Number("".concat(i,"e").concat(Number(u)+t)));return[i,u]="".concat(a,"e").split("e"),Number("".concat(i,"e").concat(Number(u)-t))}})})),Vu=Rt(ju,Hu,(e=>{var{typed:t,config:n,round:r,matrix:i,equalScalar:u,zeros:a,DenseMatrix:o}=e,s=Du({typed:t,equalScalar:u}),f=vu({typed:t,DenseMatrix:o}),c=yu({typed:t}),l=Gu({typed:t,config:n,round:r});return t("floor",{number:l.signatures.number,"number,number":l.signatures["number,number"],Complex:function(e){return e.floor()},"Complex, number":function(e,t){return e.floor(t)},"Complex, BigNumber":function(e,t){return e.floor(t.toNumber())},BigNumber:function(e){return vi(e,r(e),n.epsilon)?r(e):e.floor()},"BigNumber, BigNumber":function(e,t){return vi(e,r(e,t),n.epsilon)?r(e,t):e.toDecimalPlaces(t.toNumber(),He.ROUND_FLOOR)},Fraction:function(e){return e.floor()},"Fraction, number":function(e,t){return e.floor(t)},"Fraction, BigNumber":function(e,t){return e.floor(t.toNumber())},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0))),"Array, number | BigNumber":t.referToSelf((e=>(t,n)=>vr(t,(t=>e(t,n)),!0))),"SparseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>s(t,n,e,!1))),"DenseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>c(t,n,e,!1))),"number | Complex | Fraction | BigNumber, Array":t.referToSelf((e=>(t,n)=>c(i(n),t,e,!0).valueOf())),"number | Complex | Fraction | BigNumber, Matrix":t.referToSelf((e=>(t,n)=>u(t,0)?a(n.size(),n.storage()):"dense"===n.storage()?c(n,t,e,!0):f(n,t,e,!0)))})})),Yu="number | BigNumber | Fraction | Matrix | Array";"".concat(Yu,", ").concat(Yu,", ...").concat(Yu);var Zu="multiply",Wu=Rt(Zu,["typed","matrix","addScalar","multiplyScalar","equalScalar","dot"],(e=>{var{typed:t,matrix:n,addScalar:r,multiplyScalar:i,equalScalar:u,dot:a}=e,o=Du({typed:t,equalScalar:u}),s=yu({typed:t});function f(e,t){switch(e.length){case 1:switch(t.length){case 1:if(e[0]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Vectors must have the same length");break;case 2:if(e[0]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Vector length ("+e[0]+") must match Matrix rows ("+t[0]+")");break;default:throw new Error("Can only multiply a 1 or 2 dimensional matrix (Matrix B has "+t.length+" dimensions)")}break;case 2:switch(t.length){case 1:if(e[1]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Matrix columns ("+e[1]+") must match Vector length ("+t[0]+")");break;case 2:if(e[1]!==t[0])throw new RangeError("Dimension mismatch in multiplication. Matrix A columns ("+e[1]+") must match Matrix B rows ("+t[0]+")");break;default:throw new Error("Can only multiply a 1 or 2 dimensional matrix (Matrix B has "+t.length+" dimensions)")}break;default:throw new Error("Can only multiply a 1 or 2 dimensional matrix (Matrix A has "+e.length+" dimensions)")}}function c(e,n){if("dense"!==n.storage())throw new Error("Support for SparseMatrix not implemented");return function(e,n){var u,a=e._data,o=e._size,s=e._datatype||e.getDataType(),f=n._data,c=n._size,l=n._datatype||n.getDataType(),p=o[0],h=c[1],m=r,d=i;s&&l&&s===l&&"string"==typeof s&&"mixed"!==s&&(u=s,m=t.find(r,[u,u]),d=t.find(i,[u,u]));for(var D=[],v=0;v<h;v++){for(var g=d(a[0],f[0][v]),y=1;y<p;y++)g=m(g,d(a[y],f[y][v]));D[v]=g}return e.createDenseMatrix({data:D,size:[h],datatype:s===e._datatype&&l===n._datatype?u:void 0})}(e,n)}var l=t("_multiplyMatrixVector",{"DenseMatrix, any":function(e,n){var u,a=e._data,o=e._size,s=e._datatype||e.getDataType(),f=n._data,c=n._datatype||n.getDataType(),l=o[0],p=o[1],h=r,m=i;s&&c&&s===c&&"string"==typeof s&&"mixed"!==s&&(u=s,h=t.find(r,[u,u]),m=t.find(i,[u,u]));for(var d=[],D=0;D<l;D++){for(var v=a[D],g=m(v[0],f[0]),y=1;y<p;y++)g=h(g,m(v[y],f[y]));d[D]=g}return e.createDenseMatrix({data:d,size:[l],datatype:s===e._datatype&&c===n._datatype?u:void 0})},"SparseMatrix, any":function(e,n){var a=e._values,o=e._index,s=e._ptr,f=e._datatype||void 0===e._data?e._datatype:e.getDataType();if(!a)throw new Error("Cannot multiply Pattern only Matrix times Dense Matrix");var c,l=n._data,p=n._datatype||n.getDataType(),h=e._size[0],m=n._size[0],d=[],D=[],v=[],g=r,y=i,E=u,w=0;f&&p&&f===p&&"string"==typeof f&&"mixed"!==f&&(c=f,g=t.find(r,[c,c]),y=t.find(i,[c,c]),E=t.find(u,[c,c]),w=t.convert(0,c));var x=[],b=[];v[0]=0;for(var A=0;A<m;A++){var F=l[A];if(!E(F,w))for(var C=s[A],N=s[A+1],M=C;M<N;M++){var S=o[M];b[S]?x[S]=g(x[S],y(F,a[M])):(b[S]=!0,D.push(S),x[S]=y(F,a[M]))}}for(var B=D.length,_=0;_<B;_++){var T=D[_];d[_]=x[T]}return v[1]=D.length,e.createSparseMatrix({values:d,index:D,ptr:v,size:[h,1],datatype:f===e._datatype&&p===n._datatype?c:void 0})}}),p=t("_multiplyMatrixMatrix",{"DenseMatrix, DenseMatrix":function(e,n){var u,a=e._data,o=e._size,s=e._datatype||e.getDataType(),f=n._data,c=n._size,l=n._datatype||n.getDataType(),p=o[0],h=o[1],m=c[1],d=r,D=i;s&&l&&s===l&&"string"==typeof s&&"mixed"!==s&&"mixed"!==s&&(u=s,d=t.find(r,[u,u]),D=t.find(i,[u,u]));for(var v=[],g=0;g<p;g++){var y=a[g];v[g]=[];for(var E=0;E<m;E++){for(var w=D(y[0],f[0][E]),x=1;x<h;x++)w=d(w,D(y[x],f[x][E]));v[g][E]=w}}return e.createDenseMatrix({data:v,size:[p,m],datatype:s===e._datatype&&l===n._datatype?u:void 0})},"DenseMatrix, SparseMatrix":function(e,n){var a=e._data,o=e._size,s=e._datatype||e.getDataType(),f=n._values,c=n._index,l=n._ptr,p=n._size,h=n._datatype||void 0===n._data?n._datatype:n.getDataType();if(!f)throw new Error("Cannot multiply Dense Matrix times Pattern only Matrix");var m,d=o[0],D=p[1],v=r,g=i,y=u,E=0;s&&h&&s===h&&"string"==typeof s&&"mixed"!==s&&(m=s,v=t.find(r,[m,m]),g=t.find(i,[m,m]),y=t.find(u,[m,m]),E=t.convert(0,m));for(var w=[],x=[],b=[],A=n.createSparseMatrix({values:w,index:x,ptr:b,size:[d,D],datatype:s===e._datatype&&h===n._datatype?m:void 0}),F=0;F<D;F++){b[F]=x.length;var C=l[F],N=l[F+1];if(N>C)for(var M=0,S=0;S<d;S++){for(var B=S+1,_=void 0,T=C;T<N;T++){var O=c[T];M!==B?(_=g(a[S][O],f[T]),M=B):_=v(_,g(a[S][O],f[T]))}M!==B||y(_,E)||(x.push(S),w.push(_))}}return b[D]=x.length,A},"SparseMatrix, DenseMatrix":function(e,n){var a=e._values,o=e._index,s=e._ptr,f=e._datatype||void 0===e._data?e._datatype:e.getDataType();if(!a)throw new Error("Cannot multiply Pattern only Matrix times Dense Matrix");var c,l=n._data,p=n._datatype||n.getDataType(),h=e._size[0],m=n._size[0],d=n._size[1],D=r,v=i,g=u,y=0;f&&p&&f===p&&"string"==typeof f&&"mixed"!==f&&(c=f,D=t.find(r,[c,c]),v=t.find(i,[c,c]),g=t.find(u,[c,c]),y=t.convert(0,c));for(var E=[],w=[],x=[],b=e.createSparseMatrix({values:E,index:w,ptr:x,size:[h,d],datatype:f===e._datatype&&p===n._datatype?c:void 0}),A=[],F=[],C=0;C<d;C++){x[C]=w.length;for(var N=C+1,M=0;M<m;M++){var S=l[M][C];if(!g(S,y))for(var B=s[M],_=s[M+1],T=B;T<_;T++){var O=o[T];F[O]!==N?(F[O]=N,w.push(O),A[O]=v(S,a[T])):A[O]=D(A[O],v(S,a[T]))}}for(var R=x[C],I=w.length,z=R;z<I;z++){var U=w[z];E[z]=A[U]}}return x[d]=w.length,b},"SparseMatrix, SparseMatrix":function(e,n){var u,a=e._values,o=e._index,s=e._ptr,f=e._datatype||void 0===e._data?e._datatype:e.getDataType(),c=n._values,l=n._index,p=n._ptr,h=n._datatype||void 0===n._data?n._datatype:n.getDataType(),m=e._size[0],d=n._size[1],D=a&&c,v=r,g=i;f&&h&&f===h&&"string"==typeof f&&"mixed"!==f&&(u=f,v=t.find(r,[u,u]),g=t.find(i,[u,u]));for(var y,E,w,x,b,A,F,C,N=D?[]:void 0,M=[],S=[],B=e.createSparseMatrix({values:N,index:M,ptr:S,size:[m,d],datatype:f===e._datatype&&h===n._datatype?u:void 0}),_=D?[]:void 0,T=[],O=0;O<d;O++){S[O]=M.length;var R=O+1;for(b=p[O],A=p[O+1],x=b;x<A;x++)if(C=l[x],D)for(E=s[C],w=s[C+1],y=E;y<w;y++)T[F=o[y]]!==R?(T[F]=R,M.push(F),_[F]=g(c[x],a[y])):_[F]=v(_[F],g(c[x],a[y]));else for(E=s[C],w=s[C+1],y=E;y<w;y++)T[F=o[y]]!==R&&(T[F]=R,M.push(F));if(D)for(var I=S[O],z=M.length,U=I;U<z;U++){var P=M[U];N[U]=_[P]}}return S[d]=M.length,B}});return t(Zu,i,{"Array, Array":t.referTo("Matrix, Matrix",(e=>(t,r)=>{f(zn(t),zn(r));var i=e(n(t),n(r));return Xe(i)?i.valueOf():i})),"Matrix, Matrix":function(e,t){var n=e.size(),r=t.size();return f(n,r),1===n.length?1===r.length?function(e,t,n){if(0===n)throw new Error("Cannot multiply two empty vectors");return a(e,t)}(e,t,n[0]):c(e,t):1===r.length?l(e,t):p(e,t)},"Matrix, Array":t.referTo("Matrix,Matrix",(e=>(t,r)=>e(t,n(r)))),"Array, Matrix":t.referToSelf((e=>(t,r)=>e(n(t,r.storage()),r))),"SparseMatrix, any":function(e,t){return o(e,t,i,!1)},"DenseMatrix, any":function(e,t){return s(e,t,i,!1)},"any, SparseMatrix":function(e,t){return o(t,e,i,!0)},"any, DenseMatrix":function(e,t){return s(t,e,i,!0)},"Array, any":function(e,t){return s(n(e),t,i,!1).valueOf()},"any, Array":function(e,t){return s(n(t),e,i,!0).valueOf()},"any, any":i,"any, any, ...any":t.referToSelf((e=>(t,n,r)=>{for(var i=e(t,n),u=0;u<r.length;u++)i=e(i,r[u]);return i}))})}));var $u="ceil",Ju=["typed","config","round","matrix","equalScalar","zeros","DenseMatrix"],Xu=Rt($u,["typed","config","round"],(e=>{var{typed:t,config:n,round:r}=e;return t($u,{number:function(e){return on(e,r(e),n.epsilon)?r(e):Math.ceil(e)},"number, number":function(e,t){if(on(e,r(e,t),n.epsilon))return r(e,t);var[i,u]="".concat(e,"e").split("e"),a=Math.ceil(Number("".concat(i,"e").concat(Number(u)+t)));return[i,u]="".concat(a,"e").split("e"),Number("".concat(i,"e").concat(Number(u)-t))}})})),Qu=Rt($u,Ju,(e=>{var{typed:t,config:n,round:r,matrix:i,equalScalar:u,zeros:a,DenseMatrix:o}=e,s=Du({typed:t,equalScalar:u}),f=vu({typed:t,DenseMatrix:o}),c=yu({typed:t}),l=Xu({typed:t,config:n,round:r});return t("ceil",{number:l.signatures.number,"number,number":l.signatures["number,number"],Complex:function(e){return e.ceil()},"Complex, number":function(e,t){return e.ceil(t)},"Complex, BigNumber":function(e,t){return e.ceil(t.toNumber())},BigNumber:function(e){return vi(e,r(e),n.epsilon)?r(e):e.ceil()},"BigNumber, BigNumber":function(e,t){return vi(e,r(e,t),n.epsilon)?r(e,t):e.toDecimalPlaces(t.toNumber(),He.ROUND_CEIL)},Fraction:function(e){return e.ceil()},"Fraction, number":function(e,t){return e.ceil(t)},"Fraction, BigNumber":function(e,t){return e.ceil(t.toNumber())},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0))),"Array, number | BigNumber":t.referToSelf((e=>(t,n)=>vr(t,(t=>e(t,n)),!0))),"SparseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>s(t,n,e,!1))),"DenseMatrix, number | BigNumber":t.referToSelf((e=>(t,n)=>c(t,n,e,!1))),"number | Complex | Fraction | BigNumber, Array":t.referToSelf((e=>(t,n)=>c(i(n),t,e,!0).valueOf())),"number | Complex | Fraction | BigNumber, Matrix":t.referToSelf((e=>(t,n)=>u(t,0)?a(n.size(),n.storage()):"dense"===n.storage()?c(n,t,e,!0):f(n,t,e,!0)))})}));n(1880);var Ku=Rt("det",["typed","matrix","subtractScalar","multiply","divideScalar","isZero","unaryMinus"],(e=>{var{typed:t,matrix:n,subtractScalar:r,multiply:i,divideScalar:u,isZero:a,unaryMinus:o}=e;return t("det",{any:function(e){return Bt(e)},"Array | Matrix":function(e){var t;switch((t=Xe(e)?e.size():Array.isArray(e)?(e=n(e)).size():[]).length){case 0:return Bt(e);case 1:if(1===t[0])return Bt(e.valueOf()[0]);if(0===t[0])return 1;throw new RangeError("Matrix must be square (size: "+Sn(t)+")");case 2:var s=t[0],f=t[1];if(s===f)return function(e,t){if(1===t)return Bt(e[0][0]);if(2===t)return r(i(e[0][0],e[1][1]),i(e[1][0],e[0][1]));for(var n=!1,s=new Array(t).fill(0).map(((e,t)=>t)),f=0;f<t;f++){var c=s[f];if(a(e[c][f])){var l=void 0;for(l=f+1;l<t;l++)if(!a(e[s[l]][f])){c=s[l],s[l]=s[f],s[f]=c,n=!n;break}if(l===t)return e[c][f]}for(var p=e[c][f],h=0===f?1:e[s[f-1]][f-1],m=f+1;m<t;m++)for(var d=s[m],D=f+1;D<t;D++)e[d][D]=u(r(i(e[d][D],p),i(e[d][f],e[c][D])),h)}var v=e[s[t-1]][t-1];return n?o(v):v}(e.clone().valueOf(),s);if(0===f)return 1;throw new RangeError("Matrix must be square (size: "+Sn(t)+")");default:throw new RangeError("Matrix must be two dimensional (size: "+Sn(t)+")")}}})})),ea="fix",ta=["typed","Complex","matrix","ceil","floor","equalScalar","zeros","DenseMatrix"],na=Rt(ea,["typed","ceil","floor"],(e=>{var{typed:t,ceil:n,floor:r}=e;return t(ea,{number:function(e){return e>0?r(e):n(e)},"number, number":function(e,t){return e>0?r(e,t):n(e,t)}})})),ra=Rt(ea,ta,(e=>{var{typed:t,Complex:n,matrix:r,ceil:i,floor:u,equalScalar:a,zeros:o,DenseMatrix:s}=e,f=vu({typed:t,DenseMatrix:s}),c=yu({typed:t}),l=na({typed:t,ceil:i,floor:u});return t("fix",{number:l.signatures.number,"number, number | BigNumber":l.signatures["number,number"],Complex:function(e){return new n(e.re>0?Math.floor(e.re):Math.ceil(e.re),e.im>0?Math.floor(e.im):Math.ceil(e.im))},"Complex, number":function(e,t){return new n(e.re>0?u(e.re,t):i(e.re,t),e.im>0?u(e.im,t):i(e.im,t))},"Complex, BigNumber":function(e,t){var r=t.toNumber();return new n(e.re>0?u(e.re,r):i(e.re,r),e.im>0?u(e.im,r):i(e.im,r))},BigNumber:function(e){return e.isNegative()?i(e):u(e)},"BigNumber, number | BigNumber":function(e,t){return e.isNegative()?i(e,t):u(e,t)},Fraction:function(e){return e.s<0?e.ceil():e.floor()},"Fraction, number | BigNumber":function(e,t){return e.s<0?i(e,t):u(e,t)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e,!0))),"Array | Matrix, number | BigNumber":t.referToSelf((e=>(t,n)=>vr(t,(t=>e(t,n)),!0))),"number | Complex | Fraction | BigNumber, Array":t.referToSelf((e=>(t,n)=>c(r(n),t,e,!0).valueOf())),"number | Complex | Fraction | BigNumber, Matrix":t.referToSelf((e=>(t,n)=>a(t,0)?o(n.size(),n.storage()):"dense"===n.storage()?c(n,t,e,!0):f(n,t,e,!0)))})})),ia=Rt("inv",["typed","matrix","divideScalar","addScalar","multiply","unaryMinus","det","identity","abs"],(e=>{var{typed:t,matrix:n,divideScalar:r,addScalar:i,multiply:u,unaryMinus:a,det:o,identity:s,abs:f}=e;return t("inv",{"Array | Matrix":function(e){var t=Xe(e)?e.size():zn(e);switch(t.length){case 1:if(1===t[0])return Xe(e)?n([r(1,e.valueOf()[0])]):[r(1,e[0])];throw new RangeError("Matrix must be square (size: "+Sn(t)+")");case 2:var i=t[0],u=t[1];if(i===u)return Xe(e)?n(c(e.valueOf(),i,u),e.storage()):c(e,i,u);throw new RangeError("Matrix must be square (size: "+Sn(t)+")");default:throw new RangeError("Matrix must be two dimensional (size: "+Sn(t)+")")}},any:function(e){return r(1,e)}});function c(e,t,n){var c,l,p,h,m;if(1===t){if(0===(h=e[0][0]))throw Error("Cannot calculate inverse, determinant is zero");return[[r(1,h)]]}if(2===t){var d=o(e);if(0===d)throw Error("Cannot calculate inverse, determinant is zero");return[[r(e[1][1],d),r(a(e[0][1]),d)],[r(a(e[1][0]),d),r(e[0][0],d)]]}var D=e.concat();for(c=0;c<t;c++)D[c]=D[c].concat();for(var v=s(t).valueOf(),g=0;g<n;g++){var y=f(D[g][g]),E=g;for(c=g+1;c<t;)f(D[c][g])>y&&(y=f(D[c][g]),E=c),c++;if(0===y)throw Error("Cannot calculate inverse, determinant is zero");(c=E)!==g&&(m=D[g],D[g]=D[c],D[c]=m,m=v[g],v[g]=v[c],v[c]=m);var w=D[g],x=v[g];for(c=0;c<t;c++){var b=D[c],A=v[c];if(c!==g){if(0!==b[g]){for(p=r(a(b[g]),w[g]),l=g;l<n;l++)b[l]=i(b[l],u(p,w[l]));for(l=0;l<n;l++)A[l]=i(A[l],u(p,x[l]))}}else{for(p=w[g],l=g;l<n;l++)b[l]=r(b[l],p);for(l=0;l<n;l++)A[l]=r(A[l],p)}}}return v}})),ua=Rt("pow",["typed","config","identity","multiply","matrix","inv","fraction","number","Complex"],(e=>{var{typed:t,config:n,identity:r,multiply:i,matrix:u,inv:a,number:o,fraction:s,Complex:f}=e;return t("pow",{"number, number":c,"Complex, Complex":function(e,t){return e.pow(t)},"BigNumber, BigNumber":function(e,t){return t.isInteger()||e>=0||n.predictable?e.pow(t):new f(e.toNumber(),0).pow(t.toNumber(),0)},"Fraction, Fraction":function(e,t){var r=e.pow(t);if(null!=r)return r;if(n.predictable)throw new Error("Result of pow is non-rational and cannot be expressed as a fraction");return c(e.valueOf(),t.valueOf())},"Array, number":l,"Array, BigNumber":function(e,t){return l(e,t.toNumber())},"Matrix, number":p,"Matrix, BigNumber":function(e,t){return p(e,t.toNumber())},"Unit, number | BigNumber":function(e,t){return e.pow(t)}});function c(e,t){if(n.predictable&&!Vt(t)&&e<0)try{var r=s(t),i=o(r);if((t===i||Math.abs((t-i)/t)<1e-14)&&r.d%2==1)return(r.n%2==0?1:-1)*Math.pow(-e,t)}catch(e){}return n.predictable&&(e<-1&&t===1/0||e>-1&&e<0&&t===-1/0)?NaN:Vt(t)||e>=0||n.predictable?zr(e,t):e*e<1&&t===1/0||e*e>1&&t===-1/0?0:new f(e,0).pow(t,0)}function l(e,t){if(!Vt(t))throw new TypeError("For A^b, b must be an integer (value is "+t+")");var n=zn(e);if(2!==n.length)throw new Error("For A^b, A must be 2 dimensional (A has "+n.length+" dimensions)");if(n[0]!==n[1])throw new Error("For A^b, A must be square (size is "+n[0]+"x"+n[1]+")");if(t<0)try{return l(a(e),-t)}catch(e){if("Cannot calculate inverse, determinant is zero"===e.message)throw new TypeError("For A^b, when A is not invertible, b must be a positive integer (value is "+t+")");throw e}for(var u=r(n[0]).valueOf(),o=e;t>=1;)1&~t||(u=i(o,u)),t>>=1,o=i(o,o);return u}function p(e,t){return u(l(e.valueOf(),t))}}));function aa(e){return aa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},aa(e)}function oa(e){var t=function(e,t){if("object"!=aa(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=aa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==aa(t)?t:t+""}function sa(e,t,n){return(t=oa(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function fa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ca(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fa(Object(n),!0).forEach((function(t){sa(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var la=Rt("Unit",["?on","config","addScalar","subtractScalar","multiplyScalar","divideScalar","pow","abs","fix","round","equal","isNumeric","format","number","Complex","BigNumber","Fraction"],(e=>{var n,r,i,{on:u,config:a,addScalar:o,subtractScalar:s,multiplyScalar:f,divideScalar:c,pow:l,abs:p,fix:h,round:m,equal:d,isNumeric:D,format:v,number:g,Complex:y,BigNumber:E,Fraction:w}=e,x=g;function b(e,n){if(!(this instanceof b))throw new Error("Constructor must be called with the new operator");if(null!=e&&!D(e)&&!Ye(e))throw new TypeError("First parameter in Unit constructor must be number, BigNumber, Fraction, Complex, or undefined");if(this.fixPrefix=!1,this.skipAutomaticSimplification=!0,void 0===n)this.units=[],this.dimensions=R.map((e=>0));else if("string"==typeof n){var r=b.parse(n);this.units=r.units,this.dimensions=r.dimensions}else{if(!We(n)||null!==n.value)throw new TypeError("Second parameter in Unit constructor must be a string or valueless Unit");this.fixPrefix=n.fixPrefix,this.skipAutomaticSimplification=n.skipAutomaticSimplification,this.dimensions=n.dimensions.slice(0),this.units=n.units.map((e=>t({},e)))}this.value=this._normalize(e)}function A(){for(;" "===i||"\t"===i;)C()}function F(e){return e>="0"&&e<="9"}function C(){r++,i=n.charAt(r)}function N(e){r=e,i=n.charAt(r)}function M(){var e="",t=r;if("+"===i?C():"-"===i&&(e+=i,C()),!function(e){return e>="0"&&e<="9"||"."===e}(i))return N(t),null;if("."===i){if(e+=i,C(),!F(i))return N(t),null}else{for(;F(i);)e+=i,C();"."===i&&(e+=i,C())}for(;F(i);)e+=i,C();if("E"===i||"e"===i){var n="",u=r;if(n+=i,C(),"+"!==i&&"-"!==i||(n+=i,C()),!F(i))return N(u),e;for(e+=n;F(i);)e+=i,C()}return e}function S(){for(var e="";F(i)||b.isValidAlpha(i);)e+=i,C();var t=e.charAt(0);return b.isValidAlpha(t)?e:null}function B(e){return i===e?(C(),e):null}Object.defineProperty(b,"name",{value:"Unit"}),b.prototype.constructor=b,b.prototype.type="Unit",b.prototype.isUnit=!0,b.parse=function(e,t){if(t=t||{},r=-1,i="","string"!=typeof(n=e))throw new TypeError("Invalid argument in Unit.parse, string expected");var u=new b;u.units=[];var o=1,s=!1;C(),A();var f=M(),c=null;if(f){if("BigNumber"===a.number)c=new E(f);else if("Fraction"===a.number)try{c=new w(f)}catch(e){c=parseFloat(f)}else c=parseFloat(f);A(),B("*")?(o=1,s=!0):B("/")&&(o=-1,s=!0)}for(var l=[],p=1;;){for(A();"("===i;)l.push(o),p*=o,o=1,C(),A();var h=void 0;if(!i)break;var m=i;if(null===(h=S()))throw new SyntaxError('Unexpected "'+m+'" in "'+n+'" at index '+r.toString());var d=_(h);if(null===d)throw new SyntaxError('Unit "'+h+'" not found.');var D=o*p;if(A(),B("^")){A();var v=M();if(null===v)throw new SyntaxError('In "'+e+'", "^" must be followed by a floating-point number');D*=v}u.units.push({unit:d.unit,prefix:d.prefix,power:D});for(var g=0;g<R.length;g++)u.dimensions[g]+=(d.unit.dimensions[g]||0)*D;for(A();")"===i;){if(0===l.length)throw new SyntaxError('Unmatched ")" in "'+n+'" at index '+r.toString());p/=l.pop(),C(),A()}if(s=!1,B("*")?(o=1,s=!0):B("/")?(o=-1,s=!0):o=1,d.unit.base){var y=d.unit.base.key;q.auto[y]={unit:d.unit,prefix:d.prefix}}}if(A(),i)throw new SyntaxError('Could not parse: "'+e+'"');if(s)throw new SyntaxError('Trailing characters: "'+e+'"');if(0!==l.length)throw new SyntaxError('Unmatched "(" in "'+n+'"');if(0===u.units.length&&!t.allowNoUnits)throw new SyntaxError('"'+e+'" contains no units');return u.value=void 0!==c?u._normalize(c):null,u},b.prototype.clone=function(){var e=new b;e.fixPrefix=this.fixPrefix,e.skipAutomaticSimplification=this.skipAutomaticSimplification,e.value=Bt(this.value),e.dimensions=this.dimensions.slice(0),e.units=[];for(var t=0;t<this.units.length;t++)for(var n in e.units[t]={},this.units[t])Ot(this.units[t],n)&&(e.units[t][n]=this.units[t][n]);return e},b.prototype.valueType=function(){return St(this.value)},b.prototype._isDerived=function(){return 0!==this.units.length&&(this.units.length>1||Math.abs(this.units[0].power-1)>1e-15)},b.prototype._normalize=function(e){if(null==e||0===this.units.length)return e;for(var t=e,n=b._getNumberConverter(St(e)),r=0;r<this.units.length;r++){var i=n(this.units[r].unit.value),u=n(this.units[r].prefix.value),a=n(this.units[r].power);t=f(t,l(f(i,u),a))}return t},b.prototype._denormalize=function(e,t){if(null==e||0===this.units.length)return e;for(var n=e,r=b._getNumberConverter(St(e)),i=0;i<this.units.length;i++){var u=r(this.units[i].unit.value),a=r(this.units[i].prefix.value),o=r(this.units[i].power);n=c(n,l(f(u,a),o))}return n};var _=Dn((e=>{if(Ot(P,e)){var t=P[e];return{unit:t,prefix:t.prefixes[""]}}for(var n in P)if(Ot(P,n)&&Mn(e,n)){var r=P[n],i=e.length-n.length,u=e.substring(0,i),a=Ot(r.prefixes,u)?r.prefixes[u]:void 0;if(void 0!==a)return{unit:r,prefix:a}}return null}),{hasher:e=>e[0],limit:100});function T(e){return e.equalBase(I.NONE)&&null!==e.value&&!a.predictable?e.value:e}b.isValuelessUnit=function(e){return null!==_(e)},b.prototype.hasBase=function(e){if("string"==typeof e&&(e=I[e]),!e)return!1;for(var t=0;t<R.length;t++)if(Math.abs((this.dimensions[t]||0)-(e.dimensions[t]||0))>1e-12)return!1;return!0},b.prototype.equalBase=function(e){for(var t=0;t<R.length;t++)if(Math.abs((this.dimensions[t]||0)-(e.dimensions[t]||0))>1e-12)return!1;return!0},b.prototype.equals=function(e){return this.equalBase(e)&&d(this.value,e.value)},b.prototype.multiply=function(e){for(var t=this.clone(),n=We(e)?e:new b(e),r=0;r<R.length;r++)t.dimensions[r]=(this.dimensions[r]||0)+(n.dimensions[r]||0);for(var i=0;i<n.units.length;i++){var u=ca({},n.units[i]);t.units.push(u)}if(null!==this.value||null!==n.value){var a=null===this.value?this._normalize(1):this.value,o=null===n.value?n._normalize(1):n.value;t.value=f(a,o)}else t.value=null;return We(e)&&(t.skipAutomaticSimplification=!1),T(t)},b.prototype.divideInto=function(e){return new b(e).divide(this)},b.prototype.divide=function(e){for(var t=this.clone(),n=We(e)?e:new b(e),r=0;r<R.length;r++)t.dimensions[r]=(this.dimensions[r]||0)-(n.dimensions[r]||0);for(var i=0;i<n.units.length;i++){var u=ca(ca({},n.units[i]),{},{power:-n.units[i].power});t.units.push(u)}if(null!==this.value||null!==n.value){var a=null===this.value?this._normalize(1):this.value,o=null===n.value?n._normalize(1):n.value;t.value=c(a,o)}else t.value=null;return We(e)&&(t.skipAutomaticSimplification=!1),T(t)},b.prototype.pow=function(e){for(var t=this.clone(),n=0;n<R.length;n++)t.dimensions[n]=(this.dimensions[n]||0)*e;for(var r=0;r<t.units.length;r++)t.units[r].power*=e;return null!==t.value?t.value=l(t.value,e):t.value=null,t.skipAutomaticSimplification=!1,T(t)},b.prototype.abs=function(){var e=this.clone();if(null!==e.value)if(e._isDerived()||0===e.units.length||0===e.units[0].unit.offset)e.value=p(e.value);else{var t=e._numberConverter(),n=t(e.units[0].unit.value),r=t(e.units[0].unit.offset),i=f(n,r);e.value=s(p(o(e.value,i)),i)}for(var u in e.units)"VA"!==e.units[u].unit.name&&"VAR"!==e.units[u].unit.name||(e.units[u].unit=P.W);return e},b.prototype.to=function(e){var t,n=null===this.value?this._normalize(1):this.value;if("string"==typeof e)t=b.parse(e);else{if(!We(e))throw new Error("String or Unit expected as parameter");t=e.clone()}if(!this.equalBase(t))throw new Error("Units do not match ('".concat(t.toString(),"' != '").concat(this.toString(),"')"));if(null!==t.value)throw new Error("Cannot convert to a unit with a value");if(null===this.value||this._isDerived()||0===this.units.length||0===t.units.length||this.units[0].unit.offset===t.units[0].unit.offset)t.value=Bt(n);else{var r=b._getNumberConverter(St(n)),i=this.units[0].unit.value,u=this.units[0].unit.offset,a=f(i,u),c=t.units[0].unit.value,l=t.units[0].unit.offset,p=f(c,l);t.value=o(n,r(s(a,p)))}return t.fixPrefix=!0,t.skipAutomaticSimplification=!0,t},b.prototype.toNumber=function(e){return x(this.toNumeric(e))},b.prototype.toNumeric=function(e){var t;return(t=e?this.to(e):this.clone())._isDerived()||0===t.units.length?t._denormalize(t.value):t._denormalize(t.value,t.units[0].prefix.value)},b.prototype.toString=function(){return this.format()},b.prototype.toJSON=function(){return{mathjs:"Unit",value:this._denormalize(this.value),unit:this.units.length>0?this.formatUnits():null,fixPrefix:this.fixPrefix}},b.fromJSON=function(e){var t,n=new b(e.value,null!==(t=e.unit)&&void 0!==t?t:void 0);return n.fixPrefix=e.fixPrefix||!1,n},b.prototype.valueOf=b.prototype.toString,b.prototype.simplify=function(){var e,t,n=this.clone(),r=[];for(var i in j)if(Ot(j,i)&&n.hasBase(I[i])){e=i;break}if("NONE"===e)n.units=[];else if(e&&Ot(j,e)&&(t=j[e]),t)n.units=[{unit:t.unit,prefix:t.prefix,power:1}];else{for(var u=!1,a=0;a<R.length;a++){var o=R[a];Math.abs(n.dimensions[a]||0)>1e-12&&(Ot(j,o)?r.push({unit:j[o].unit,prefix:j[o].prefix,power:n.dimensions[a]||0}):u=!0)}r.length<n.units.length&&!u&&(n.units=r)}return n},b.prototype.toSI=function(){for(var e=this.clone(),t=[],n=0;n<R.length;n++){var r=R[n];if(Math.abs(e.dimensions[n]||0)>1e-12){if(!Ot(q.si,r))throw new Error("Cannot express custom unit "+r+" in SI units");t.push({unit:q.si[r].unit,prefix:q.si[r].prefix,power:e.dimensions[n]||0})}}return e.units=t,e.fixPrefix=!0,e.skipAutomaticSimplification=!0,null!==this.value?(e.value=null,this.to(e)):e},b.prototype.formatUnits=function(){for(var e="",t="",n=0,r=0,i=0;i<this.units.length;i++)this.units[i].power>0?(n++,e+=" "+this.units[i].prefix.name+this.units[i].unit.name,Math.abs(this.units[i].power-1)>1e-15&&(e+="^"+this.units[i].power)):this.units[i].power<0&&r++;if(r>0)for(var u=0;u<this.units.length;u++)this.units[u].power<0&&(n>0?(t+=" "+this.units[u].prefix.name+this.units[u].unit.name,Math.abs(this.units[u].power+1)>1e-15&&(t+="^"+-this.units[u].power)):(t+=" "+this.units[u].prefix.name+this.units[u].unit.name,t+="^"+this.units[u].power));e=e.substr(1),t=t.substr(1),n>1&&r>0&&(e="("+e+")"),r>1&&n>0&&(t="("+t+")");var a=e;return n>0&&r>0&&(a+=" / "),a+=t},b.prototype.format=function(e){var t=this.skipAutomaticSimplification||null===this.value?this.clone():this.simplify(),n=!1;for(var r in void 0!==t.value&&null!==t.value&&Ye(t.value)&&(n=Math.abs(t.value.re)<1e-14),t.units)Ot(t.units,r)&&t.units[r].unit&&("VA"===t.units[r].unit.name&&n?t.units[r].unit=P.VAR:"VAR"!==t.units[r].unit.name||n||(t.units[r].unit=P.VA));1!==t.units.length||t.fixPrefix||Math.abs(t.units[0].power-Math.round(t.units[0].power))<1e-14&&(t.units[0].prefix=t._bestPrefix());var i=t._denormalize(t.value),u=null!==t.value?v(i,e||{}):"",a=t.formatUnits();return t.value&&Ye(t.value)&&(u="("+u+")"),a.length>0&&u.length>0&&(u+=" "),u+=a},b.prototype._bestPrefix=function(){if(1!==this.units.length)throw new Error("Can only compute the best prefix for single units with integer powers, like kg, s^2, N^-1, and so forth!");if(Math.abs(this.units[0].power-Math.round(this.units[0].power))>=1e-14)throw new Error("Can only compute the best prefix for single units with integer powers, like kg, s^2, N^-1, and so forth!");var e=null!==this.value?p(this.value):0,t=p(this.units[0].unit.value),n=this.units[0].prefix;if(0===e)return n;var r=this.units[0].power,i=Math.log(e/Math.pow(n.value*t,r))/Math.LN10-1.2;if(i>-2.200001&&i<1.800001)return n;i=Math.abs(i);var u=this.units[0].unit.prefixes;for(var a in u)if(Ot(u,a)){var o=u[a];if(o.scientific){var s=Math.abs(Math.log(e/Math.pow(o.value*t,r))/Math.LN10-1.2);(s<i||s===i&&o.name.length<n.name.length)&&(n=o,i=s)}}return n},b.prototype.splitUnit=function(e){for(var t=this.clone(),n=[],r=0;r<e.length&&(t=t.to(e[r]),r!==e.length-1);r++){var i=t.toNumeric(),u=m(i),a=new b(d(u,i)?u:h(t.toNumeric()),e[r].toString());n.push(a),t=s(t,a)}for(var f=0,c=0;c<n.length;c++)f=o(f,n[c].value);return d(f,this.value)&&(t.value=0),n.push(t),n};var O={NONE:{"":{name:"",value:1,scientific:!0}},SHORT:{"":{name:"",value:1,scientific:!0},da:{name:"da",value:10,scientific:!1},h:{name:"h",value:100,scientific:!1},k:{name:"k",value:1e3,scientific:!0},M:{name:"M",value:1e6,scientific:!0},G:{name:"G",value:1e9,scientific:!0},T:{name:"T",value:1e12,scientific:!0},P:{name:"P",value:1e15,scientific:!0},E:{name:"E",value:1e18,scientific:!0},Z:{name:"Z",value:1e21,scientific:!0},Y:{name:"Y",value:1e24,scientific:!0},R:{name:"R",value:1e27,scientific:!0},Q:{name:"Q",value:1e30,scientific:!0},d:{name:"d",value:.1,scientific:!1},c:{name:"c",value:.01,scientific:!1},m:{name:"m",value:.001,scientific:!0},u:{name:"u",value:1e-6,scientific:!0},n:{name:"n",value:1e-9,scientific:!0},p:{name:"p",value:1e-12,scientific:!0},f:{name:"f",value:1e-15,scientific:!0},a:{name:"a",value:1e-18,scientific:!0},z:{name:"z",value:1e-21,scientific:!0},y:{name:"y",value:1e-24,scientific:!0},r:{name:"r",value:1e-27,scientific:!0},q:{name:"q",value:1e-30,scientific:!0}},LONG:{"":{name:"",value:1,scientific:!0},deca:{name:"deca",value:10,scientific:!1},hecto:{name:"hecto",value:100,scientific:!1},kilo:{name:"kilo",value:1e3,scientific:!0},mega:{name:"mega",value:1e6,scientific:!0},giga:{name:"giga",value:1e9,scientific:!0},tera:{name:"tera",value:1e12,scientific:!0},peta:{name:"peta",value:1e15,scientific:!0},exa:{name:"exa",value:1e18,scientific:!0},zetta:{name:"zetta",value:1e21,scientific:!0},yotta:{name:"yotta",value:1e24,scientific:!0},ronna:{name:"ronna",value:1e27,scientific:!0},quetta:{name:"quetta",value:1e30,scientific:!0},deci:{name:"deci",value:.1,scientific:!1},centi:{name:"centi",value:.01,scientific:!1},milli:{name:"milli",value:.001,scientific:!0},micro:{name:"micro",value:1e-6,scientific:!0},nano:{name:"nano",value:1e-9,scientific:!0},pico:{name:"pico",value:1e-12,scientific:!0},femto:{name:"femto",value:1e-15,scientific:!0},atto:{name:"atto",value:1e-18,scientific:!0},zepto:{name:"zepto",value:1e-21,scientific:!0},yocto:{name:"yocto",value:1e-24,scientific:!0},ronto:{name:"ronto",value:1e-27,scientific:!0},quecto:{name:"quecto",value:1e-30,scientific:!0}},SQUARED:{"":{name:"",value:1,scientific:!0},da:{name:"da",value:100,scientific:!1},h:{name:"h",value:1e4,scientific:!1},k:{name:"k",value:1e6,scientific:!0},M:{name:"M",value:1e12,scientific:!0},G:{name:"G",value:1e18,scientific:!0},T:{name:"T",value:1e24,scientific:!0},P:{name:"P",value:1e30,scientific:!0},E:{name:"E",value:1e36,scientific:!0},Z:{name:"Z",value:1e42,scientific:!0},Y:{name:"Y",value:1e48,scientific:!0},R:{name:"R",value:1e54,scientific:!0},Q:{name:"Q",value:1e60,scientific:!0},d:{name:"d",value:.01,scientific:!1},c:{name:"c",value:1e-4,scientific:!1},m:{name:"m",value:1e-6,scientific:!0},u:{name:"u",value:1e-12,scientific:!0},n:{name:"n",value:1e-18,scientific:!0},p:{name:"p",value:1e-24,scientific:!0},f:{name:"f",value:1e-30,scientific:!0},a:{name:"a",value:1e-36,scientific:!0},z:{name:"z",value:1e-42,scientific:!0},y:{name:"y",value:1e-48,scientific:!0},r:{name:"r",value:1e-54,scientific:!0},q:{name:"q",value:1e-60,scientific:!0}},CUBIC:{"":{name:"",value:1,scientific:!0},da:{name:"da",value:1e3,scientific:!1},h:{name:"h",value:1e6,scientific:!1},k:{name:"k",value:1e9,scientific:!0},M:{name:"M",value:1e18,scientific:!0},G:{name:"G",value:1e27,scientific:!0},T:{name:"T",value:1e36,scientific:!0},P:{name:"P",value:1e45,scientific:!0},E:{name:"E",value:1e54,scientific:!0},Z:{name:"Z",value:1e63,scientific:!0},Y:{name:"Y",value:1e72,scientific:!0},R:{name:"R",value:1e81,scientific:!0},Q:{name:"Q",value:1e90,scientific:!0},d:{name:"d",value:.001,scientific:!1},c:{name:"c",value:1e-6,scientific:!1},m:{name:"m",value:1e-9,scientific:!0},u:{name:"u",value:1e-18,scientific:!0},n:{name:"n",value:1e-27,scientific:!0},p:{name:"p",value:1e-36,scientific:!0},f:{name:"f",value:1e-45,scientific:!0},a:{name:"a",value:1e-54,scientific:!0},z:{name:"z",value:1e-63,scientific:!0},y:{name:"y",value:1e-72,scientific:!0},r:{name:"r",value:1e-81,scientific:!0},q:{name:"q",value:1e-90,scientific:!0}},BINARY_SHORT_SI:{"":{name:"",value:1,scientific:!0},k:{name:"k",value:1e3,scientific:!0},M:{name:"M",value:1e6,scientific:!0},G:{name:"G",value:1e9,scientific:!0},T:{name:"T",value:1e12,scientific:!0},P:{name:"P",value:1e15,scientific:!0},E:{name:"E",value:1e18,scientific:!0},Z:{name:"Z",value:1e21,scientific:!0},Y:{name:"Y",value:1e24,scientific:!0}},BINARY_SHORT_IEC:{"":{name:"",value:1,scientific:!0},Ki:{name:"Ki",value:1024,scientific:!0},Mi:{name:"Mi",value:Math.pow(1024,2),scientific:!0},Gi:{name:"Gi",value:Math.pow(1024,3),scientific:!0},Ti:{name:"Ti",value:Math.pow(1024,4),scientific:!0},Pi:{name:"Pi",value:Math.pow(1024,5),scientific:!0},Ei:{name:"Ei",value:Math.pow(1024,6),scientific:!0},Zi:{name:"Zi",value:Math.pow(1024,7),scientific:!0},Yi:{name:"Yi",value:Math.pow(1024,8),scientific:!0}},BINARY_LONG_SI:{"":{name:"",value:1,scientific:!0},kilo:{name:"kilo",value:1e3,scientific:!0},mega:{name:"mega",value:1e6,scientific:!0},giga:{name:"giga",value:1e9,scientific:!0},tera:{name:"tera",value:1e12,scientific:!0},peta:{name:"peta",value:1e15,scientific:!0},exa:{name:"exa",value:1e18,scientific:!0},zetta:{name:"zetta",value:1e21,scientific:!0},yotta:{name:"yotta",value:1e24,scientific:!0}},BINARY_LONG_IEC:{"":{name:"",value:1,scientific:!0},kibi:{name:"kibi",value:1024,scientific:!0},mebi:{name:"mebi",value:Math.pow(1024,2),scientific:!0},gibi:{name:"gibi",value:Math.pow(1024,3),scientific:!0},tebi:{name:"tebi",value:Math.pow(1024,4),scientific:!0},pebi:{name:"pebi",value:Math.pow(1024,5),scientific:!0},exi:{name:"exi",value:Math.pow(1024,6),scientific:!0},zebi:{name:"zebi",value:Math.pow(1024,7),scientific:!0},yobi:{name:"yobi",value:Math.pow(1024,8),scientific:!0}},BTU:{"":{name:"",value:1,scientific:!0},MM:{name:"MM",value:1e6,scientific:!0}}};O.SHORTLONG=t({},O.SHORT,O.LONG),O.BINARY_SHORT=t({},O.BINARY_SHORT_SI,O.BINARY_SHORT_IEC),O.BINARY_LONG=t({},O.BINARY_LONG_SI,O.BINARY_LONG_IEC);var R=["MASS","LENGTH","TIME","CURRENT","TEMPERATURE","LUMINOUS_INTENSITY","AMOUNT_OF_SUBSTANCE","ANGLE","BIT"],I={NONE:{dimensions:[0,0,0,0,0,0,0,0,0]},MASS:{dimensions:[1,0,0,0,0,0,0,0,0]},LENGTH:{dimensions:[0,1,0,0,0,0,0,0,0]},TIME:{dimensions:[0,0,1,0,0,0,0,0,0]},CURRENT:{dimensions:[0,0,0,1,0,0,0,0,0]},TEMPERATURE:{dimensions:[0,0,0,0,1,0,0,0,0]},LUMINOUS_INTENSITY:{dimensions:[0,0,0,0,0,1,0,0,0]},AMOUNT_OF_SUBSTANCE:{dimensions:[0,0,0,0,0,0,1,0,0]},FORCE:{dimensions:[1,1,-2,0,0,0,0,0,0]},SURFACE:{dimensions:[0,2,0,0,0,0,0,0,0]},VOLUME:{dimensions:[0,3,0,0,0,0,0,0,0]},ENERGY:{dimensions:[1,2,-2,0,0,0,0,0,0]},POWER:{dimensions:[1,2,-3,0,0,0,0,0,0]},PRESSURE:{dimensions:[1,-1,-2,0,0,0,0,0,0]},ELECTRIC_CHARGE:{dimensions:[0,0,1,1,0,0,0,0,0]},ELECTRIC_CAPACITANCE:{dimensions:[-1,-2,4,2,0,0,0,0,0]},ELECTRIC_POTENTIAL:{dimensions:[1,2,-3,-1,0,0,0,0,0]},ELECTRIC_RESISTANCE:{dimensions:[1,2,-3,-2,0,0,0,0,0]},ELECTRIC_INDUCTANCE:{dimensions:[1,2,-2,-2,0,0,0,0,0]},ELECTRIC_CONDUCTANCE:{dimensions:[-1,-2,3,2,0,0,0,0,0]},MAGNETIC_FLUX:{dimensions:[1,2,-2,-1,0,0,0,0,0]},MAGNETIC_FLUX_DENSITY:{dimensions:[1,0,-2,-1,0,0,0,0,0]},FREQUENCY:{dimensions:[0,0,-1,0,0,0,0,0,0]},ANGLE:{dimensions:[0,0,0,0,0,0,0,1,0]},BIT:{dimensions:[0,0,0,0,0,0,0,0,1]}};for(var z in I)Ot(I,z)&&(I[z].key=z);var U={name:"",base:{},value:1,offset:0,dimensions:R.map((e=>0))},P={meter:{name:"meter",base:I.LENGTH,prefixes:O.LONG,value:1,offset:0},inch:{name:"inch",base:I.LENGTH,prefixes:O.NONE,value:.0254,offset:0},foot:{name:"foot",base:I.LENGTH,prefixes:O.NONE,value:.3048,offset:0},yard:{name:"yard",base:I.LENGTH,prefixes:O.NONE,value:.9144,offset:0},mile:{name:"mile",base:I.LENGTH,prefixes:O.NONE,value:1609.344,offset:0},link:{name:"link",base:I.LENGTH,prefixes:O.NONE,value:.201168,offset:0},rod:{name:"rod",base:I.LENGTH,prefixes:O.NONE,value:5.0292,offset:0},chain:{name:"chain",base:I.LENGTH,prefixes:O.NONE,value:20.1168,offset:0},angstrom:{name:"angstrom",base:I.LENGTH,prefixes:O.NONE,value:1e-10,offset:0},m:{name:"m",base:I.LENGTH,prefixes:O.SHORT,value:1,offset:0},in:{name:"in",base:I.LENGTH,prefixes:O.NONE,value:.0254,offset:0},ft:{name:"ft",base:I.LENGTH,prefixes:O.NONE,value:.3048,offset:0},yd:{name:"yd",base:I.LENGTH,prefixes:O.NONE,value:.9144,offset:0},mi:{name:"mi",base:I.LENGTH,prefixes:O.NONE,value:1609.344,offset:0},li:{name:"li",base:I.LENGTH,prefixes:O.NONE,value:.201168,offset:0},rd:{name:"rd",base:I.LENGTH,prefixes:O.NONE,value:5.02921,offset:0},ch:{name:"ch",base:I.LENGTH,prefixes:O.NONE,value:20.1168,offset:0},mil:{name:"mil",base:I.LENGTH,prefixes:O.NONE,value:254e-7,offset:0},m2:{name:"m2",base:I.SURFACE,prefixes:O.SQUARED,value:1,offset:0},sqin:{name:"sqin",base:I.SURFACE,prefixes:O.NONE,value:64516e-8,offset:0},sqft:{name:"sqft",base:I.SURFACE,prefixes:O.NONE,value:.09290304,offset:0},sqyd:{name:"sqyd",base:I.SURFACE,prefixes:O.NONE,value:.83612736,offset:0},sqmi:{name:"sqmi",base:I.SURFACE,prefixes:O.NONE,value:2589988.110336,offset:0},sqrd:{name:"sqrd",base:I.SURFACE,prefixes:O.NONE,value:25.29295,offset:0},sqch:{name:"sqch",base:I.SURFACE,prefixes:O.NONE,value:404.6873,offset:0},sqmil:{name:"sqmil",base:I.SURFACE,prefixes:O.NONE,value:6.4516e-10,offset:0},acre:{name:"acre",base:I.SURFACE,prefixes:O.NONE,value:4046.86,offset:0},hectare:{name:"hectare",base:I.SURFACE,prefixes:O.NONE,value:1e4,offset:0},m3:{name:"m3",base:I.VOLUME,prefixes:O.CUBIC,value:1,offset:0},L:{name:"L",base:I.VOLUME,prefixes:O.SHORT,value:.001,offset:0},l:{name:"l",base:I.VOLUME,prefixes:O.SHORT,value:.001,offset:0},litre:{name:"litre",base:I.VOLUME,prefixes:O.LONG,value:.001,offset:0},cuin:{name:"cuin",base:I.VOLUME,prefixes:O.NONE,value:16387064e-12,offset:0},cuft:{name:"cuft",base:I.VOLUME,prefixes:O.NONE,value:.028316846592,offset:0},cuyd:{name:"cuyd",base:I.VOLUME,prefixes:O.NONE,value:.************,offset:0},teaspoon:{name:"teaspoon",base:I.VOLUME,prefixes:O.NONE,value:5e-6,offset:0},tablespoon:{name:"tablespoon",base:I.VOLUME,prefixes:O.NONE,value:15e-6,offset:0},drop:{name:"drop",base:I.VOLUME,prefixes:O.NONE,value:5e-8,offset:0},gtt:{name:"gtt",base:I.VOLUME,prefixes:O.NONE,value:5e-8,offset:0},minim:{name:"minim",base:I.VOLUME,prefixes:O.NONE,value:6.161152e-8,offset:0},fluiddram:{name:"fluiddram",base:I.VOLUME,prefixes:O.NONE,value:36966911e-13,offset:0},fluidounce:{name:"fluidounce",base:I.VOLUME,prefixes:O.NONE,value:2957353e-11,offset:0},gill:{name:"gill",base:I.VOLUME,prefixes:O.NONE,value:.0001182941,offset:0},cc:{name:"cc",base:I.VOLUME,prefixes:O.NONE,value:1e-6,offset:0},cup:{name:"cup",base:I.VOLUME,prefixes:O.NONE,value:.0002365882,offset:0},pint:{name:"pint",base:I.VOLUME,prefixes:O.NONE,value:.0004731765,offset:0},quart:{name:"quart",base:I.VOLUME,prefixes:O.NONE,value:.0009463529,offset:0},gallon:{name:"gallon",base:I.VOLUME,prefixes:O.NONE,value:.003785412,offset:0},beerbarrel:{name:"beerbarrel",base:I.VOLUME,prefixes:O.NONE,value:.1173478,offset:0},oilbarrel:{name:"oilbarrel",base:I.VOLUME,prefixes:O.NONE,value:.1589873,offset:0},hogshead:{name:"hogshead",base:I.VOLUME,prefixes:O.NONE,value:.238481,offset:0},fldr:{name:"fldr",base:I.VOLUME,prefixes:O.NONE,value:36966911e-13,offset:0},floz:{name:"floz",base:I.VOLUME,prefixes:O.NONE,value:2957353e-11,offset:0},gi:{name:"gi",base:I.VOLUME,prefixes:O.NONE,value:.0001182941,offset:0},cp:{name:"cp",base:I.VOLUME,prefixes:O.NONE,value:.0002365882,offset:0},pt:{name:"pt",base:I.VOLUME,prefixes:O.NONE,value:.0004731765,offset:0},qt:{name:"qt",base:I.VOLUME,prefixes:O.NONE,value:.0009463529,offset:0},gal:{name:"gal",base:I.VOLUME,prefixes:O.NONE,value:.003785412,offset:0},bbl:{name:"bbl",base:I.VOLUME,prefixes:O.NONE,value:.1173478,offset:0},obl:{name:"obl",base:I.VOLUME,prefixes:O.NONE,value:.1589873,offset:0},g:{name:"g",base:I.MASS,prefixes:O.SHORT,value:.001,offset:0},gram:{name:"gram",base:I.MASS,prefixes:O.LONG,value:.001,offset:0},ton:{name:"ton",base:I.MASS,prefixes:O.SHORT,value:907.18474,offset:0},t:{name:"t",base:I.MASS,prefixes:O.SHORT,value:1e3,offset:0},tonne:{name:"tonne",base:I.MASS,prefixes:O.LONG,value:1e3,offset:0},grain:{name:"grain",base:I.MASS,prefixes:O.NONE,value:6479891e-11,offset:0},dram:{name:"dram",base:I.MASS,prefixes:O.NONE,value:.0017718451953125,offset:0},ounce:{name:"ounce",base:I.MASS,prefixes:O.NONE,value:.028349523125,offset:0},poundmass:{name:"poundmass",base:I.MASS,prefixes:O.NONE,value:.45359237,offset:0},hundredweight:{name:"hundredweight",base:I.MASS,prefixes:O.NONE,value:45.359237,offset:0},stick:{name:"stick",base:I.MASS,prefixes:O.NONE,value:.115,offset:0},stone:{name:"stone",base:I.MASS,prefixes:O.NONE,value:6.35029318,offset:0},gr:{name:"gr",base:I.MASS,prefixes:O.NONE,value:6479891e-11,offset:0},dr:{name:"dr",base:I.MASS,prefixes:O.NONE,value:.0017718451953125,offset:0},oz:{name:"oz",base:I.MASS,prefixes:O.NONE,value:.028349523125,offset:0},lbm:{name:"lbm",base:I.MASS,prefixes:O.NONE,value:.45359237,offset:0},cwt:{name:"cwt",base:I.MASS,prefixes:O.NONE,value:45.359237,offset:0},s:{name:"s",base:I.TIME,prefixes:O.SHORT,value:1,offset:0},min:{name:"min",base:I.TIME,prefixes:O.NONE,value:60,offset:0},h:{name:"h",base:I.TIME,prefixes:O.NONE,value:3600,offset:0},second:{name:"second",base:I.TIME,prefixes:O.LONG,value:1,offset:0},sec:{name:"sec",base:I.TIME,prefixes:O.LONG,value:1,offset:0},minute:{name:"minute",base:I.TIME,prefixes:O.NONE,value:60,offset:0},hour:{name:"hour",base:I.TIME,prefixes:O.NONE,value:3600,offset:0},day:{name:"day",base:I.TIME,prefixes:O.NONE,value:86400,offset:0},week:{name:"week",base:I.TIME,prefixes:O.NONE,value:604800,offset:0},month:{name:"month",base:I.TIME,prefixes:O.NONE,value:2629800,offset:0},year:{name:"year",base:I.TIME,prefixes:O.NONE,value:31557600,offset:0},decade:{name:"decade",base:I.TIME,prefixes:O.NONE,value:315576e3,offset:0},century:{name:"century",base:I.TIME,prefixes:O.NONE,value:315576e4,offset:0},millennium:{name:"millennium",base:I.TIME,prefixes:O.NONE,value:315576e5,offset:0},hertz:{name:"Hertz",base:I.FREQUENCY,prefixes:O.LONG,value:1,offset:0,reciprocal:!0},Hz:{name:"Hz",base:I.FREQUENCY,prefixes:O.SHORT,value:1,offset:0,reciprocal:!0},rad:{name:"rad",base:I.ANGLE,prefixes:O.SHORT,value:1,offset:0},radian:{name:"radian",base:I.ANGLE,prefixes:O.LONG,value:1,offset:0},deg:{name:"deg",base:I.ANGLE,prefixes:O.SHORT,value:null,offset:0},degree:{name:"degree",base:I.ANGLE,prefixes:O.LONG,value:null,offset:0},grad:{name:"grad",base:I.ANGLE,prefixes:O.SHORT,value:null,offset:0},gradian:{name:"gradian",base:I.ANGLE,prefixes:O.LONG,value:null,offset:0},cycle:{name:"cycle",base:I.ANGLE,prefixes:O.NONE,value:null,offset:0},arcsec:{name:"arcsec",base:I.ANGLE,prefixes:O.NONE,value:null,offset:0},arcmin:{name:"arcmin",base:I.ANGLE,prefixes:O.NONE,value:null,offset:0},A:{name:"A",base:I.CURRENT,prefixes:O.SHORT,value:1,offset:0},ampere:{name:"ampere",base:I.CURRENT,prefixes:O.LONG,value:1,offset:0},K:{name:"K",base:I.TEMPERATURE,prefixes:O.SHORT,value:1,offset:0},degC:{name:"degC",base:I.TEMPERATURE,prefixes:O.SHORT,value:1,offset:273.15},degF:{name:"degF",base:I.TEMPERATURE,prefixes:O.SHORT,value:new w(5,9),offset:459.67},degR:{name:"degR",base:I.TEMPERATURE,prefixes:O.SHORT,value:new w(5,9),offset:0},kelvin:{name:"kelvin",base:I.TEMPERATURE,prefixes:O.LONG,value:1,offset:0},celsius:{name:"celsius",base:I.TEMPERATURE,prefixes:O.LONG,value:1,offset:273.15},fahrenheit:{name:"fahrenheit",base:I.TEMPERATURE,prefixes:O.LONG,value:new w(5,9),offset:459.67},rankine:{name:"rankine",base:I.TEMPERATURE,prefixes:O.LONG,value:new w(5,9),offset:0},mol:{name:"mol",base:I.AMOUNT_OF_SUBSTANCE,prefixes:O.SHORT,value:1,offset:0},mole:{name:"mole",base:I.AMOUNT_OF_SUBSTANCE,prefixes:O.LONG,value:1,offset:0},cd:{name:"cd",base:I.LUMINOUS_INTENSITY,prefixes:O.SHORT,value:1,offset:0},candela:{name:"candela",base:I.LUMINOUS_INTENSITY,prefixes:O.LONG,value:1,offset:0},N:{name:"N",base:I.FORCE,prefixes:O.SHORT,value:1,offset:0},newton:{name:"newton",base:I.FORCE,prefixes:O.LONG,value:1,offset:0},dyn:{name:"dyn",base:I.FORCE,prefixes:O.SHORT,value:1e-5,offset:0},dyne:{name:"dyne",base:I.FORCE,prefixes:O.LONG,value:1e-5,offset:0},lbf:{name:"lbf",base:I.FORCE,prefixes:O.NONE,value:4.4482216152605,offset:0},poundforce:{name:"poundforce",base:I.FORCE,prefixes:O.NONE,value:4.4482216152605,offset:0},kip:{name:"kip",base:I.FORCE,prefixes:O.LONG,value:4448.2216,offset:0},kilogramforce:{name:"kilogramforce",base:I.FORCE,prefixes:O.NONE,value:9.80665,offset:0},J:{name:"J",base:I.ENERGY,prefixes:O.SHORT,value:1,offset:0},joule:{name:"joule",base:I.ENERGY,prefixes:O.LONG,value:1,offset:0},erg:{name:"erg",base:I.ENERGY,prefixes:O.SHORTLONG,value:1e-7,offset:0},Wh:{name:"Wh",base:I.ENERGY,prefixes:O.SHORT,value:3600,offset:0},BTU:{name:"BTU",base:I.ENERGY,prefixes:O.BTU,value:1055.05585262,offset:0},eV:{name:"eV",base:I.ENERGY,prefixes:O.SHORT,value:1602176565e-28,offset:0},electronvolt:{name:"electronvolt",base:I.ENERGY,prefixes:O.LONG,value:1602176565e-28,offset:0},W:{name:"W",base:I.POWER,prefixes:O.SHORT,value:1,offset:0},watt:{name:"watt",base:I.POWER,prefixes:O.LONG,value:1,offset:0},hp:{name:"hp",base:I.POWER,prefixes:O.NONE,value:745.6998715386,offset:0},VAR:{name:"VAR",base:I.POWER,prefixes:O.SHORT,value:y.I,offset:0},VA:{name:"VA",base:I.POWER,prefixes:O.SHORT,value:1,offset:0},Pa:{name:"Pa",base:I.PRESSURE,prefixes:O.SHORT,value:1,offset:0},psi:{name:"psi",base:I.PRESSURE,prefixes:O.NONE,value:6894.75729276459,offset:0},atm:{name:"atm",base:I.PRESSURE,prefixes:O.NONE,value:101325,offset:0},bar:{name:"bar",base:I.PRESSURE,prefixes:O.SHORTLONG,value:1e5,offset:0},torr:{name:"torr",base:I.PRESSURE,prefixes:O.NONE,value:133.322,offset:0},mmHg:{name:"mmHg",base:I.PRESSURE,prefixes:O.NONE,value:133.322,offset:0},mmH2O:{name:"mmH2O",base:I.PRESSURE,prefixes:O.NONE,value:9.80665,offset:0},cmH2O:{name:"cmH2O",base:I.PRESSURE,prefixes:O.NONE,value:98.0665,offset:0},coulomb:{name:"coulomb",base:I.ELECTRIC_CHARGE,prefixes:O.LONG,value:1,offset:0},C:{name:"C",base:I.ELECTRIC_CHARGE,prefixes:O.SHORT,value:1,offset:0},farad:{name:"farad",base:I.ELECTRIC_CAPACITANCE,prefixes:O.LONG,value:1,offset:0},F:{name:"F",base:I.ELECTRIC_CAPACITANCE,prefixes:O.SHORT,value:1,offset:0},volt:{name:"volt",base:I.ELECTRIC_POTENTIAL,prefixes:O.LONG,value:1,offset:0},V:{name:"V",base:I.ELECTRIC_POTENTIAL,prefixes:O.SHORT,value:1,offset:0},ohm:{name:"ohm",base:I.ELECTRIC_RESISTANCE,prefixes:O.SHORTLONG,value:1,offset:0},henry:{name:"henry",base:I.ELECTRIC_INDUCTANCE,prefixes:O.LONG,value:1,offset:0},H:{name:"H",base:I.ELECTRIC_INDUCTANCE,prefixes:O.SHORT,value:1,offset:0},siemens:{name:"siemens",base:I.ELECTRIC_CONDUCTANCE,prefixes:O.LONG,value:1,offset:0},S:{name:"S",base:I.ELECTRIC_CONDUCTANCE,prefixes:O.SHORT,value:1,offset:0},weber:{name:"weber",base:I.MAGNETIC_FLUX,prefixes:O.LONG,value:1,offset:0},Wb:{name:"Wb",base:I.MAGNETIC_FLUX,prefixes:O.SHORT,value:1,offset:0},tesla:{name:"tesla",base:I.MAGNETIC_FLUX_DENSITY,prefixes:O.LONG,value:1,offset:0},T:{name:"T",base:I.MAGNETIC_FLUX_DENSITY,prefixes:O.SHORT,value:1,offset:0},b:{name:"b",base:I.BIT,prefixes:O.BINARY_SHORT,value:1,offset:0},bits:{name:"bits",base:I.BIT,prefixes:O.BINARY_LONG,value:1,offset:0},B:{name:"B",base:I.BIT,prefixes:O.BINARY_SHORT,value:8,offset:0},bytes:{name:"bytes",base:I.BIT,prefixes:O.BINARY_LONG,value:8,offset:0}},L={meters:"meter",inches:"inch",feet:"foot",yards:"yard",miles:"mile",links:"link",rods:"rod",chains:"chain",angstroms:"angstrom",lt:"l",litres:"litre",liter:"litre",liters:"litre",teaspoons:"teaspoon",tablespoons:"tablespoon",minims:"minim",fluiddrams:"fluiddram",fluidounces:"fluidounce",gills:"gill",cups:"cup",pints:"pint",quarts:"quart",gallons:"gallon",beerbarrels:"beerbarrel",oilbarrels:"oilbarrel",hogsheads:"hogshead",gtts:"gtt",grams:"gram",tons:"ton",tonnes:"tonne",grains:"grain",drams:"dram",ounces:"ounce",poundmasses:"poundmass",hundredweights:"hundredweight",sticks:"stick",lb:"lbm",lbs:"lbm",kips:"kip",kgf:"kilogramforce",acres:"acre",hectares:"hectare",sqfeet:"sqft",sqyard:"sqyd",sqmile:"sqmi",sqmiles:"sqmi",mmhg:"mmHg",mmh2o:"mmH2O",cmh2o:"cmH2O",seconds:"second",secs:"second",minutes:"minute",mins:"minute",hours:"hour",hr:"hour",hrs:"hour",days:"day",weeks:"week",months:"month",years:"year",decades:"decade",centuries:"century",millennia:"millennium",hertz:"hertz",radians:"radian",degrees:"degree",gradians:"gradian",cycles:"cycle",arcsecond:"arcsec",arcseconds:"arcsec",arcminute:"arcmin",arcminutes:"arcmin",BTUs:"BTU",watts:"watt",joules:"joule",amperes:"ampere",amps:"ampere",amp:"ampere",coulombs:"coulomb",volts:"volt",ohms:"ohm",farads:"farad",webers:"weber",teslas:"tesla",electronvolts:"electronvolt",moles:"mole",bit:"bits",byte:"bytes"};function k(e){if("BigNumber"===e.number){var t=gn(E);P.rad.value=new E(1),P.deg.value=t.div(180),P.grad.value=t.div(200),P.cycle.value=t.times(2),P.arcsec.value=t.div(648e3),P.arcmin.value=t.div(10800)}else P.rad.value=1,P.deg.value=Math.PI/180,P.grad.value=Math.PI/200,P.cycle.value=2*Math.PI,P.arcsec.value=Math.PI/648e3,P.arcmin.value=Math.PI/10800;P.radian.value=P.rad.value,P.degree.value=P.deg.value,P.gradian.value=P.grad.value}k(a),u&&u("config",(function(e,t){e.number!==t.number&&k(e)}));var q={si:{NONE:{unit:U,prefix:O.NONE[""]},LENGTH:{unit:P.m,prefix:O.SHORT[""]},MASS:{unit:P.g,prefix:O.SHORT.k},TIME:{unit:P.s,prefix:O.SHORT[""]},CURRENT:{unit:P.A,prefix:O.SHORT[""]},TEMPERATURE:{unit:P.K,prefix:O.SHORT[""]},LUMINOUS_INTENSITY:{unit:P.cd,prefix:O.SHORT[""]},AMOUNT_OF_SUBSTANCE:{unit:P.mol,prefix:O.SHORT[""]},ANGLE:{unit:P.rad,prefix:O.SHORT[""]},BIT:{unit:P.bits,prefix:O.SHORT[""]},FORCE:{unit:P.N,prefix:O.SHORT[""]},ENERGY:{unit:P.J,prefix:O.SHORT[""]},POWER:{unit:P.W,prefix:O.SHORT[""]},PRESSURE:{unit:P.Pa,prefix:O.SHORT[""]},ELECTRIC_CHARGE:{unit:P.C,prefix:O.SHORT[""]},ELECTRIC_CAPACITANCE:{unit:P.F,prefix:O.SHORT[""]},ELECTRIC_POTENTIAL:{unit:P.V,prefix:O.SHORT[""]},ELECTRIC_RESISTANCE:{unit:P.ohm,prefix:O.SHORT[""]},ELECTRIC_INDUCTANCE:{unit:P.H,prefix:O.SHORT[""]},ELECTRIC_CONDUCTANCE:{unit:P.S,prefix:O.SHORT[""]},MAGNETIC_FLUX:{unit:P.Wb,prefix:O.SHORT[""]},MAGNETIC_FLUX_DENSITY:{unit:P.T,prefix:O.SHORT[""]},FREQUENCY:{unit:P.Hz,prefix:O.SHORT[""]}}};q.cgs=JSON.parse(JSON.stringify(q.si)),q.cgs.LENGTH={unit:P.m,prefix:O.SHORT.c},q.cgs.MASS={unit:P.g,prefix:O.SHORT[""]},q.cgs.FORCE={unit:P.dyn,prefix:O.SHORT[""]},q.cgs.ENERGY={unit:P.erg,prefix:O.NONE[""]},q.us=JSON.parse(JSON.stringify(q.si)),q.us.LENGTH={unit:P.ft,prefix:O.NONE[""]},q.us.MASS={unit:P.lbm,prefix:O.NONE[""]},q.us.TEMPERATURE={unit:P.degF,prefix:O.NONE[""]},q.us.FORCE={unit:P.lbf,prefix:O.NONE[""]},q.us.ENERGY={unit:P.BTU,prefix:O.BTU[""]},q.us.POWER={unit:P.hp,prefix:O.NONE[""]},q.us.PRESSURE={unit:P.psi,prefix:O.NONE[""]},q.auto=JSON.parse(JSON.stringify(q.si));var j=q.auto;for(var H in b.setUnitSystem=function(e){if(!Ot(q,e))throw new Error("Unit system "+e+" does not exist. Choices are: "+Object.keys(q).join(", "));j=q[e]},b.getUnitSystem=function(){for(var e in q)if(Ot(q,e)&&q[e]===j)return e},b.typeConverters={BigNumber:function(e){return null!=e&&e.isFraction?new E(e.n).div(e.d).times(e.s):new E(e+"")},Fraction:function(e){return new w(e)},Complex:function(e){return e},number:function(e){return null!=e&&e.isFraction?g(e):e}},b.prototype._numberConverter=function(){var e=b.typeConverters[this.valueType()];if(e)return e;throw new TypeError('Unsupported Unit value type "'+this.valueType()+'"')},b._getNumberConverter=function(e){if(!b.typeConverters[e])throw new TypeError('Unsupported type "'+e+'"');return b.typeConverters[e]},P)if(Ot(P,H)){var G=P[H];G.dimensions=G.base.dimensions}for(var V in L)if(Ot(L,V)){var Y=P[L[V]],Z={};for(var W in Y)Ot(Y,W)&&(Z[W]=Y[W]);Z.name=V,P[V]=Z}return b.isValidAlpha=function(e){return/^[a-zA-Z]$/.test(e)},b.createUnit=function(e,t){if("object"!=typeof e)throw new TypeError("createUnit expects first parameter to be of type 'Object'");if(t&&t.override)for(var n in e)if(Ot(e,n)&&b.deleteUnit(n),e[n].aliases)for(var r=0;r<e[n].aliases.length;r++)b.deleteUnit(e[n].aliases[r]);var i;for(var u in e)Ot(e,u)&&(i=b.createUnitSingle(u,e[u]));return i},b.createUnitSingle=function(e,t){if(null==t&&(t={}),"string"!=typeof e)throw new TypeError("createUnitSingle expects first parameter to be of type 'string'");if(Ot(P,e))throw new Error('Cannot create unit "'+e+'": a unit with that name already exists');!function(e){for(var t=0;t<e.length;t++){if(i=e.charAt(t),0===t&&!b.isValidAlpha(i))throw new Error('Invalid unit name (must begin with alpha character): "'+e+'"');if(t>0&&!b.isValidAlpha(i)&&!F(i))throw new Error('Invalid unit name (only alphanumeric characters are allowed): "'+e+'"')}}(e);var n,r,u,a=null,o=[],s=0;if(t&&"Unit"===t.type)a=t.clone();else if("string"==typeof t)""!==t&&(n=t);else{if("object"!=typeof t)throw new TypeError('Cannot create unit "'+e+'" from "'+t.toString()+'": expecting "string" or "Unit" or "Object"');n=t.definition,r=t.prefixes,s=t.offset,u=t.baseName,t.aliases&&(o=t.aliases.valueOf())}if(o)for(var f=0;f<o.length;f++)if(Ot(P,o[f]))throw new Error('Cannot create alias "'+o[f]+'": a unit with that name already exists');if(n&&"string"==typeof n&&!a)try{a=b.parse(n,{allowNoUnits:!0})}catch(t){throw t.message='Could not create unit "'+e+'" from "'+n+'": '+t.message,t}else n&&"Unit"===n.type&&(a=n.clone());o=o||[],s=s||0,r=r&&r.toUpperCase&&O[r.toUpperCase()]||O.NONE;var c={};if(a){c={name:e,value:a.value,dimensions:a.dimensions.slice(0),prefixes:r,offset:s};var l=!1;for(var p in I)if(Ot(I,p)){for(var h=!0,m=0;m<R.length;m++)if(Math.abs((c.dimensions[m]||0)-(I[p].dimensions[m]||0))>1e-12){h=!1;break}if(h){l=!0,c.base=I[p];break}}if(!l){u=u||e+"_STUFF";var d={dimensions:a.dimensions.slice(0)};d.key=u,I[u]=d,j[u]={unit:c,prefix:O.NONE[""]},c.base=I[u]}}else{if(u=u||e+"_STUFF",R.indexOf(u)>=0)throw new Error('Cannot create new base unit "'+e+'": a base unit with that name already exists (and cannot be overridden)');for(var D in R.push(u),I)Ot(I,D)&&(I[D].dimensions[R.length-1]=0);for(var v={dimensions:[]},g=0;g<R.length;g++)v.dimensions[g]=0;v.dimensions[R.length-1]=1,v.key=u,I[u]=v,c={name:e,value:1,dimensions:I[u].dimensions.slice(0),prefixes:r,offset:s,base:I[u]},j[u]={unit:c,prefix:O.NONE[""]}}b.UNITS[e]=c;for(var y=0;y<o.length;y++){var E=o[y],w={};for(var x in c)Ot(c,x)&&(w[x]=c[x]);w.name=E,b.UNITS[E]=w}return delete _.cache,new b(null,e)},b.deleteUnit=function(e){delete b.UNITS[e],delete _.cache},b.PREFIXES=O,b.BASE_DIMENSIONS=R,b.BASE_UNITS=I,b.UNIT_SYSTEMS=q,b.UNITS=P,b}),{isClass:!0}),pa=Rt("divide",["typed","matrix","multiply","equalScalar","divideScalar","inv"],(e=>{var{typed:t,matrix:n,multiply:r,equalScalar:i,divideScalar:u,inv:a}=e,o=Du({typed:t,equalScalar:i}),s=yu({typed:t});return t("divide",_t({"Array | Matrix, Array | Matrix":function(e,t){return r(e,a(t))},"DenseMatrix, any":function(e,t){return s(e,t,u,!1)},"SparseMatrix, any":function(e,t){return o(e,t,u,!1)},"Array, any":function(e,t){return s(n(e),t,u,!1).valueOf()},"any, Array | Matrix":function(e,t){return r(e,a(t))}},u.signatures))}));var ha="unit",ma=Rt(ha,["typed","Unit"],(e=>{var{typed:t,Unit:n}=e;return t(ha,{Unit:function(e){return e.clone()},string:function(e){return n.isValuelessUnit(e)?new n(null,e):n.parse(e,{allowNoUnits:!0})},"number | BigNumber | Fraction | Complex, string | Unit":function(e,t){return new n(e,t)},"number | BigNumber | Fraction":function(e){return new n(e)},"Array | Matrix":t.referToSelf((e=>t=>vr(t,e)))})}));var da=zt({config:a}),Da=mn({}),va=xn({}),ga=bn({}),ya=er({Matrix:ga}),Ea=hr({BigNumber:da,Complex:Da,DenseMatrix:ya,Fraction:va}),wa=Pr({typed:Ea}),xa=ri({typed:Ea}),ba=ii({BigNumber:da,typed:Ea}),Aa=Di({typed:Ea}),Fa=Ei({config:a,typed:Ea}),Ca=xi({typed:Ea}),Na=Ai({typed:Ea}),Ma=_i({typed:Ea}),Sa=Pi({typed:Ea}),Ba=Gi({typed:Ea}),_a=Yi({Matrix:ga,equalScalar:Fa,typed:Ea}),Ta=Wi({typed:Ea}),Oa=Ji({typed:Ea}),Ra=Xi({Fraction:va,typed:Ea}),Ia=Ki({typed:Ea}),za=tu({DenseMatrix:ya,Matrix:ga,SparseMatrix:_a,typed:Ea}),Ua=uu({bignumber:ba,fraction:Ra,number:Ba}),Pa=ou({matrix:za,config:a,typed:Ea}),La=fu({BigNumber:da,config:a,matrix:za,typed:Ea}),ka=lu({isInteger:Na,matrix:za,typed:Ea}),qa=hu({numeric:Ua,typed:Ea}),ja=bu({DenseMatrix:ya,concat:ka,equalScalar:Fa,matrix:za,typed:Ea}),Ha=Cu({isNumeric:Ia,typed:Ea}),Ga=Mu({BigNumber:da,DenseMatrix:ya,SparseMatrix:_a,config:a,matrix:za,typed:Ea}),Va=Ru({BigNumber:da,DenseMatrix:ya,config:a,equalScalar:Fa,matrix:za,typed:Ea,zeros:La}),Ya=Uu({DenseMatrix:ya,concat:ka,equalScalar:Fa,matrix:za,subtractScalar:Ta,typed:Ea,unaryMinus:Oa}),Za=ku({DenseMatrix:ya,SparseMatrix:_a,addScalar:xa,concat:ka,equalScalar:Fa,matrix:za,typed:Ea}),Wa=qu({addScalar:xa,conj:Aa,multiplyScalar:Sa,size:Pa,typed:Ea}),$a=Vu({DenseMatrix:ya,config:a,equalScalar:Fa,matrix:za,round:Va,typed:Ea,zeros:La}),Ja=Wu({addScalar:xa,dot:Wa,equalScalar:Fa,matrix:za,multiplyScalar:Sa,typed:Ea}),Xa=Qu({DenseMatrix:ya,config:a,equalScalar:Fa,matrix:za,round:Va,typed:Ea,zeros:La}),Qa=Ku({divideScalar:qa,isZero:Ma,matrix:za,multiply:Ja,subtractScalar:Ta,typed:Ea,unaryMinus:Oa}),Ka=ra({Complex:Da,DenseMatrix:ya,ceil:Xa,equalScalar:Fa,floor:$a,matrix:za,typed:Ea,zeros:La}),eo=ia({abs:wa,addScalar:xa,det:Qa,divideScalar:qa,identity:Ga,matrix:za,multiply:Ja,typed:Ea,unaryMinus:Oa}),to=ua({Complex:Da,config:a,fraction:Ra,identity:Ga,inv:eo,matrix:za,multiply:Ja,number:Ba,typed:Ea}),no=la({BigNumber:da,Complex:Da,Fraction:va,abs:wa,addScalar:xa,config:a,divideScalar:qa,equal:ja,fix:Ka,format:Ca,isNumeric:Ia,multiplyScalar:Sa,number:Ba,pow:to,round:Va,subtractScalar:Ta}),ro=pa({divideScalar:qa,equalScalar:Fa,inv:eo,matrix:za,multiply:Ja,typed:Ea}),io=ma({Unit:no,typed:Ea}),uo=n(1669),ao=function(e,t,n){if(n||2===arguments.length)for(var r,i=0,u=t.length;i<u;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},oo={settings:{delayTimer:0,number:{precision:0,thousandsSep:",",decimalsSep:"."},currency:{symbol:"$",format:"%s%v",decimalsSep:".",thousandsSep:",",precision:2}},delay:function(e,t){return clearTimeout(this.settings.delayTimer),this.settings.delayTimer=setTimeout(e,t),this.settings.delayTimer},filterQuery:function(e,t){for(var n=e.split("&"),r=0;r<n.length;r++){var i=n[r].split("=");if(i[0]===t)return i[1]}return!1},filterByData:function(e,t,n){return void 0===n?e.filter((function(e,n){return void 0!==uo(n).data(t)})):e.filter((function(e,r){return uo(r).data(t)==n}))},addNotice:function(e,t,n,r){void 0===n&&(n=!1),void 0===r&&(r=5);var i=uo('<div class="notice-'.concat(e,' notice is-dismissible"><p><strong>').concat(t,"</strong></p></div>")).hide(),u=uo("<button />",{type:"button",class:"notice-dismiss"}),a=uo(".wp-header-end");a.siblings(".notice").remove(),a.before(i.append(u)),i.slideDown(100),u.on("click.wp-dismiss-notice",(function(e){e.preventDefault(),i.fadeTo(100,0,(function(){i.slideUp(100,(function(){i.remove()}))}))})),n&&setTimeout((function(){u.trigger("click.wp-dismiss-notice")}),1e3*r)},imagesLoaded:function(e){var t=e.find('img[src!=""]');if(!t.length)return uo.Deferred().resolve().promise();var n=[];return t.each((function(e,t){var r=uo.Deferred(),i=new Image;n.push(r),i.onload=function(){return r.resolve()},i.onerror=function(){return r.resolve()},i.src=uo(t).attr("src")})),uo.when.apply(uo,n)},getUrlParameter:function(e){if("undefined"!=typeof URLSearchParams)return new URLSearchParams(window.location.search).get(e);e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var t=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(window.location.search);return null===t?"":decodeURIComponent(t[1].replace(/\+/g," "))},getQueryParams:function(e){var t={};return new URLSearchParams(e).forEach((function(e,n){var r=decodeURIComponent(n),i=decodeURIComponent(e);r.endsWith("[]")?(r=r.replace("[]",""),t[r]||(t[r]=[]),t[r].push(i)):t[r]=i})),t},htmlDecode:function(e){var t=document.createElement("div");return t.innerHTML=e,t.childNodes[0].nodeValue},areEquivalent:function(e,t,n){void 0===n&&(n=!1);var r=Object.getOwnPropertyNames(e),i=Object.getOwnPropertyNames(t);if(r.length!=i.length)return!1;for(var u=0;u<r.length;u++){var a=r[u];if(n&&e[a]!==t[a]||!n&&e[a]!=t[a])return!1}return!0},toggleNodes:function(e,t){for(var n=0;n<e.length;n++)e[n].isExpanded="open"==t,e[n].children&&e[n].children.length>0&&this.toggleNodes(e[n].children,t)},formatNumber:function(e,t,n,r,i){void 0===t&&(t=this.settings.number.precision),void 0===n&&(n=this.settings.number.thousandsSep),void 0===r&&(r=this.settings.number.decimalsSep),void 0===i&&(i=1),e>999&&n===r&&!Number.isInteger(e)&&(n="");var u={minimumFractionDigits:t};return i&&(u.minimumSignificantDigits=i),e.toLocaleString("en",u).replace(new RegExp("\\,","g"),n).replace(new RegExp("\\."),r)},formatMoney:function(e,t,n,r,i,u){void 0===t&&(t=this.settings.currency.symbol),void 0===n&&(n=this.settings.currency.precision),void 0===r&&(r=this.settings.currency.thousandsSep),void 0===i&&(i=this.settings.currency.decimalsSep),void 0===u&&(u=this.settings.currency.format),this.isNumeric(e)||(e=this.unformat(e));var a=this.checkCurrencyFormat(u);return(e>0?a.pos:e<0?a.neg:a.zero).replace("%s",t).replace("%v",this.formatNumber(Math.abs(e),this.checkPrecision(n),r,i,null))},unformat:function(e,t){if(void 0===t&&(t=this.settings.number.decimalsSep),"number"==typeof e)return e;var n=new RegExp("[^0-9-".concat(t,"]"),"g"),r=parseFloat((""+e).replace(/\((.*)\)/,"-$1").replace(n,"").replace(t,"."));return isNaN(r)?0:r},checkPrecision:function(e,t){return void 0===t&&(t=0),e=Math.round(Math.abs(e)),isNaN(e)?t:e},checkCurrencyFormat:function(e){if("function"==typeof e)return e();if("string"==typeof e&&e.match("%v"))return{pos:e,neg:e.replace("-","").replace("%v","-%v"),zero:e};if(!e||"object"==typeof e&&(!e.pos||!e.pos.match("%v"))){var t=this.settings.currency.format;return"string"!=typeof t?t:this.settings.currency.format={pos:t,neg:t.replace("%v","-%v"),zero:t}}},countDecimals:function(e){return Math.floor(e)===e?0:e.toString().split(".")[1].length||0},multiplyDecimals:function(e,t){return Ba(Ja(ba(this.isNumeric(e)?e:0),ba(this.isNumeric(t)?t:0)))},divideDecimals:function(e,t){return Ba(ro(ba(this.isNumeric(e)?e:0),ba(this.isNumeric(t)?t:0)))},sumDecimals:function(e,t){return Ba(Za(ba(this.isNumeric(e)?e:0),ba(this.isNumeric(t)?t:0)))},subtractDecimals:function(e,t){return Ba(Ya(ba(this.isNumeric(e)?e:0),ba(this.isNumeric(t)?t:0)))},isNumeric:function(e){return Ha(e)},round:function(e,t){return Va(e,t)},convertUnit:function(e,t,n){return Ba(io(e,t),n)},convertElemsToString:function(e){return uo("<div />").append(e).html()},mergeArrays:function(e,t){return Array.from(new Set(ao(ao([],e,!0),t,!0)))},restrictNumberInputValues:function(e){if("number"===e.attr("type")){var t=e.val(),n=parseFloat(t||"0"),r=e.attr("min"),i=e.attr("max"),u=parseFloat(r||"0"),a=parseFloat(i||"0");this.isNumeric(t)?void 0!==r&&n<u?e.val(u):void 0!==i&&n>a&&e.val(a):e.val(void 0!==r&&!isNaN(u)&&u>0?u:0)}},checkRTL:function(e){var t=!1;switch(uo('html[ dir="rtl" ]').length>0&&(t=!0),e){case"isRTL":case"reverse":return t;case"xSide":return t?"right":"left";default:return!1}},calcTaxesFromBase:function(e,t){var n,r=this,i=[0];return uo.each(t,(function(t,n){if("yes"===n.compound)return!0;i.push(r.divideDecimals(r.multiplyDecimals(e,n.rate),100))})),n=i.reduce((function(e,t){return r.sumDecimals(e,t)}),0),uo.each(t,(function(t,u){var a;if("no"===u.compound)return!0;a=r.divideDecimals(r.multiplyDecimals(r.sumDecimals(e,n),u.rate),100),i.push(a),n=r.sumDecimals(a,n)})),i.reduce((function(e,t){return r.sumDecimals(e,t)}),0)},pseudoClick:function(e,t,n){void 0===n&&(n="both");var r=!1,i=!1,u=t.get(0),a=parseInt(u.getBoundingClientRect().left.toString(),10),o=parseInt(u.getBoundingClientRect().top.toString(),10),s=e.clientX,f=e.clientY;if(["before","both"].includes(n)){var c=window.getComputedStyle(u,":before"),l=a+parseInt(c.getPropertyValue("left"),10),p=l+parseInt(c.width,10),h=o+parseInt(c.getPropertyValue("top"),10),m=h+parseInt(c.height,10);r=s>=l&&s<=p&&f>=h&&f<=m}if(["after","both"].includes(n)){var d=window.getComputedStyle(u,":after"),D=a+parseInt(d.getPropertyValue("left"),10),v=D+parseInt(d.width,10),g=o+parseInt(d.getPropertyValue("top"),10),y=g+parseInt(d.height,10);i=s>=D&&s<=v&&f>=g&&f<=y}switch(n){case"after":return i;case"before":return r;default:return{before:r,after:i}}},isElementInViewport:function(e){var t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom+80<=window.innerHeight&&t.right<=window.innerWidth}};const so=oo;var fo=n(1669);const co=function(){function e(e){var t=this;this.settings=e,this.$pageWrapper=fo("#wpbody-content"),this.$tabContentWrapper=fo("#screen-meta"),this.$tabsWrapper=fo("#screen-meta-links"),this.createExportTab(),this.$pageWrapper.on("submit","#atum-export-settings",(function(e){e.preventDefault(),t.downloadReport()})).on("change","#disableMaxLength",(function(e){var t=fo(e.currentTarget),n=t.parent().siblings("input[type=number]");t.is(":checked")?n.prop("disabled",!0):n.prop("disabled",!1)}))}return e.prototype.createExportTab=function(){var e=this.$tabsWrapper.find("#screen-options-link-wrap").clone(),t=this.$tabContentWrapper.find("#screen-options-wrap").clone();if(t.attr({id:"atum-export-wrap","aria-label":this.settings.get("tabTitle")}),t.find("form").attr("id","atum-export-settings").find("input").removeAttr("id"),t.find(".screen-options").remove(),t.find("input[type=submit]").val(this.settings.get("submitTitle")),t.find("#screenoptionnonce").remove(),void 0!==this.settings.get("productTypes")){var n=fo('<fieldset class="product-type" />');n.append("<legend>".concat(this.settings.get("productTypesTitle"),"</legend>")),n.append(this.settings.get("productTypes")),n.insertAfter(t.find("fieldset").last())}if(void 0!==this.settings.get("categories")){var r=fo('<fieldset class="product-category" />');r.append("<legend>".concat(this.settings.get("categoriesTitle"),"</legend>")),r.append(this.settings.get("categories")),r.insertAfter(t.find("fieldset").last())}"object"==typeof this.settings.get("extraFields")&&this.settings.get("extraFields").forEach((function(e){var n="hidden"===e.title?fo("<span>"):fo('<fieldset class="extra" />');"hidden"!==e.title&&n.append("<legend>".concat(e.title,"</legend>")),n.append(e.content),n.insertAfter(t.find("fieldset").last())}));var i=fo('<fieldset class="title-length" />');i.append("<legend>".concat(this.settings.get("titleLength"),"</legend>")),i.append('<input type="number" step="1" min="0" name="title_max_length" value="'.concat(this.settings.get("maxLength"),'"> ')),i.append('<label><input type="checkbox" id="disableMaxLength" value="yes">'.concat(this.settings.get("disableMaxLength"),"</label>")),i.insertAfter(t.find("fieldset").last());var u=fo('<fieldset class="output-format" />');u.append("<legend>".concat(this.settings.get("outputFormatTitle"),"</legend>")),fo.each(this.settings.get("outputFormats"),(function(e,t){u.append('<label><input type="radio" name="output-format" value="'.concat(e,'">').concat(t,"</label>"))})),u.find("input[name=output-format]").first().prop("checked",!0),u.insertAfter(t.find("fieldset").last()),t.find(".submit").before('<div class="clear"></div>'),e.attr("id","atum-export-link-wrap").find("button").attr({id:"show-export-settings-link","aria-controls":"atum-export-wrap"}).text(this.settings.get("tabTitle")),this.$tabContentWrapper.append(t),this.$tabsWrapper.prepend(e),fo("#show-export-settings-link").on("click",window.screenMeta.toggleEvent),this.$exportForm=this.$pageWrapper.find("#atum-export-settings")},e.prototype.downloadReport=function(){window.open("".concat(window.ajaxurl,"?action=atum_export_data&page=").concat(so.getUrlParameter("page"),"&screen=").concat(this.settings.get("screen"),"&security=").concat(this.settings.get("exportNonce"),"&").concat(this.$exportForm.serialize()),"_blank")},e}();n(1669)((function(t){var n=new e("atumExport");new co(n)}))})()})();