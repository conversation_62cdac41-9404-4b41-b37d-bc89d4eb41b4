/**
 * gridstack.js 1.0.0-dev
 * http://troolee.github.io/gridstack.js/
 * (c) 2014-2017 <PERSON>, <PERSON>
 * gridstack.js may be freely distributed under the MIT license.
 * @preserve
*/
!function(t){if("function"==typeof define&&define.amd)define(["jquery","lodash"],t);else if("undefined"!=typeof exports){try{jQuery=require("jquery")}catch(t){}try{_=require("lodash")}catch(t){}t(jQuery,_)}else t(jQuery,_)}(function(t,e){function i(t){this.grid=t}var o=window,a=function(t,e,i){var o=function(){return console.warn("gridstack.js: Function `"+e+"` is deprecated as of v0.2.5 and has been replaced with `"+i+"`. It will be **completely** removed in v1.0."),t.apply(this,arguments)};return o.prototype=t.prototype,o},s=function(t,e){console.warn("gridstack.js: Option `"+t+"` is deprecated as of v0.2.5 and has been replaced with `"+e+"`. It will be **completely** removed in v1.0.")},r={isIntercepted:function(t,e){return!(t.x+t.width<=e.x||e.x+e.width<=t.x||t.y+t.height<=e.y||e.y+e.height<=t.y)},sort:function(t,i,o){return o=o||e.chain(t).map(function(t){return t.x+t.width}).max().value(),i=-1!=i?1:-1,e.sortBy(t,function(t){return i*(t.x+t.y*o)})},createStylesheet:function(t){var e=document.createElement("style");return e.setAttribute("type","text/css"),e.setAttribute("data-gs-style-id",t),e.styleSheet?e.styleSheet.cssText="":e.appendChild(document.createTextNode("")),document.getElementsByTagName("head")[0].appendChild(e),e.sheet},removeStylesheet:function(e){t("STYLE[data-gs-style-id="+e+"]").remove()},insertCSSRule:function(t,e,i,o){"function"==typeof t.insertRule?t.insertRule(e+"{"+i+"}",o):"function"==typeof t.addRule&&t.addRule(e,i,o)},toBool:function(t){return"boolean"==typeof t?t:"string"==typeof t?!(""===(t=t.toLowerCase())||"no"==t||"false"==t||"0"==t):Boolean(t)},_collisionNodeCheck:function(t){return t!=this.node&&r.isIntercepted(t,this.nn)},_didCollide:function(t){return r.isIntercepted({x:this.n.x,y:this.newY,width:this.n.width,height:this.n.height},t)},_isAddNodeIntercepted:function(t){return r.isIntercepted({x:this.x,y:this.y,width:this.node.width,height:this.node.height},t)},parseHeight:function(t){var i=t,o="px";if(i&&e.isString(i)){var a=i.match(/^(-[0-9]+\.[0-9]+|[0-9]*\.[0-9]+|-[0-9]+|[0-9]+)(px|em|rem|vh|vw)?$/);if(!a)throw new Error("Invalid height");o=a[2]||"px",i=parseFloat(a[1])}return{height:i,unit:o}},removePositioningStyles:function(t){var e=t[0].style;e.position&&e.removeProperty("position"),e.left&&e.removeProperty("left"),e.top&&e.removeProperty("top"),e.width&&e.removeProperty("width"),e.height&&e.removeProperty("height")}};r.is_intercepted=a(r.isIntercepted,"is_intercepted","isIntercepted"),r.create_stylesheet=a(r.createStylesheet,"create_stylesheet","createStylesheet"),r.remove_stylesheet=a(r.removeStylesheet,"remove_stylesheet","removeStylesheet"),r.insert_css_rule=a(r.insertCSSRule,"insert_css_rule","insertCSSRule"),i.registeredPlugins=[],i.registerPlugin=function(t){i.registeredPlugins.push(t)},i.prototype.resizable=function(t,e){return this},i.prototype.draggable=function(t,e){return this},i.prototype.droppable=function(t,e){return this},i.prototype.isDroppable=function(t){return!1},i.prototype.on=function(t,e,i){return this};var d=0,n=function(t,e,i,o,a){this.width=t,this.float=i||!1,this.height=o||0,this.nodes=a||[],this.onchange=e||function(){},this._updateCounter=0,this._float=this.float,this._addedNodes=[],this._removedNodes=[]};n.prototype.batchUpdate=function(){this._updateCounter=1,this.float=!0},n.prototype.commit=function(){0!==this._updateCounter&&(this._updateCounter=0,this.float=this._float,this._packNodes(),this._notify())},n.prototype.getNodeDataByDOMEl=function(t){return e.find(this.nodes,function(e){return t.get(0)===e.el.get(0)})},n.prototype._fixCollisions=function(t){this._sortNodes(-1);var i=t,o=Boolean(e.find(this.nodes,function(t){return t.locked}));for(this.float||o||(i={x:0,y:t.y,width:this.width,height:t.height});;){var a=e.find(this.nodes,e.bind(r._collisionNodeCheck,{node:t,nn:i}));if(void 0===a)return;this.moveNode(a,a.x,t.y+t.height,a.width,a.height,!0)}},n.prototype.isAreaEmpty=function(t,i,o,a){var s={x:t||0,y:i||0,width:o||1,height:a||1},d=e.find(this.nodes,e.bind(function(t){return r.isIntercepted(t,s)},this));return null===d||void 0===d},n.prototype._sortNodes=function(t){this.nodes=r.sort(this.nodes,t,this.width)},n.prototype._packNodes=function(){this._sortNodes(),this.float?e.each(this.nodes,e.bind(function(t,i){if(!t._updating&&void 0!==t._origY&&t.y!=t._origY)for(var o=t.y;o>=t._origY;)e.chain(this.nodes).take(i).find(e.bind(r._didCollide,{n:t,newY:o})).value()||(t._dirty=!0,t.y=o),--o},this)):e.each(this.nodes,e.bind(function(t,i){if(!t.locked)for(;t.y>0;){var o=t.y-1,a=0===i;if(i>0&&(a=void 0===e.chain(this.nodes).take(i).find(e.bind(r._didCollide,{n:t,newY:o})).value()),!a)break;t._dirty=t.y!=o,t.y=o}},this))},n.prototype._prepareNode=function(t,i){return t=e.defaults(t||{},{width:1,height:1,x:0,y:0}),t.x=parseInt(""+t.x),t.y=parseInt(""+t.y),t.width=parseInt(""+t.width),t.height=parseInt(""+t.height),t.autoPosition=t.autoPosition||!1,t.noResize=t.noResize||!1,t.noMove=t.noMove||!1,t.width>this.width?t.width=this.width:t.width<1&&(t.width=1),t.height<1&&(t.height=1),t.x<0&&(t.x=0),t.x+t.width>this.width&&(i?t.width=this.width-t.x:t.x=this.width-t.width),t.y<0&&(t.y=0),t},n.prototype._notify=function(){var t=Array.prototype.slice.call(arguments,0);if(t[0]=void 0===t[0]?[]:[t[0]],t[1]=void 0===t[1]||t[1],!this._updateCounter){var e=t[0].concat(this.getDirtyNodes());this.onchange(e,t[1])}},n.prototype.cleanNodes=function(){this._updateCounter||e.each(this.nodes,function(t){t._dirty=!1})},n.prototype.getDirtyNodes=function(){return e.filter(this.nodes,function(t){return t._dirty})},n.prototype.addNode=function(t,i){if(void 0!==(t=this._prepareNode(t)).maxWidth&&(t.width=Math.min(t.width,t.maxWidth)),void 0!==t.maxHeight&&(t.height=Math.min(t.height,t.maxHeight)),void 0!==t.minWidth&&(t.width=Math.max(t.width,t.minWidth)),void 0!==t.minHeight&&(t.height=Math.max(t.height,t.minHeight)),t._id=++d,t._dirty=!0,t.autoPosition){this._sortNodes();for(var o=0;;++o){var a=o%this.width,s=Math.floor(o/this.width);if(!(a+t.width>this.width)&&!e.find(this.nodes,e.bind(r._isAddNodeIntercepted,{x:a,y:s,node:t}))){t.x=a,t.y=s;break}}}return this.nodes.push(t),void 0!==i&&i&&this._addedNodes.push(e.clone(t)),this._fixCollisions(t),this._packNodes(),this._notify(),t},n.prototype.removeNode=function(t,i){i=void 0===i||i,this._removedNodes.push(e.clone(t)),t._id=null,this.nodes=e.without(this.nodes,t),this._packNodes(),this._notify(t,i)},n.prototype.canMoveNode=function(i,o,a,s,r){if(!this.isNodeChangedPosition(i,o,a,s,r))return!1;var d=Boolean(e.find(this.nodes,function(t){return t.locked}));if(!this.height&&!d)return!0;var h,l=new n(this.width,null,this.float,0,e.map(this.nodes,function(e){return e==i?h=t.extend({},e):t.extend({},e)}));if(void 0===h)return!0;l.moveNode(h,o,a,s,r);var p=!0;return d&&(p&=!Boolean(e.find(l.nodes,function(t){return t!=h&&Boolean(t.locked)&&Boolean(t._dirty)}))),this.height&&(p&=l.getGridHeight()<=this.height),p},n.prototype.canBePlacedWithRespectToHeight=function(i){if(!this.height)return!0;var o=new n(this.width,null,this.float,0,e.map(this.nodes,function(e){return t.extend({},e)}));return o.addNode(i),o.getGridHeight()<=this.height},n.prototype.isNodeChangedPosition=function(t,e,i,o,a){return"number"!=typeof e&&(e=t.x),"number"!=typeof i&&(i=t.y),"number"!=typeof o&&(o=t.width),"number"!=typeof a&&(a=t.height),void 0!==t.maxWidth&&(o=Math.min(o,t.maxWidth)),void 0!==t.maxHeight&&(a=Math.min(a,t.maxHeight)),void 0!==t.minWidth&&(o=Math.max(o,t.minWidth)),void 0!==t.minHeight&&(a=Math.max(a,t.minHeight)),t.x!=e||t.y!=i||t.width!=o||t.height!=a},n.prototype.moveNode=function(t,e,i,o,a,s){if(!this.isNodeChangedPosition(t,e,i,o,a))return t;if("number"!=typeof e&&(e=t.x),"number"!=typeof i&&(i=t.y),"number"!=typeof o&&(o=t.width),"number"!=typeof a&&(a=t.height),void 0!==t.maxWidth&&(o=Math.min(o,t.maxWidth)),void 0!==t.maxHeight&&(a=Math.min(a,t.maxHeight)),void 0!==t.minWidth&&(o=Math.max(o,t.minWidth)),void 0!==t.minHeight&&(a=Math.max(a,t.minHeight)),t.x==e&&t.y==i&&t.width==o&&t.height==a)return t;var r=t.width!=o;return t._dirty=!0,t.x=e,t.y=i,t.width=o,t.height=a,t.lastTriedX=e,t.lastTriedY=i,t.lastTriedWidth=o,t.lastTriedHeight=a,t=this._prepareNode(t,r),this._fixCollisions(t),s||(this._packNodes(),this._notify()),t},n.prototype.getGridHeight=function(){return e.reduce(this.nodes,function(t,e){return Math.max(t,e.y+e.height)},0)},n.prototype.beginUpdate=function(t){e.each(this.nodes,function(t){t._origY=t.y}),t._updating=!0},n.prototype.endUpdate=function(){e.each(this.nodes,function(t){t._origY=t.y});var t=e.find(this.nodes,function(t){return t._updating});t&&(t._updating=!1)};var h=function(o,a){var d,h,l=this;a=a||{},this.container=t(o),void 0!==a.handle_class&&(a.handleClass=a.handle_class,s("handle_class","handleClass")),void 0!==a.item_class&&(a.itemClass=a.item_class,s("item_class","itemClass")),void 0!==a.placeholder_class&&(a.placeholderClass=a.placeholder_class,s("placeholder_class","placeholderClass")),void 0!==a.placeholder_text&&(a.placeholderText=a.placeholder_text,s("placeholder_text","placeholderText")),void 0!==a.cell_height&&(a.cellHeight=a.cell_height,s("cell_height","cellHeight")),void 0!==a.vertical_margin&&(a.verticalMargin=a.vertical_margin,s("vertical_margin","verticalMargin")),void 0!==a.min_width&&(a.minWidth=a.min_width,s("min_width","minWidth")),void 0!==a.static_grid&&(a.staticGrid=a.static_grid,s("static_grid","staticGrid")),void 0!==a.is_nested&&(a.isNested=a.is_nested,s("is_nested","isNested")),void 0!==a.always_show_resize_handle&&(a.alwaysShowResizeHandle=a.always_show_resize_handle,s("always_show_resize_handle","alwaysShowResizeHandle")),a.itemClass=a.itemClass||"grid-stack-item";var p=this.container.closest("."+a.itemClass).length>0;if(this.opts=e.defaults(a||{},{width:parseInt(this.container.attr("data-gs-width"))||12,height:parseInt(this.container.attr("data-gs-height"))||0,itemClass:"grid-stack-item",placeholderClass:"grid-stack-placeholder",placeholderText:"",handle:".grid-stack-item-content",handleClass:null,cellHeight:60,verticalMargin:20,auto:!0,minWidth:768,float:!1,staticGrid:!1,_class:"grid-stack-instance-"+(1e4*Math.random()).toFixed(0),animate:Boolean(this.container.attr("data-gs-animate"))||!1,alwaysShowResizeHandle:a.alwaysShowResizeHandle||!1,resizable:e.defaults(a.resizable||{},{autoHide:!a.alwaysShowResizeHandle,handles:"se"}),draggable:e.defaults(a.draggable||{},{handle:(a.handleClass?"."+a.handleClass:a.handle?a.handle:"")||".grid-stack-item-content",scroll:!1,appendTo:"body"}),disableDrag:a.disableDrag||!1,disableResize:a.disableResize||!1,rtl:"auto",removable:!1,removableOptions:e.defaults(a.removableOptions||{},{accept:"."+a.itemClass}),removeTimeout:2e3,verticalMarginUnit:"px",cellHeightUnit:"px",disableOneColumnMode:a.disableOneColumnMode||!1,oneColumnModeClass:a.oneColumnModeClass||"grid-stack-one-column-mode",ddPlugin:null}),!1===this.opts.ddPlugin?this.opts.ddPlugin=i:null===this.opts.ddPlugin&&(this.opts.ddPlugin=e.first(i.registeredPlugins)||i),this.dd=new this.opts.ddPlugin(this),"auto"===this.opts.rtl&&(this.opts.rtl="rtl"===this.container.css("direction")),this.opts.rtl&&this.container.addClass("grid-stack-rtl"),this.opts.isNested=p,h="auto"===this.opts.cellHeight,h?l.cellHeight(l.cellWidth(),!0):this.cellHeight(this.opts.cellHeight,!0),this.verticalMargin(this.opts.verticalMargin,!0),this.container.addClass(this.opts._class),this._setStaticClass(),p&&this.container.addClass("grid-stack-nested"),this._initStyles(),this.grid=new n(this.opts.width,function(t,i){i=void 0===i||i;var o=0;e.each(t,function(t){i&&null===t._id?t.el&&t.el.remove():(t.el.attr("data-gs-x",t.x).attr("data-gs-y",t.y).attr("data-gs-width",t.width).attr("data-gs-height",t.height),o=Math.max(o,t.y+t.height))}),l._updateStyles(o+10)},this.opts.float,this.opts.height),this.opts.auto){var g=[],c=this;this.container.children("."+this.opts.itemClass+":not(."+this.opts.placeholderClass+")").each(function(e,i){i=t(i),g.push({el:i,i:parseInt(i.attr("data-gs-x"))+parseInt(i.attr("data-gs-y"))*c.opts.width})}),e.chain(g).sortBy(function(t){return t.i}).each(function(t){l._prepareElement(t.el)}).value()}if(this.setAnimation(this.opts.animate),this.placeholder=t('<div class="'+this.opts.placeholderClass+" "+this.opts.itemClass+'"><div class="placeholder-content">'+this.opts.placeholderText+"</div></div>").hide(),this._updateContainerHeight(),this._updateHeightsOnResize=e.throttle(function(){l.cellHeight(l.cellWidth(),!1)},100),this.onResizeHandler=function(){if(h&&l._updateHeightsOnResize(),l._isOneColumnMode()&&!l.opts.disableOneColumnMode){if(d)return;l.container.addClass(l.opts.oneColumnModeClass),d=!0,l.grid._sortNodes(),e.each(l.grid.nodes,function(t){l.container.append(t.el),l.opts.staticGrid||(l.dd.draggable(t.el,"disable"),l.dd.resizable(t.el,"disable"),t.el.trigger("resize"))})}else{if(!d)return;if(l.container.removeClass(l.opts.oneColumnModeClass),d=!1,l.opts.staticGrid)return;e.each(l.grid.nodes,function(t){t.noMove||l.opts.disableDrag||l.dd.draggable(t.el,"enable"),t.noResize||l.opts.disableResize||l.dd.resizable(t.el,"enable"),t.el.trigger("resize")})}},t(window).resize(this.onResizeHandler),this.onResizeHandler(),!l.opts.staticGrid&&"string"==typeof l.opts.removable){var _=t(l.opts.removable);this.dd.isDroppable(_)||this.dd.droppable(_,l.opts.removableOptions),this.dd.on(_,"dropover",function(e,i){var o=t(i.draggable);o.data("_gridstack_node")._grid===l&&(o.data("inTrashZone",!0),l._setupRemovingTimeout(o))}).on(_,"dropout",function(e,i){var o=t(i.draggable);o.data("_gridstack_node")._grid===l&&(o.data("inTrashZone",!1),l._clearRemovingTimeout(o))})}if(!l.opts.staticGrid&&l.opts.acceptWidgets){var u=null,m=function(t,e){var i=u,o=i.data("_gridstack_node"),a=l.getCellFromPixel({left:t.pageX,top:t.pageY},!0),s=Math.max(0,a.x),r=Math.max(0,a.y);o._added||(o._added=!0,o.el=i,o.autoPosition=!0,o.x=s,o.y=r,l.grid.cleanNodes(),l.grid.beginUpdate(o),l.grid.addNode(o),l.container.append(l.placeholder),l.placeholder.attr("data-gs-x",o.x).attr("data-gs-y",o.y).attr("data-gs-width",o.width).attr("data-gs-height",o.height).show(),o.el=l.placeholder,o._beforeDragX=o.x,o._beforeDragY=o.y,l._updateContainerHeight()),l.grid.canMoveNode(o,s,r)&&(l.grid.moveNode(o,s,r),l._updateContainerHeight())};this.dd.droppable(l.container,{accept:function(e){var i=(e=t(e)).data("_gridstack_node");return(!i||i._grid!==l)&&e.is(!0===l.opts.acceptWidgets?".grid-stack-item":l.opts.acceptWidgets)}}).on(l.container,"dropover",function(e,i){l.container.offset();var o=t(i.draggable),a=l.cellWidth(),s=l.cellHeight(),r=o.data("_gridstack_node"),d=r?r.width:Math.ceil(o.outerWidth()/a),n=r?r.height:Math.ceil(o.outerHeight()/s);u=o;var h=l.grid._prepareNode({width:d,height:n,_added:!1,_temporary:!0});o.data("_gridstack_node",h),o.data("_gridstack_node_orig",r),o.on("drag",m)}).on(l.container,"dropout",function(e,i){var o=t(i.draggable);if(o.data("_gridstack_node")){o.unbind("drag",m);var a=o.data("_gridstack_node");a.el=null,l.grid.removeNode(a),l.placeholder.detach(),l._updateContainerHeight(),o.data("_gridstack_node",o.data("_gridstack_node_orig"))}}).on(l.container,"drop",function(e,i){l.placeholder.detach();var o=t(i.draggable).data("_gridstack_node");o._grid=l;var a=t(i.draggable).clone(!1);a.data("_gridstack_node",o);var s=t(i.draggable).data("_gridstack_node_orig");void 0!==s&&void 0!==s._grid&&s._grid._triggerRemoveEvent(),t(i.helper).remove(),o.el=a,l.placeholder.hide(),r.removePositioningStyles(a),a.find("div.ui-resizable-handle").remove(),a.attr("data-gs-x",o.x).attr("data-gs-y",o.y).attr("data-gs-width",o.width).attr("data-gs-height",o.height).addClass(l.opts.itemClass).enableSelection().removeData("draggable").removeClass("ui-draggable ui-draggable-dragging ui-draggable-disabled").unbind("drag",m),l.container.append(a),l._prepareElementsByNode(a,o),l._updateContainerHeight(),l.grid._addedNodes.push(o),l._triggerAddEvent(),l._triggerChangeEvent(),l.grid.endUpdate(),t(i.draggable).unbind("drag",m),t(i.draggable).removeData("_gridstack_node"),t(i.draggable).removeData("_gridstack_node_orig")})}};return h.prototype._triggerChangeEvent=function(t){var e=this.grid.getDirtyNodes(),i=!1,o=[];e&&e.length&&(o.push(e),i=!0),(i||!0===t)&&this.container.trigger("change",o)},h.prototype._triggerAddEvent=function(){this.grid._addedNodes&&this.grid._addedNodes.length>0&&(this.container.trigger("added",[e.map(this.grid._addedNodes,e.clone)]),this.grid._addedNodes=[])},h.prototype._triggerRemoveEvent=function(){this.grid._removedNodes&&this.grid._removedNodes.length>0&&(this.container.trigger("removed",[e.map(this.grid._removedNodes,e.clone)]),this.grid._removedNodes=[])},h.prototype._initStyles=function(){this._stylesId&&r.removeStylesheet(this._stylesId),this._stylesId="gridstack-style-"+(1e5*Math.random()).toFixed(),this._styles=r.createStylesheet(this._stylesId),null!==this._styles&&(this._styles._max=0)},h.prototype._updateStyles=function(t){if(null!==this._styles&&void 0!==this._styles){var e,i="."+this.opts._class+" ."+this.opts.itemClass,o=this;if(void 0===t&&(t=this._styles._max),!(0!==this._styles._max&&t<=this._styles._max)&&(this._initStyles(),this._updateContainerHeight(),this.opts.cellHeight&&(e=this.opts.verticalMargin&&this.opts.cellHeightUnit!==this.opts.verticalMarginUnit?function(t,e){return t&&e?"calc("+(o.opts.cellHeight*t+o.opts.cellHeightUnit)+" + "+(o.opts.verticalMargin*e+o.opts.verticalMarginUnit)+")":o.opts.cellHeight*t+o.opts.verticalMargin*e+o.opts.cellHeightUnit}:function(t,e){return o.opts.cellHeight*t+o.opts.verticalMargin*e+o.opts.cellHeightUnit},0===this._styles._max&&r.insertCSSRule(this._styles,i,"min-height: "+e(1,0)+";",0),t>this._styles._max))){for(var a=this._styles._max;a<t;++a)r.insertCSSRule(this._styles,i+'[data-gs-height="'+(a+1)+'"]',"height: "+e(a+1,a)+";",a),r.insertCSSRule(this._styles,i+'[data-gs-min-height="'+(a+1)+'"]',"min-height: "+e(a+1,a)+";",a),r.insertCSSRule(this._styles,i+'[data-gs-max-height="'+(a+1)+'"]',"max-height: "+e(a+1,a)+";",a),r.insertCSSRule(this._styles,i+'[data-gs-y="'+a+'"]',"top: "+e(a,a)+";",a);this._styles._max=t}}},h.prototype._updateContainerHeight=function(){if(!this.grid._updateCounter){var t=this.grid.getGridHeight(),e=parseInt(this.container.css("min-height"));if(e>0){var i=(e+this.opts.verticalMargin)/(this.cellHeight()+this.opts.verticalMargin);t<i&&(t=i)}this.container.attr("data-gs-current-height",t),this.opts.cellHeight&&(this.opts.verticalMargin?this.opts.cellHeightUnit===this.opts.verticalMarginUnit?this.container.css("height",t*(this.opts.cellHeight+this.opts.verticalMargin)-this.opts.verticalMargin+this.opts.cellHeightUnit):this.container.css("height","calc("+(t*this.opts.cellHeight+this.opts.cellHeightUnit)+" + "+(t*(this.opts.verticalMargin-1)+this.opts.verticalMarginUnit)+")"):this.container.css("height",t*this.opts.cellHeight+this.opts.cellHeightUnit))}},h.prototype._isOneColumnMode=function(){return(window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth)<=this.opts.minWidth},h.prototype._setupRemovingTimeout=function(e){var i=this,o=t(e).data("_gridstack_node");!o._removeTimeout&&i.opts.removable&&(o._removeTimeout=setTimeout(function(){e.addClass("grid-stack-item-removing"),o._isAboutToRemove=!0},i.opts.removeTimeout))},h.prototype._clearRemovingTimeout=function(e){var i=t(e).data("_gridstack_node");i._removeTimeout&&(clearTimeout(i._removeTimeout),i._removeTimeout=null,e.removeClass("grid-stack-item-removing"),i._isAboutToRemove=!1)},h.prototype._prepareElementsByNode=function(e,i){var o,a,s=this,d=function(t,r){var d,n,h=Math.round(r.position.left/o),l=Math.floor((r.position.top+a/2)/a);if("drag"!=t.type&&(d=Math.round(r.size.width/o),n=Math.round(r.size.height/a)),"drag"==t.type)e.data("inTrashZone")||h<0||h>=s.grid.width||l<0||!s.grid.float&&l>s.grid.getGridHeight()?i._temporaryRemoved||(!0===s.opts.removable&&s._setupRemovingTimeout(e),h=i._beforeDragX,l=i._beforeDragY,s.placeholder.detach(),s.placeholder.hide(),s.grid.removeNode(i),s._updateContainerHeight(),i._temporaryRemoved=!0):(s._clearRemovingTimeout(e),i._temporaryRemoved&&(s.grid.addNode(i),s.placeholder.attr("data-gs-x",h).attr("data-gs-y",l).attr("data-gs-width",d).attr("data-gs-height",n).show(),s.container.append(s.placeholder),i.el=s.placeholder,i._temporaryRemoved=!1));else if("resize"==t.type&&h<0)return;var p=void 0!==d?d:i.lastTriedWidth,g=void 0!==n?n:i.lastTriedHeight;!s.grid.canMoveNode(i,h,l,d,n)||i.lastTriedX===h&&i.lastTriedY===l&&i.lastTriedWidth===p&&i.lastTriedHeight===g||(i.lastTriedX=h,i.lastTriedY=l,i.lastTriedWidth=d,i.lastTriedHeight=n,s.grid.moveNode(i,h,l,d,n),s._updateContainerHeight())},n=function(r,d){s.container.append(s.placeholder);var n=t(this);s.grid.cleanNodes(),s.grid.beginUpdate(i),o=s.cellWidth();var h=Math.ceil(n.outerHeight()/n.attr("data-gs-height"));a=s.container.height()/parseInt(s.container.attr("data-gs-current-height")),s.placeholder.attr("data-gs-x",n.attr("data-gs-x")).attr("data-gs-y",n.attr("data-gs-y")).attr("data-gs-width",n.attr("data-gs-width")).attr("data-gs-height",n.attr("data-gs-height")).show(),i.el=s.placeholder,i._beforeDragX=i.x,i._beforeDragY=i.y,s.dd.resizable(e,"option","minWidth",o*(i.minWidth||1)),s.dd.resizable(e,"option","minHeight",h*(i.minHeight||1)),"resizestart"==r.type&&n.find(".grid-stack-item").trigger("resizestart")},h=function(o,a){var d=t(this);if(d.data("_gridstack_node")){var n=!1;s.placeholder.detach(),i.el=d,s.placeholder.hide(),i._isAboutToRemove?(n=!0,e.data("_gridstack_node")._grid._triggerRemoveEvent(),e.removeData("_gridstack_node"),e.remove()):(s._clearRemovingTimeout(e),i._temporaryRemoved?(r.removePositioningStyles(d),d.attr("data-gs-x",i._beforeDragX).attr("data-gs-y",i._beforeDragY).attr("data-gs-width",i.width).attr("data-gs-height",i.height),i.x=i._beforeDragX,i.y=i._beforeDragY,s.grid.addNode(i)):(r.removePositioningStyles(d),d.attr("data-gs-x",i.x).attr("data-gs-y",i.y).attr("data-gs-width",i.width).attr("data-gs-height",i.height))),s._updateContainerHeight(),s._triggerChangeEvent(n),s.grid.endUpdate();var h=d.find(".grid-stack");h.length&&"resizestop"==o.type&&(h.each(function(e,i){t(i).data("gridstack").onResizeHandler()}),d.find(".grid-stack-item").trigger("resizestop"),d.find(".grid-stack-item").trigger("gsresizestop")),"resizestop"==o.type&&s.container.trigger("gsresizestop",d)}};this.dd.draggable(e,{start:n,stop:h,drag:d}).resizable(e,{start:n,stop:h,resize:d}),(i.noMove||this._isOneColumnMode()&&!s.opts.disableOneColumnMode||this.opts.disableDrag||this.opts.staticGrid)&&this.dd.draggable(e,"disable"),(i.noResize||this._isOneColumnMode()&&!s.opts.disableOneColumnMode||this.opts.disableResize||this.opts.staticGrid)&&this.dd.resizable(e,"disable"),e.attr("data-gs-locked",i.locked?"yes":null)},h.prototype._prepareElement=function(e,i){i=void 0!==i&&i;var o=this;(e=t(e)).addClass(this.opts.itemClass);var a=o.grid.addNode({x:parseInt(e.attr("data-gs-x"),10),y:parseInt(e.attr("data-gs-y"),10),width:e.attr("data-gs-width"),height:e.attr("data-gs-height"),maxWidth:e.attr("data-gs-max-width"),minWidth:e.attr("data-gs-min-width"),maxHeight:e.attr("data-gs-max-height"),minHeight:e.attr("data-gs-min-height"),autoPosition:r.toBool(e.attr("data-gs-auto-position")),noResize:r.toBool(e.attr("data-gs-no-resize")),noMove:r.toBool(e.attr("data-gs-no-move")),locked:r.toBool(e.attr("data-gs-locked")),resizeHandles:e.attr("data-gs-resize-handles"),el:e,id:e.attr("data-gs-id"),_grid:o},i);e.data("_gridstack_node",a),this._prepareElementsByNode(e,a)},h.prototype.setAnimation=function(t){t?this.container.addClass("grid-stack-animate"):this.container.removeClass("grid-stack-animate")},h.prototype.addWidget=function(e,i,o,a,s,r,d,n,h,l,p){return e=t(e),void 0!==i&&e.attr("data-gs-x",i),void 0!==o&&e.attr("data-gs-y",o),void 0!==a&&e.attr("data-gs-width",a),void 0!==s&&e.attr("data-gs-height",s),void 0!==r&&e.attr("data-gs-auto-position",r?"yes":null),void 0!==d&&e.attr("data-gs-min-width",d),void 0!==n&&e.attr("data-gs-max-width",n),void 0!==h&&e.attr("data-gs-min-height",h),void 0!==l&&e.attr("data-gs-max-height",l),void 0!==p&&e.attr("data-gs-id",p),this.container.append(e),this._prepareElement(e,!0),this._triggerAddEvent(),this._updateContainerHeight(),this._triggerChangeEvent(!0),e},h.prototype.makeWidget=function(e){return e=t(e),this._prepareElement(e,!0),this._triggerAddEvent(),this._updateContainerHeight(),this._triggerChangeEvent(!0),e},h.prototype.willItFit=function(t,e,i,o,a){var s={x:t,y:e,width:i,height:o,autoPosition:a};return this.grid.canBePlacedWithRespectToHeight(s)},h.prototype.removeWidget=function(e,i){i=void 0===i||i;var o=(e=t(e)).data("_gridstack_node");o||(o=this.grid.getNodeDataByDOMEl(e)),this.grid.removeNode(o,i),e.removeData("_gridstack_node"),this._updateContainerHeight(),i&&e.remove(),this._triggerChangeEvent(!0),this._triggerRemoveEvent()},h.prototype.removeAll=function(t){e.each(this.grid.nodes,e.bind(function(e){this.removeWidget(e.el,t)},this)),this.grid.nodes=[],this._updateContainerHeight()},h.prototype.destroy=function(e){t(window).off("resize",this.onResizeHandler),this.disable(),void 0===e||e?this.container.remove():(this.removeAll(!1),this.container.removeData("gridstack")),r.removeStylesheet(this._stylesId),this.grid&&(this.grid=null)},h.prototype.resizable=function(e,i){var o=this;return(e=t(e)).each(function(e,a){var s=(a=t(a)).data("_gridstack_node");void 0!==s&&null!==s&&(s.noResize=!i,s.noResize||o._isOneColumnMode()&&!o.opts.disableOneColumnMode?o.dd.resizable(a,"disable"):o.dd.resizable(a,"enable"))}),this},h.prototype.movable=function(e,i){var o=this;return(e=t(e)).each(function(e,a){var s=(a=t(a)).data("_gridstack_node");void 0!==s&&null!==s&&(s.noMove=!i,s.noMove||o._isOneColumnMode()&&!o.opts.disableOneColumnMode?(o.dd.draggable(a,"disable"),a.removeClass("ui-draggable-handle")):(o.dd.draggable(a,"enable"),a.addClass("ui-draggable-handle")))}),this},h.prototype.enableMove=function(t,e){this.movable(this.container.children("."+this.opts.itemClass),t),e&&(this.opts.disableDrag=!t)},h.prototype.enableResize=function(t,e){this.resizable(this.container.children("."+this.opts.itemClass),t),e&&(this.opts.disableResize=!t)},h.prototype.disable=function(){this.movable(this.container.children("."+this.opts.itemClass),!1),this.resizable(this.container.children("."+this.opts.itemClass),!1),this.container.trigger("disable")},h.prototype.enable=function(){this.movable(this.container.children("."+this.opts.itemClass),!0),this.resizable(this.container.children("."+this.opts.itemClass),!0),this.container.trigger("enable")},h.prototype.locked=function(e,i){return(e=t(e)).each(function(e,o){var a=(o=t(o)).data("_gridstack_node");void 0!==a&&null!==a&&(a.locked=i||!1,o.attr("data-gs-locked",a.locked?"yes":null))}),this},h.prototype.maxHeight=function(e,i){return(e=t(e)).each(function(e,o){var a=(o=t(o)).data("_gridstack_node");void 0!==a&&null!==a&&(isNaN(i)||(a.maxHeight=i||!1,o.attr("data-gs-max-height",i)))}),this},h.prototype.minHeight=function(e,i){return(e=t(e)).each(function(e,o){var a=(o=t(o)).data("_gridstack_node");void 0!==a&&null!==a&&(isNaN(i)||(a.minHeight=i||!1,o.attr("data-gs-min-height",i)))}),this},h.prototype.maxWidth=function(e,i){return(e=t(e)).each(function(e,o){var a=(o=t(o)).data("_gridstack_node");void 0!==a&&null!==a&&(isNaN(i)||(a.maxWidth=i||!1,o.attr("data-gs-max-width",i)))}),this},h.prototype.minWidth=function(e,i){return(e=t(e)).each(function(e,o){var a=(o=t(o)).data("_gridstack_node");void 0!==a&&null!==a&&(isNaN(i)||(a.minWidth=i||!1,o.attr("data-gs-min-width",i)))}),this},h.prototype._updateElement=function(e,i){var o=(e=t(e).first()).data("_gridstack_node");if(void 0!==o&&null!==o){var a=this;a.grid.cleanNodes(),a.grid.beginUpdate(o),i.call(this,e,o),a._updateContainerHeight(),a._triggerChangeEvent(),a.grid.endUpdate()}},h.prototype.resize=function(t,e,i){this._updateElement(t,function(t,o){e=null!==e&&void 0!==e?e:o.width,i=null!==i&&void 0!==i?i:o.height,this.grid.moveNode(o,o.x,o.y,e,i)})},h.prototype.move=function(t,e,i){this._updateElement(t,function(t,o){e=null!==e&&void 0!==e?e:o.x,i=null!==i&&void 0!==i?i:o.y,this.grid.moveNode(o,e,i,o.width,o.height)})},h.prototype.update=function(t,e,i,o,a){this._updateElement(t,function(t,s){e=null!==e&&void 0!==e?e:s.x,i=null!==i&&void 0!==i?i:s.y,o=null!==o&&void 0!==o?o:s.width,a=null!==a&&void 0!==a?a:s.height,this.grid.moveNode(s,e,i,o,a)})},h.prototype.verticalMargin=function(t,e){if(void 0===t)return this.opts.verticalMargin;var i=r.parseHeight(t);this.opts.verticalMarginUnit===i.unit&&this.opts.height===i.height||(this.opts.verticalMarginUnit=i.unit,this.opts.verticalMargin=i.height,e||this._updateStyles())},h.prototype.cellHeight=function(t,e){if(void 0===t){if(this.opts.cellHeight)return this.opts.cellHeight;var i=this.container.children("."+this.opts.itemClass).first();return Math.ceil(i.outerHeight()/i.attr("data-gs-height"))}var o=r.parseHeight(t);this.opts.cellHeightUnit===o.unit&&this.opts.cellHeight===o.height||(this.opts.cellHeightUnit=o.unit,this.opts.cellHeight=o.height,e||this._updateStyles())},h.prototype.cellWidth=function(){return Math.round(this.container.outerWidth()/this.opts.width)},h.prototype.getCellFromPixel=function(t,e){var i=void 0!==e&&e?this.container.offset():this.container.position(),o=t.left-i.left,a=t.top-i.top,s=Math.floor(this.container.width()/this.opts.width),r=Math.floor(this.container.height()/parseInt(this.container.attr("data-gs-current-height")));return{x:Math.floor(o/s),y:Math.floor(a/r)}},h.prototype.batchUpdate=function(){this.grid.batchUpdate()},h.prototype.commit=function(){this.grid.commit(),this._updateContainerHeight()},h.prototype.isAreaEmpty=function(t,e,i,o){return this.grid.isAreaEmpty(t,e,i,o)},h.prototype.setStatic=function(t){this.opts.staticGrid=!0===t,this.enableMove(!t),this.enableResize(!t),this._setStaticClass()},h.prototype._setStaticClass=function(){!0===this.opts.staticGrid?this.container.addClass("grid-stack-static"):this.container.removeClass("grid-stack-static")},h.prototype._updateNodeWidths=function(t,e){this.grid._sortNodes(),this.grid.batchUpdate();for(var i={},o=0;o<this.grid.nodes.length;o++)i=this.grid.nodes[o],this.update(i.el,Math.round(i.x*e/t),void 0,Math.round(i.width*e/t),void 0);this.grid.commit()},h.prototype.setGridWidth=function(t,e){this.container.removeClass("grid-stack-"+this.opts.width),!0!==e&&this._updateNodeWidths(this.opts.width,t),this.opts.width=t,this.grid.width=t,this.container.addClass("grid-stack-"+t)},n.prototype.batch_update=a(n.prototype.batchUpdate),n.prototype._fix_collisions=a(n.prototype._fixCollisions,"_fix_collisions","_fixCollisions"),n.prototype.is_area_empty=a(n.prototype.isAreaEmpty,"is_area_empty","isAreaEmpty"),n.prototype._sort_nodes=a(n.prototype._sortNodes,"_sort_nodes","_sortNodes"),n.prototype._pack_nodes=a(n.prototype._packNodes,"_pack_nodes","_packNodes"),n.prototype._prepare_node=a(n.prototype._prepareNode,"_prepare_node","_prepareNode"),n.prototype.clean_nodes=a(n.prototype.cleanNodes,"clean_nodes","cleanNodes"),n.prototype.get_dirty_nodes=a(n.prototype.getDirtyNodes,"get_dirty_nodes","getDirtyNodes"),n.prototype.add_node=a(n.prototype.addNode,"add_node","addNode, "),n.prototype.remove_node=a(n.prototype.removeNode,"remove_node","removeNode"),n.prototype.can_move_node=a(n.prototype.canMoveNode,"can_move_node","canMoveNode"),n.prototype.move_node=a(n.prototype.moveNode,"move_node","moveNode"),n.prototype.get_grid_height=a(n.prototype.getGridHeight,"get_grid_height","getGridHeight"),n.prototype.begin_update=a(n.prototype.beginUpdate,"begin_update","beginUpdate"),n.prototype.end_update=a(n.prototype.endUpdate,"end_update","endUpdate"),n.prototype.can_be_placed_with_respect_to_height=a(n.prototype.canBePlacedWithRespectToHeight,"can_be_placed_with_respect_to_height","canBePlacedWithRespectToHeight"),h.prototype._trigger_change_event=a(h.prototype._triggerChangeEvent,"_trigger_change_event","_triggerChangeEvent"),h.prototype._init_styles=a(h.prototype._initStyles,"_init_styles","_initStyles"),h.prototype._update_styles=a(h.prototype._updateStyles,"_update_styles","_updateStyles"),h.prototype._update_container_height=a(h.prototype._updateContainerHeight,"_update_container_height","_updateContainerHeight"),h.prototype._is_one_column_mode=a(h.prototype._isOneColumnMode,"_is_one_column_mode","_isOneColumnMode"),h.prototype._prepare_element=a(h.prototype._prepareElement,"_prepare_element","_prepareElement"),h.prototype.set_animation=a(h.prototype.setAnimation,"set_animation","setAnimation"),h.prototype.add_widget=a(h.prototype.addWidget,"add_widget","addWidget"),h.prototype.make_widget=a(h.prototype.makeWidget,"make_widget","makeWidget"),h.prototype.will_it_fit=a(h.prototype.willItFit,"will_it_fit","willItFit"),h.prototype.remove_widget=a(h.prototype.removeWidget,"remove_widget","removeWidget"),h.prototype.remove_all=a(h.prototype.removeAll,"remove_all","removeAll"),h.prototype.min_height=a(h.prototype.minHeight,"min_height","minHeight"),h.prototype.min_width=a(h.prototype.minWidth,"min_width","minWidth"),h.prototype._update_element=a(h.prototype._updateElement,"_update_element","_updateElement"),h.prototype.cell_height=a(h.prototype.cellHeight,"cell_height","cellHeight"),h.prototype.cell_width=a(h.prototype.cellWidth,"cell_width","cellWidth"),h.prototype.get_cell_from_pixel=a(h.prototype.getCellFromPixel,"get_cell_from_pixel","getCellFromPixel"),h.prototype.batch_update=a(h.prototype.batchUpdate,"batch_update","batchUpdate"),h.prototype.is_area_empty=a(h.prototype.isAreaEmpty,"is_area_empty","isAreaEmpty"),h.prototype.set_static=a(h.prototype.setStatic,"set_static","setStatic"),h.prototype._set_static_class=a(h.prototype._setStaticClass,"_set_static_class","_setStaticClass"),o.GridStackUI=h,o.GridStackUI.Utils=r,o.GridStackUI.Engine=n,o.GridStackUI.GridStackDragDropPlugin=i,t.fn.gridstack=function(e){return this.each(function(){var i=t(this);i.data("gridstack")||i.data("gridstack",new h(this,e))})},o.GridStackUI});
