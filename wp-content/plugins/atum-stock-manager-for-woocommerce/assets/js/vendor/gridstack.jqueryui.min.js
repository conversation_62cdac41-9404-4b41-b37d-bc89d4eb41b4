/**
 * gridstack.js 1.0.0-dev
 * http://troolee.github.io/gridstack.js/
 * (c) 2014-2017 <PERSON>, <PERSON>
 * gridstack.js may be freely distributed under the MIT license.
 * @preserve
*/
!function(e){if("function"==typeof define&&define.amd)define(["jquery","lodash","gridstack","jquery-ui/data","jquery-ui/disable-selection","jquery-ui/focusable","jquery-ui/form","jquery-ui/ie","jquery-ui/keycode","jquery-ui/labels","jquery-ui/jquery-1-7","jquery-ui/plugin","jquery-ui/safe-active-element","jquery-ui/safe-blur","jquery-ui/scroll-parent","jquery-ui/tabbable","jquery-ui/unique-id","jquery-ui/version","jquery-ui/widget","jquery-ui/widgets/mouse","jquery-ui/widgets/draggable","jquery-ui/widgets/droppable","jquery-ui/widgets/resizable"],e);else if("undefined"!=typeof exports){try{jQuery=require("jquery")}catch(e){}try{_=require("lodash")}catch(e){}try{GridStackUI=require("gridstack")}catch(e){}e(jQuery,_,GridStackUI)}else e(jQuery,_,GridStackUI)}(function(e,r,t){function i(e){t.GridStackDragDropPlugin.call(this,e)}window;return t.GridStackDragDropPlugin.registerPlugin(i),i.prototype=Object.create(t.GridStackDragDropPlugin.prototype),i.prototype.constructor=i,i.prototype.resizable=function(t,i){if(t=e(t),"disable"===i||"enable"===i)t.resizable(i);else if("option"===i){var u=arguments[2],a=arguments[3];t.resizable(i,u,a)}else{var n=t.data("gs-resize-handles")?t.data("gs-resize-handles"):this.grid.opts.resizable.handles;t.resizable(r.extend({},this.grid.opts.resizable,{handles:n},{start:i.start||function(){},stop:i.stop||function(){},resize:i.resize||function(){}}))}return this},i.prototype.draggable=function(t,i){return t=e(t),"disable"===i||"enable"===i?t.draggable(i):t.draggable(r.extend({},this.grid.opts.draggable,{containment:this.grid.opts.isNested?this.grid.container.parent():null,start:i.start||function(){},stop:i.stop||function(){},drag:i.drag||function(){}})),this},i.prototype.droppable=function(r,t){return(r=e(r)).droppable(t),this},i.prototype.isDroppable=function(r,t){return r=e(r),Boolean(r.data("droppable"))},i.prototype.on=function(r,t,i){return e(r).on(t,i),this},i});
