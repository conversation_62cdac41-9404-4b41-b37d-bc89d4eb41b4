@use "../common/breakpoints" as *;
@use "../common/mixins/utilities" as *;
@use "../common/variables" as *;

//
// Sweet Alert 2 customizations
//------------------------------

@keyframes scaleUpSwal {
	0% {
		transform:scale(.8) translateY(1000px);
		opacity:0;
	}
	100% {
		transform:scale(1) translateY(0px);
		opacity:1;
	}
}

@keyframes scaleDownSwal {
	0% {
		transform:scale(1) translateY(0px);
		opacity:1;
	}
	100% {
		transform:scale(.8) translateY(1000px);
		opacity:0;
	}
}

@keyframes fadeInSwal {
	0% {
		background: rgba($black, .0);
	}
	100% {
		background: rgba($black, .7);
	}
}

@keyframes fadeOutSwal {
	0% {
		background: rgba($black, .7);
	}
	100% {
		background: rgba($black, .0);
	}
}

.swal2-container {
	z-index: 100000 !important;
	word-break: normal;
	
	.swal2-html-container {
		padding: 0;
		overflow: visible;
		
		input[type=text] {
			margin: 0;
			border-radius: 4px;
			height: 45px;
			box-shadow: none;
		}
		
		.atum-link {
			text-decoration: underline;
		}
	}
	
	.swal2-title {
		line-height: 1.3;
		font-size: 23px;
		color: var(--main-title);
		
		small {
			flex-basis: 100%;
		}
	}
	
	.swal2-modal {
		font-family: inherit;
		
		.swal2-close {
			justify-content: flex-end;
			font-weight: 100;
			font-family: inherit;
			font-size: 30px;
			line-height: 30px;
			color: var(--gray-500);
			z-index: 100001;
			transition: .2s ease-in-out;
			padding-right: 10px;
			
			&:hover {
				color: var(--gray-800);
			}
		}
		
		.swal2-validation-message {
			margin: 1.625em 0 0;
			font-size: 14px;
			text-align: left;
			align-items: center;
			justify-content: flex-start;
		}
	
		p {
			font-size: 15px;
			text-align: center;
		}
		
		button, a {
			transition: background-color 0.2s ease-in-out;
			
			&:focus {
				outline: none;
				box-shadow: none;
			}
		}
		
		button {
			border-radius: 5px;
			text-transform: uppercase;
			font-weight: 400;
			padding: 9px 19px;
			font-size: 13px;
			letter-spacing: 1px;
			background-image: none !important;
			
			&.swal2-confirm {
				background-color: var(--primary);
			}
			
			&.btn-sm {
				padding: 0.25rem 0.5rem;
				font-size: 12px;
			}
			
			.atum-icon {
				position: relative;
				font-size: 1.15em;
				top: .15em;
				padding-right: 3px;
			}
			
			@include mobile-max {
				width: 100%;
				margin-bottom: 10px;
			}
			
		}
		
		a {
			text-decoration: none;
			color: var(--primary) !important;
			
			&:hover {
				color: var(--wp-link-hover);
			}
		}
		
		.alert {
			p {
				text-align: left;
				margin: 0;
			}
		}
		
		.swal2-loader {
			border-color: var(--primary) transparent var(--primary) transparent;
		}
		
	}
	
	.swal2-progress-steps {
		.swal2-progress-step {
			margin-bottom: 0;
		}
	}
	
	.atum-loading {
		@include loader($size: 38px, $color: $primary, $border-size: 3px);
		top: $grid-gutter-width;
		left: 0;
		right: 0;
		margin: auto;
	}
	
	.swal2-show {
		opacity:0;
		animation: scaleUpSwal .2s cubic-bezier(0.165, 0.840, 0.440, 1.000) forwards;
	}
	
	.swal2-hide {
		animation: scaleDownSwal .2s cubic-bezier(0.165, 0.840, 0.440, 1.000) forwards;
	}
	
	&.swal2-backdrop-show {
		background: rgba($black, .0);
		animation: fadeInSwal .2s cubic-bezier(0.165, 0.840, 0.440, 1.000) forwards;
	}
	
	&.swal2-backdrop-hide {
		animation: fadeOutSwal .2s cubic-bezier(0.165, 0.840, 0.440, 1.000) forwards;
	}
	
}

@include loader-rotate-animation;