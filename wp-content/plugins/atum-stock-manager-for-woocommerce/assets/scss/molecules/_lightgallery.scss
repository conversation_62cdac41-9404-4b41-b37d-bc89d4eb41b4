@use "../common/variables" as *;

// Theme default

.lg-actions {
	.lg-next, .lg-prev {
		background-color: $lg-next-prev-bg;
		border-radius: $lg-border-radius-base;
		color: $lg-next-prev-color;
		cursor: pointer;
		display: block;
		font-size: 22px;
		margin-top: -10px;
		padding: 8px 10px 9px;
		position: absolute;
		top: 50%;
		z-index: $zindex-controls;
		
		&.disabled {
			pointer-events: none;
			opacity: 0.5;
		}
		
		&:hover {
			color: $lg-next-prev-hover-color;
		}
	}
	
	.lg-next {
		right: 20px;
		
		&:before {
			content: "\e095";
		}
	}
	
	.lg-prev {
		left: 20px;
		
		&:after {
			content: "\e094";
		}
	}
}

@keyframes lg-right-end {
	0% {
		left: 0;
	}
	
	50% {
		left: -30px;
	}
	
	100% {
		left: 0;
	}
}


@keyframes lg-left-end {
	0% {
		left: 0;
	}
	
	50% {
		left: 30px;
	}
	
	100% {
		left: 0;
	}
}


.lg-outer {
	&.lg-right-end {
		.lg-object {
			animation: lg-right-end 0.3s;
			position: relative;
		}
	}
	
	&.lg-left-end {
		.lg-object {
			animation: lg-left-end 0.3s;
			position: relative;
		}
	}
}

// lg toolbar
.lg-toolbar {
	z-index: $zindex-toolbar;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
	background-color: $lg-toolbar-bg;
	
	.lg-icon {
		color: $lg-toolbar-icon-color;
		cursor: pointer;
		float: right;
		font-size: 24px;
		height: 47px;
		line-height: 27px;
		padding: 10px 0;
		text-align: center;
		width: 50px;
		text-decoration: none !important;
		outline: medium none;
		transition: color 0.2s linear;
		
		&:hover {
			color: $lg-toolbar-icon-hover-color;
		}
	}
	
	.lg-close {
		&:after {
			font-family: atum-icon-font;
			content: $atmi-cross;
		}
	}
	
	.lg-download {
		&:after {
			font-family: atum-icon-font;
			content: $atmi-download;
		}
	}
}

// lightGallery title
.lg-sub-html {
	background-color: $lg-sub-html-bg;
	bottom: 0;
	color: $lg-sub-html-color;
	font-size: 16px;
	left: 0;
	padding: 10px 40px;
	position: fixed;
	right: 0;
	text-align: center;
	z-index: $zindex-subhtml;
	
	h4 {
		margin: 0;
		font-size: 13px;
		font-weight: bold;
	}
	
	p {
		font-size: 12px;
		margin: 5px 0 0;
	}
}

// lg image counter
#lg-counter {
	color: $lg-icon-color;
	display: inline-block;
	font-size: $lg-counter-font-size;
	padding-left: 20px;
	padding-top: 12px;
	vertical-align: middle;
}

// for idle state
.lg-toolbar, .lg-prev, .lg-next {
	opacity: 1;
	transition: transform 0.35s cubic-bezier(0, 0, 0.25, 1) 0s, opacity 0.35s cubic-bezier(0, 0, 0.25, 1) 0s, color 0.2s linear;
}

.lg-hide-items {
	.lg-prev {
		opacity: 0;
		transform: translate3d(-10px, 0, 0);
	}
	
	.lg-next {
		opacity: 0;
		transform: translate3d(10px, 0, 0);
	}
	
	.lg-toolbar {
		opacity: 0;
		transform: translate3d(0, -10px, 0);
	}
}

// Starting effect
body:not(.lg-from-hash){
	.lg-outer {
		&.lg-start-zoom{
			.lg-object{
				transform: scale3d(0.5, 0.5, 0.5);
				opacity: 0;
				transition: transform 250ms cubic-bezier(0, 0, 0.25, 1) 0s, opacity 250ms cubic-bezier(0, 0, 0.25, 1) !important;
				transform-origin: 50% 50%;
			}
			.lg-item.lg-complete{
				.lg-object{
					transform: scale3d(1, 1, 1);
					opacity: 1;
				}
			}
		}
	}
}

// lightgallery core
.lg-outer {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: $zindex-outer;
    opacity: 0;
    // For start/end transition
    transition: opacity 0.15s ease 0s;

    * {
		box-sizing: border-box;
    }

    &.lg-visible {
        opacity: 1;
    }

    // Set transition speed and timing function
    &.lg-css3 {
        .lg-item {
            &.lg-prev-slide, &.lg-next-slide, &.lg-current {
                transition-duration: inherit !important;
                transition-timing-function: inherit !important;
            }
        }
    }

    // Remove transition while dragging
    &.lg-css3.lg-dragging {
        .lg-item {
            &.lg-prev-slide, &.lg-next-slide, &.lg-current {
                transition-duration: 0s !important;
                opacity: 1;
            }
        }
    }

    // Set cursor grab while dragging
    &.lg-grab {
        img.lg-object {
            cursor: grab;
        }
    }

    &.lg-grabbing {
        img.lg-object {
            cursor: grabbing;
        }
    }

    .lg {
        height: 100%;
        width: 100%;
        position: relative;
        overflow: hidden;
        margin-left: auto;
        margin-right: auto;
        max-width: 100%;
        max-height: 100%;
    }

    .lg-inner {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        white-space: nowrap;
    }

    .lg-item {
        display: none !important; 
    }
    &.lg-css3{
        .lg-prev-slide, .lg-current, .lg-next-slide{
            display: inline-block !important; 
        }
    }
    &.lg-css{
        .lg-current{
            display: inline-block !important; 
        }
    }

    .lg-item, .lg-img-wrap {
        display: inline-block;
        text-align: center;
        position: absolute;
        width: 100%;
        height: 100%;

        &:before {
            content: "";
            display: inline-block;
            height: 50%;
            width: 1px;
            margin-right: -1px;
        }
    }

    .lg-img-wrap {
        position: absolute;
        padding: 0 5px;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0
    }

    .lg-item {
        &.lg-complete {
            background-image: none;
        }

        &.lg-current {
            z-index: $zindex-item;
        }
    }

    .lg-image {
        display: inline-block;
        vertical-align: middle;
        max-width: 100%;
        max-height: 100%;
        width: auto !important;
        height: auto !important;
    }

    &.lg-show-after-load {
        .lg-item {
            .lg-object, .lg-video-play {
                opacity: 0;
                transition: opacity 0.15s ease 0s;
            }

            &.lg-complete {
                .lg-object, .lg-video-play {
                    opacity: 1;
                }
            }
        }
    }

    // Hide title div if empty
    .lg-empty-html {
        display: none;
    }

    &.lg-hide-download{
        #lg-download{
            display: none;
        }
    }
}
.lg-backdrop{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $zindex-backdrop;
    background-color: #000;
    opacity: 0;
    transition: opacity 0.15s ease 0s;
    &.in{
        opacity: $backdrop-opacity;
    }
}

// Default slide animations. Should be placed at the bottom of the animation css
.lg-css3 {

    // Remove all transition effects
    &.lg-no-trans {
        .lg-prev-slide, .lg-next-slide, .lg-current {
            transition: none 0s ease 0s !important;
        }
    }

    &.lg-use-css3 {
        .lg-item {
            backface-visibility: hidden;
        }
    }

    &.lg-use-left {
        .lg-item {
            backface-visibility: hidden;
        }
    }

    // Fade mode
    &.lg-fade {
        .lg-item {
            opacity: 0;

            &.lg-current {
                opacity: 1;
            }

            // transition timing property and duration will be over written from javascript
            &.lg-prev-slide, &.lg-next-slide, &.lg-current {
                transition: opacity 0.1s ease 0s;
            }
        }
    }

    &.lg-slide {
        &.lg-use-css3 {
            .lg-item {
                opacity: 0;

                &.lg-prev-slide {
                    transform: translate3d(-100%, 0, 0);
                }

                &.lg-next-slide {
                    transform: translate3d(100%, 0, 0);
                }

                &.lg-current {
                    transform: translate3d(0, 0, 0);
                    opacity: 1;
                }

                // transition timing property and duration will be over written from javascript
                &.lg-prev-slide, &.lg-next-slide, &.lg-current {
                    transition: transform 1s cubic-bezier(0, 0, 0.25, 1) 0s, opacity 0.1s ease 0s;
                }
            }
        }

        &.lg-use-left {
            .lg-item {
                opacity: 0;
                position: absolute;
                left: 0;

                &.lg-prev-slide {
                    left: -100%;
                }

                &.lg-next-slide {
                    left: 100%;
                }

                &.lg-current {
                    left: 0;
                    opacity: 1;
                }

                // transition timing property and duration will be over written from javascript
                &.lg-prev-slide, &.lg-next-slide, &.lg-current {
                    transition: left 1s cubic-bezier(0, 0, 0.25, 1) 0s, opacity 0.1s ease 0s;
                }
            }
        }
    }
}
