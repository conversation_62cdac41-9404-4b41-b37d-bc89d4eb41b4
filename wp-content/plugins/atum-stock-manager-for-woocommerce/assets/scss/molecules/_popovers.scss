@use "../common/functions" as *;
@use "../common/variables" as *;
@use "../common/mixins/reset-text" as *;
@use "../common/mixins/rfs" as *;
@use "../common/mixins/border-radius" as *;

//
// Bootstrap Popovers
// --------------------------------------------------

.popover {
	position: absolute;
	top: 0;
	left: 0;
	z-index: $zindex-popover;
	display: block;
	max-width: $popover-max-width;
	@include reset-text();
	@include font-size($popover-font-size);
	word-wrap: break-word;
	background-color: $popover-bg;
	background-clip: padding-box;
	border: 1px solid var(--atum-text-color-var1);
	border-radius: $popover-border-radius;
	box-shadow: 0 1px 5px 0 var(--popover-black-shadow);
	
	.popover-arrow {
		position: absolute;
		display: block;
		width: $popover-arrow-width;
		height: $popover-arrow-height;
		margin: 0 $popover-border-radius;
		
		&::before,
		&::after {
			position: absolute;
			display: block;
			content: "";
			border-color: transparent;
			border-style: solid;
		}
	}
	
	.content-footer {
		padding-top: 7px;
		margin-bottom: -12px;
		
		a {
			color: var(--primary);
			text-decoration: none;
		}
		
		.footer-text {
			padding-left: 3px;
		}
	}
	
	&.with-select {
		max-width: 400px;
		
		.meta-value {
			max-width: 317px;
			min-width: 200px;
		}
		
		~ .select2-container {
			.select2-dropdown {
				box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.2);
				border: 1px solid var(--gray-500) !important;
				top: 3px;
			}
		}
	}
	
	input {
		width: 100%;
	}
}

.bs-popover-top {
	
	> .popover-arrow {
		bottom: subtract(-($popover-arrow-height), $popover-border-width);
		
		&::before {
			bottom: 0;
			border-width: $popover-arrow-height ($popover-arrow-width * .5) 0;
			border-top-color: $popover-arrow-outer-color;
		}
		
		&::after {
			bottom: $popover-border-width;
			border-width: $popover-arrow-height ($popover-arrow-width * .5) 0;
			border-top-color: $popover-arrow-color;
		}
	}
	
}

.bs-popover-end {
	
	> .popover-arrow {
		left: calc(-1 * $popover-arrow-width + $popover-border-width);
		width: $popover-arrow-height;
		height: $popover-arrow-width;
		
		&::before {
			left: 0;
			border-width: ($popover-arrow-width * .5) $popover-arrow-height ($popover-arrow-width * .5) 0;
			border-right-color: $popover-arrow-outer-color;
		}
		
		&::after {
			left: $popover-border-width;
			border-width: ($popover-arrow-width * .5) $popover-arrow-height ($popover-arrow-width * .5) 0;
			border-right-color: $popover-arrow-color;
		}
	}
}

.bs-popover-bottom {
	
	> .popover-arrow {
		top: subtract(-($popover-arrow-height), $popover-border-width);
		
		&::before {
			top: 0;
			border-width: 0 ($popover-arrow-width * .5) $popover-arrow-height ($popover-arrow-width * .5);
			border-bottom-color: $popover-arrow-outer-color;
		}
		
		&::after {
			top: $popover-border-width;
			border-width: 0 ($popover-arrow-width * .5) $popover-arrow-height ($popover-arrow-width * .5);
			border-bottom-color: $popover-arrow-color;
		}
	}
	
	// This will remove the popover-header's border just below the arrow
	.popover-header::before {
		position: absolute;
		top: 0;
		left: 50%;
		display: block;
		width: $popover-arrow-width;
		margin-left: -($popover-arrow-width) * .5;
		content: "";
		border-bottom: $popover-border-width solid $popover-header-bg;
	}
}

.bs-popover-start {
	
	> .popover-arrow {
		right: subtract(-($popover-arrow-height), $popover-border-width);
		width: $popover-arrow-height;
		height: $popover-arrow-width;
		
		&::before {
			right: -6px;
			border-width: ($popover-arrow-width * .5) 0 ($popover-arrow-width * .5) $popover-arrow-height;
			border-left-color: $popover-arrow-outer-color;
		}
		
		&::after {
			right: -5px;
			border-width: ($popover-arrow-width * .5) 0 ($popover-arrow-width * .5) $popover-arrow-height;
			border-left-color: $popover-arrow-color;
		}
	}
}

.bs-popover-auto {
	&[data-popper-placement^="top"] {
		@extend .bs-popover-top;
	}
	&[data-popper-placement^="right"] {
		@extend .bs-popover-end;
	}
	&[data-popper-placement^="bottom"] {
		@extend .bs-popover-bottom;
	}
	&[data-popper-placement^="left"] {
		@extend .bs-popover-start;
	}
}

// Offset the popover to account for the popover arrow
.popover-header {
	padding: $popover-header-padding-y $popover-header-padding-x;
	margin: 0; // Reset the default from Reboot
	border-bottom: $popover-border-width solid shade-color($popover-header-bg, 10%);
	@include border-top-radius($popover-inner-border-radius);
	background-color: var(--atum-table-bg);
	color: var(--atum-text-color-var1);
	font-size: 14px;
	text-align: center;
	font-weight: bold;
	
	.atum-list-table & {
		text-align: center;
	}
	
	&:empty {
		display: none;
	}
}

.popover-body {
	padding: $popover-body-padding-y $popover-body-padding-x;
	color: $popover-body-color;
	flex-wrap: wrap;
	
	&, .edit-popover-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.edit-popover-content {
		flex-wrap: nowrap;
		align-items: flex-start;
	}
	
	button {
		height: 30px;
		line-height: 0;
		margin-left: 5px;
		color: var(--atum-table-bg);
	}
	
	> input[type=number], > input[type=text] {
		border-radius: 5px;
		background-color: var(--atum-table-bg);
		border: 1px solid var(--main-border-alt);
		font-size: 15px;
		line-height: 1.47px;
		text-align: left;
		color: var(--atum-text-color-var1);
		box-shadow: none;
		
		&:focus {
			border-color: var(--primary);
		}
		
		&:hover {
			border-color: var(--atum-text-color-var1);
		}
	}
	
}