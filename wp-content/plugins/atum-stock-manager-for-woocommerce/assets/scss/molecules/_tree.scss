@use "../common/variables" as *;
@use "../common/mixins/utilities" as *;

//
// ATUM Tree
//-----------

@use "sass:math";

.atum-tree {
	position: relative;
	min-height: 100px;
	
	.alert {
		margin-top: math.div($grid-gutter-width, 2);
		font-size: 12px;
		text-align: left;
		i {
			font-size: 17px;
		}
	}
	
	.no-locations-set {
		justify-content: center;
	}
}

.ui-helper-hidden {
	display: none;
}

ul.easytree-container {
	font-size: 15px;
	font-weight: normal;
	line-height: 1.3;
	white-space: nowrap;
	padding: 15px 10px !important;
	margin: 0;
	border: 1px solid var(--gray-500);
	border-radius: 6px;
	overflow: auto;
	height: 100%;
	text-align: left;
	padding: 10px 5px;
	
	> li {
		
		&:first-child {
			padding-top: 3px;
		}
		
		&:not(:first-child) {
			padding: math.div($grid-gutter-width, 2) 0;
			border-top: 1px solid var(--gray-200);
		}
		
		&:nth-child(2) {
			margin-top: 20px;
		}
		
		&:last-child {
			padding-bottom: 0;
		}
	}
	
	&:focus {
		outline: none;
	}
	
	.easytree-node {
		display: flex;
		align-items: center;
	}
	
	ul {
		padding: 0 0 0 16px;
		margin: 0;
		
		&:focus {
			outline: none;
		}
		
		li {
			list-style: none;
			margin: 0;
			padding: 2px 0;
			
			&, a {
				color: var(--dark);
			}
			
			.stock-negative {
				&, a {
					color: var(--danger);
				}
			}
			
			&.easytree-lastsib {
				background-image: none;
			}
		}
	}
	
	// Style, when control is disabled
	.ui-easytree-disabled & {
		opacity: 0.5;
	}
	
	// Custom icons
	img {
		width: 16px;
		height: 16px;
		margin-left: 3px;
		vertical-align: top;
		border-style: none;
	}
	
}

// Suppress lines if level is fixed expanded (option minExpandLevel)
ul.easytree-no-connector {
	
	> li {
		background-image: none;
	}
	
}

//
// Common icon definitions
//-------------------------

span {
	
	&.easytree-empty, &.easytree-vline, &.easytree-expander, &.easytree-icon {
		width: 16px;
		height: 16px;
		font-size: 16px;
		line-height: 1;
		display: inline-block;
		vertical-align: top;
		@include atum-icon-font-family;
		cursor: pointer;
		color: var(--main-text);
	}
	
	&.easytree-custom-icon {
		display: inline-flex;
		gap: 3px;
		color: var(--main-text);
		margin-left: 3px;
		text-indent: 0 !important; // In case jQuery UI CSS is loaded
		background-image: none !important; // In case jQuery UI CSS is loaded
		
		// Multi-Inventory support
		&.atmi-multi-inventory {
			&:before {
				order: 2;
			}
			
			&:after {
				content: $atmi-arrow-child;
			}
			
			+ .easytree-title {
				color: var(--gray-600);
			}
		}
	}
	
}

//
// Expander icon
//
// Prefix: easytree-exp-
// 1st character: 'e': expanded, 'c': collapsed, 'n': no children
// 2nd character (optional): 'd': lazy (Delayed)
// 3rd character (optional): 'l': Last sibling
//-----------------------------------------------------------------

span.easytree-expander {
	transition: all .2s ease-in-out;
	margin-right: 3px;
	
	&:before {
		font-family: atum-icon-font;
		content: $atmi-plus-circle;
	}
	
	&:hover {
		color: var(--primary) !important;
	}
}

.easytree-exp-e, .easytree-exp-ed, .easytree-exp-el, .easytree-exp-edl {
	span.easytree-expander {
		&:before {
			font-family: atum-icon-font;
			content: $atmi-circle-minus;
		}
	}
}

// Connector instead of expander, if node has no children
.easytree-exp-n, .easytree-exp-nl {
	span.easytree-expander {
		cursor: default;
		
		&:before {
			content: '';
			cursor: default;
		}
	}
}

//
// Node type icon
// Note: IE6 doesn't correctly evaluate multiples class names,
//		 so we create combined class names that can be used in the CSS.
//
// Prefix: easytree-ico-
// 1st character: 'e': expanded, 'c': collapsed
// 2nd character (optional): 'f': folder
//----------------------------------------------------------------------

// Default icon
.easytree-ico-c {
	span.easytree-icon {
		margin-left: 3px;
		
		&:before {
			font-family: atum-icon-font;
			content: $atmi-map-marker;
		}
	}
}

// Collapsed Folder
.easytree-ico-ef {
	span.easytree-icon {
		&:before {
			font-family: atum-icon-font;
			content: $atmi-map-marker;
		}
	}
}

// check and uncheck icons for the 1.4.11 update : editable locations on SC
.checked{
	span.easytree-icon {
		&:before {
			color: var(--primary);
		}
	}
}

//
// Node titles
//-------------

.easytree-title {
	display: flex;
	align-items: center;
	padding-left: 3px;
	padding-right: 3px;
	vertical-align: top;
	margin-left: 3px;
	border-radius: 0;
	text-decoration: none;
	cursor: pointer;
	
	a {
		color: var(--dark) !important;
		transition: .2s ease-in-out;
		
		&:hover {
			color: var(--primary) !important;
		}
		
		.multiplier {
			color: var(--gray-600);
		}
	}
	
}

.zero-linked .easytree-title {
	
	.easytree-custom-icon {
		opacity: 0.5;
	}
	
	
	a {
		color: var(--gray-300);
	}
	.multiplier {
		color: var(--gray-300);
	}
}


ul.easytree-container.easytree-focused {
	
	span {
		&.easytree-selected, &.easytree-active {
			.easytree-custom-icon {
				color: var(--primary);
			}
		}
	}
	
}

// Tree controls
.tree-controls {
	text-align: right;
	margin-bottom: 5px;
	padding-right: 5px;
	
	a {
		font-size: 11px;
		font-weight: 400;
		
		&:not(:first-child) {
			border-left: 1px solid var(--gray-500);
			padding-left: 5px;
		}
		
	}
}

// ATUM locations tree
.atum-modal {
	.atum-tree {
		margin-top: 25px;
	}
}

.edit-locations-modal {
	
	.swal2-html-container {
		margin-bottom: 0;
	}
	
	.swal2-actions {
		margin-top: 0;
	}
	
	.easytree-node {
		
		a {
			color: var(--atum-easytree-node) !important;
		}
		
		&.checked {
			a {
				color: var(--primary) !important;
			}
			
			&.easytree-active {
				.easytree-custom-icon {
					color: var(--primary) !important;
				}
			}
		}
		
	}

}

// BOM hierarchy tree speficity fix
.swal2-modal {
	.atum-tree {
		margin-bottom: math.div($grid-gutter-width, 2);
		
		.easytree-title {
			a {
				color: var(--dark) !important;
				
				&:hover {
					color: var(--primary) !important;
				}
				
			}
		}
		
		.zero-linked {
			.easytree-custom-icon {
				opacity: 0.5;
			}
			a {
				color: var(--gray-300) !important;
				&:hover {
					opacity: 0.5;
				}
			}
		}
	}
}