@use "../common/breakpoints" as *;
@use "../common/variables" as *;
@use "../molecules/list-table" as *;

//
// Atum List Tables for Post Types
//--------------------------------

#posts-filter {
	@extend .atum-list-wrapper;
	
	table.wp-list-table {
		th.check-column {
			input[type=checkbox] {
				transform: none;
				top: 9px;
			}
		}
		
		tbody {
			th, td {
				vertical-align: top;
			}
		}
	}
	
	.tablenav {
		gap: 10px;
		
		&.top {
			> .actions {
				display: flex;
				align-items: center;
				gap: 10px;
				
				&.bulkactions {
					margin-right: 5px;
				}
				
				input[type=submit] {
					margin-left: -5px;
					margin-right: 0;
					padding: 16px;
					height: auto;
				}
			}
			
			.select2 {
				height: inherit;
				
				.select2-selection {
					height: 32px;
					
					.select2-selection__rendered {
						line-height: 29px;
					}
				}
			}
			
			#atum-search-by-column {
				margin-left: 0;
			}
		}
	}
	
	.tablenav-pages {
		float: none;
		flex-grow: 1;
	}
	
	.subsubsub {
		@extend .extend-list-table;
	}
	
	.nav-with-scroll-effect .subsubsub {
		
		li, li a {
			display: flex;
			flex-wrap: nowrap;
		}
		
		li {
			color: transparent;
			padding: 20px 0;
			margin-right: 0;
			
			&:first-child {
				padding-left: 10px;
			}
			
			a {
				padding: 0 10px;
				
				&:not(.current) {
					&:hover {
						color: $primary;
					}
				}
			}
			
			@include mobile-max {
				&, &:first-child {
					padding: 0;
				}
			}
		}
	}
	
	.search-box {
		flex-direction: row;
	}
}

.wrap {
	
	.wp-heading-inline {
		~ .subsubsub {
			display: none;
		}
		
		@include mobile-max {
			a.page-title-action {
				padding: 0 12px;
			}
		}
		
	}
}