@use "../common/variables" as *;

.swal2-container {
	
	.marketing-popup {
		min-height: 433px;
		padding: 10px 10px 20px 10px;
		
		&.wide {
			padding: 10px 0 0 0;
		}
		
		.swal2-image {
			width: 143px;
			height: auto;
			position: absolute;
			top: 0;
			left: 10px;
			margin: 10px 0;
			z-index: 10;
		}
		
		.swal2-html-container {
			font-size: 14px;
			line-height: 1.29;
			text-align: center;
			color: $gray-500;
			height: 100%;
			
			h1 {
				font-weight: 400;
				font-size: 40px;
				margin: -3px 0 10px;
				letter-spacing: 1px;
				line-height: 1;
				
				> span {
					position: relative;
				}
				
				strong {
					font-weight: 600;
				}
				
				span {
					font-weight: 300;
				}
			}
			
			p {
				margin: 0;
				line-height: 1.3;
				font-weight: normal;
				
				strong {
					color: $blue-dark;
					font-weight: bold;
				}
			}
			
			.version {
				font-size: 14px;
				padding: 1px 9px;
				line-height: 20px;
				border-radius: 11.5px;
				position: absolute;
				top: -15px;
				right: -25px;
			}
			
			button {
				margin: 0 5px;
				
				&.btn-success, &.btn-success:focus, &.btn-outline-success, &.btn-outline-success:focus {
					box-shadow: none;
				}
				
				&.btn-outline-success {
					background: $white;
					
					&:hover {
						background: $green;
					}
				}
			}
			
			.footer-notice {
				font-size: 10px;
				color: $black;
				border-radius: 5px;
				padding: 5px 10px;
				position: absolute;
				bottom: 5px;
				left: 0;
				right: 0;
				max-width: 445px;
				margin: auto;
			}
			
		}
		
		.swal2-title, .swal2-html-container {
			margin-bottom: 0;
		}
		
		.swal2-confirm {
			margin-right: 10px;
		}
		
		.swal2-close {
			background: #FFF; // Avoid visibility issues when there is any image below the button.
		}
		
		.mp-logo {
			width: 170px;
			height: auto;
		}
	}
	
}
