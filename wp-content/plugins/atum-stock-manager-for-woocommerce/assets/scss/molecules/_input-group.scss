@use "sass:color";
@use "../common/variables" as *;
@use "../common/mixins/border-radius" as *;

//
// Input Groups
//--------------

.input-group {
	position: relative;
	display: flex;
	flex-wrap: nowrap;
	align-items: stretch;
	width: 100%;
	
	> .form-control,
	> .custom-select,
	> .custom-file {
		position: relative; // For focus state's z-index
		flex: 1 1 auto;
		// Add width 1% and flex-basis auto to ensure that button will not wrap out
		// the column. Applies to IE Edge+ and Firefox. Chrome does not require this.
		width: 1%;
		min-width: 0;
		margin-bottom: 0;
		
		// Bring the "active" form control to the top of surrounding elements
		&:focus {
			z-index: 3;
		}
		
		+ .form-control,
		+ .custom-select,
		+ .custom-file {
			margin-left: -($input-border-width);
		}
	}
	
	> .form-control,
	> .custom-select{
		&:not(:last-child) {
			border-bottom-right-radius: 0;
			border-top-right-radius: 0;
		}
		&:not(:first-child) {
			border-bottom-left-radius: 0;
			border-top-left-radius: 0;
		}
	}
	
	// Custom file inputs have more complex markup, thus requiring different
	// border-radius overrides.
	> .custom-file {
		display: flex;
		align-items: center;
		
		&:not(:last-child) .custom-file-label,
		&:not(:last-child) .custom-file-label::after {
			border-bottom-right-radius: 0;
			border-top-right-radius: 0;
		}
		
		&:not(:first-child) .custom-file-label,
		&:not(:first-child) .custom-file-label::after {
			border-bottom-left-radius: 0;
			border-top-left-radius: 0;
		}
	}
	
	> .select2-container {
		
		&.atum-select2, &.atum-enhanced-select {
			z-index: 0;
			
			.select2-selection--single {
				@include border-left-radius(0);
				@include border-right-radius($input-border-radius);
			}
			
		}
		
	}
	
	&.invalid {
		input[type=text], input[type=number], textarea, select, .select2-container {
			background-color: color.adjust($danger, $alpha: -0.85);
		}
	}
	
}


// Prepend and append
//
// While it requires one extra layer of HTML for each, dedicated prepend and
// append elements allow us to 1) be less clever, 2) simplify our selectors, and
// 3) support HTML5 form validation.

.input-group-prepend,
.input-group-append {
	display: flex;
	z-index: 1;
	border: $input-border-width solid var(--main-border-alt);
	
	
	// Ensure buttons are always above inputs for more visually pleasing borders.
	// This isn't needed for `.input-group-text` since it shares the same border-color
	// as our inputs.
	.btn {
		position: relative;
		z-index: 2;
	}
	
	.btn + .btn,
	.btn + .input-group-text,
	.input-group-text + .input-group-text,
	.input-group-text + .btn {
		margin-left: -($input-border-width);
	}
}

.input-group-prepend { border-right: none }
.input-group-append { border-left: none }


// Textual addons
//
// Serves as a catch-all element for any text or radio/checkbox input you wish
// to prepend or append to an input.

.input-group-text {
	display: flex;
	align-items: center;
	padding: $input-padding-y $input-padding-x;
	margin-bottom: 0; // Allow use of <label> elements by overriding our default margin-bottom
	font-size: $font-size-base; // Match inputs
	font-weight: normal;
	line-height: $line-height-base;
	color: $input-group-addon-color;
	text-align: center;
	white-space: nowrap;
	background-color: $input-group-addon-bg;
	border-radius: $input-border-radius;
	
	// Nuke default margins from checkboxes and radios to vertically center within.
	input[type="radio"],
	input[type="checkbox"] {
		margin-top: 0;
	}
	
	.input-group.invalid & {
		background-color: color.adjust($danger, $alpha: -0.35);
	}
}

// Sizing
//
// Remix the default form control sizing classes into new ones for easier
// manipulation.
// @TODO: IF WE ADD THE _forms.scss later, redo this

.input-group-lg > .form-control,
.input-group-lg > .input-group-prepend > .input-group-text,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
.input-group-lg > .input-group-append > .btn {
	//@extend .form-control-lg;
	
	padding: $input-padding-y-lg $input-padding-x-lg;
	font-size: $font-size-lg;
	line-height: $input-line-height-lg;
	border-radius: $input-border-radius-lg;
}

.input-group-sm > .form-control,
.input-group-sm > .input-group-prepend > .input-group-text,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-append > .btn {
	//@extend .form-control-sm;
	
	padding: $input-padding-y-sm $input-padding-x-sm;
	font-size: $font-size-sm;
	line-height: $input-line-height-sm;
	border-radius: $input-border-radius-sm;
}


// Prepend and append rounded corners
//
// These rulesets must come after the sizing ones to properly override sm and lg
// border-radius values when extending. They're more specific than we'd like
// with the `.input-group >` part, but without it, we cannot override the sizing.

.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
	border-bottom-right-radius: 0;
	border-top-right-radius: 0;
}

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
	border-bottom-left-radius: 0;
	border-top-left-radius: 0;
}

// Number mask fields
.input-group.number-mask {
	
	input {
		width: auto !important;
		@include border-left-radius($input-border-radius);
		@include border-right-radius(0);
		z-index: 2;
		
		&:focus {
			outline: none;
			box-shadow: none;
		}
	}
	
	.input-group-append {
		margin-left: -1px;
		@include border-right-radius($input-border-radius);
		cursor: pointer;
		
		.input-group-text {
			background-color: $gray-200;
			@include border-right-radius($input-border-radius !important);
			transition: .2s ease-in-out;
			
			&:not(.active) {
				display: none;
			}
		}
		
		&:hover {
			.input-group-text {
				background-color: $gray-600;
				color: $white;
			}
		}
	}
	
	input:disabled {
		+ .input-group-append {
			border-color: $input-disabled-border-color;
			
			&, &:hover {
				.input-group-text {
					color: rgba($input-color, .6);
				}
			}
			
		}
	}
	
}