@use "../common/variables" as *;

//
// Gridstack
// http://troolee.github.io/gridstack.js/
// (c) 2014-2017 <PERSON>, <PERSON>
//-------------------------------------------

@use "sass:math";

$gridstack-columns:             12 !default;
$horizontal_padding:            $grid-gutter-width !default;
$vertical_padding:              $grid-gutter-width !default;
$animation_speed:               .3s !default;

:root .grid-stack-item > .ui-resizable-handle { filter: none; }

.grid-stack {
    position: relative;

    &.grid-stack-rtl {
        direction: ltr;

        > .grid-stack-item {
            direction: rtl;
        }
    }

    .grid-stack-placeholder > .placeholder-content {
        border: 1px dashed $gray-300;
        margin: 0;
        position: absolute;
        top: 0;
        left: math.div($horizontal_padding, 2);
        right: math.div($horizontal_padding, 2);
        bottom: 0;
        width: auto;
        z-index: 0 !important;
        text-align: center;
    }

    > .grid-stack-item {
        min-width: math.div(100%, $gridstack-columns);
        position: absolute;
        padding: 0;

        > .grid-stack-item-content {
            margin: 0;
            position: absolute;
            top: 0;
            left: math.div($horizontal_padding, 2);
            right: math.div($horizontal_padding, 2);
            bottom: 0;
            width: auto;
            z-index: 0 !important;
            overflow-x: hidden;
            overflow-y: auto;
        }

        > .ui-resizable-handle {
            position: absolute;
            font-size: 0.1px;
            display: block;
	        opacity: 0.2;
            -ms-touch-action: none;
            touch-action: none;
	        transition: opacity 0.2s ease-in-out;
	        
	        &:hover {
		        opacity: 0.7;
	        }
        }

        &.ui-resizable-disabled > .ui-resizable-handle,
        &.ui-resizable-autohide > .ui-resizable-handle { display: none; }

        &.ui-draggable-dragging,
        &.ui-resizable-resizing {
            z-index: 100;

            > .grid-stack-item-content,
            > .grid-stack-item-content {
                box-shadow: 1px 4px 6px rgba($black, 0.2);
                opacity: 0.8;
            }
        }

        > .ui-resizable-se,
        > .ui-resizable-sw {
            background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTYuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjE2cHgiIGhlaWdodD0iMTZweCIgdmlld0JveD0iMCAwIDUxMS42MjYgNTExLjYyNyIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTExLjYyNiA1MTEuNjI3OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxnPgoJPHBhdGggZD0iTTMyOC45MDYsNDAxLjk5NGgtMzYuNTUzVjEwOS42MzZoMzYuNTUzYzQuOTQ4LDAsOS4yMzYtMS44MDksMTIuODQ3LTUuNDI2YzMuNjEzLTMuNjE1LDUuNDIxLTcuODk4LDUuNDIxLTEyLjg0NSAgIGMwLTQuOTQ5LTEuODAxLTkuMjMxLTUuNDI4LTEyLjg1MWwtNzMuMDg3LTczLjA5QzI2NS4wNDQsMS44MDksMjYwLjc2LDAsMjU1LjgxMywwYy00Ljk0OCwwLTkuMjI5LDEuODA5LTEyLjg0Nyw1LjQyNCAgIGwtNzMuMDg4LDczLjA5Yy0zLjYxOCwzLjYxOS01LjQyNCw3LjkwMi01LjQyNCwxMi44NTFjMCw0Ljk0NiwxLjgwNyw5LjIyOSw1LjQyNCwxMi44NDVjMy42MTksMy42MTcsNy45MDEsNS40MjYsMTIuODUsNS40MjYgICBoMzYuNTQ1djI5Mi4zNThoLTM2LjU0MmMtNC45NTIsMC05LjIzNSwxLjgwOC0xMi44NSw1LjQyMWMtMy42MTcsMy42MjEtNS40MjQsNy45MDUtNS40MjQsMTIuODU0ICAgYzAsNC45NDUsMS44MDcsOS4yMjcsNS40MjQsMTIuODQ3bDczLjA4OSw3My4wODhjMy42MTcsMy42MTcsNy44OTgsNS40MjQsMTIuODQ3LDUuNDI0YzQuOTUsMCw5LjIzNC0xLjgwNywxMi44NDktNS40MjQgICBsNzMuMDg3LTczLjA4OGMzLjYxMy0zLjYyLDUuNDIxLTcuOTAxLDUuNDIxLTEyLjg0N2MwLTQuOTQ4LTEuODA4LTkuMjMyLTUuNDIxLTEyLjg1NCAgIEMzMzguMTQyLDQwMy44MDIsMzMzLjg1Nyw0MDEuOTk0LDMyOC45MDYsNDAxLjk5NHoiIGZpbGw9IiM2NjY2NjYiLz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8L3N2Zz4K);
            background-repeat: no-repeat;
            background-position: center;
            transform: rotate(45deg);
        }

        > .ui-resizable-se {
            transform: rotate(-45deg);
        }


        > .ui-resizable-nw { cursor: nw-resize; width: 20px; height: 20px; left: 15px; top: 0; }
        > .ui-resizable-n  { cursor: n-resize;  height: 10px; top: 0; left: 25px; right: 25px; }
        > .ui-resizable-ne { cursor: ne-resize; width: 20px; height: 20px; right: 15px; top: 0; }
        > .ui-resizable-e  { cursor: e-resize;  width: 10px; right: math.div($horizontal_padding, 2); top: 15px; bottom: 15px; }
        > .ui-resizable-se { cursor: se-resize; width: 20px; height: 20px; right: 15px; bottom: 0; }
        > .ui-resizable-s  { cursor: s-resize;  height: 10px; left: 25px; bottom: 0; right: 25px; }
        > .ui-resizable-sw { cursor: sw-resize; width: 20px; height: 20px; left: 15px; bottom: 0; }
        > .ui-resizable-w  { cursor: w-resize;  width: 10px; left: math.div($horizontal_padding, 2); top: 15px; bottom: 15px; }

        &.ui-draggable-dragging {
            &> .ui-resizable-handle {
                display: none !important;
            }
        }

        @for $i from 1 through $gridstack-columns {
            &[data-gs-width='#{$i}'] { width: (math.div(100%, $gridstack-columns)) * $i; }
            &[data-gs-x='#{$i}'] { left: (math.div(100%, $gridstack-columns)) * $i; }
            &[data-gs-min-width='#{$i}'] { min-width: (math.div(100%, $gridstack-columns)) * $i; }
            &[data-gs-max-width='#{$i}'] { max-width: (math.div(100%, $gridstack-columns)) * $i; }
        }
    }

    &.grid-stack-animate,
    &.grid-stack-animate .grid-stack-item {
        transition: left $animation_speed, top $animation_speed, height $animation_speed, width $animation_speed;
    }

    &.grid-stack-animate .grid-stack-item.ui-draggable-dragging,
    &.grid-stack-animate .grid-stack-item.ui-resizable-resizing,
    &.grid-stack-animate .grid-stack-item.grid-stack-placeholder{
        transition: left .0s, top .0s, height .0s, width .0s;
    }
    
	&.grid-stack-one-column-mode {
		height: auto !important;
		&> .grid-stack-item {
	        position: relative !important;
	        width: auto !important;
	        left: 0 !important;
	        top: auto !important;
	        margin-bottom: $vertical_padding;
	        max-width: none !important;

	        &> .ui-resizable-handle { display: none; }
		}
	}
}

@mixin grid-stack-items($gridstack-columns) {
	.grid-stack.grid-stack-#{$gridstack-columns} {
		
		> .grid-stack-item {
			min-width: math.div(100%, $gridstack-columns);
			
			@for $i from 1 through $gridstack-columns {
				&[data-gs-width='#{$i}'] { width: (math.div(100%, $gridstack-columns)) * $i; }
				&[data-gs-x='#{$i}'] { left: (math.div(100%, $gridstack-columns)) * $i; }
				&[data-gs-min-width='#{$i}'] { min-width: (math.div(100%, $gridstack-columns)) * $i; }
				&[data-gs-max-width='#{$i}'] { max-width: (math.div(100%, $gridstack-columns)) * $i; }
			}
		}
	}
}

@for $j from 1 through $gridstack-columns {
	@include grid-stack-items($j)
}