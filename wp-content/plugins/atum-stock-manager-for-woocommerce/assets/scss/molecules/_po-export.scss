@use "sass:color";
@use "../common/variables" as *;

body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.po-wrapper {
	width: calc(100% - 40px);
	padding: 30px 20px 20px 20px;
	font-size: 14px;
}

.float-left {
	float: left;
}

.float-right {
	float: right;
}

.label {
	font-weight: bold;
}

.content-header {
	.float-right {
		text-align: right;
		width: 250px;
	}
	.float-left {
		width: 60%;
	}
	
	strong {
		font-size: 20px;
	}
}

.po-title {
	margin-top: 0;
	margin-bottom: 10px;
	font-size: 25px;
	color: $primary;
	text-transform: uppercase;
}

.content-address {
	
	.float-left {
		width: 40%;
	}
	
	.float-right {
		width: 40%;
	}
	
	h4 {
		font-size: 18px;
		background-color: $primary;
		color: $white;
		padding: 5px 10px;
		margin: 0;
		text-transform: uppercase;
	}
	
}

.content-lines {
	padding: 0 20px;
	
	table {
		width: 100%;
		border-collapse: collapse;
	}
	
	.po-li-head {
		background-color: $primary;
		th {
			color: $white;
			text-transform: uppercase;
		}
	}
	
	th, td {
		font-size: 14px;
		text-align: right;
		width: 10%;
		white-space: nowrap;
		vertical-align: top;
		padding: 5px 10px;
		
	}
	
	.description {
		text-align: left;
		white-space: unset !important;
	}
	
	.qty {
		text-align: center;
	}
	
	td.total, .po-lines tr:nth-child(even) td {
		background-color: color.adjust($primary, $alpha: -.9);
	}
}

.atum-order-item-sku {
	color: #888;
	font-size: 12px;
}

.content-totals {
	.subtotal td{
		border-top: 2px solid $primary;
		padding-top: 20px;
	}
}
.po-total {
	.label, .total {
		border-top: 2px solid $primary;
		font-weight: bold;
	}
}

.content-description {
	.label {
		text-transform: uppercase;
	}
}

.po-content {
	border: 1px solid $wp-gray-4;
	padding: 10px;
	margin-top: 10px;
}