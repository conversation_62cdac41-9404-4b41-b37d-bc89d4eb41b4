//
// ATUM fancy tables
// (https://github.com/myspace-nu/jquery.fancyTable)
//--------------------------------------------------------

.atum-fancy-table {
	width: 100%;
	text-align: left;
	font-size: 14px;
	border-collapse: collapse;
	border: none !important;
	padding: 1px 15px !important;
	
	thead {
		tr {
			&:not(.fancySearchRow) {
				th {
					border-top: 1px solid $gray-200;
					border-bottom: 1px solid $gray-200;
				}
			}
		}
		
		th {
			.sortArrow {
				border: none !important;
				font-family: '#{$atum-icon-font}' !important;
				speak: none;
				font-style: normal;
				font-weight: normal;
				font-variant: normal;
				text-transform: none;
				line-height: 1;
				
				// Better Font Rendering
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
			}
			
			&[aria-sort=descending] {
				.sortArrow {
					&:before {
						content: $atmi-arrow-up;
					}
				}
			}
			
			&[aria-sort=ascending] {
				.sortArrow {
					&:before {
						content: $atmi-arrow-down;
					}
				}
			}
		}
	}
	
	th, td {
		padding: 10px;
		color: $gray-600;
	}
	
	tbody {
		
		td {
			border-bottom: 5px solid $white !important;
		}
		
		tr {
			background-color: $gray-100;
		}
		
	}
	
	a {
		text-decoration: none;
		color: $primary;
		font-weight: bold;
		
		&:focus {
			box-shadow: none;
			outline: none;
		}
	}
	
	.fancySearchRow {
		input {
			padding: 0 8px;
			margin: 6px 0;
			line-height: 2;
			width: 100%;
			box-sizing: border-box;
			font-weight: normal;
			border-radius: 4px;
			border: 1px solid $input-border-color;
		}
	}
	
	tfoot {
		.pag {
			padding: 5px 0 0;
			text-align: right;
			
			a {
				background-color: $gray-100 !important;
				padding: 6px 12px;
				font-size: 1em;
				color: $gray-600;
				font-weight: normal;
				display: inline-block;
				border-radius: 0;
				border: 1px solid $gray-600 !important;
				margin: 0 !important;
				
				&:first-child {
					border-top-left-radius: 5px;
					border-bottom-left-radius: 5px;
				}
				
				&:last-child {
					border-top-right-radius: 5px;
					border-bottom-right-radius: 5px;
				}
				
				&.active {
					color: $white !important;
					cursor: default !important;
					box-shadow: none !important;
					background-color: $primary !important;
				}
				
				&:not(.active) {
					&:hover {
						color: $primary;
					}
				}
				
				&:not(:first-child) {
					margin-left: -1px !important;
				}
				
				> span {
					border: 1px solid $gray-600;
				}
			}
		}
	}
}
