@use "sass:math";
@use "../common/breakpoints" as *;
@use "../common/variables" as *;

//
// Page Title
//------------


.wrap {
	h1 {
		&.wp-heading-inline {
			width: 100%;
			display: flex;
			align-items: center;
			text-transform: capitalize;
			margin-right: math.div($grid-gutter-width, 2);
			font-weight: bold;
			color: $blue-dark;
			font-size: 36px;
			line-height: 1.5;
			position: relative;
			padding-top: 0;
			
			+ .page-title-action {
				display: none; // Prevent the WP button (with the wrong style) to be shown until we move it inside the h1.
			}
			
			.page-title-action {
				
				&, &:active {
					border-radius: 5px;
					background-color: var(--tertiary-var);
					color: var(--white);
					font-size: 14px;
					font-weight: normal;
					line-height: 30px;
					text-transform: uppercase;
					border: none;
					margin-left: 14px;
					top: 0;
					transition: .2s ease-in-out;
					display: inline-block;
					box-sizing: content-box;
					
					&.extend-list-table {
						font-size: 12px;
						padding: 0 12px;
						top: 1px;
						margin-left: 14px;
						display: inline-block;
					}
					
					&:hover, &:focus {
						background-color: var(--tertiary-var);
						color: var(--white);
						opacity: 0.9;
						box-shadow: none;
					}
					
					@include tablet-max-wp {
						font-size: 14px;
						height: 30px;
						line-height: 30px;
					}
					
					@include mobile-max {
						margin-top: 0;
						margin-bottom: 0;
					}
					
				}
				
			}
			
			.spacer {
				flex-grow: 1;
			}
			
		}
	}
}