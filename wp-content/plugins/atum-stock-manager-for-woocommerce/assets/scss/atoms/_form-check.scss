@use "sass:color";
@use "../common/functions" as *;
@use "../common/variables" as *;

//
// Bootstrap 5 form-check
//-----------------------

@use "sass:math";

input.form-check-input {
	width: $form-check-input-width;
	height: $form-check-input-width;
	margin-top: math.div(($line-height-base - $form-check-input-width), 2); // line-height minus check height
	vertical-align: top;
	background-color: $form-check-input-bg;
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
	border: $form-check-input-border;
	appearance: none;
	print-color-adjust: exact; // Keep themed appearance for print
	transition: $form-check-transition;
	
	&[type="checkbox"] {
		border-radius: $form-check-input-border-radius;
	}
	
	&[type="radio"] {
		// stylelint-disable-next-line property-disallowed-list
		border-radius: $form-check-radio-border-radius;
	}
	
	&:active {
		filter: $form-check-input-active-filter;
	}
	
	&:focus {
		border-color: $form-check-input-focus-border;
		outline: 0;
		box-shadow: $form-check-input-focus-box-shadow;
	}
	
	&:checked {
		background-color: $form-check-input-checked-bg-color;
		border-color: $form-check-input-checked-border-color;
		
		&[type="checkbox"] {
			@if $enable-gradients {
				background-image: escape-svg($form-check-input-checked-bg-image), var(--#{$variable-prefix}gradient);
			} @else {
				background-image: escape-svg($form-check-input-checked-bg-image);
			}
		}
		
		&[type="radio"] {
			@if $enable-gradients {
				background-image: escape-svg($form-check-radio-checked-bg-image), var(--#{$variable-prefix}gradient);
			} @else {
				background-image: escape-svg($form-check-radio-checked-bg-image);
			}
		}
	}
	
	&[type="checkbox"]:indeterminate {
		background-color: $form-check-input-indeterminate-bg-color;
		border-color: $form-check-input-indeterminate-border-color;
		
		@if $enable-gradients {
			background-image: escape-svg($form-check-input-indeterminate-bg-image), var(--#{$variable-prefix}gradient);
		} @else {
			background-image: escape-svg($form-check-input-indeterminate-bg-image);
		}
	}
	
	&:disabled {
		pointer-events: none;
		filter: none;
		opacity: $form-check-input-disabled-opacity;
	}
	
	// Use disabled attribute in addition of :disabled pseudo-class
	// See: https://github.com/twbs/bootstrap/issues/28247
	&[disabled],
	&:disabled {
		~ .form-check-label {
			opacity: $form-check-label-disabled-opacity;
		}
	}
}

.form-check-label {
	color: $form-check-label-color;
	cursor: $form-check-label-cursor;
	display: inline-block;
}

.form-switch {
	padding-left: $form-switch-padding-start;
	
	input.form-check-input {
		width: $form-switch-width;
		margin-left: $form-switch-padding-start * -1;
		background-image: escape-svg($form-switch-bg-image);
		background-position: left center;
		border-radius: $form-switch-border-radius;
		transition: $form-switch-transition;
		
		&:before {
			content: none !important;
		}
		
		&:focus {
			background-image: escape-svg($form-switch-focus-bg-image);
		}
		
		&:checked {
			background-position: $form-switch-checked-bg-position;
			
			@if $enable-gradients {
				background-image: escape-svg($form-switch-checked-bg-image), var(--#{$variable-prefix}gradient);
			} @else {
				background-image: escape-svg($form-switch-checked-bg-image);
			}
		}
		
		// Blue switches
		&.blue-switch {
			
			$form-blue-switch-focus-bg-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{color.adjust($primary, $lightness: 25%)}'/></svg>") !default;
			
			&:focus {
				border-color: $primary;
				box-shadow: 0 0 0 $input-btn-focus-width rgba($primary, .25);
				background-image: escape-svg($form-blue-switch-focus-bg-image);
			}
			
			&:checked {
				background-color: $primary;
				border-color: $primary;
				background-image: escape-svg($form-switch-checked-bg-image);
			}
		}
	}
}

.woocommerce-help-tip {
	+ .form-switch {
		margin-left: 8px;
		vertical-align: -4px;
	}
}