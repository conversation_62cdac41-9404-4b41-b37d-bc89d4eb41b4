@use "../common/breakpoints" as *;
@use "../common/variables" as *;
@use "../common/mixins/buttons" as *;
@use "../common/mixins/utilities" as *;

//
// Intro.js help guides
//-----------------------

@use "sass:math";

.introjs-overlay {
	position: absolute;
	box-sizing: content-box;
	z-index: 999999;
	opacity: 0;
	transition: all 0.3s ease-out;
}

.introjs-fixParent {
	z-index: auto !important;
	opacity: 1.0 !important;
}

.introjs-showElement {
	z-index: 9999999 !important;
}

tr.introjs-showElement {
	> td {
		z-index: 9999999 !important;
		position: relative;
	}
	
	> th {
		z-index: 9999999 !important;
		position: relative;
	}
}

.introjs-disableInteraction {
	z-index: 99999999 !important;
	position: absolute;
	background-color: $white;
	opacity: 0;
	filter: alpha(opacity=0);
}

.introjs-relativePosition {
	position: relative;
}

.introjs-helperLayer {
	box-sizing: content-box;
	position: absolute;
	z-index: 9999998;
	border-radius: 4px;
	transition: all 0.3s ease-out;
	background-color: rgba($white, .1);
	box-shadow: 0 5px 8px -3px rgba($black, .6);
	
	* {
		box-sizing: content-box;
		
		&:before {
			box-sizing: content-box;
		}
		
		&:after {
			box-sizing: content-box;
		}
	}
}

.introjs-tooltipReferenceLayer {
	box-sizing: content-box;
	position: absolute;
	visibility: hidden;
	z-index: 100000000;
	background-color: transparent;
	transition: all 0.3s ease-out;
}

.introjs-helperNumberLayer {
	color: $gray-500;
	text-align: center;
	position: absolute;
	top: -16px;
	left: -16px;
	z-index: 9999999999 !important;
	padding: 2px;
	font-size: 13px;
	font-weight: bold;
	color: $white;
	background: $primary;
	width: 20px;
	height: 20px;
	text-align: center;
	line-height: 20px;
	border: 3px solid $blue-dark-light-2;
	border-radius: 10px 10px 0;
}

.introjs-arrow {
	border: 5px solid $white;
	content: '';
	position: absolute;
	
	&.top {
		top: -10px;
		left: 10px;
		border-top-color: transparent;
		border-right-color: transparent;
		border-bottom-color: $gray-100;
		border-left-color: transparent;
	}
	
	&.top-right {
		top: -10px;
		right: 10px;
		border-top-color: transparent;
		border-right-color: transparent;
		border-bottom-color: $gray-100;
		border-left-color: transparent;
	}
	
	&.top-middle {
		top: -10px;
		left: 50%;
		margin-left: -5px;
		border-top-color: transparent;
		border-right-color: transparent;
		border-bottom-color: $gray-100;
		border-left-color: transparent;
	}
	
	&.right {
		right: -10px;
		top: 10px;
		border-top-color: transparent;
		border-right-color: transparent;
		border-bottom-color: transparent;
		border-left-color: $gray-100;
	}
	
	&.right-bottom {
		bottom: 10px;
		right: -10px;
		border-top-color: transparent;
		border-right-color: transparent;
		border-bottom-color: transparent;
		border-left-color: $gray-100;
	}
	
	&.bottom {
		bottom: -10px;
		left: 10px;
		border-top-color: $gray-100;
		border-right-color: transparent;
		border-bottom-color: transparent;
		border-left-color: transparent;
	}
	
	&.bottom-right {
		bottom: -10px;
		right: 10px;
		border-top-color: $gray-100;
		border-right-color: transparent;
		border-bottom-color: transparent;
		border-left-color: transparent;
	}
	
	&.bottom-middle {
		bottom: -10px;
		left: 50%;
		margin-left: -5px;
		border-top-color: $gray-100;
		border-right-color: transparent;
		border-bottom-color: transparent;
		border-left-color: transparent;
	}
	
	&.left {
		left: -10px;
		top: 10px;
		border-top-color: transparent;
		border-right-color: $gray-100;
		border-bottom-color: transparent;
		border-left-color: transparent;
	}
	
	&.left-bottom {
		left: -10px;
		bottom: 10px;
		border-top-color: transparent;
		border-right-color: $gray-100;
		border-bottom-color: transparent;
		border-left-color: transparent;
	}
	
}

.introjs-tooltip {
	box-sizing: content-box;
	position: absolute;
	visibility: visible;
	background-color: $white;
	min-width: 290px;
	max-width: 90%;
	border-radius: 5px;
	box-shadow: 0 3px 30px rgba($gray-900, 0.3);
	transition: opacity 0.1s ease-out;
	
	&.introjs-floating {
		width: 350px;
		position: fixed;
		margin: auto !important;
		transform: translate(-50%, -50%);
	}
	
	&.wider-tooltip {
		min-width: 500px;
	}
}

.introjs-tooltiptext {
	padding: math.div($grid-gutter-width, 2);
	border-top: 1px solid $white;
	color: $gray-600;
	border-bottom: 1px solid $white;
	font-size: 14px;
	line-height: 1.4;
	
	img {
		max-width: 100%;
		display: block;
		margin: auto;
	}
}

.introjs-tooltip-title {
	font-size: 18px;
	line-height: 1.3;
	margin: 0;
	padding: 0;
	font-weight: 500;
	text-align: left;
}

.introjs-tooltip-header {
	padding: 10px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid $gray-400;
}

.introjs-tooltipbuttons {
	border-top: 1px solid $gray-400;
	padding: 10px;
	text-align: center;
	white-space: nowrap;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.introjs-button {
	display: inline-block;
	font-weight: $btn-font-weight;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	user-select: none;
	border: $btn-border-width solid transparent;
	@include button-size($btn-padding-y, $btn-padding-x, $font-size-base, $btn-line-height, $btn-border-radius);
	transition: $btn-transition;
	text-transform: uppercase;
	color: $white;
	font-weight: 100;
	font-size: 12px;
	text-decoration: none;
	cursor: pointer;
	background-color: $primary;
	
	&:hover, &:focus {
		color: $white;
	}
	
	&:hover {
		background-color: rgba($primary, .6);
	}
	
	&:focus {
		outline: 0;
		box-shadow: $btn-focus-box-shadow;
	}
	
}

.introjs-skipbutton {
	color: $gray-600;
	font-size: 20px;
	cursor: pointer;
	
	&:hover {
		color: $blue-dark;
	}

	&:focus {
		outline: none;
		box-shadow: none;
	}
}

.introjs-disabled {
	&, &:hover {
		color: $gray-500;
		border-color: $gray-400;
		cursor: not-allowed;
		background-color: $gray-100;
		outline: none;
		box-shadow: none;
	}
}

.introjs-hidden {
	display: none;
}

.introjs-bullets {
	text-align: center;
	padding-top: 10px;
	padding-bottom: 10px;
	
	ul {
		box-sizing: content-box;
		clear: both;
		margin: 15px auto 0;
		padding: 0;
		display: inline-block;
		
		li {
			box-sizing: content-box;
			list-style: none;
			float: left;
			margin: 0 2px;
			
			a {
				transition: width 0.1s ease-in;
				box-sizing: content-box;
				display: block;
				width: 6px;
				height: 6px;
				background: $gray-400;
				border-radius: 10px;
				text-decoration: none;
				cursor: pointer;
				
				&:hover,
				&:focus {
					width: 15px;
					background: $gray-500;
					text-decoration: none;
					outline: none;
				}
				
				&.active {
					width: 15px;
					background: $gray-500;
				}
			}
			
		}
	}
}

.introjs-progress {
	box-sizing: content-box;
	overflow: hidden;
	height: 10px;
	margin: 10px;
	border-radius: 4px;
	background-color: $gray-300;
}

.introjs-progressbar {
	box-sizing: content-box;
	float: left;
	width: 0%;
	height: 100%;
	font-size: 10px;
	line-height: 10px;
	text-align: center;
	background-color: $primary;
}

.introjsFloatingElement {
	position: absolute;
	height: 0;
	width: 0;
	left: 50%;
	top: 50%;
}

.introjs-fixedTooltip {
	position: fixed;
}

.introjs-hint {
	box-sizing: content-box;
	position: absolute;
	background: transparent;
	width: 20px;
	height: 15px;
	cursor: pointer;
	
	&:focus {
		border: 0;
		outline: 0;
	}
	
	&:hover {
		> .introjs-hint-pulse {
			background-color: rgba($gray-800, 0.57);
		}
	}
}

.introjs-hidehint {
	display: none;
}

.introjs-fixedhint {
	position: fixed;
}

@keyframes introjspulse {
	0% {
		transform: scale(0.95);
		box-shadow: 0 0 0 0 rgba($black, 0.7);
	}
	
	70% {
		transform: scale(1);
		box-shadow: 0 0 0 10px rgba($black, 0);
	}
	
	100% {
		transform: scale(0.95);
		box-shadow: 0 0 0 0 rgba($black, 0);
	}
}

.introjs-hint-pulse {
	box-sizing: content-box;
	width: 15px;
	height: 15px;
	border-radius: 30px;
	background-color: $gray-800;
	z-index: 10;
	position: absolute;
	transition: all 0.2s ease-out;
	animation: introjspulse 2s infinite;
}

.introjs-hint-no-anim {
	.introjs-hint-pulse {
		animation: none;
	}
}

.introjs-hint-dot {
	box-sizing: content-box;
	background: transparent;
	border-radius: 60px;
	height: 50px;
	width: 50px;
	position: absolute;
	top: -18px;
	left: -18px;
	z-index: 1;
	opacity: 0;
}

.introjs-tooltip {
	.introjs-tooltiptext {
		
		a {
			word-wrap: break-word;
		}
		
		ul {
			padding-left: math.div($grid-gutter-width, 2);
			list-style: outside;
		}
		
	}
	
	.introjs-bullets {
		display: none;
	}
}

// ATUM buttons to show a specific intro guide or the help markers.
.help-guide-buttons {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 5px;
	margin-top: 7px;
	
	i {
		border-radius: 5px;
		padding: 5px;
		transition: .2s ease-in-out;
		cursor: help;
		border-radius: 5px;
		border: 1px solid $gray-600;
		color: $gray-600;
		background-color: $white;
		font-weight: normal;
		font-size: 18px;
		display: inline-flex;
		position: relative;
		
		&.show-intro-guide {
			&:not(.loading-guide) {
				&:hover {
					background-color: $primary;
					color: $white;
					border-color: $primary;
				}
			}
			
		}
		
		&.show-help-markers {
			&:not(.loading-guide) {
				font-size: 14px;
				padding: 7px;
				
				&.active, &:hover {
					color: $white;
				}
				
				&.active {
					background-color: $success;
					border-color: $success;
				}
				
				&:hover {
					background-color: rgba($success, .8);
					border-color: rgba($success, .8);
				}
			}
		}
		
		&.loading-guide {
			pointer-events: none;
			
			&:before {
				color: transparent;
			}
			
			&:after {
				content: '';
				@include loader(14px, $primary);
			}
		}
		
	}
	
	@include mobile-max {
		margin-top: 0;
	}
	
}

body {
	
	&.atum-show-help-markers {
		
		.atum-help-marker {
			position: relative;
			display: inline-flex;
			
			&.active {
				
				&, &:hover {
					
					&:before {
						display: inline-flex !important;
						align-items: center !important;
						justify-content: center !important;
						content: $atmi-question;
						@include atum-icon-font-family;
						background-color: rgba($red, .8) !important;
						border: 1px solid $white !important;
						color: $white !important;
						font-size: 9px !important;
						font-weight: normal !important;
						font-style: normal !important;
						vertical-align: 5px !important;
						text-align: center !important;
						position: absolute !important;
						overflow: hidden !important;
						box-shadow: 0 2px 8px 0 rgba($black, 0.3) !important;
						width: 16px !important;
						height: 16px !important;
						right: -8px !important;
						top: -8px !important;
						z-index: 9989 !important; // One under the WP menu sidebar.
						border-radius: 50px !important;
						text-indent: unset !important;
						cursor: help !important;
					}
					
					&[data-marker-position="top-left"] {
						&:before {
							right: unset !important;
							left: -8px !important;
						}
					}
					
					&[data-marker-position="bottom-right"] {
						&:before {
							top: unset !important;
							bottom: -8px !important;
						}
					}
					
					&[data-marker-position="bottom-left"] {
						&:before {
							top: unset !important;
							right: unset !important;
							bottom: -8px !important;
							left: -8px !important;
						}
					}
					
				}
				
				&:hover {
					&:before {
						background-color: $red !important;
					}
				}
				
			}
			
		}
		
	}
	
	&.running-atum-help-guide {
		.atum-help-marker.active {
			&, &:hover {
				&:before {
					visibility: hidden !important;
				}
			}
		}
	}
	
}

// Help guide buttons on the screen options tabs
#screen-meta-links {
	
	#atum-help-guide-link-wrap {
		float: left;
		margin: -6px 0 0 6px;
		
		.show-settings {
			padding-left: 7px;
			
			&:after{
				content: none;
			}
		}
		
		.help-guide-buttons {
			margin-top: 0;
			position: relative;
			top: 2px;
			
			i {
				border: none;
				padding: 5px;
				
				&:hover {
					background-color: transparent;
					color: $blue;
				}
			}
		}
	}
	
	@include tablet-max-wp {
		#atum-help-guide-link-wrap {
			margin-top: -1px !important;
			
			.help-guide-buttons {
				i {
					padding: 8px;
				}
			}
		}
	}
}


@include loader-rotate-animation;