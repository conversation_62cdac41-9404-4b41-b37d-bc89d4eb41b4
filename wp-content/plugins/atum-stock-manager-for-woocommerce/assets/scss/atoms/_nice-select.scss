@use "../common/variables" as *;

//
// Nice Select
// (https://github.com/hernansartorio/jquery-nice-select)
//--------------------------------------------------------

@use "sass:math";

// Scoped variables
$ns-font-size:                     14px !default;
$ns-font-size-small:               12px !default;

$ns-dropdown-border-radius:        5px !default;
$ns-list-border-radius:            5px !default;
$ns-dropdown-height:               35px !default;
$ns-dropdown-height-small:         26px !default;
$ns-dropdown-padding:              18px !default;

$ns-arrow-color:                   var(--main-text) !default;
$ns-arrow-size:                    5px !default;


// Style the dropdown
.nice-select {
	-webkit-tap-highlight-color: rgba($black, 0);
	background-color: var(--dash-nice-select-bg);
	border-radius: $ns-dropdown-border-radius;
	border: solid 1px var(--main-border-alt);
	box-sizing: border-box;
	clear: both;
	cursor: pointer;
	display: block;
	font-family: inherit;
	font-size: $ns-font-size;
	font-weight: 100;
	height: $ns-dropdown-height;
	line-height: $ns-dropdown-height - 2;
	outline: none;
	padding-left: $ns-dropdown-padding;
	padding-right: $ns-dropdown-padding + 12;
	position: relative;
	text-align: left !important;
	transition: all 0.2s ease-in-out;
	user-select: none;
	white-space: nowrap;
	width: auto;
	color: var(--atum-text-color-var1);
	
	&:hover, &:active, &:focus {
		border-color: var(--dash-nice-select-hover);
	}
	
	// Arrow
	&:after {
		border-bottom: 1px solid var(--main-border-alt);
		border-right: 1px solid var(--main-border-alt);
		content: '';
		display: block;
		height: $ns-arrow-size;
		margin-top: -($ns-arrow-size - 1);
		pointer-events: none;
		position: absolute;
		right: 12px;
		top: 50%;
		transform-origin: 66% 66%;
		transform: rotate(45deg);
		transition: all 0.15s ease-in-out;
		width: $ns-arrow-size;
	}
	
	&.open {
		border-color: var(--dash-nice-select-hover);
		
		&:after {
			transform: rotate(-135deg);
		}
		
		.list {
			opacity: 1;
			pointer-events: auto;
			transform: scale(1) translateY(0);
		}
	}
	
	&.disabled {
		border-color: var(--dash-nice-select-disabled);
		color: var(--gray-500);
		pointer-events: none;
		
		&:after {
			border-color: var(--dash-nice-select-disabled-after);
		}
	}
	
	// Modifiers
	&.wide {
		width: 100%;
		
		.list {
			left: 0 !important;
			right: 0 !important;
		}
	}
	
	&.right {
		float: right;
		
		.list {
			left: auto;
			right: 0;
		}
	}
	
	&.left {
		float: left;
		
		.list {
			left: 0;
			right: auto;
		}
	}
	
	&.small {
		font-size: $ns-font-size-small;
		height: $ns-dropdown-height-small;
		line-height: $ns-dropdown-height-small - 2;
		
		&:after {
			height: 4px;
			width: 4px;
		}
		
		.option {
			line-height: $ns-dropdown-height-small - 2;
			min-height: $ns-dropdown-height-small - 2;
		}
	}
	
	// List and options
	.list {
		background-color: var(--dash-nice-select-list-bg);
		border-radius: $ns-list-border-radius;
		box-shadow: 0 0 10px 0 var(--dark-shadow);
		box-sizing: border-box;
		margin-top: 4px;
		opacity: 0;
		overflow: hidden;
		padding: 0;
		pointer-events: none;
		position: absolute;
		top: 100%; left: 0;
		transform-origin: 50% 0;
		transform: scale(.75) translateY(math.div($ns-dropdown-height, -2));
		transition: all .2s cubic-bezier(0.5, 0, 0, 1.25), opacity .15s ease-out;
		z-index: 9;
		
		&:hover .option:not(:hover) {
			background-color: transparent !important;
		}
	}
	
	.option {
		cursor: pointer;
		font-weight: 400;
		line-height: $ns-dropdown-height - 2;
		list-style: none;
		min-height: $ns-dropdown-height - 2;
		outline: none;
		padding-left: $ns-dropdown-padding;
		padding-right: $ns-dropdown-padding + 11;
		margin: 0;
		text-align: left;
		transition: all 0.2s;
		
		&:hover, &.focus, &.selected.focus {
			background-color: var(--dash-nice-select-option-hover-bg);
			color: var(--primary-var-dark);
		}
		
		&.selected {
			background-color: var(--dash-nice-select-option-selected-bg) !important;
			color: var(--primary-var-dark);
		}
		
		&.disabled {
			background-color: transparent;
			color: var(--gray-500);
			cursor: default;
		}
	}
}

// Use display instead of opacity for IE <= 10
.no-csspointerevents .nice-select {
	.list {
		display: none;
	}
	
	&.open {
		.list {
			display: block;
		}
	}
}