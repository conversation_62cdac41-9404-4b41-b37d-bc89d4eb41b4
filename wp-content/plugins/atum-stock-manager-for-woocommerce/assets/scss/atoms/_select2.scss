@use "../common/variables" as *;
@use "../common/mixins/utilities" as *;
@use "../common/mixins/border-radius" as *;

//
// Select2 Customizations
//-----------------------

body {
	
	.select2-container {
		
		&.atum-select2, &.atum-enhanced-select {
			font-size: 14px;
			box-sizing: border-box;
			display: inline-block;
			margin: 0;
			position: relative;
			vertical-align: middle;
			
			.select2-results {
				display: block;
				box-shadow: $dropdown-box-shadow;
				max-height: inherit;
				padding: 0;
				margin: 0;
				position: relative;
				overflow-x: inherit;
				overflow-y: inherit;
				
				.select2-results__option {
					padding: 8px 10px;
				}
			}
			
			&, * {
				&:focus {
					outline: none;
				}
			}
			
			&:focus {
				.select2-selection {
					box-shadow: none;
					border-color: $input-focus;
				}
			}
			
			.select2-selection__arrow {
				color: var(--main-border-alt);
				
				b {
					display: none;
				}
				
				&:after {
					content: $atmi-chevron-down;
					line-height: 27px;
					font-family: atum-icon-font;
					font-size: 10px;
				}
			}
			
			.select2-selection--single {
				box-sizing: border-box;
				cursor: pointer;
				display: block;
				height: 28px;
				user-select: none;
				
				.select2-selection__rendered {
					display: block;
					padding-left: 8px;
					padding-right: 25px;
					@include text-overflow;
				}
				
				.select2-selection__clear {
					position: relative;
					visibility: hidden;
					color: var(--main-border-alt);
					
					&:after {
						font-family: atum-icon-font;
						content: $atmi-cross;
						visibility: visible;
						line-height: 2;
						vertical-align: middle;
					}
				}
			}
			
			.select2-selection--multiple {
				box-sizing: border-box;
				cursor: pointer;
				display: block;
				min-height: 32px;
				user-select: none;
				
				.select2-selection__rendered {
					padding-left: 8px;
					@include text-overflow;
				}
			}
			
			&[dir="rtl"] {
				.select2-selection--single {
					.select2-selection__rendered {
						padding-right: 8px;
						padding-left: 25px;
					}
				}
			}
			
			.select2-search--inline {
				float: left;
				
				.select2-search__field {
					box-sizing: border-box;
					border: none;
					font-size: 100%;
					padding: 0;
					background-image: none !important;
					
					&::-webkit-search-cancel-button {
						appearance: none;
					}
					
					&:focus {
						box-shadow: none;
					}
				}
			}
			
		}
		
	}
	
	span.select2-dropdown {
		&.atum-select2-dropdown {
			box-shadow: $dropdown-box-shadow !important;  // Fix: added important to override WP admin CSS.
			background-color: var(--atum-table-bg) !important;
			border: 1px solid var(--gray-500);
			border-radius: 4px;
			overflow: hidden;
			box-sizing: border-box;
			display: block;
			position: absolute;
			left: -100000px;
			width: 100%;
			z-index: 100000; // Same as the sweet alert's index, so we can use it within modals
			
			.select2-results__options {
				list-style: none;
				margin: 0;
				padding: 0;
				
				&::-webkit-scrollbar {
					background-color: transparent;
					border: none;
					width: 7px;
				}
				
				&::-webkit-scrollbar-track {
					background-color: transparent;
					border: none;
				}
				
				&::-webkit-scrollbar-thumb {
					border-radius: 4px;
					background-color: var(--gray-500);
				}
			}
			
			.select2-results__option {
				padding: 6px;
				user-select: none;
				color: var(--atum-text-color-var3);
				font-size: 12px;
				
				&:focus {
					outline: none;
				}
				
			}
			
			.select2-results__option[aria-selected] {
				cursor: pointer;
			}
			
		}
	}
	
	.select2-container--open {
		
		.select2-dropdown {
			&.atum-select2-dropdown {
				left: 0;
				
				&.select2-dropdown--above {
					border-bottom: none;
					border-bottom-left-radius: 0;
					border-bottom-right-radius: 0;
				}
				
				&.select2-dropdown--below {
					border: none;
				}
				
				.select2-selection__arrow {
					transform: rotateX(180deg);
				}
			}
		}
		
	}
	
	.select2-dropdown {
		&.atum-select2-dropdown {
			
			.select2-search--dropdown {
				display: block;
				padding: 4px;
				
				.select2-search__field {
					padding: 4px;
					width: 100%;
					box-sizing: border-box;
					min-width: inherit;
					border-radius: 4px;
					border-color: var(--main-border-alt);
					
					&::-webkit-search-cancel-button {
						appearance: none;
					}
				}
				
				&.select2-search--hide {
					display: none;
				}
			}
			
		}
	}
	
	.select2-close-mask {
		border: 0;
		margin: 0;
		padding: 0;
		display: block;
		position: fixed;
		left: 0;
		top: 0;
		min-height: 100%;
		min-width: 100%;
		height: auto;
		width: auto;
		opacity: 0;
		z-index: 99;
		background-color: var(--white);
	}
	
	.select2-hidden-accessible {
		border: 0 !important;
		clip: rect(0 0 0 0) !important;
		height: 1px !important;
		margin: -1px !important;
		overflow: hidden !important;
		padding: 0 !important;
		position: absolute !important;
		width: 1px !important;
	}
	
	.select2-container--default {
		
		&.atum-select2, &.atum-enhanced-select {
			
			.select2-selection--single {
				height: 30px;
				border-radius: 4px;
				border: 1px solid var(--main-border-alt);
				background-color: var(--atum-table-bg);
				
				.select2-selection__rendered {
					line-height: 27px;
					color: var(--atum-text-color-var3);
					font-size: 15px;
					padding-left: 10px;
				}
				
				.select2-selection__clear {
					cursor: pointer;
					float: right;
					font-weight: bold;
				}
				
				.select2-selection__placeholder {
					color: var(--atum-text-color-var3);
				}
				
				.select2-selection__arrow {
					height: 26px;
					position: absolute;
					top: 3px;
					right: 0;
					width: 18px;
					
					b {
						border-color: var(--main-border-alt) transparent transparent transparent;
						border-style: solid;
						border-width: 5px 4px 0 4px;
						height: 0;
						left: 50%;
						margin-left: -4px;
						margin-top: -2px;
						position: absolute;
						top: 50%;
						width: 0;
					}
				}
				
			}
			
		}
		
	}
	
	#bulk-edit {
		.select2-selection__arrow {
			right: 0px;
			height: 30px;
			top: 0;
		}
	}
	
	.select2-container--default[dir="rtl"] {
		
		&.atum-select2, &.atum-enhanced-select {
			
			.select2-selection--single {
				.select2-selection__clear {
					float: left;
				}
				
				.select2-selection__arrow {
					left: 1px;
					right: auto;
				}
			}
			
		}
		
	}
	
	.select2-container--default {
		
		&.atum-select2, &.atum-enhanced-select {
			
			&.select2-container--disabled {
				.select2-selection--single {
					background-color: $wp-gray-5;
					border-color: $input-disabled-border-color;
					cursor: default;
					
					.select2-selection__clear {
						display: none;
					}
				}
			}
			
			&.select2-container--open {
				.select2-selection--single .select2-selection__arrow b {
					border-color: transparent transparent $wp-gray-7 transparent;
					border-width: 0 4px 5px 4px;
				}
			}
			
			.select2-selection--multiple {
				background-color: var(--white);
				border: 1px solid var(--gray-500);
				border-radius: 4px;
				cursor: text;
				
				.select2-selection__rendered {
					box-sizing: border-box;
					list-style: none;
					margin: 0;
					padding: 0 5px;
					width: 100%;
					
					li {
						list-style: none;
					}
				}
				
				.select2-selection__placeholder {
					color: $wp-gray-1;
					margin-top: 5px;
					float: left;
				}
				
				.select2-selection__clear {
					cursor: pointer;
					float: right;
					font-weight: bold;
					margin-top: 5px;
					margin-right: 10px;
				}
				
				.select2-selection__choice {
					background-color: var(--gray-500);
					border: none;
					border-radius: 4px;
					cursor: default;
					float: left;
					margin: 4px 5px 0 0;
					padding: 3px 5px;
					height: 24px;
					overflow: hidden;
					margin: 4px 5px 0 0;
					color: var(--white);
					line-height: 1;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
				
				.select2-selection__choice__remove {
					color: $wp-gray-1;
					cursor: pointer;
					font-weight: bold;
					margin-right: 0;
					visibility: hidden;
					
					&:after {
						font-family: atum-icon-font;
						content: $atmi-cross;
						visibility: visible;
						position: relative;
						left: -8px;
						font-size: 14px;
						line-height: 2;
						color: var(--white);
						transition: .2s ease-in-out;
					}
					
					&:hover {
						&:after {
							color: var(--gray-800);
						}
					}
				}
			}
			
		}
		
	}
	
	.select2-container--default[dir="rtl"] {
		
		&.atum-select2, &.atum-enhanced-select {
			
			.select2-selection--multiple {
				.select2-selection__choice, .select2-selection__placeholder, .select2-search--inline {
					float: right;
				}
				
				.select2-selection__choice {
					margin-left: 5px;
					margin-right: auto;
				}
				
				.select2-selection__choice__remove {
					margin-left: 2px;
					margin-right: auto;
				}
			}
			
		}
	}
	
	.select2-container--default {
		
		&.atum-select2, &.atum-enhanced-select {
			
			&.select2-container--focus {
				.select2-selection--multiple {
					box-shadow: none;
					border-color: $input-focus;
					outline: 0;
				}
			}
			
			&.select2-container--disabled {
				.select2-selection--multiple {
					cursor: default;
					
					&, input {
						background-color: $wp-gray-5;
					}
				}
				
				.select2-selection__choice__remove {
					display: none;
				}
			}
			
			&.select2-container--open {
				&.select2-container--above {
					.select2-selection--single, .select2-selection--multiple {
						border-top-left-radius: 0;
						border-top-right-radius: 0;
					}
				}
				
				&.select2-container--below {
					.select2-selection--single, .select2-selection--multiple {
						border-bottom-left-radius: 0;
						border-bottom-right-radius: 0;
					}
				}
			}
			
		}
		
		.select2-dropdown {
			&.atum-select2-dropdown {
				
				.select2-search--dropdown {
					.select2-search__field {
						height: 30px;
						border-color: var(--main-border-alt);
						box-shadow: none;
						border-radius: 4px;
					}
				}
				
				.select2-search--inline {
					.select2-search__field {
						background: transparent;
						border: none;
						outline: 0;
						box-shadow: none;
						appearance: textfield;
					}
				}
				
				.select2-results__option--highlighted {
					color: var(--white) !important;
					background-color: var(--atum-menu-text-highlight) !important; // Fix: added important to override WP admin CSS.
				}
				
				.select2-results__option {
					
					&[role=group] {
						padding: 0;
					}
					
					&[aria-disabled=true] {
						color: $wp-gray-1;
					}
					
					&[aria-selected=true] {
						color: var(--white) !important;
						background-color: var(--atum-menu-text-highlight);
					}
					
					&[data-selected=true] {
						background-color: var(--primary-dark) !important;
						color: var(--atum-text-color-var3) !important;
					}
					
					&.select2-results__message {
						line-height: 1.3;
					}
					
					.select2-results__option {
						padding-left: 1em;
						
						.select2-results__group {
							padding-left: 0;
						}
						
						.select2-results__option {
							margin-left: -1em;
							padding-left: 2em;
							
							.select2-results__option {
								margin-left: -2em;
								padding-left: 3em;
								
								.select2-results__option {
									margin-left: -3em;
									padding-left: 4em;
									
									.select2-results__option {
										margin-left: -4em;
										padding-left: 5em;
										
										.select2-results__option {
											margin-left: -5em;
											padding-left: 6em;
										}
									}
								}
							}
						}
					}
					
				}
				
				.select2-results__group {
					cursor: default;
					display: block;
					padding: 6px;
				}
				
			}
		}
	}
	
	.select2-container--classic {
		
		&.atum-select2, &.atum-enhanced-select {
			
			.select2-selection--single {
				background-color: var(--gray-100);
				border: 1px solid var(--gray-500);
				border-radius: 4px;
				outline: 0;
				background-image: linear-gradient(to bottom, $white 50%, $wp-gray-5 100%);
				background-repeat: repeat-x;
				
				&:focus {
					border: 1px solid var(--primary);
				}
				
				.select2-selection__rendered {
					color: var(--gray-700);
					line-height: 28px;
				}
				
				.select2-selection__clear {
					cursor: pointer;
					float: right;
					font-weight: bold;
					margin-right: 10px;
				}
				
				.select2-selection__placeholder {
					color: var(--dark);
				}
				
				.select2-selection__arrow {
					background-color: $wp-gray-4;
					border: none;
					border-left: 1px solid var(--main-border-alt);
					border-top-right-radius: 4px;
					border-bottom-right-radius: 4px;
					height: 26px;
					position: absolute;
					top: 1px;
					right: 1px;
					width: 20px;
					background-image: linear-gradient(to bottom, $wp-gray-5 50%, $gray-500 100%);
					background-repeat: repeat-x;
					
					b {
						border-color: $wp-gray-7 transparent transparent transparent;
						border-style: solid;
						border-width: 5px 4px 0 4px;
						height: 0;
						left: 50%;
						margin-left: -4px;
						margin-top: -2px;
						position: absolute;
						top: 50%;
						width: 0;
					}
				}
			}
			
			&[dir="rtl"] {
				.select2-selection--single {
					.select2-selection__clear {
						float: left;
					}
					
					.select2-selection__arrow {
						border: none;
						border-right: 1px solid var(--main-border-alt);
						border-radius: 0;
						border-top-left-radius: 4px;
						border-bottom-left-radius: 4px;
						left: 1px;
						right: auto;
					}
				}
			}
			
			&.select2-container--open {
				.select2-selection--single {
					border: 1px solid var(--primary);
					
					.select2-selection__arrow {
						background: transparent;
						border: none;
						
						b {
							border-color: transparent transparent $wp-gray-7 transparent;
							border-width: 0 4px 5px 4px;
						}
					}
				}
				
				&.select2-container--above {
					.select2-selection--single {
						border-top: none;
						border-top-left-radius: 0;
						border-top-right-radius: 0;
						background-image: linear-gradient(to bottom, $white 0%, $wp-gray-5 50%);
						background-repeat: repeat-x;
					}
				}
				
				&.select2-container--below {
					.select2-selection--single {
						border-bottom: none;
						border-bottom-left-radius: 0;
						border-bottom-right-radius: 0;
						background-image: linear-gradient(to bottom, $wp-gray-5 50%, $white 100%);
						background-repeat: repeat-x;
					}
				}
			}
			
			.select2-selection--multiple {
				background-color: var(--white);
				border: 1px solid var(--gray-500);
				border-radius: 4px;
				cursor: text;
				outline: 0;
				
				&:focus {
					border: 1px solid var(--primary);
				}
				
				.select2-selection__rendered {
					list-style: none;
					margin: 0;
					padding: 0 5px;
				}
				
				.select2-selection__clear {
					display: none;
				}
				
				.select2-selection__choice {
					background-color: $wp-gray-5;
					border: 1px solid var(--gray-500);
					border-radius: 4px;
					cursor: default;
					float: left;
					margin-right: 5px;
					margin-top: 5px;
					padding: 0 5px;
				}
				
				.select2-selection__choice__remove {
					color: $wp-gray-7;
					cursor: pointer;
					display: inline-block;
					font-weight: bold;
					margin-right: 2px;
					
					&:hover {
						color: $wp-gray-2;
					}
				}
			}
			
			&[dir="rtl"] {
				.select2-selection--multiple {
					.select2-selection__choice {
						float: right;
					}
					
					.select2-selection__choice {
						margin-left: 5px;
						margin-right: auto;
					}
					
					.select2-selection__choice__remove {
						margin-left: 2px;
						margin-right: auto;
					}
				}
			}
			
			&.select2-container--open {
				.select2-selection--multiple {
					border: 1px solid var(--primary);
				}
				
				&.select2-container--above {
					.select2-selection--multiple {
						border-top: none;
						border-top-left-radius: 0;
						border-top-right-radius: 0;
					}
				}
				
				&.select2-container--below {
					.select2-selection--multiple {
						border-bottom: none;
						border-bottom-left-radius: 0;
						border-bottom-right-radius: 0;
					}
				}
			}
			
			.select2-search--dropdown {
				.select2-search__field {
					border: 1px solid var(--gray-500);
					outline: 0;
				}
			}
			
			.select2-search--inline {
				.select2-search__field {
					outline: 0;
					box-shadow: none;
				}
			}
			
		}
		
	}
	
	.select2-container--default {
		
		.select2-dropdown {
			&.atum-select2-dropdown {
				background-color: var(--white);
				border: 1px solid var(--main-dropdown-border);
				border-top-color: transparent;
				
				&.select2-dropdown--above {
					border-bottom: none;
					@include border-bottom-radius(0);
				}
				
				&.select2-dropdown--below {
					border-top: none;
					@include border-top-radius(0);
				}
				
				.select2-results {
					> .select2-results__options {
						max-height: 210px;
						overflow-y: auto;
					}
				}
				
				.select2-results__option {
					&[role=group] {
						padding: 0;
					}
					
					&[aria-disabled=true] {
						color: $wp-gray-1;
					}
				}
				
				.select2-results__group {
					cursor: default;
					display: block;
					padding: 6px;
				}
				
			}
		}
			
	}
		
}
