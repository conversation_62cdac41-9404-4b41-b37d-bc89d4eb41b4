@use "sass:color";
@use "variables" as *;

:root {
	// Custom variable values only support SassScript inside `#{}`.
	@each $color, $value in $grays {
		--gray-#{$color}: #{$value};
		--gray-#{$color}-hover: #{rgba($value,0.6)};
		--gray-#{$color}-shadow: #{rgba($value, 0.2)};
	}
	
	@each $color, $value in $colors {
		--#{$color}: #{$value};
		--#{$color}-hover: #{rgba($value,0.6)};
		--#{$color}-shadow: #{rgba($value, 0.2)};
	}
	
	@each $color, $value in $theme-colors {
		--#{$color}: #{$value};
		--#{$color}-hover: #{rgba($value,0.6)};
		--#{$color}-shadow: #{rgba($value, 0.2)};
		--#{$color}-hover-border: none;
	}
	
	@each $color, $value in $wp-colors {
		--#{$color}: #{$value};
		--#{$color}-hover: #{rgba($value,0.6)};
		--#{$color}-shadow: #{rgba($value, 0.2)};
	}
	
	@each $color, $value in $wc-colors {
		--#{$color}: #{$value};
		--#{$color}-hover: #{rgba($value,0.6)};
		--#{$color}-shadow: #{rgba($value, 0.2)};
	}
	
	--atum-add-widget-title: #{$dark};
	--atum-add-widget-separator: #{$light};
	--atum-bg-white: #{$white};
	--atum-border-expanded: #{$gray-500};
	--atum-border-var: #{$gray-200};
	--atum-cloned-list-table-shadow: #{rgba($black, 0.04)};
	--atum-column-groups-bg: #{$gray-200};
	--atum-dark-bg: #{$blue-dark-lighten};
	--atum-dropdown-toggle-bg: #{$gray-200};
	--atum-easytree-node: #{$dark};
	--atum-expanded-bg: #{$white};
	--atum-footer-title: #{$blue-dark};
	--atum-footer-link: #{$wp-link};
	--atum-footer-text: #{$gray-700};
	--atum-menu-text: #{$primary};
	--atum-menu-text-highlight: #{$primary};
	--atum-pagination-bg: #{$gray-200};
	--atum-pagination-border: transparent;
	--atum-pagination-border-disabled: transparent;
	--atum-row-shadow: #{rgba($black, 0.04)};
	--atum-settings-btn-save: #{$white};
	--atum-settings-btn-save-hover: #{$blue-light};
	--atum-settings-input-border: #{$gray-200};
	--atum-settings-heads-bg: #{$blue};
	--atum-settings-nav-bg: #{$white};
	--atum-settings-nav-link: #{$blue};
	--atum-settings-section-general-bg: #{$blue-dark-lighten};
	--atum-settings-section-bg: #{$white};
	--atum-settings-text-logo: #{$blue-dark};
	--atum-settings-wp-color-bg: transparent;
	--atum-table-bg: #{$white};
	--atum-table-bg2: #{$light};
	--atum-table-filter-dropdown: #{$gray-600};
	--atum-table-link-text: #{$primary};
	--atum-table-row-variation-text: #{$white};
	--atum-table-search-submit-bg: #{$gray-500};
	--atum-table-search-text-disabled: #{$gray-400};
	--atum-table-views-tabs: #{$gray-600};
	--atum-table-views-tabs-active: #{$blue};
	--atum-table-views-tabs-active-text: #{$white};
	--atum-table-text: #{$gray-800};
	--atum-text-color-dark2: #{$gray-600};
	--atum-text-color-var1: #{$gray-500};
	--atum-text-color-var2: #{$gray-500};
	--atum-text-color-var3: #{$gray-600};
	--atum-text-white: #{$white};
	--atum-version: #{$white};
	--atum-version-bg: #{rgba($black, 0.2)};
	--atum-checkbox-label: #{$black};
	--atum-input-text: #{$gray-600};
	--atum-setting-info: #{$gray-700};
	--atum-section-field: #{$white};
	--black-shadow-light: #{rgba($black, 0.04)};
	--blue-light-expanded: #{$blue-light};
	--danger-hover-border: 1px solid transparent;
	--danger-hover-text: #{$white};
	--dash-add-widget-color: #{$gray-500};
	--dash-add-widget-color-dark: #{$blue};
	--dash-atum-version: #{color.adjust($black, $alpha: -0.9)};
	--dash-blue-trans: #{$blue-trans};
	--dash-card-bg: #{$gray-100};
	--dash-card-text: #{$gray-600};
	--dash-green-blue: #{$green-blue};
	--dash-green-light: #{$green-light};
	--dash-green-trans: #{$green-trans};
	--dash-input-group-bg: #{color.adjust($white, $alpha: -0.34)};
	--dash-input-group-shadow: #{color.adjust($white, $alpha: -0.8)};
	--dash-next-text:  #{$gray-200};
	--dash-nice-select-bg: #{$white};
	--dash-nice-select-disabled: #{color.adjust($gray-200, $lightness: 2%)};
	--dash-nice-select-disabled-after: #{color.adjust($gray-500, $lightness: 20%)};
	--dash-nice-select-hover: #{color.adjust($gray-200, $lightness: -10%)};
	--dash-nice-select-list-bg: #{$white};
	--dash-nice-select-option-hover-bg: #{$gray-100};
	--dash-nice-select-option-selected-bg: #{$blue-light};
	--dash-statistics-chart-type-bg: #{color.adjust($light, $alpha: -0.8)};
	--dash-statistics-chart-type-selected-bg: transparent;
	--dash-statistics-chart-type-selected-text: #{$warning};
	--dash-statistics-grid-lines: #{$gray-200};
	--dash-statistics-legend-switch-bg: #{color.adjust($light, $alpha: -0.8)};
	--dash-statistics-ticks: #{$blue-dark};
	--dash-stats-data-widget-primary: #{$widget-primary};
	--dash-stats-data-widget-success: #{$widget-success};
	--dash-subscription-input: #{rgba($white, 0.7)};
	--dash-video-title: #{$dark};
	--dash-video-subs-text: #{$gray-500};
	--dash-widget-current-stock-value-bg: #{$red-light};
	--dash-widget-current-stock-value-text: #{$red};
	--dash-widget-icon: #{$gray-500};
	--dash-widget-list-border: #{color.adjust($light, $alpha: -0.5)};
	--green-light-expanded: #{$green-light};
	--info-hover-border: 1px solid transparent;
	--main-border: #{rgba($white, 0.2)};
	--main-border-alt: #{$gray-600};
	--main-dropdown-border: #{$gray-300};
	--main-text: #{$dark};
	--main-text-expanded: #{$white};
	--main-title: #{$blue-dark};
	--orange-dark-light: #{$orange-dark-light};
	--order-green-light: #{$green-light};
	--overflow-opacity-left: linear-gradient(to left, rgba(255,255,255,0), rgba(255,255,255,0.9));
	--overflow-opacity-rigth: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9));
	--popover-black-shadow: #{rgba($black, 0.2)};
	--primary-dark: #{$blue-light};
	--primary-light: #{$blue-light-2};
	--primary-hover-border: none;
	--primary-hover-text: #{$blue};
	--primary-var-dark: #{$blue};
	--primary-var-text2: #{$blue};
	--secondary: #{$secondary};
	--secondary-dark: #{$orange-light};
	--secondary-hover: #{rgba($warning, 0.6)};
	--secondary-hover-border: none;
	--secondary-hover-text: #EFAF00;
	--secondary-light: #{$orange-light-2};
	--secondary-var: #{$orange};
	--success-hover-border: 1px solid transparent;
	--tertiary-hover-border: none;
	--tertiary-hover-text: #{$green};
	--tertiary-light: #{$green-light-2};
	--tertiary-var: #{$tertiary};
	--warning-hover-border: 1px solid transparent;
	--wp-link-hover: #{$primary};
	--wp-nth-child: #{$gray-100};
	--wp-pink-darken-expanded: #{$wp-pink-darken};
	--wc-purple-expanded: #{$wc-purple};
	--wp-yiq-text-dark: #{$yiq-text-dark};
	--wp-yiq-text-light: #{$yiq-text-light};
}
