//
// CSS Animations
// Ported from Animate.css
// https://github.com/daneden/animate.css
//---------------------------------------

// FadeIn
@keyframes fadeIn{
	from {
		opacity: 0;
	}
	
	to {
		opacity: 1;
	}
}

// FadeInDown
@keyframes fadeInDown {
	from {
		opacity: 0;
		transform: translate3d(0, -100%, 0);
	}
	
	to {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}

// FadeInUp
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translate3d(0, 100%, 0);
	}
	
	to {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}

// FadeOut
@keyframes fadeOut {
	from {
		opacity: 1;
	}
	
	to {
		opacity: 0;
	}
}

// FadeOutDown
@keyframes fadeOutDown {
	from {
		opacity: 1;
	}
	
	to {
		opacity: 0;
		transform: translate3d(0, 100%, 0);
	}
}

// FadeOutUp
@keyframes fadeOutUp {
	from {
		opacity: 1;
	}
	
	to {
		opacity: 1;
		transform: translate3d(0, -100%, 0);
	}
}

// ATUM menu animation
@keyframes anim-atum-menu {
	50% {
		opacity: 0;
		transform: translate3d(0, 100%, 0);
	}
	
	51% {
		opacity: 0;
		transform: translate3d(0, -100%, 0);
	}
	
	100% {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}