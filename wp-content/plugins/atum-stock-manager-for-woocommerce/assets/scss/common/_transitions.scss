@use "variables" as *;

// stylelint-disable selector-no-qualifying-type

.fade {
	transition: $transition-fade;
	
	&:not(.show) {
		opacity: 0;
	}
	
	// Deprecated (Bootstrap 3 compatibility)
	&.in {
		opacity: 1;
	}
}

.collapse {
	display: none;
	
	&.show {
		display: block;
	}
	
	// Deprecated (Bootstrap 3 compatibility)
	&.in {
		display: block;
	}
}

// Deprecated (Bootstrap 3 compatibility)
tr.collapse.in {
	display: table-row;
}

// Deprecated (Bootstrap 3 compatibility)
tbody.collapse.in {
	display: table-row-group;
}

.collapsing {
	position: relative;
	height: 0;
	overflow: hidden;
	transition: $transition-collapse;
}
