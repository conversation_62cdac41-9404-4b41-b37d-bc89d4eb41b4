@use "sass:color";
@use "sass:list";
@use "sass:map";
@use "sass:meta";
@use "sass:string";
@use "variables" as *;

// Bootstrap functions
//
// Utility mixins and functions for evalutating source code across our variables, maps, and mixins.

@use "sass:math";

// Ascending
// Used to evaluate Sass maps like our grid breakpoints.
@mixin _assert-ascending($map, $map-name) {
	$prev-key: null;
	$prev-num: null;
	@each $key, $num in $map {
		@if $prev-num == null {
			// Do nothing
		} @else if not math.compatible($prev-num, $num) {
			@warn "Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !";
		} @else if $prev-num >= $num {
			@warn "Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !";
		}
		$prev-key: $key;
		$prev-num: $num;
	}
}

// Starts at zero
// Another grid mixin that ensures the min-width of the lowest breakpoint starts at 0.
@mixin _assert-starts-at-zero($map) {
	$values: map.values($map);
	$first-value: list.nth($values, 1);
	@if $first-value != 0 {
		@warn "First breakpoint in `$grid-breakpoints` must start at 0, but starts at #{$first-value}.";
	}
}

// Replace `$search` with `$replace` in `$string`
// Used on our SVG icon backgrounds for custom forms.
//
// <AUTHOR> Giraudel
// @param {String} $string - Initial string
// @param {String} $search - Substring to replace
// @param {String} $replace ('') - New value
// @return {String} - Updated string
@function str-replace($string, $search, $replace: "") {
	$index: string.index($string, $search);
	
	@if $index {
		@return string.slice($string, 1, $index - 1) + $replace + str-replace(string.slice($string, $index + string.length($search)), $search, $replace);
	}
	
	@return $string;
}

// Color contrast
@function color-yiq($color) {
	$r: color.channel($color, "red", $space: rgb);
	$g: color.channel($color, "green", $space: rgb);
	$b: color.channel($color, "blue", $space: rgb);
	
	$yiq: math.div((($r * 299) + ($g * 587) + ($b * 114)), 1000);
	
	@if ($yiq >= $yiq-contrasted-threshold) {
		//@return $yiq-text-dark;
		@return var(--wp-yiq-text-dark);
	} @else {
		//@return $yiq-text-light;
		@return var(--wp-yiq-text-light);
	}
}

// Retrieve color Sass maps
@function color($key: "blue") {
	@return map.get($colors, $key);
}

@function theme-color($key: "primary") {
	@return map.get($theme-colors, $key);
}

@function gray($key: "100") {
	@return map.get($grays, $key);
}

// Request a theme color level
@function theme-color-level($color-name: "primary", $level: 0) {
	$color: theme-color($color-name);
	$color-base: if($level > 0, $black, $white);
	$level: math.abs($level);
	
	@return color.mix($color-base, $color, $level * $theme-color-interval);
}

@function subtract($value1, $value2, $return-calc: true) {
	@if $value1 == null and $value2 == null {
		@return null;
	}
	
	@if $value1 == null {
		@return -$value2;
	}
	
	@if $value2 == null {
		@return $value1;
	}
	
	@if meta.type-of($value1) == number and meta.type-of($value2) == number and math.compatible($value1, $value2) {
		@return $value1 - $value2;
	}
	
	@return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + string.unquote(" - ") + $value2);
}

// Shade a color: mix a color with black
@function shade-color($color, $weight) {
	@return color.mix(black, $color, $weight);
}

/*
 * Bootstrap 5
 */

// See https://codepen.io/kevinweber/pen/dXWoRw
//
// Requires the use of quotes around data URIs.

@function escape-svg($string) {
	@if string.index($string, "data:image/svg+xml") {
		@each $char, $encoded in $escaped-characters {
			// Do not escape the url brackets
			@if string.index($string, "url(") == 1 {
				$string: url("#{str-replace(string.slice($string, 6, -3), $char, $encoded)}");
			} @else {
				$string: str-replace($string, $char, $encoded);
			}
		}
	}
	
	@return $string;
}