@use "../functions";
@use "../variables" as *;
@use "hover";

// Button variants
//
// Easily pump out default styles, as well as :hover, :focus, :active,
// and disabled options for all buttons

@mixin button-variant($color, $background, $border, $hover-background: rgba($background, 0.6), $hover-border: rgba($border, 0.6), $active-background: rgba($background, 0.6), $active-border: rgba($border, 0.6)) {
	color: functions.color-yiq($background);
	background-color: var(--#{$color});
	border-color: var(--#{$color});
	
	@include hover.hover {
		color: functions.color-yiq($background);
		background-color: var(--#{$color}-hover);
		border-color: var(--#{$color}-hover);
	}
	
	&:focus,
	&.focus {
		color: functions.color-yiq($hover-background);
		// Avoid using mixin so we can pass custom focus shadow properly
		@if $enable-shadows {
			box-shadow: $btn-box-shadow, 0 0 0 $btn-focus-width rgba($border, .5);
		} @else {
			box-shadow: 0 0 0 $btn-focus-width rgba($border, .5);
		}
	}
	
	// Disabled comes first so active can properly restyle
	&.disabled,
	&:disabled {
		color: functions.color-yiq($background);
		background-color: var(--#{$color});
		border-color: var(--#{$color});
	}
	
	&:not(:disabled):not(.disabled):active,
	&:not(:disabled):not(.disabled).active,
	.show > &.dropdown-toggle {
		color: functions.color-yiq($active-background);
		background-color: var(--#{$color}-hover);
		@if $enable-gradients {
			background-image: none; // Remove the gradient for the pressed/active state
		}
		border-color: var(--#{$color}-hover);
		
		&:focus {
			color: functions.color-yiq($hover-background);
			// Avoid using mixin so we can pass custom focus shadow properly
			@if $enable-shadows {
				box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($border, .5);
			} @else {
				box-shadow: 0 0 0 $btn-focus-width rgba($border, .5);
			}
		}
	}
}

@mixin button-outline-variant($color, $value, $color-hover: functions.color-yiq($value), $active-background: var(--#{$color}), $active-border: var(--#{$color})) {
	color: var(--#{$color});
	background-color: transparent;
	background-image: none;
	border-color: var(--#{$color});
	
	&:hover {
		color: $color-hover;
		background-color: $active-background;
		border-color: $active-border;
	}
	
	&:focus,
	&.focus {
		box-shadow: 0 0 0 $btn-focus-width var(--#{$color}-shadow);
	}
	
	&.disabled,
	&:disabled {
		color: var(--#{$color});
		background-color: transparent;
	}
	
	&:not(:disabled):not(.disabled):active,
	&:not(:disabled):not(.disabled).active,
	.show > &.dropdown-toggle {
		color: functions.color-yiq($value);
		background-color: $active-background;
		border-color: $active-border;
		
		&:focus {
			// Avoid using mixin so we can pass custom focus shadow properly
			@if $enable-shadows and $btn-active-box-shadow != none {
				box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width var(--#{$color}-shadow);
			} @else {
				box-shadow: 0 0 0 $btn-focus-width var(--#{$color}-shadow);
			}
		}
	}
}

// Button sizes
@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {
	padding: $padding-y $padding-x;
	font-size: $font-size;
	line-height: $line-height;
	// Manually declare to provide an override to the browser default
	@if $enable-rounded {
		border-radius: $border-radius;
	} @else {
		border-radius: 0;
	}
}